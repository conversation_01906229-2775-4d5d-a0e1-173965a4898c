<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Autofill Status</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: #f8fafc;
            padding: 15px;
            height: 100vh;
            overflow-y: auto;
        }

        .container {
            max-width: 100%;
            height: 100%;
        }

        .status-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            height: calc(100% - 30px);
            overflow-y: auto;
        }

        .status-header {
            text-align: center;
            margin-bottom: 20px;
        }

        .status-header h2 {
            color: #2d3748;
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .current-status {
            background: linear-gradient(135deg, #667eea20, #764ba220);
            border: 2px solid #667eea;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }

        .status-icon {
            font-size: 2rem;
            margin-bottom: 8px;
        }

        .status-text {
            color: #2d3748;
            font-weight: 600;
            font-size: 1rem;
            margin-bottom: 5px;
        }

        .status-description {
            color: #718096;
            font-size: 0.85rem;
        }

        .progress-section {
            margin-bottom: 20px;
        }

        .progress-title {
            color: #4a5568;
            font-weight: 600;
            font-size: 0.9rem;
            margin-bottom: 10px;
        }

        .progress-bar {
            background: #e2e8f0;
            border-radius: 10px;
            height: 8px;
            overflow: hidden;
            margin-bottom: 8px;
        }

        .progress-fill {
            background: linear-gradient(135deg, #667eea, #764ba2);
            height: 100%;
            border-radius: 10px;
            transition: width 0.5s ease;
        }

        .progress-text {
            color: #718096;
            font-size: 0.8rem;
            text-align: right;
        }

        .steps-list {
            margin-bottom: 20px;
        }

        .step-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 0;
            border-bottom: 1px solid #f1f5f9;
        }

        .step-item:last-child {
            border-bottom: none;
        }

        .step-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .step-icon.completed {
            background: #48bb78;
            color: white;
        }

        .step-icon.current {
            background: #667eea;
            color: white;
        }

        .step-icon.pending {
            background: #e2e8f0;
            color: #a0aec0;
        }

        .step-text {
            flex: 1;
            color: #2d3748;
            font-size: 0.85rem;
        }

        .step-text.completed {
            color: #48bb78;
        }

        .step-text.current {
            color: #667eea;
            font-weight: 600;
        }

        .step-text.pending {
            color: #a0aec0;
        }

        .results-section {
            background: #f7fafc;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .results-title {
            color: #4a5568;
            font-weight: 600;
            font-size: 0.9rem;
            margin-bottom: 10px;
        }

        .field-result {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px 0;
            border-bottom: 1px solid #e2e8f0;
            font-size: 0.8rem;
        }

        .field-result:last-child {
            border-bottom: none;
        }

        .field-name {
            color: #2d3748;
            font-weight: 500;
        }

        .field-status {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
        }

        .field-status.success {
            background: #c6f6d5;
            color: #22543d;
        }

        .field-status.failed {
            background: #fed7d7;
            color: #742a2a;
        }

        .field-status.skipped {
            background: #fef5e7;
            color: #744210;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
            margin-top: 15px;
        }

        .btn {
            flex: 1;
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }

        .btn-secondary:hover {
            background: #cbd5e0;
        }

        .btn:disabled {
            background: #cbd5e0;
            color: #a0aec0;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="status-container">
            <div class="status-header">
                <h2>🚀 Autofill Status</h2>
            </div>

            <div class="current-status">
                <div class="status-icon">🔄</div>
                <div class="status-text">Analyzing Page</div>
                <div class="status-description">Please wait while we analyze the form structure</div>
            </div>

            <div class="progress-section">
                <div class="progress-title">Overall Progress</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 0%" id="progressFill"></div>
                </div>
                <div class="progress-text" id="progressText">0% Complete</div>
            </div>

            <div class="steps-list">
                <div class="step-item">
                    <div class="step-icon pending">1</div>
                    <div class="step-text pending">Analyzing page structure</div>
                </div>
                <div class="step-item">
                    <div class="step-icon pending">2</div>
                    <div class="step-text pending">Identifying form fields</div>
                </div>
                <div class="step-item">
                    <div class="step-icon pending">3</div>
                    <div class="step-text pending">Matching user data</div>
                </div>
                <div class="step-item">
                    <div class="step-icon pending">4</div>
                    <div class="step-text pending">Filling form fields</div>
                </div>
                <div class="step-item">
                    <div class="step-icon pending">5</div>
                    <div class="step-text pending">Validation complete</div>
                </div>
            </div>

            <div class="results-section" id="resultsSection" style="display: none;">
                <div class="results-title">Fill Results</div>
                <div class="field-result">
                    <span class="field-name">Email Address</span>
                    <span class="field-status success">✓ Filled</span>
                </div>
                <div class="field-result">
                    <span class="field-name">Full Name</span>
                    <span class="field-status success">✓ Filled</span>
                </div>
                <div class="field-result">
                    <span class="field-name">Phone Number</span>
                    <span class="field-status skipped">⚠ Skipped</span>
                </div>
                <div class="field-result">
                    <span class="field-name">Address</span>
                    <span class="field-status success">✓ Filled</span>
                </div>
            </div>

            <div class="action-buttons">
                <button class="btn btn-secondary" id="cancelBtn">Cancel</button>
                <button class="btn btn-primary" id="startBtn" disabled style="display: none;">Start Autofill</button>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 0;
        let isRunning = false;

        function startAutofill() {
            if (isRunning) return;

            isRunning = true;
            currentStep = 0;

            // Update UI
            document.getElementById('startBtn').disabled = true;
            document.getElementById('startBtn').textContent = 'Running...';
            document.querySelector('.status-text').textContent = 'Analyzing Page';
            document.querySelector('.status-description').textContent = 'Please wait while we analyze the form structure';
            document.querySelector('.status-icon').textContent = '🔄';

            // Simulate autofill process
            simulateAutofillProcess();
        }

        // Auto-start the autofill process when page loads
        window.addEventListener('load', function() {
            // Start automatically after a brief delay
            setTimeout(startAutofill, 500);
        });

        function simulateAutofillProcess() {
            const steps = [
                'Analyzing page structure',
                'Identifying form fields',
                'Matching user data',
                'Filling form fields',
                'Validation complete'
            ];

            const stepElements = document.querySelectorAll('.step-item');

            function processStep() {
                if (currentStep < steps.length) {
                    // Update current step
                    const stepIcon = stepElements[currentStep].querySelector('.step-icon');
                    const stepText = stepElements[currentStep].querySelector('.step-text');

                    stepIcon.className = 'step-icon current';
                    stepText.className = 'step-text current';

                    // Update progress
                    const progress = ((currentStep + 1) / steps.length) * 100;
                    document.getElementById('progressFill').style.width = progress + '%';
                    document.getElementById('progressText').textContent = Math.round(progress) + '% Complete';

                    setTimeout(() => {
                        // Mark as completed
                        stepIcon.className = 'step-icon completed';
                        stepIcon.textContent = '✓';
                        stepText.className = 'step-text completed';

                        currentStep++;

                        if (currentStep < steps.length) {
                            setTimeout(processStep, 500);
                        } else {
                            // Process complete
                            completeAutofill();
                        }
                    }, 1500);
                }
            }

            processStep();
        }

        function completeAutofill() {
            // Update status
            document.querySelector('.status-text').textContent = 'Autofill Complete';
            document.querySelector('.status-description').textContent = '4 fields filled successfully, 1 skipped';
            document.querySelector('.status-icon').textContent = '✅';

            // Show results
            document.getElementById('resultsSection').style.display = 'block';

            // Show and enable the "Start Again" button
            document.getElementById('startBtn').style.display = 'inline-block';
            document.getElementById('startBtn').disabled = false;
            document.getElementById('startBtn').textContent = 'Start Again';
            document.getElementById('startBtn').onclick = function() {
                // Reset to initial state and restart
                location.reload();
            };

            isRunning = false;
        }

        // Cancel button
        document.getElementById('cancelBtn').addEventListener('click', function() {
            if (isRunning) {
                if (confirm('Are you sure you want to cancel the autofill process?')) {
                    location.reload();
                }
            } else {
                // Reset to initial state
                location.reload();
            }
        });
    </script>
</body>
</html>
