<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile Editor</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: #f8fafc;
            padding: 20px;
            height: 100vh;
            overflow-y: auto;
        }

        .container {
            max-width: 100%;
            height: 100%;
        }

        .form-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            height: calc(100% - 40px);
            display: flex;
            flex-direction: column;
        }

        .form-header {
            text-align: center;
            margin-bottom: 25px;
        }

        .form-header h2 {
            color: #2d3748;
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .form-header p {
            color: #718096;
            font-size: 0.9rem;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            color: #4a5568;
            font-weight: 600;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            font-family: inherit;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-textarea {
            min-height: 120px;
            resize: vertical;
            font-family: inherit;
            line-height: 1.5;
        }

        .form-actions {
            display: flex;
            gap: 12px;
            margin-top: auto;
            padding-top: 20px;
        }

        .btn {
            flex: 1;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }

        .btn-secondary:hover {
            background: #cbd5e0;
            transform: translateY(-2px);
        }

        .btn:disabled {
            background: #cbd5e0;
            color: #a0aec0;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .character-count {
            text-align: right;
            font-size: 0.8rem;
            color: #718096;
            margin-top: 5px;
        }

        .validation-message {
            color: #e53e3e;
            font-size: 0.8rem;
            margin-top: 5px;
            display: none;
        }

        .form-input.error {
            border-color: #e53e3e;
        }

        .form-input.error:focus {
            box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
        }

        .example-info {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin-top: 10px;
        }

        .example-info h4 {
            color: #4a5568;
            font-size: 0.9rem;
            margin-bottom: 8px;
        }

        .example-info p {
            color: #718096;
            font-size: 0.8rem;
            line-height: 1.4;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="form-container">
            <div class="form-header">
                <h2>✏️ Profile Editor</h2>
                <p>Create or edit your autofill profile information</p>
            </div>

            <form id="profileForm">
                <div class="form-group">
                    <label for="profileName" class="form-label">Profile Name *</label>
                    <input 
                        type="text" 
                        id="profileName" 
                        class="form-input" 
                        placeholder="e.g., jaysean, work profile, personal"
                        maxlength="50"
                        required
                    >
                    <div class="character-count">
                        <span id="nameCount">0</span>/50
                    </div>
                    <div class="validation-message" id="nameError">
                        Profile name is required and must be unique
                    </div>
                </div>

                <div class="form-group">
                    <label for="profileInfo" class="form-label">Profile Information *</label>
                    <textarea 
                        id="profileInfo" 
                        class="form-input form-textarea" 
                        placeholder="Enter your information in natural language..."
                        maxlength="1000"
                        required
                    ></textarea>
                    <div class="character-count">
                        <span id="infoCount">0</span>/1000
                    </div>
                    <div class="validation-message" id="infoError">
                        Profile information is required
                    </div>
                </div>

                <div class="example-info">
                    <h4>💡 Example Information:</h4>
                    <p><strong>Personal:</strong> 我叫千岁，英文名是jaysean 出生于1989/07/31 邮箱jaysean<EMAIL></p>
                    <p><strong>Address:</strong> 我喜欢唱歌跳舞，叫韩心怡，最近在澳洲读design ,Unit 308, 119 Ross Street, Glebe, NSW 2037, Australia</p>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" id="cancelBtn">
                        Cancel
                    </button>
                    <button type="submit" class="btn btn-primary" id="saveBtn" disabled>
                        💾 Save Profile
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        const form = document.getElementById('profileForm');
        const nameInput = document.getElementById('profileName');
        const infoInput = document.getElementById('profileInfo');
        const saveBtn = document.getElementById('saveBtn');
        const cancelBtn = document.getElementById('cancelBtn');
        const nameCount = document.getElementById('nameCount');
        const infoCount = document.getElementById('infoCount');

        // Character counting
        nameInput.addEventListener('input', function() {
            nameCount.textContent = this.value.length;
            validateForm();
        });

        infoInput.addEventListener('input', function() {
            infoCount.textContent = this.value.length;
            validateForm();
        });

        // Form validation
        function validateForm() {
            const nameValid = nameInput.value.trim().length > 0;
            const infoValid = infoInput.value.trim().length > 0;
            
            // Update UI
            nameInput.classList.toggle('error', !nameValid && nameInput.value.length > 0);
            infoInput.classList.toggle('error', !infoValid && infoInput.value.length > 0);
            
            // Enable/disable save button
            saveBtn.disabled = !(nameValid && infoValid);
        }

        // Form submission
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const profileData = {
                name: nameInput.value.trim(),
                info: infoInput.value.trim()
            };
            
            // In a real implementation, this would save to localStorage
            alert(`Profile saved:\nName: ${profileData.name}\nInfo: ${profileData.info.substring(0, 50)}...`);
            
            // Reset form
            form.reset();
            nameCount.textContent = '0';
            infoCount.textContent = '0';
            validateForm();
        });

        // Cancel button
        cancelBtn.addEventListener('click', function() {
            if (nameInput.value || infoInput.value) {
                if (confirm('Are you sure you want to cancel? Unsaved changes will be lost.')) {
                    form.reset();
                    nameCount.textContent = '0';
                    infoCount.textContent = '0';
                    validateForm();
                }
            }
        });

        // Initialize
        validateForm();
    </script>
</body>
</html>
