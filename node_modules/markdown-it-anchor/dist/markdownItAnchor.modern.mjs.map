{"version": 3, "file": "markdownItAnchor.modern.mjs", "sources": ["../permalink.js", "../index.js"], "sourcesContent": ["let emittedWarning = false\n\nconst position = {\n  false: 'push',\n  true: 'unshift',\n  after: 'push',\n  before: 'unshift'\n}\n\nconst permalinkSymbolMeta = {\n  isPermalinkSymbol: true\n}\n\nexport function legacy (slug, opts, state, idx) {\n  if (!emittedWarning) {\n    const warningText = 'Using deprecated markdown-it-anchor permalink option, see https://github.com/valeriangalliat/markdown-it-anchor#permalinks'\n\n    if (typeof process === 'object' && process && process.emitWarning) {\n      process.emitWarning(warningText)\n    } else {\n      console.warn(warningText)\n    }\n\n    emittedWarning = true\n  }\n\n  const linkTokens = [\n    Object.assign(new state.Token('link_open', 'a', 1), {\n      attrs: [\n        ...(opts.permalinkClass ? [['class', opts.permalinkClass]] : []),\n        ['href', opts.permalinkHref(slug, state)],\n        ...Object.entries(opts.permalinkAttrs(slug, state))\n      ]\n    }),\n    Object.assign(new state.Token('html_block', '', 0), { content: opts.permalinkSymbol, meta: permalinkSymbolMeta }),\n    new state.Token('link_close', 'a', -1)\n  ]\n\n  if (opts.permalinkSpace) {\n    state.tokens[idx + 1].children[position[opts.permalinkBefore]](Object.assign(new state.Token('text', '', 0), { content: ' ' }))\n  }\n\n  state.tokens[idx + 1].children[position[opts.permalinkBefore]](...linkTokens)\n}\n\nexport function renderHref (slug) {\n  return `#${slug}`\n}\n\nexport function renderAttrs (slug) {\n  return {}\n}\n\nconst commonDefaults = {\n  class: 'header-anchor',\n  symbol: '#',\n  renderHref,\n  renderAttrs\n}\n\nexport function makePermalink (renderPermalinkImpl) {\n  function renderPermalink (opts) {\n    opts = Object.assign({}, renderPermalink.defaults, opts)\n\n    return (slug, anchorOpts, state, idx) => {\n      return renderPermalinkImpl(slug, opts, anchorOpts, state, idx)\n    }\n  }\n\n  renderPermalink.defaults = Object.assign({}, commonDefaults)\n  renderPermalink.renderPermalinkImpl = renderPermalinkImpl\n\n  return renderPermalink\n}\n\nexport const linkInsideHeader = makePermalink((slug, opts, anchorOpts, state, idx) => {\n  const linkTokens = [\n    Object.assign(new state.Token('link_open', 'a', 1), {\n      attrs: [\n        ...(opts.class ? [['class', opts.class]] : []),\n        ['href', opts.renderHref(slug, state)],\n        ...(opts.ariaHidden ? [['aria-hidden', 'true']] : []),\n        ...Object.entries(opts.renderAttrs(slug, state))\n      ]\n    }),\n    Object.assign(new state.Token('html_inline', '', 0), { content: opts.symbol, meta: permalinkSymbolMeta }),\n    new state.Token('link_close', 'a', -1)\n  ]\n\n  if (opts.space) {\n    const space = typeof opts.space === 'string' ? opts.space : ' '\n    const type = typeof opts.space === 'string' ? 'html_inline' : 'text'\n    state.tokens[idx + 1].children[position[opts.placement]](Object.assign(new state.Token(type, '', 0), { content: space }))\n  }\n\n  state.tokens[idx + 1].children[position[opts.placement]](...linkTokens)\n})\n\nObject.assign(linkInsideHeader.defaults, {\n  space: true,\n  placement: 'after',\n  ariaHidden: false\n})\n\nexport const ariaHidden = makePermalink(linkInsideHeader.renderPermalinkImpl)\n\nariaHidden.defaults = Object.assign({}, linkInsideHeader.defaults, {\n  ariaHidden: true\n})\n\nexport const headerLink = makePermalink((slug, opts, anchorOpts, state, idx) => {\n  const linkTokens = [\n    Object.assign(new state.Token('link_open', 'a', 1), {\n      attrs: [\n        ...(opts.class ? [['class', opts.class]] : []),\n        ['href', opts.renderHref(slug, state)],\n        ...Object.entries(opts.renderAttrs(slug, state))\n      ]\n    }),\n    ...(opts.safariReaderFix ? [new state.Token('span_open', 'span', 1)] : []),\n    ...state.tokens[idx + 1].children,\n    ...(opts.safariReaderFix ? [new state.Token('span_close', 'span', -1)] : []),\n    new state.Token('link_close', 'a', -1)\n  ]\n\n  state.tokens[idx + 1] = Object.assign(new state.Token('inline', '', 0), {\n    children: linkTokens\n  })\n})\n\nObject.assign(headerLink.defaults, {\n  safariReaderFix: false\n})\n\nexport const linkAfterHeader = makePermalink((slug, opts, anchorOpts, state, idx) => {\n  if (!['visually-hidden', 'aria-label', 'aria-describedby', 'aria-labelledby'].includes(opts.style)) {\n    throw new Error(`\\`permalink.linkAfterHeader\\` called with unknown style option \\`${opts.style}\\``)\n  }\n\n  if (!['aria-describedby', 'aria-labelledby'].includes(opts.style) && !opts.assistiveText) {\n    throw new Error(`\\`permalink.linkAfterHeader\\` called without the \\`assistiveText\\` option in \\`${opts.style}\\` style`)\n  }\n\n  if (opts.style === 'visually-hidden' && !opts.visuallyHiddenClass) {\n    throw new Error('`permalink.linkAfterHeader` called without the `visuallyHiddenClass` option in `visually-hidden` style')\n  }\n\n  const title = state.tokens[idx + 1]\n    .children\n    .filter(token => token.type === 'text' || token.type === 'code_inline')\n    .reduce((acc, t) => acc + t.content, '')\n\n  const subLinkTokens = []\n  const linkAttrs = []\n\n  if (opts.class) {\n    linkAttrs.push(['class', opts.class])\n  }\n\n  linkAttrs.push(['href', opts.renderHref(slug, state)])\n  linkAttrs.push(...Object.entries(opts.renderAttrs(slug, state)))\n\n  if (opts.style === 'visually-hidden') {\n    subLinkTokens.push(\n      Object.assign(new state.Token('span_open', 'span', 1), {\n        attrs: [['class', opts.visuallyHiddenClass]],\n      }),\n      Object.assign(new state.Token('text', '', 0), {\n        content: opts.assistiveText(title)\n      }),\n      new state.Token('span_close', 'span', -1)\n    )\n\n    if (opts.space) {\n      const space = typeof opts.space === 'string' ? opts.space : ' '\n      const type = typeof opts.space === 'string' ? 'html_inline' : 'text'\n      subLinkTokens[position[opts.placement]](Object.assign(new state.Token(type, '', 0), { content: space }))\n    }\n\n    subLinkTokens[position[opts.placement]](\n      Object.assign(new state.Token('span_open', 'span', 1), {\n        attrs: [['aria-hidden', 'true']],\n      }),\n      Object.assign(new state.Token('html_inline', '', 0), {\n        content: opts.symbol,\n        meta: permalinkSymbolMeta\n      }),\n      new state.Token('span_close', 'span', -1)\n    )\n  } else {\n    subLinkTokens.push(\n      Object.assign(new state.Token('html_inline', '', 0), {\n        content: opts.symbol,\n        meta: permalinkSymbolMeta\n      })\n    )\n  }\n\n  if (opts.style === 'aria-label') {\n    linkAttrs.push(['aria-label', opts.assistiveText(title)])\n  } else if (['aria-describedby', 'aria-labelledby'].includes(opts.style)) {\n    linkAttrs.push([opts.style, slug])\n  }\n\n  const linkTokens = [\n    Object.assign(new state.Token('link_open', 'a', 1), {\n      attrs: linkAttrs\n    }),\n    ...subLinkTokens,\n    new state.Token('link_close', 'a', -1),\n  ]\n\n  state.tokens.splice(idx + 3, 0, ...linkTokens)\n\n  if (opts.wrapper) {\n    state.tokens.splice(idx, 0, Object.assign(new state.Token('html_block', '', 0), {\n      content: opts.wrapper[0] + '\\n'\n    }))\n\n    state.tokens.splice(idx + 3 + linkTokens.length + 1, 0, Object.assign(new state.Token('html_block', '', 0), {\n      content: opts.wrapper[1] + '\\n'\n    }))\n  }\n})\n\nObject.assign(linkAfterHeader.defaults, {\n  style: 'visually-hidden',\n  space: true,\n  placement: 'after',\n  wrapper: null\n})\n", "import * as permalink from './permalink'\n\nconst slugify = (s) => encodeURIComponent(String(s).trim().toLowerCase().replace(/\\s+/g, '-'))\n\nfunction getTokensText (tokens) {\n  return tokens\n    .filter(t => ['text', 'code_inline'].includes(t.type))\n    .map(t => t.content)\n    .join('')\n}\n\nfunction uniqueSlug (slug, slugs, failOnNonUnique, startIndex) {\n  let uniq = slug\n  let i = startIndex\n\n  if (failOnNonUnique && Object.prototype.hasOwnProperty.call(slugs, uniq)) {\n    throw new Error(`User defined \\`id\\` attribute \\`${slug}\\` is not unique. Please fix it in your Markdown to continue.`)\n  } else {\n    while (Object.prototype.hasOwnProperty.call(slugs, uniq)) {\n      uniq = `${slug}-${i}`\n      i += 1\n    }\n  }\n\n  slugs[uniq] = true\n\n  return uniq\n}\n\nconst isLevelSelectedNumber = selection => level => level >= selection\nconst isLevelSelectedArray = selection => level => selection.includes(level)\n\nfunction anchor (md, opts) {\n  opts = Object.assign({}, anchor.defaults, opts)\n\n  md.core.ruler.push('anchor', state => {\n    const slugs = {}\n    const tokens = state.tokens\n\n    const isLevelSelected = Array.isArray(opts.level)\n      ? isLevelSelectedArray(opts.level)\n      : isLevelSelectedNumber(opts.level)\n\n    for (let idx = 0; idx < tokens.length; idx++) {\n      const token = tokens[idx]\n\n      if (token.type !== 'heading_open') {\n        continue\n      }\n\n      if (!isLevelSelected(Number(token.tag.substr(1)))) {\n        continue\n      }\n\n      // Aggregate the next token children text.\n      const title = opts.getTokensText(tokens[idx + 1].children)\n\n      let slug = token.attrGet('id')\n\n      if (slug == null) {\n        slug = uniqueSlug(opts.slugify(title), slugs, false, opts.uniqueSlugStartIndex)\n      } else {\n        slug = uniqueSlug(slug, slugs, true, opts.uniqueSlugStartIndex)\n      }\n\n      token.attrSet('id', slug)\n\n      if (opts.tabIndex !== false) {\n        token.attrSet('tabindex', `${opts.tabIndex}`)\n      }\n\n      if (typeof opts.permalink === 'function') {\n        opts.permalink(slug, opts, state, idx)\n      } else if (opts.permalink) {\n        opts.renderPermalink(slug, opts, state, idx)\n      } else if (opts.renderPermalink && opts.renderPermalink !== permalink.legacy) {\n        opts.renderPermalink(slug, opts, state, idx)\n      }\n\n      // A permalink renderer could modify the `tokens` array so\n      // make sure to get the up-to-date index on each iteration.\n      idx = tokens.indexOf(token)\n\n      if (opts.callback) {\n        opts.callback(token, { slug, title })\n      }\n    }\n  })\n}\n\nanchor.permalink = permalink\n\nanchor.defaults = {\n  level: 1,\n  slugify,\n  uniqueSlugStartIndex: 1,\n  tabIndex: '-1',\n  getTokensText,\n\n  // Legacy options.\n  permalink: false,\n  renderPermalink: permalink.legacy,\n  permalinkClass: permalink.ariaHidden.defaults.class,\n  permalinkSpace: permalink.ariaHidden.defaults.space,\n  permalinkSymbol: '¶',\n  permalinkBefore: permalink.ariaHidden.defaults.placement === 'before',\n  permalinkHref: permalink.ariaHidden.defaults.renderHref,\n  permalinkAttrs: permalink.ariaHidden.defaults.renderAttrs\n}\n\n// Dirty hack to make `import anchor from 'markdown-it-anchor'` work with\n// TypeScript which doesn't support the `module` field of `package.json` and\n// will always get the CommonJS version which otherwise wouldn't have a\n// `default` key, resulting in markdown-it-anchor being undefined when being\n// imported that way.\nanchor.default = anchor\n\nexport default anchor\n"], "names": ["emitted<PERSON><PERSON>ning", "position", "false", "true", "after", "before", "isPermalinkSymbol", "slug", "opts", "state", "idx", "warningText", "process", "emitWarning", "console", "warn", "linkTokens", "Object", "assign", "Token", "attrs", "permalinkClass", "permalinkHref", "entries", "permalinkAttrs", "content", "permalinkSymbol", "meta", "permalinkSymbolMeta", "permalinkSpace", "tokens", "children", "permalinkBefore", "renderAttrs", "commonDefaults", "class", "symbol", "renderHref", "makePermalink", "renderPermalinkImpl", "renderPermalink", "defaults", "anchorOpts", "linkInsideHeader", "ariaHidden", "space", "placement", "safariReaderFix", "headerLink", "linkAfterHeader", "includes", "style", "Error", "assistiveText", "visuallyHiddenClass", "filter", "token", "type", "reduce", "acc", "t", "subLinkTokens", "linkAttrs", "push", "title", "splice", "wrapper", "length", "uniqueSlug", "slugs", "failOnNonUnique", "startIndex", "uniq", "i", "prototype", "hasOwnProperty", "call", "anchor", "md", "core", "ruler", "isLevelSelected", "Array", "isArray", "level", "selection", "isLevelSelectedNumber", "Number", "tag", "substr", "getTokensText", "attrGet", "slugify", "uniqueSlugStartIndex", "attrSet", "tabIndex", "permalink", "indexOf", "callback", "s", "encodeURIComponent", "String", "trim", "toLowerCase", "replace", "map", "join", "default"], "mappings": "AAAA,IAAIA,GAAiB,EAErB,MAAcC,EAAG,CACfC,MAAO,OACPC,KAAM,UACNC,MAAO,OACPC,OAAQ,aAGkB,CAC1BC,mBAAmB,GAGd,WAAiBC,EAAMC,EAAMC,EAAOC,GACzC,IAAKV,EAAgB,CACnB,MAAiBW,EAAG,6HAEG,iBAALC,SAAiBA,SAAWA,QAAQC,YACpDD,QAAQC,YAAYF,GAEpBG,QAAQC,KAAKJ,GAGfX,GAAiB,CACnB,CAEA,MAAgBgB,EAAG,CACjBC,OAAOC,OAAO,IAAIT,EAAMU,MAAM,YAAa,IAAK,GAAI,CAClDC,MAAO,IACDZ,EAAKa,eAAiB,CAAC,CAAC,QAASb,EAAKa,iBAAmB,GAC7D,CAAC,OAAQb,EAAKc,cAAcf,EAAME,OAC/BQ,OAAOM,QAAQf,EAAKgB,eAAejB,EAAME,OAGhDQ,OAAOC,OAAO,IAAST,EAACU,MAAM,aAAc,GAAI,GAAI,CAAEM,QAASjB,EAAKkB,gBAAiBC,KAAMC,IAC3F,MAAUT,MAAM,aAAc,KAAM,IAGlCX,EAAKqB,gBACPpB,EAAMqB,OAAOpB,EAAM,GAAGqB,SAAS9B,EAASO,EAAKwB,kBAAkBf,OAAOC,OAAO,IAAIT,EAAMU,MAAM,OAAQ,GAAI,GAAI,CAAEM,QAAS,OAG1HhB,EAAMqB,OAAOpB,EAAM,GAAGqB,SAAS9B,EAASO,EAAKwB,qBAAqBhB,EACpE,CAEO,WAAqBT,GAC1B,MAAQ,IAAGA,GACb,CAEgB0B,SAAAA,EAAa1B,GAC3B,MAAO,CAAA,CACT,CAEA,MAAoB2B,EAAG,CACrBC,MAAO,gBACPC,OAAQ,IACRC,aACAJ,eAGcK,SAAAA,EAAeC,GAC7B,SAASC,EAAiBhC,GAGxB,OAFAA,EAAOS,OAAOC,OAAO,CAAE,EAAEsB,EAAgBC,SAAUjC,GAE5C,CAACD,EAAMmC,EAAYjC,EAAOC,IACxB6B,EAAoBhC,EAAMC,EAAMkC,EAAYjC,EAAOC,EAE9D,CAKA,OAHA8B,EAAgBC,SAAWxB,OAAOC,OAAO,CAAA,EAAIgB,GAC7CM,EAAgBD,oBAAsBA,EAGxCC,CAAA,CAEaG,MAAAA,EAAmBL,EAAc,CAAC/B,EAAMC,EAAMkC,EAAYjC,EAAOC,KAC5E,QAAmB,CACjBO,OAAOC,OAAO,IAAIT,EAAMU,MAAM,YAAa,IAAK,GAAI,CAClDC,MAAO,IACDZ,EAAK2B,MAAQ,CAAC,CAAC,QAAS3B,EAAK2B,QAAU,GAC3C,CAAC,OAAQ3B,EAAK6B,WAAW9B,EAAME,OAC3BD,EAAKoC,WAAa,CAAC,CAAC,cAAe,SAAW,MAC/C3B,OAAOM,QAAQf,EAAKyB,YAAY1B,EAAME,OAG7CQ,OAAOC,OAAO,IAAST,EAACU,MAAM,cAAe,GAAI,GAAI,CAAEM,QAASjB,EAAK4B,OAAQT,KAAMC,IACnF,IAASnB,EAACU,MAAM,aAAc,KAAM,IAGtC,GAAIX,EAAKqC,MAAO,CACd,QAAoC,mBAAVA,MAAqBrC,EAAKqC,MAAQ,IAE5DpC,EAAMqB,OAAOpB,EAAM,GAAGqB,SAAS9B,EAASO,EAAKsC,YAAY7B,OAAOC,OAAO,IAAST,EAACU,MAD9C,iBAAXX,EAACqC,MAAqB,cAAgB,OAC+B,GAAI,GAAI,CAAEpB,QAASoB,IAClH,CAEApC,EAAMqB,OAAOpB,EAAM,GAAGqB,SAAS9B,EAASO,EAAKsC,eAAe9B,EAAU,GAGxEC,OAAOC,OAAOyB,EAAiBF,SAAU,CACvCI,OAAO,EACPC,UAAW,QACXF,YAAY,IAGP,MAAgBA,EAAGN,EAAcK,EAAiBJ,qBAEzDK,EAAWH,SAAWxB,OAAOC,OAAO,CAAA,EAAIyB,EAAiBF,SAAU,CACjEG,YAAY,IAGP,QAAmBN,EAAc,CAAC/B,EAAMC,EAAMkC,EAAYjC,EAAOC,KACtE,MAAgBM,EAAG,CACjBC,OAAOC,OAAO,MAAUC,MAAM,YAAa,IAAK,GAAI,CAClDC,MAAO,IACDZ,EAAK2B,MAAQ,CAAC,CAAC,QAAS3B,EAAK2B,QAAU,GAC3C,CAAC,OAAQ3B,EAAK6B,WAAW9B,EAAME,OAC5BQ,OAAOM,QAAQf,EAAKyB,YAAY1B,EAAME,UAGzCD,EAAKuC,gBAAkB,CAAC,IAAStC,EAACU,MAAM,YAAa,OAAQ,IAAM,MACpEV,EAAMqB,OAAOpB,EAAM,GAAGqB,YACrBvB,EAAKuC,gBAAkB,CAAC,IAAItC,EAAMU,MAAM,aAAc,QAAS,IAAM,GACzE,IAASV,EAACU,MAAM,aAAc,KAAM,IAGtCV,EAAMqB,OAAOpB,EAAM,GAAKO,OAAOC,OAAO,IAAST,EAACU,MAAM,SAAU,GAAI,GAAI,CACtEY,SAAUf,GAEd,GAEAC,OAAOC,OAAO8B,EAAWP,SAAU,CACjCM,iBAAiB,IAGZ,MAAqBE,EAAGX,EAAc,CAAC/B,EAAMC,EAAMkC,EAAYjC,EAAOC,KAC3E,IAAK,CAAC,kBAAmB,aAAc,mBAAoB,mBAAmBwC,SAAS1C,EAAK2C,OAC1F,MAAM,IAASC,MAAE,oEAAmE5C,EAAK2C,WAG3F,IAAK,CAAC,mBAAoB,mBAAmBD,SAAS1C,EAAK2C,SAAW3C,EAAK6C,cACzE,MAAUD,IAAAA,MAAO,kFAAiF5C,EAAK2C,iBAGzG,GAAmB,oBAAf3C,EAAK2C,QAAgC3C,EAAK8C,oBAC5C,MAAM,UAAU,0GAGlB,QAAc7C,EAAMqB,OAAOpB,EAAM,GAC9BqB,SACAwB,OAAOC,GAAwB,SAAfA,EAAMC,MAAkC,gBAAfD,EAAMC,MAC/CC,OAAO,CAACC,EAAKC,IAAMD,EAAMC,EAAEnC,QAAS,IAEjCoC,EAAgB,GACPC,EAAG,GASlB,GAPItD,EAAK2B,OACP2B,EAAUC,KAAK,CAAC,QAASvD,EAAK2B,QAGhC2B,EAAUC,KAAK,CAAC,OAAQvD,EAAK6B,WAAW9B,EAAME,KAC9CqD,EAAUC,QAAQ9C,OAAOM,QAAQf,EAAKyB,YAAY1B,EAAME,KAErC,oBAAfD,EAAK2C,MAA6B,CAWpC,GAVAU,EAAcE,KACZ9C,OAAOC,OAAO,IAAIT,EAAMU,MAAM,YAAa,OAAQ,GAAI,CACrDC,MAAO,CAAC,CAAC,QAASZ,EAAK8C,wBAEzBrC,OAAOC,OAAO,MAAUC,MAAM,OAAQ,GAAI,GAAI,CAC5CM,QAASjB,EAAK6C,cAAcW,KAE9B,IAASvD,EAACU,MAAM,aAAc,QAAS,IAGrCX,EAAKqC,MAAO,CACd,MAAMA,EAA8B,iBAAfrC,EAAKqC,MAAqBrC,EAAKqC,MAAQ,IAE5DgB,EAAc5D,EAASO,EAAKsC,YAAY7B,OAAOC,OAAO,IAAST,EAACU,MAD7B,iBAAfX,EAAKqC,MAAqB,cAAgB,OACc,GAAI,GAAI,CAAEpB,QAASoB,IACjG,CAEAgB,EAAc5D,EAASO,EAAKsC,YAC1B7B,OAAOC,OAAO,MAAUC,MAAM,YAAa,OAAQ,GAAI,CACrDC,MAAO,CAAC,CAAC,cAAe,WAE1BH,OAAOC,OAAO,IAAST,EAACU,MAAM,cAAe,GAAI,GAAI,CACnDM,QAASjB,EAAK4B,OACdT,KAAMC,IAER,IAASnB,EAACU,MAAM,aAAc,QAAS,GAE3C,MACE0C,EAAcE,KACZ9C,OAAOC,OAAO,IAAST,EAACU,MAAM,cAAe,GAAI,GAAI,CACnDM,QAASjB,EAAK4B,OACdT,KAAMC,KAKO,eAAfpB,EAAK2C,MACPW,EAAUC,KAAK,CAAC,aAAcvD,EAAK6C,cAAcW,KACxC,CAAC,mBAAoB,mBAAmBd,SAAS1C,EAAK2C,QAC/DW,EAAUC,KAAK,CAACvD,EAAK2C,MAAO5C,IAG9B,MAAgBS,EAAG,CACjBC,OAAOC,OAAO,IAAST,EAACU,MAAM,YAAa,IAAK,GAAI,CAClDC,MAAO0C,OAEND,EACH,MAAU1C,MAAM,aAAc,KAAM,IAGtCV,EAAMqB,OAAOmC,OAAOvD,EAAM,EAAG,KAAMM,GAE/BR,EAAK0D,UACPzD,EAAMqB,OAAOmC,OAAOvD,EAAK,EAAGO,OAAOC,OAAO,IAAST,EAACU,MAAM,aAAc,GAAI,GAAI,CAC9EM,QAASjB,EAAK0D,QAAQ,GAAK,QAG7BzD,EAAMqB,OAAOmC,OAAOvD,EAAM,EAAIM,EAAWmD,OAAS,EAAG,EAAGlD,OAAOC,OAAO,IAAST,EAACU,MAAM,aAAc,GAAI,GAAI,CAC1GM,QAASjB,EAAK0D,QAAQ,GAAK,QAE/B,GCnNF,SAAmBE,EAAE7D,EAAM8D,EAAOC,EAAiBC,GACjD,IAAIC,EAAOjE,EACPkE,EAAIF,EAER,GAAID,GAAmBrD,OAAOyD,UAAUC,eAAeC,KAAKP,EAAOG,GACjE,MAAUpB,IAAAA,MAAO,mCAAkC7C,kEAEnD,KAAOU,OAAOyD,UAAUC,eAAeC,KAAKP,EAAOG,IACjDA,EAAQ,GAAEjE,KAAQkE,IAClBA,GAAK,EAMT,OAFAJ,EAAMG,IAAQ,EAEPA,CACT,CAKA,SAASK,EAAQC,EAAItE,GACnBA,EAAOS,OAAOC,OAAO,CAAE,EAAE2D,EAAOpC,SAAUjC,GAE1CsE,EAAGC,KAAKC,MAAMjB,KAAK,SAAUtD,IAC3B,QAAc,CAAA,EACFqB,EAAGrB,EAAMqB,OAEfmD,EAAkBC,MAAMC,QAAQ3E,EAAK4E,QATlBC,EAUA7E,EAAK4E,MAVQA,GAASC,EAAUnC,SAASkC,IADxCC,IAAaD,GAASA,GAASC,EAYrDC,CAAsB9E,EAAK4E,OAXNC,MAazB,IAAK,IAAO3E,EAAG,EAAGA,EAAMoB,EAAOqC,OAAQzD,IAAO,CAC5C,MAAM8C,EAAQ1B,EAAOpB,GAErB,GAAmB,iBAAf8C,EAAMC,KACR,SAGF,IAAKwB,EAAgBM,OAAO/B,EAAMgC,IAAIC,OAAO,KAC3C,SAIF,MAAWzB,EAAGxD,EAAKkF,cAAc5D,EAAOpB,EAAM,GAAGqB,UAEjD,IAAIxB,EAAOiD,EAAMmC,QAAQ,MAGvBpF,EADU,MAARA,EACK6D,EAAW5D,EAAKoF,QAAQ5B,GAAQK,GAAO,EAAO7D,EAAKqF,sBAEnDzB,EAAW7D,EAAM8D,GAAO,EAAM7D,EAAKqF,sBAG5CrC,EAAMsC,QAAQ,KAAMvF,IAEE,IAAlBC,EAAKuF,UACPvC,EAAMsC,QAAQ,WAAa,GAAEtF,EAAKuF,YAGN,qBAAdC,UACdxF,EAAKwF,UAAUzF,EAAMC,EAAMC,EAAOC,IACzBF,EAAKwF,WAELxF,EAAKgC,iBAAmBhC,EAAKgC,kBAAoBwD,IAD1DxF,EAAKgC,gBAAgBjC,EAAMC,EAAMC,EAAOC,GAO1CA,EAAMoB,EAAOmE,QAAQzC,GAEjBhD,EAAK0F,UACP1F,EAAK0F,SAAS1C,EAAO,CAAEjD,OAAMyD,SAEjC,GAEJ,CDyIA/C,OAAOC,OAAO+B,EAAgBR,SAAU,CACtCU,MAAO,kBACPN,OAAO,EACPC,UAAW,QACXoB,QAAS,OC3IXW,EAAOmB,8IAEPnB,EAAOpC,SAAW,CAChB2C,MAAO,EACPQ,QA5FeO,GAAMC,mBAAmBC,OAAOF,GAAGG,OAAOC,cAAcC,QAAQ,OAAQ,MA6FvFX,qBAAsB,EACtBE,SAAU,KACVL,cA7FF,SAAwB5D,GACtB,OAAaA,EACVyB,OAAOK,GAAK,CAAC,OAAQ,eAAeV,SAASU,EAAEH,OAC/CgD,IAAI7C,GAAKA,EAAEnC,SACXiF,KAAK,GACV,EA2FEV,WAAW,EACXxD,gBAAiBwD,EACjB3E,eAAgB2E,EAAqBvD,SAASN,MAC9CN,eAAgBmE,EAAqBvD,SAASI,MAC9CnB,gBAAiB,IACjBM,gBAA6D,WAA5CgE,EAAqBvD,SAASK,UAC/CxB,cAAe0E,EAAqBvD,SAASJ,WAC7Cb,eAAgBwE,EAAqBvD,SAASR,aAQhD4C,EAAO8B,QAAU9B"}