{"name": "jsdoc", "version": "4.0.4", "revision": "1729364734219", "description": "An API documentation generator for JavaScript.", "keywords": ["documentation", "javascript"], "license": "Apache-2.0", "repository": {"type": "git", "url": "https://github.com/jsdoc/jsdoc"}, "dependencies": {"@babel/parser": "^7.20.15", "@jsdoc/salty": "^0.2.1", "@types/markdown-it": "^14.1.1", "bluebird": "^3.7.2", "catharsis": "^0.9.0", "escape-string-regexp": "^2.0.0", "js2xmlparser": "^4.0.2", "klaw": "^3.0.0", "markdown-it": "^14.1.0", "markdown-it-anchor": "^8.6.7", "marked": "^4.0.10", "mkdirp": "^1.0.4", "requizzle": "^0.2.3", "strip-json-comments": "^3.1.0", "underscore": "~1.13.2"}, "devDependencies": {"ajv": "^6.12.0", "gulp": "^5.0.0", "gulp-eslint": "^6.0.0", "gulp-json-editor": "^2.5.6", "nyc": "^15.1.0"}, "engines": {"node": ">=12.0.0"}, "scripts": {"test": "gulp lint; gulp test"}, "bin": {"jsdoc": "./jsdoc.js"}, "bugs": "https://github.com/jsdoc/jsdoc/issues", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The JSDoc Contributors", "url": "https://github.com/jsdoc/jsdoc/graphs/contributors"}], "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>"}]}