{"name": "@types/markdown-it", "version": "14.1.2", "description": "TypeScript definitions for markdown-it", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/markdown-it", "license": "MIT", "contributors": [{"name": "York Yao", "githubUsername": "plantain-00", "url": "https://github.com/plantain-00"}, {"name": "<PERSON>", "githubUsername": "rapropos", "url": "https://github.com/rapropos"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/duduluu"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "exports": {".": {"import": "./index.d.mts", "require": "./dist/index.cjs.d.ts"}, "./*": {"import": "./*", "require": "./*"}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/markdown-it"}, "scripts": {}, "dependencies": {"@types/linkify-it": "^5", "@types/mdurl": "^2"}, "typesPublisherContentHash": "72b750af20eb4973f000b8b6a751de43787503125afcd200798f88f096b221e7", "typeScriptVersion": "4.8"}