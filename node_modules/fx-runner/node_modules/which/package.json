{"author": "<PERSON> <<EMAIL>> (http://blog.izs.me)", "name": "which", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "version": "1.2.4", "repository": {"type": "git", "url": "git://github.com/isaacs/node-which.git"}, "main": "which.js", "bin": "./bin/which", "license": "ISC", "dependencies": {"is-absolute": "^0.1.7", "isexe": "^1.1.1"}, "devDependencies": {"mkdirp": "^0.5.0", "rimraf": "^2.3.3", "tap": "^5.1.1"}, "scripts": {"test": "tap test/*.js --cov"}}