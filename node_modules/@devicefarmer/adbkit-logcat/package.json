{"name": "@devicefarmer/adbkit-logcat", "version": "2.1.3", "description": "A Node.js interface for working with Android's logcat output.", "keywords": ["adb", "adbkit", "logcat"], "bugs": {"url": "https://github.com/devicefarmer/adbkit-logcat/issues"}, "license": "Apache-2.0", "author": {"name": "The OpenSTF Project", "email": "<EMAIL>", "url": "https://devicefarmer,com"}, "main": "./index", "repository": {"type": "git", "url": "https://github.com/devicefarmer/adbkit-logcat.git"}, "scripts": {"compile": "tsc -p .", "format": "eslint ./lib --fix --ext .ts", "lint": "eslint ./ --ext .ts", "prepublish": "npm run compile && npm run test", "test": "mocha --recursive"}, "dependencies": {}, "devDependencies": {"@types/node": "^18.15.11", "@typescript-eslint/eslint-plugin": "^5.54.0", "@typescript-eslint/parser": "^5.57.0", "chai": "^4.3.3", "eslint": "^8.24.0", "eslint-config-prettier": "^8.0.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-progress": "0.0.1", "mocha": "^10.2.0", "prettier": "^2.1.2", "sinon": "^15.0.1", "sinon-chai": "^3.7.0", "typescript": "^5.0.2"}, "engines": {"node": ">= 4"}, "files": ["/lib/**/*.js", "/lib/**/*.d.ts", "/index.js", "/index.d.ts"], "types": "./index.d.ts"}