{"name": "@devicefarmer/adbkit", "version": "3.2.3", "description": "A Typescript client for the Android Debug Bridge.", "keywords": ["adb", "adbkit", "android", "logcat", "typescript", "monkey"], "bin": {"adbkit": "./bin/adbkit"}, "bugs": {"url": "https://github.com/devicefarmer/adbkit/issues"}, "license": "Apache-2.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://devicefarmer.com/"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://urielch.github.io/urielch/"}], "main": "./dist/index.js", "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "https://github.com/devicefarmer/adbkit.git"}, "scripts": {"clean": "rimraf dist index.d.ts index.js", "keycode": "node tasks/keycode.js", "prepublish": "npm run clean && npm run compile && npm run test", "compile": "tsc  -p .", "compile2": "tsc -p tsconfig-dist.json", "lint": "eslint   ./ --ext .ts", "format": "eslint ./ --ext .ts --fix", "testJS": "mocha --reporter spec --colors dist/test/**/*.js", "test": "mocha -r ts-node/register --reporter spec --colors test/**/*.ts"}, "dependencies": {"@devicefarmer/adbkit-logcat": "^2.1.2", "@devicefarmer/adbkit-monkey": "~1.2.0", "bluebird": "~3.7", "commander": "^9.1.0", "debug": "~4.3.1", "node-forge": "^1.3.1", "split": "~1.0.1"}, "devDependencies": {"@types/debug": "^4.1.5", "@types/mocha": "^9.0.0", "@types/node": "^14.14.10", "@types/sinon-chai": "^3.2.5", "@types/bluebird": "^3.5.33", "@types/node-forge": "^1.0.1", "@typescript-eslint/eslint-plugin": "^4.9.1", "@typescript-eslint/parser": "^4.9.1", "bench": "~0.3.6", "chai": "~4.3.0", "eslint": "^8.12.0", "eslint-config-prettier": "^8.0.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-progress": "0.0.1", "mocha": "~8.3.1", "prettier": "^2.2.1", "rimraf": "^3.0.2", "sinon": "~13.0.1", "sinon-chai": "~3.7.0", "ts-node": "^10.4.0", "typescript": "^4.1.3"}, "engines": {"node": ">= 0.10.4"}}