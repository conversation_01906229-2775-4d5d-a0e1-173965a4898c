"use strict";
// export { default } from './adb';
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Adb = void 0;
var adb_1 = require("./adb");
Object.defineProperty(exports, "Adb", { enumerable: true, get: function () { return __importDefault(adb_1).default; } });
// export { default as Client } from './adb/client';
// export { default as ShellCommand } from './adb/command/host-transport/shell';
//# sourceMappingURL=index.js.map