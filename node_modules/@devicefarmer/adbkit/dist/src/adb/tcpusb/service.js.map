{"version": 3, "file": "service.js", "sourceRoot": "", "sources": ["../../../../src/adb/tcpusb/service.ts"], "names": [], "mappings": ";;;;;AAAA,kDAAsB;AACtB,mCAAsC;AACtC,sDAA8B;AAC9B,wDAAgC;AAChC,2DAAmC;AAKnC,MAAM,KAAK,GAAG,IAAA,eAAC,EAAC,oBAAoB,CAAC,CAAC;AAEtC,MAAM,oBAAqB,SAAQ,KAAK;IACtC,YAAmB,MAAc;QAC/B,KAAK,EAAE,CAAC;QADS,WAAM,GAAN,MAAM,CAAQ;QAE/B,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,oBAAoB,CAAC,SAAS,CAAC,CAAC;QAC5D,IAAI,CAAC,IAAI,GAAG,sBAAsB,CAAC;QACnC,IAAI,CAAC,OAAO,GAAG,kBAAkB,CAAC;QAClC,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,CAAC,oBAAoB,CAAC,CAAC;IAC9D,CAAC;CACF;AAED,MAAM,kBAAmB,SAAQ,KAAK;IACpC;QACE,KAAK,EAAE,CAAC;QACR,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAC1D,IAAI,CAAC,IAAI,GAAG,oBAAoB,CAAC;QACjC,IAAI,CAAC,OAAO,GAAG,gBAAgB,CAAC;QAChC,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,CAAC,kBAAkB,CAAC,CAAC;IAC5D,CAAC;CACF;AAED,MAAqB,OAAQ,SAAQ,qBAAY;IAS/C,YACU,MAAc,EACd,MAAc,EACd,OAAe,EACf,QAAgB,EAChB,MAAc;QAEtB,KAAK,EAAE,CAAC;QANA,WAAM,GAAN,MAAM,CAAQ;QACd,WAAM,GAAN,MAAM,CAAQ;QACd,YAAO,GAAP,OAAO,CAAQ;QACf,aAAQ,GAAR,QAAQ,CAAQ;QAChB,WAAM,GAAN,MAAM,CAAQ;QAVhB,WAAM,GAAG,KAAK,CAAC;QACf,UAAK,GAAG,KAAK,CAAC;QAEd,YAAO,GAAG,KAAK,CAAC;IAUxB,CAAC;IAEM,GAAG;QACR,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;SACtB;QACD,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,OAAO,IAAI,CAAC;SACb;QACD,KAAK,CAAC,UAAU,CAAC,CAAC;QAClB,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,mCAAmC;QACnF,IAAI;YACF,qEAAqE;YACrE,YAAY;YACZ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAM,CAAC,QAAQ,CAAC,gBAAM,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC;SACjF;QAAC,OAAO,KAAK,EAAE,GAAE;QAClB,YAAY;QACZ,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjB,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,MAAM,CAAC,MAAc;QAC1B,OAAO,kBAAQ,CAAC,GAAG,CAAoB,GAAG,EAAE;YAC1C,QAAQ,MAAM,CAAC,OAAO,EAAE;gBACtB,KAAK,gBAAM,CAAC,MAAM;oBAChB,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;gBACxC,KAAK,gBAAM,CAAC,MAAM;oBAChB,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;gBACxC,KAAK,gBAAM,CAAC,MAAM;oBAChB,OAAO,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;gBACzC,KAAK,gBAAM,CAAC,MAAM;oBAChB,OAAO,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;gBACzC;oBACE,MAAM,IAAI,KAAK,CAAC,qBAAqB,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;aAC1D;QACH,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;YACf,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;YACxB,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC;QACpB,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,iBAAiB,CAAC,MAAM;QAC9B,KAAK,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAC1B,OAAO,IAAI,CAAC,MAAM;aACf,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC;aACtB,SAAS,EAAE;aACX,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE;YAClB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;YAC3B,IAAI,IAAI,CAAC,KAAK,EAAE;gBACd,MAAM,IAAI,kBAAkB,EAAE,CAAC;aAChC;YACD,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,kBAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,2BAA2B;YAChG,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;gBACvD,QAAQ,KAAK,EAAE;oBACb,KAAK,kBAAQ,CAAC,IAAI;wBAChB,KAAK,CAAC,UAAU,CAAC,CAAC;wBAClB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAM,CAAC,QAAQ,CAAC,gBAAM,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC;wBACrF,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC;oBAC9B,KAAK,kBAAQ,CAAC,IAAI;wBAChB,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;oBAC3C;wBACE,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;iBAClE;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,EAAE;YACT,OAAO,IAAI,kBAAQ,CAAU,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBAC/C,IAAI,CAAC,SAAS,CAAC,MAAM;qBAClB,EAAE,CAAC,UAAU,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;qBACrC,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC;qBAClB,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;gBACvB,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;YACzB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;aACD,OAAO,CAAC,GAAG,EAAE;YACZ,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC;QACpB,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,iBAAiB,CAAC,MAAc;QACtC,KAAK,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAC1B,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,OAAO;SACR;QACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,MAAM,IAAI,OAAO,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;SAChD;QACD,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;IACzB,CAAC;IAEO,kBAAkB,CAAC,MAAc;QACvC,KAAK,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAC1B,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,OAAO;SACR;QACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,MAAM,IAAI,OAAO,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;SAChD;QACD,IAAI,IAAI,CAAC,SAAS,IAAI,MAAM,CAAC,IAAI,EAAE;YACjC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;SACnC;QACD,KAAK,CAAC,UAAU,CAAC,CAAC;QAClB,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAM,CAAC,QAAQ,CAAC,gBAAM,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC;IAC9F,CAAC;IAEO,kBAAkB,CAAC,MAAc;QACvC,KAAK,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAC1B,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,OAAO;SACR;QACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,MAAM,IAAI,OAAO,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;SAChD;QACD,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC;IACpB,CAAC;IAEO,QAAQ;QACd,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,EAAE;YAC9B,OAAO;SACR;QACD,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACrD,IAAI,KAAK,EAAE;YACT,KAAK,CAAC,UAAU,CAAC,CAAC;YAClB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAM,CAAC,QAAQ,CAAC,gBAAM,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;YACtF,OAAO,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC;SAC9B;IACH,CAAC;IAEO,UAAU,CAAC,MAAsB;QACvC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,MAAM,CAAC,IAAI,EAAE,CAAW,CAAC;IAC1E,CAAC;;AAtJH,0BAuJC;AAtJe,4BAAoB,GAAG,oBAAoB,CAAC;AAC5C,0BAAkB,GAAG,kBAAkB,CAAC"}