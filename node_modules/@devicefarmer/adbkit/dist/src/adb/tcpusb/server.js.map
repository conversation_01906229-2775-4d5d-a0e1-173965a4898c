{"version": 3, "file": "server.js", "sourceRoot": "", "sources": ["../../../../src/adb/tcpusb/server.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,yCAA2B;AAC3B,sDAA8B;AAC9B,mCAAsC;AAMtC,MAAqB,MAAO,SAAQ,qBAAY;IAI9C,YACmB,MAAc,EACd,MAAc,EACd,OAAsB;QAEvC,KAAK,EAAE,CAAC;QAJS,WAAM,GAAN,MAAM,CAAQ;QACd,WAAM,GAAN,MAAM,CAAQ;QACd,YAAO,GAAP,OAAO,CAAe;QALjC,gBAAW,GAAa,EAAE,CAAC;QAQjC,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC;YAC7B,aAAa,EAAE,IAAI;SACpB,CAAC,CAAC;QACH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;QAC1D,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,WAAW,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;QAC1D,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;QAClD,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,IAAI,EAAE,EAAE;YACpC,MAAM,MAAM,GAAG,IAAI,gBAAM,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YACxE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC9B,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;gBACzB,oCAAoC;gBACpC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC;YACH,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE;gBACtB,oCAAoC;gBACpC,OAAO,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,UAAU,GAAG;oBAC9D,OAAO,GAAG,KAAK,MAAM,CAAC;gBACxB,CAAC,CAAC,CAAC,CAAC;YACN,CAAC,CAAC,CAAC;YACH,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,MAAM,CAAC,GAAG,IAAqC;QACpD,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC;QAC5B,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,KAAK;QACV,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;QACpB,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,GAAG;QACR,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC;QAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAC9C,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;SACd;QACD,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAlDD,yBAkDC"}