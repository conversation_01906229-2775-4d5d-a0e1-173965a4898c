{"version": 3, "file": "packet.js", "sourceRoot": "", "sources": ["../../../../src/adb/tcpusb/packet.ts"], "names": [], "mappings": ";;AAAA,MAAqB,MAAM;IAsDzB,YACkB,OAAe,EACf,IAAY,EACZ,IAAY,EACnB,MAAc,EACd,KAAa,EACb,KAAa,EACf,IAAa;QANJ,YAAO,GAAP,OAAO,CAAQ;QACf,SAAI,GAAJ,IAAI,CAAQ;QACZ,SAAI,GAAJ,IAAI,CAAQ;QACnB,WAAM,GAAN,MAAM,CAAQ;QACd,UAAK,GAAL,KAAK,CAAQ;QACb,UAAK,GAAL,KAAK,CAAQ;QACf,SAAI,GAAJ,IAAI,CAAS;IACnB,CAAC;IArDG,MAAM,CAAC,QAAQ,CAAC,IAAa;QAClC,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,IAAI,IAAI,EAAE;YACR,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;gBAC/C,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBACrB,GAAG,IAAI,IAAI,CAAC;aACb;SACF;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAEM,MAAM,CAAC,KAAK,CAAC,OAAe;QACjC,2EAA2E;QAC3E,OAAO,CAAC,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;IACtC,CAAC;IAEM,MAAM,CAAC,QAAQ,CAAC,OAAe,EAAE,IAAY,EAAE,IAAY,EAAE,IAAa;QAC/E,IAAI,IAAI,EAAE;YACR,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;YAC7C,KAAK,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YAChC,KAAK,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YAC7B,KAAK,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YAC7B,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;YACrC,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;YAC/C,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;YAC/C,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;YACrB,OAAO,KAAK,CAAC;SACd;aAAM;YACL,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAC/B,KAAK,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YAChC,KAAK,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YAC7B,KAAK,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YAC7B,KAAK,CAAC,aAAa,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAC3B,KAAK,CAAC,aAAa,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAC3B,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;YAC/C,OAAO,KAAK,CAAC;SACd;IACH,CAAC;IAED,MAAM,CAAC,MAAM,CAAC,CAAS;QACrB,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC/B,MAAM,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3B,OAAO,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC;IAYM,cAAc;QACnB,OAAO,IAAI,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,KAAK,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7E,CAAC;IAEM,WAAW;QAChB,OAAO,IAAI,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACnD,CAAC;IAEO,OAAO;QACb,QAAQ,IAAI,CAAC,OAAO,EAAE;YACpB,KAAK,MAAM,CAAC,MAAM;gBAChB,OAAO,MAAM,CAAC;YAChB,KAAK,MAAM,CAAC,MAAM;gBAChB,OAAO,MAAM,CAAC;YAChB,KAAK,MAAM,CAAC,MAAM;gBAChB,OAAO,MAAM,CAAC;YAChB,KAAK,MAAM,CAAC,MAAM;gBAChB,OAAO,MAAM,CAAC;YAChB,KAAK,MAAM,CAAC,MAAM;gBAChB,OAAO,MAAM,CAAC;YAChB,KAAK,MAAM,CAAC,MAAM;gBAChB,OAAO,MAAM,CAAC;YAChB,KAAK,MAAM,CAAC,MAAM;gBAChB,OAAO,MAAM,CAAC;YAChB;gBACE,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;SACjD;IACH,CAAC;IAED,QAAQ;QACN,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC5B,OAAO,GAAG,IAAI,SAAS,IAAI,CAAC,IAAI,SAAS,IAAI,CAAC,IAAI,WAAW,IAAI,CAAC,MAAM,EAAE,CAAC;IAC7E,CAAC;;AAhGH,yBAiGC;AAhGe,aAAM,GAAG,UAAU,CAAC;AACpB,aAAM,GAAG,UAAU,CAAC;AACpB,aAAM,GAAG,UAAU,CAAC;AACpB,aAAM,GAAG,UAAU,CAAC;AACpB,aAAM,GAAG,UAAU,CAAC;AACpB,aAAM,GAAG,UAAU,CAAC;AACpB,aAAM,GAAG,UAAU,CAAC"}