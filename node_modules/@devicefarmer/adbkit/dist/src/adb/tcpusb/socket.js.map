{"version": 3, "file": "socket.js", "sourceRoot": "", "sources": ["../../../../src/adb/tcpusb/socket.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,mCAAsC;AACtC,+CAAiC;AACjC,kDAAsB;AACtB,wDAAgC;AAChC,kEAA0C;AAC1C,sEAA8C;AAC9C,sDAA8B;AAC9B,mDAA2B;AAG3B,8DAAsC;AACtC,wDAAgC;AAGhC,MAAM,KAAK,GAAG,IAAA,eAAC,EAAC,mBAAmB,CAAC,CAAC;AACrC,MAAM,UAAU,GAAG,UAAU,CAAC;AAC9B,MAAM,UAAU,GAAG,MAAM,CAAC;AAC1B,MAAM,UAAU,GAAG,CAAC,CAAC;AACrB,MAAM,cAAc,GAAG,CAAC,CAAC;AACzB,MAAM,iBAAiB,GAAG,CAAC,CAAC;AAC5B,MAAM,YAAY,GAAG,EAAE,CAAC;AAExB,MAAM,SAAU,SAAQ,KAAK;IAC3B,YAAY,OAAe;QACzB,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;QACjD,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC;QACxB,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;IAClD,CAAC;CACF;AAED,MAAM,iBAAkB,SAAQ,KAAK;IACnC;QACE,KAAK,CAAC,qBAAqB,CAAC,CAAC;QAC7B,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,iBAAiB,CAAC,SAAS,CAAC,CAAC;QACzD,IAAI,CAAC,IAAI,GAAG,mBAAmB,CAAC;QAChC,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,MAAM,CAAC,iBAAiB,CAAC,CAAC;IAC1D,CAAC;CACF;AAED,MAAqB,MAAO,SAAQ,qBAAY;IAgB9C,YACmB,MAAc,EACd,MAAc,EACvB,MAAkB,EAClB,UAAyB,EAAE;QAEnC,KAAK,EAAE,CAAC;QALS,WAAM,GAAN,MAAM,CAAQ;QACd,WAAM,GAAN,MAAM,CAAQ;QACvB,WAAM,GAAN,MAAM,CAAY;QAClB,YAAO,GAAP,OAAO,CAAoB;QAhB7B,UAAK,GAAG,KAAK,CAAC;QAEd,eAAU,GAAG,KAAK,CAAC;QACnB,cAAS,GAAG,IAAI,wBAAc,CAAC,UAAU,CAAC,CAAC;QAC3C,aAAQ,GAAG,IAAI,wBAAc,CAAC,UAAU,CAAC,CAAC;QAC1C,aAAQ,GAAG,IAAI,oBAAU,EAAE,CAAC;QAI7B,YAAO,GAAG,CAAC,CAAC;QACZ,eAAU,GAAG,IAAI,CAAC;QAUvB,IAAI,IAAmB,CAAC;QACxB,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,GAAG,EAAE,CAAC,kBAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;QACzE,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAC7B,IAAI,CAAC,MAAM,GAAG,IAAI,sBAAY,CAAC,IAAI,CAAC,MAAM,CAAC;aACxC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACrC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;YACnB,KAAK,CAAC,uBAAuB,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;YAC5C,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC;QACpB,CAAC,CAAC;aACD,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAClC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;QAC/C,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;QACvB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAEM,GAAG;QACR,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,OAAO,IAAI,CAAC;SACb;QACD,uEAAuE;QACvE,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;QACpB,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;QAClB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjB,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,MAAM,CAAC,GAAU;QACvB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QACxB,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC;IACpB,CAAC;IAEO,OAAO,CAAC,MAAc;QAC5B,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,OAAO,kBAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SAChC;QACD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;QAClC,OAAO,kBAAQ,CAAC,GAAG,CAAC,GAAG,EAAE;YACvB,QAAQ,MAAM,CAAC,OAAO,EAAE;gBACtB,KAAK,gBAAM,CAAC,MAAM;oBAChB,OAAO,kBAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC;gBACpD,KAAK,gBAAM,CAAC,MAAM;oBAChB,OAAO,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;gBAC9C,KAAK,gBAAM,CAAC,MAAM;oBAChB,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzD,KAAK,gBAAM,CAAC,MAAM,CAAC;gBACnB,KAAK,gBAAM,CAAC,MAAM,CAAC;gBACnB,KAAK,gBAAM,CAAC,MAAM;oBAChB,OAAO,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7D,KAAK,gBAAM,CAAC,MAAM;oBAChB,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;gBACxC;oBACE,MAAM,IAAI,KAAK,CAAC,mBAAmB,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;aACxD;QACH,CAAC,CAAC;aACC,KAAK,CAAC,MAAM,CAAC,SAAS,EAAE,GAAG,EAAE;YAC5B,IAAI,CAAC,GAAG,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACf,CAAC,CAAC;aACD,KAAK,CAAC,MAAM,CAAC,iBAAiB,EAAE,GAAG,EAAE;YACpC,IAAI,CAAC,GAAG,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACf,CAAC,CAAC;aACD,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;YACb,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACjB,OAAO,KAAK,CAAC;QACf,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,iBAAiB;QACvB,0BAA0B;QAC1B,KAAK,CAAC,UAAU,CAAC,CAAC;QAClB,KAAK,CAAC,UAAU,CAAC,CAAC;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAM,CAAC,QAAQ,CAAC,gBAAM,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IAC9E,CAAC;IAEO,uBAAuB,CAAC,MAAM;QACpC,KAAK,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAC1B,IAAI,CAAC,OAAO,GAAG,gBAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC1C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;QACpD,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;YACxC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;YACnB,KAAK,CAAC,sBAAsB,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAC9D,KAAK,CAAC,UAAU,CAAC,CAAC;YAClB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAM,CAAC,QAAQ,CAAC,gBAAM,CAAC,MAAM,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QAC/E,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,iBAAiB,CAAC,MAAc;QACtC,KAAK,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAC1B,QAAQ,MAAM,CAAC,IAAI,EAAE;YACnB,KAAK,cAAc;gBACjB,yCAAyC;gBACzC,IAAI,MAAM,CAAC,IAAI;oBAAE,KAAK,CAAC,uBAAuB,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;gBACjF,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;oBACnB,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC;iBAC9B;gBACD,KAAK,CAAC,UAAU,CAAC,CAAC;gBAClB,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAM,CAAC,QAAQ,CAAC,gBAAM,CAAC,MAAM,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;gBAChF,OAAO,kBAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC7B,KAAK,iBAAiB;gBACpB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;oBACnB,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC;iBAChE;gBACD,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;oBAC1C,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC;iBACpD;gBACD,KAAK,CAAC,4BAA4B,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;gBACrE,OAAO,cAAI,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC;qBAC/D,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE;oBACZ,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAC7C,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAC9C,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;wBAC5B,KAAK,CAAC,oBAAoB,CAAC,CAAC;wBAC5B,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;qBAClD;oBACD,KAAK,CAAC,oBAAoB,CAAC,CAAC;oBAC5B,OAAO,GAAG,CAAC;gBACb,CAAC,CAAC;qBACD,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE;oBACZ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI;wBAAE,OAAO;oBAC/B,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE;wBACvC,KAAK,CAAC,kDAAkD,CAAC,CAAC;wBAC1D,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC;oBACjE,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC;qBACD,IAAI,CAAC,GAAG,EAAE;oBACT,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC;gBAC1B,CAAC,CAAC;qBACD,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE;oBACX,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;oBACvB,KAAK,CAAC,UAAU,CAAC,CAAC;oBAClB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAM,CAAC,QAAQ,CAAC,gBAAM,CAAC,MAAM,EAAE,gBAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC;gBACtG,CAAC,CAAC,CAAC;YACP;gBACE,MAAM,IAAI,KAAK,CAAC,iCAAiC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;SACnE;IACH,CAAC;IAEO,iBAAiB,CAAC,MAAc;QACtC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACpB,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE,CAAC;SACtC;QACD,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC;QAC7B,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QACrC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,EAAE;YAC7C,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;SACvC;QACD,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACzC,KAAK,CAAC,WAAW,IAAI,EAAE,CAAC,CAAC;QACzB,MAAM,OAAO,GAAG,IAAI,iBAAO,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;QAC/E,OAAO,IAAI,kBAAQ,CAAoB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACzD,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAC5B,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAC3B,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACvC,KAAK,CAAC,YAAY,IAAI,CAAC,QAAQ,CAAC,KAAK,0BAA0B,CAAC,CAAC;YACjE,OAAO,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAChC,CAAC,CAAC;aACC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC;aACjB,OAAO,CAAC,GAAG,EAAE;YACZ,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAC9B,KAAK,CAAC,YAAY,IAAI,CAAC,QAAQ,CAAC,KAAK,0BAA0B,CAAC,CAAC;YACjE,OAAO,OAAO,CAAC,GAAG,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,qBAAqB,CAAC,MAAc;QAC1C,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACpB,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE,CAAC;SACtC;QACD,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC;QAC5B,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC3C,IAAI,OAAO,EAAE;YACX,OAAO,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;SAC/B;aAAM;YACL,KAAK,CAAC,kEAAkE,CAAC,CAAC;YAC1E,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SAC/B;IACH,CAAC;IAEM,KAAK,CAAC,KAAsB;QACjC,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,OAAO,KAAK,CAAC;SACd;QACD,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAClC,CAAC;IAEO,YAAY;QAClB,OAAO,kBAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,YAAY,CAAC,CAAC;IAC9D,CAAC;IAEO,SAAS,CAAC,IAAY;QAC5B,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,2BAA2B;IACvD,CAAC;IAEO,SAAS;QACf,KAAK,CAAC,wDAAwD,CAAC,CAAC;QAChE,OAAO,IAAI,CAAC,MAAM;aACf,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC;aACtB,aAAa,EAAE;aACf,IAAI,CAAC,UAAU,UAAU;YACxB,MAAM,EAAE,GAAG,CAAC;gBACV,MAAM,GAAG,GAAG,CAAC,iBAAiB,EAAE,kBAAkB,EAAE,mBAAmB,CAAC,CAAC;gBACzE,MAAM,OAAO,GAAG,EAAE,CAAC;gBACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;oBAC9C,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;oBACpB,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;iBAC9C;gBACD,OAAO,OAAO,CAAC;YACjB,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACd,OAAO,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACP,CAAC;;AA5OH,yBA6OC;AA5Oe,gBAAS,GAAG,SAAS,CAAC;AACtB,wBAAiB,GAAG,iBAAiB,CAAC"}