{"version": 3, "file": "packetreader.js", "sourceRoot": "", "sources": ["../../../../src/adb/tcpusb/packetreader.ts"], "names": [], "mappings": ";;;;;AAAA,mCAAsC;AACtC,sDAA8B;AAG9B,MAAM,aAAc,SAAQ,KAAK;IAC/B,YAAmB,MAAc;QAC/B,KAAK,EAAE,CAAC;QADS,WAAM,GAAN,MAAM,CAAQ;QAE/B,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,aAAa,CAAC,SAAS,CAAC,CAAC;QACrD,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC;QAC5B,IAAI,CAAC,OAAO,GAAG,mBAAmB,CAAC;QACnC,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,YAAY,CAAC,aAAa,CAAC,CAAC;IAC5D,CAAC;CACF;AAED,MAAM,UAAW,SAAQ,KAAK;IAC5B,YAAmB,MAAc;QAC/B,KAAK,EAAE,CAAC;QADS,WAAM,GAAN,MAAM,CAAQ;QAE/B,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC;QAClD,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC;QACzB,IAAI,CAAC,OAAO,GAAG,sBAAsB,CAAC;QACtC,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC;IACzD,CAAC;CACF;AAED,MAAqB,YAAa,SAAQ,qBAAY;IAQpD,YAAoB,MAAsB;QACxC,KAAK,EAAE,CAAC;QADU,WAAM,GAAN,MAAM,CAAgB;QAJlC,WAAM,GAAG,KAAK,CAAC;QAMrB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACrD,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;YAC9B,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;YACzB,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QACH,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACzC,CAAC;IAEO,QAAQ;QACd,OAAO,IAAI,CAAC,YAAY,EAAE,EAAE;YAC1B,OAAO,IAAI,CAAC,MAAM,EAAE;gBAClB,IAAI,IAAI,CAAC,MAAM,EAAE;oBACf,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;wBAC/C,MAAM;qBACP;oBACD,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;oBACrD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE;wBACjC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;wBAChE,OAAO;qBACR;oBACD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;oBACjC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;iBACrB;qBAAM;oBACL,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,EAAE,CAAC,EAAE;wBAC/B,MAAM;qBACP;oBACD,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;oBACjC,IAAI,CAAC,MAAM,GAAG,IAAI,gBAAM,CACtB,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,EACtB,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,EACtB,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,EACtB,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,EACvB,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,EACvB,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,EACvB,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAChB,CAAC;oBACF,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,EAAE;wBAC9B,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;wBAC7D,OAAO;qBACR;oBACD,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;wBAC5B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;qBAClC;yBAAM;wBACL,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;qBACpB;iBACF;aACF;SACF;IACH,CAAC;IAEO,YAAY;QAClB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAY,CAAC;QAC3C,IAAI,KAAK,EAAE;YACT,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;aAC/F;iBAAM;gBACL,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC;aAC9B;SACF;aAAM;YACL,OAAO,IAAI,CAAC;SACb;IACH,CAAC;IAEO,QAAQ,CAAC,MAAM;QACrB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;QAC3C,IAAI,CAAC,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAC/E,OAAO,KAAK,CAAC;IACf,CAAC;;AA/EH,+BAgFC;AA/Ee,0BAAa,GAAG,aAAa,CAAC;AAC9B,uBAAU,GAAG,UAAU,CAAC"}