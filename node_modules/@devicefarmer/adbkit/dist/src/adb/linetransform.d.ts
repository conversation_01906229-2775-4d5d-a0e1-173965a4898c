/// <reference types="node" />
import Stream, { TransformCallback, TransformOptions } from 'stream';
interface LineTransformOptions extends TransformOptions {
    autoDetect?: boolean;
}
export default class LineTransform extends Stream.Transform {
    private savedR?;
    private autoDetect;
    private transformNeeded;
    private skipBytes;
    constructor(options?: LineTransformOptions);
    _nullTransform(chunk: <PERSON><PERSON><PERSON>, encoding: string, done: TransformCallback): void;
    _transform(chunk: <PERSON><PERSON><PERSON>, encoding: string, done: TransformCallback): void;
    _flush(done: TransformCallback): void;
}
export {};
//# sourceMappingURL=linetransform.d.ts.map