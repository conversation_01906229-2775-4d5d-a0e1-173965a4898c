{"version": 3, "file": "parser.js", "sourceRoot": "", "sources": ["../../../src/adb/parser.ts"], "names": [], "mappings": ";;;;;AAAA,wDAAgC;AAChC,0DAAkC;AAGlC,MAAM,SAAU,SAAQ,KAAK;IAC3B,YAAY,OAAe;QACzB,KAAK,CAAC,aAAa,OAAO,GAAG,CAAC,CAAC;QAC/B,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;QACjD,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC;QACxB,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC3C,CAAC;CACF;AAED,MAAM,iBAAkB,SAAQ,KAAK;IAEnC,YAAY,cAAsB;QAChC,KAAK,CAAC,mCAAmC,cAAc,aAAa,CAAC,CAAC;QACtE,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,iBAAiB,CAAC,SAAS,CAAC,CAAC;QACzD,IAAI,CAAC,IAAI,GAAG,mBAAmB,CAAC;QAChC,IAAI,CAAC,YAAY,GAAG,cAAc,CAAC;QACnC,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC;IACnD,CAAC;CACF;AAED,MAAM,mBAAoB,SAAQ,KAAK;IACrC,YAAmB,UAAkB,EAAS,QAAgB;QAC5D,KAAK,CAAC,eAAe,UAAU,oBAAoB,QAAQ,EAAE,CAAC,CAAC;QAD9C,eAAU,GAAV,UAAU,CAAQ;QAAS,aAAQ,GAAR,QAAQ,CAAQ;QAE5D,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,mBAAmB,CAAC,SAAS,CAAC,CAAC;QAC3D,IAAI,CAAC,IAAI,GAAG,qBAAqB,CAAC;QAClC,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC;IACrD,CAAC;CACF;AAED,kBAAQ,CAAC,MAAM,CAAC;IACd,kBAAkB;IAClB,kBAAkB;IAClB,2BAA2B;IAC3B,yBAAyB;IACzB,sBAAsB;IACtB,YAAY,EAAE,IAAI;IAClB,oBAAoB;IACpB,oBAAoB;CACrB,CAAC,CAAC;AAEH,MAAqB,MAAM;IAMzB,YAAmB,MAAc;QAAd,WAAM,GAAN,MAAM,CAAQ;QAFzB,UAAK,GAAG,KAAK,CAAC;QAGpB,QAAQ;IACV,CAAC;IAEM,GAAG;QACR,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,OAAO,kBAAQ,CAAC,OAAO,CAAU,IAAI,CAAC,CAAC;SACxC;QACD,IAAI,OAAmB,CAAC;QACxB,IAAI,aAAqC,CAAC;QAC1C,IAAI,WAAuB,CAAC;QAC5B,OAAO,IAAI,kBAAQ,CAAU,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE;YACzD,OAAO,GAAG,GAAG,EAAE;gBACb,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE;oBACzB,SAAS;iBACV;YACH,CAAC,CAAC;YACF,aAAa,GAAG,UAAU,GAAG;gBAC3B,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;YACrB,CAAC,CAAC;YACF,WAAW,GAAG,GAAG,EAAE;gBACjB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;gBAClB,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC;YACvB,CAAC,CAAC;YACF,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YACpC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;YACvC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;YACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACpB,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;YAClB,QAAQ,CAAC,GAAG,EAAE;gBACZ,+BAA+B;YACjC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE;YACd,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YAChD,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;YACnD,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;YAC/C,YAAY;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,GAAG;QACR,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAEM,OAAO;QACZ,IAAI,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAE1B,IAAI,OAAmB,CAAC;QACxB,IAAI,aAAqC,CAAC;QAC1C,IAAI,WAAuB,CAAC;QAE5B,OAAO,IAAI,kBAAQ,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE;YACxD,OAAO,GAAG,GAAG,EAAE;gBACb,IAAI,KAAK,CAAC;gBACV,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE;oBACnC,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;iBACnC;gBACD,IAAI,IAAI,CAAC,KAAK,EAAE;oBACd,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC;iBACrB;YACH,CAAC,CAAC;YACF,aAAa,GAAG,UAAU,GAAG;gBAC3B,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;YACrB,CAAC,CAAC;YACF,WAAW,GAAG,GAAG,EAAE;gBACjB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;gBAClB,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC;YACtB,CAAC,CAAC;YACF,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YACpC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;YACvC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;YACnC,OAAO,EAAE,CAAC;YACV,QAAQ,CAAC,GAAG,EAAE;gBACZ,+BAA+B;YACjC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE;YACd,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YAChD,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;YACnD,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,SAAS,CAAC,OAAe;QAC9B,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;IAC1E,CAAC;IAEM,SAAS,CAAC,OAAe;QAC9B,IAAI,OAAmB,CAAC;QACxB,IAAI,aAAqC,CAAC;QAC1C,IAAI,WAAuB,CAAC;QAC5B,OAAO,IAAI,kBAAQ,CAAS,CAAC,OAAO,EAAE,MAAM,CAAC,cAAc,EAAE,EAAE;YAC7D,OAAO,GAAG,GAAG,EAAE;gBACb,IAAI,OAAO,EAAE;oBACX,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBACxC,IAAI,KAAK,EAAE;wBACT,oEAAoE;wBACpE,uDAAuD;wBACvD,OAAO,IAAI,KAAK,CAAC,MAAM,CAAC;wBACxB,IAAI,OAAO,KAAK,CAAC,EAAE;4BACjB,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC;yBACvB;qBACF;oBACD,IAAI,IAAI,CAAC,KAAK,EAAE;wBACd,OAAO,MAAM,CAAC,IAAI,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC;qBACtD;iBACF;qBAAM;oBACL,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;iBACjC;YACH,CAAC,CAAC;YACF,WAAW,GAAG,GAAG,EAAE;gBACjB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;gBAClB,OAAO,MAAM,CAAC,IAAI,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC;YACvD,CAAC,CAAC;YACF,aAAa,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACrC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YACpC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;YACvC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;YACnC,OAAO,EAAE,CAAC;YACV,sBAAsB;QACxB,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE;YACd,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YAChD,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;YACnD,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,YAAY,CAAC,OAAe,EAAE,YAAoB;QACvD,IAAI,OAAmB,CAAC;QACxB,IAAI,aAAqC,CAAC;QAC1C,IAAI,WAAuB,CAAC;QAC5B,OAAO,IAAI,kBAAQ,CAAO,CAAC,OAAO,EAAE,MAAM,CAAC,cAAc,EAAE,EAAE;YAC3D,OAAO,GAAG,GAAG,EAAE;gBACb,IAAI,OAAO,EAAE;oBACX,mEAAmE;oBACnE,oEAAoE;oBACpE,4BAA4B;oBAC5B,IAAI,KAAK,CAAC;oBACV,OAAO,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE;wBAC9D,OAAO,IAAI,KAAK,CAAC,MAAM,CAAC;wBACxB,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;wBAC1B,IAAI,OAAO,KAAK,CAAC,EAAE;4BACjB,OAAO,OAAO,EAAE,CAAC;yBAClB;qBACF;oBACD,IAAI,IAAI,CAAC,KAAK,EAAE;wBACd,OAAO,MAAM,CAAC,IAAI,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC;qBACtD;iBACF;qBAAM;oBACL,OAAO,OAAO,EAAE,CAAC;iBAClB;YACH,CAAC,CAAC;YACF,WAAW,GAAG,GAAG,EAAE;gBACjB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;gBAClB,OAAO,MAAM,CAAC,IAAI,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC;YACvD,CAAC,CAAC;YACF,aAAa,GAAG,UAAU,GAAG;gBAC3B,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;YACrB,CAAC,CAAC;YACF,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YACpC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;YACvC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;YACnC,OAAO,EAAE,CAAC;YACV,sBAAsB;QACxB,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE;YACd,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YAChD,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;YACnD,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,SAAS;QACd,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,UAAU,KAAK;YAC1C,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,SAAS;QACd,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;YACtC,MAAM,MAAM,GAAG,kBAAQ,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC5C,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,SAAS,CAAC,IAAY;QAC3B,IAAI,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC9B,MAAM,IAAI,GAAG,GAAG,EAAE;YAChB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,KAAK;gBAC3C,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;oBACrB,OAAO,OAAO,CAAC;iBAChB;qBAAM;oBACL,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;oBAC1C,OAAO,IAAI,EAAE,CAAC;iBACf;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;QACF,OAAO,IAAI,EAAE,CAAC;IAChB,CAAC;IAED,UAAU,CAAC,EAAU;QACnB,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;YACnC,MAAM,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;YACvC,IAAI,KAAK,EAAE;gBACT,OAAO,KAAK,CAAC;aACd;iBAAM;gBACL,OAAO,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;aAC5B;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,QAAQ;QACb,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,IAAI;YAC7C,OAAO;YACP,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE;gBAClC,OAAO;gBACP,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;aAC1B;iBAAM;gBACL,OAAO,IAAI,CAAC;aACb;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,8DAA8D;IACvD,UAAU,CAAC,IAAY,EAAE,QAAgB;QAC9C,OAAO,kBAAQ,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,mBAAmB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;IACzE,CAAC;;AAtOH,yBAuOC;AAtOe,gBAAS,GAAG,SAAS,CAAC;AACtB,wBAAiB,GAAG,iBAAiB,CAAC;AACtC,0BAAmB,GAAG,mBAAmB,CAAC"}