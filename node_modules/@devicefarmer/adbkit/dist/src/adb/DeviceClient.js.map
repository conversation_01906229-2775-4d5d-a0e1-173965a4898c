{"version": 3, "file": "DeviceClient.js", "sourceRoot": "", "sources": ["../../../src/adb/DeviceClient.ts"], "names": [], "mappings": ";;;;;AAAA,gFAAiD;AACjD,gFAAiD;AAEjD,kDAA0B;AAC1B,sDAA8B;AAC9B,uDAAmC;AAEnC,yCAAsD;AACtD,6DA4BkC;AAClC,uDAO+B;AAC/B,kDAAsB;AAKtB,wDAAgC;AAehC,MAAM,KAAK,GAAG,IAAA,eAAC,EAAC,YAAY,CAAC,CAAC;AAE9B,MAAM,iBAAiB,GAAG,CAAC,GAAU,EAAE,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;AAE/E,MAAqB,YAAY;IAC/B,YAA4B,MAAc,EAAkB,MAAc;QAA9C,WAAM,GAAN,MAAM,CAAQ;QAAkB,WAAM,GAAN,MAAM,CAAQ;QACxE,UAAU;IACZ,CAAC;IAED;;;;OAIG;IACI,WAAW;QAChB,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,gCAAkB,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IAC7F,CAAC;IAED;;;OAGG;IACI,aAAa;QAClB,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,kCAAoB,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IAC/F,CAAC;IACD;;;;OAIG;IACI,QAAQ;QACb,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,6BAAe,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IAC1F,CAAC;IAED;;;;OAIG;IACI,aAAa;QAClB,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,qCAAoB,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;IAC7F,CAAC;IAED;;;;;;;;;;OAUG;IACI,WAAW;QAChB,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,mCAAkB,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;IAC3F,CAAC;IAED;;;;;OAKG;IACI,WAAW,CAAC,KAAc;QAC/B,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,mCAAkB,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;IAChG,CAAC;IAED;;;;;;OAMG;IACI,gBAAgB,CAAC,KAAK,GAAG,OAAO;QACrC,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,EAAE;YAC9C,MAAM,EAAE,GAAG,UAAU,CAAC,QAAQ,KAAK,YAAY,CAAC,CAAC;YACjD,IAAI,EAAE,EAAE;gBACN,OAAO,EAAE,CAAC;aACX;YACD,MAAM,KAAK,CAAC,iCAAiC,KAAK,GAAG,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;OAaG;IACI,OAAO,CAAC,KAAa,EAAE,MAAc;QAC1C,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,4BAAc,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;IACxG,CAAC;IAED;;;;;;;OAOG;IACI,YAAY;QACjB,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,iCAAmB,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IAC9F,CAAC;IAED;;;;;;;;OAQG;IACI,OAAO,CAAC,MAAc,EAAE,KAAa;QAC1C,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,+BAAc,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;IACpG,CAAC;IACD;;;;;;OAMG;IACI,YAAY;QACjB,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,oCAAmB,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;IAC5F,CAAC;IAED;;OAEG;IACK,UAAU;QAChB,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;IAClC,CAAC;IAED;;OAEG;IACI,SAAS;QACd,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,2BAAoB,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IAC5G,CAAC;IAED;;;;;;OAMG;IACI,KAAK,CAAC,OAAyC;QACpD,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,6BAAY,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;IAC5F,CAAC;IAED;;;;OAIG;IACI,MAAM;QACX,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,8BAAa,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;IACtF,CAAC;IAED;;;;OAIG;IACI,OAAO;QACZ,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,+BAAc,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;IACvF,CAAC;IAED;;;;OAIG;IACI,IAAI;QACT,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,4BAAW,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;IACpF,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACI,SAAS;QACd,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,iCAAgB,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;IACzF,CAAC;IAED;;;;;;;;OAQG;IACI,WAAW,CAAC,MAAM,GAAG,KAAK;QAC/B,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,mCAAkB,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;IACjG,CAAC;IAED;;;;;;;;OAQG;IACI,SAAS;QACd,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CACzC,IAAI,iCAAgB,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;YACtD,KAAK,CAAC,uCAAuC,GAAG,GAAG,CAAC,CAAC;YACrD,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QACjC,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAED;;;;;;OAMG;IACI,SAAS,CAAC,IAAY;QAC3B,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,6BAAY,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;IACzF,CAAC;IAED;;;;;;OAMG;IACI,OAAO,CAAC,IAAY;QACzB,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,2BAAU,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;IACvF,CAAC;IAED;;;;;;;SAOK;IACE,OAAO,CAAC,IAAY,EAAE,IAAa;QACxC,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,2BAAU,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;IAC7F,CAAC;IAED;;;;;;;;OAQG;IACI,UAAU,CAAC,IAAI,GAAG,IAAI;QAC3B,MAAM,UAAU,GAAG,CAAC,KAAa,EAAoB,EAAE;YACrD,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;iBACtB,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,uBAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;iBAC9C,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;gBACb,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,EAAE;oBAChB,KAAK,CAAC,mCAAmC,KAAK,aAAa,CAAC,CAAC;oBAC7D,OAAO,kBAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;iBAC1D;qBAAM;oBACL,MAAM,GAAG,CAAC;iBACX;YACH,CAAC,CAAC,CAAC;QACP,CAAC,CAAC;QACF,OAAO,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE;YAC9B,OAAO,IAAI,CAAC,SAAS,EAAE;iBACpB,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,8BAAa,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;iBAC/D,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QACzF,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;OASG;IACI,UAAU,CAAC,UAA+B,EAAE;QACjD,OAAO,IAAI,CAAC,SAAS,EAAE;aACpB,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,8BAAa,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;aAClE,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,uBAAM,CAAC,UAAU,CAAC,MAAM,EAAE,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;IAC1E,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IACI,YAAY;QACjB,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,cAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/D,CAAC;IAED;;;;;;OAMG;IACI,KAAK,CAAC,GAAW;QACtB,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,6BAAY,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;IACxF,CAAC;IAED;;;;;;;OAOG;IACI,OAAO,CAAC,GAAwB;QACrC,MAAM,IAAI,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;QACtE,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;YAC5C,IAAI,WAAuB,CAAC;YAC5B,IAAI,aAAmC,CAAC;YACxC,OAAO,IAAI,kBAAQ,CAAU,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBAC/C,aAAa,GAAG,CAAC,GAAU,EAAE,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBAC5C,WAAW,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,KAAc,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;gBACtF,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;gBACpC,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE;gBACd,QAAQ,CAAC,cAAc,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;gBAChD,QAAQ,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;OAOG;IACI,aAAa,CAAC,GAAW;QAC9B,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE;YACzC,OAAO,IAAI,+BAAc,CAAC,SAAS,CAAC;iBACjC,OAAO,CAAC,GAAG,CAAC;iBACZ,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;iBACzC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,gBAAM,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,CAAC;iBAC9C,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,GAAW;QAC1B,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,iCAAgB,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;IAC5F,CAAC;IAED;;;;;;OAMG;IACI,WAAW,CAAC,GAAW;QAC5B,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,mCAAkB,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;IAC9F,CAAC;IAED;;;;OAIG;IACI,aAAa,CAAC,OAA6B;QAChD,OAAO,IAAI,CAAC,SAAS,EAAE;aACpB,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,qCAAoB,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;aACzE,KAAK,CAAC,iBAAiB,EAAE,GAAG,EAAE;YAC7B,OAAO,CAAC,IAAI,GAAG,SAAS,CAAC;YACzB,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG;IACI,YAAY,CAAC,OAA4B;QAC9C,OAAO,IAAI,CAAC,SAAS,EAAE;aACpB,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE;YAClB,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE;gBAC5C,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC;aAClB;YACD,OAAO,IAAI,oCAAmB,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC7D,CAAC,CAAC;aACD,KAAK,CAAC,iBAAiB,EAAE,GAAG,EAAE;YAC7B,OAAO,CAAC,IAAI,GAAG,SAAS,CAAC;YACzB,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;OAIG;IACI,WAAW;QAChB,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,4BAAW,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;IACpF,CAAC;IAED;;;;;;;;;SASK;IACE,IAAI,CAAC,IAAY;QACtB,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IACtF,CAAC;IAED;;;;;OAKG;IACI,OAAO,CAAC,IAAY;QACzB,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IACzF,CAAC;IAED;;;;;;OAMG;IACI,IAAI,CAAC,IAAY;QACtB,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IACxF,CAAC;IAED;;;;;;OAMG;IACI,IAAI,CAAC,QAA6B,EAAE,IAAY,EAAE,IAAa;QACpE,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IACxG,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,IAAI,GAAG,IAAI;QACtB,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,6BAAY,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;IACzF,CAAC;IAED;;;;OAIG;IACI,GAAG;QACR,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,2BAAU,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;IACnF,CAAC;IAED;;;;OAIG;IACI,gBAAgB;QACrB,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,wCAAuB,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;IAChG,CAAC;IAED;;;;OAIG;IACI,aAAa;QAClB,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,kCAAoB,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IAC/F,CAAC;CACF;AAxhBD,+BAwhBC"}