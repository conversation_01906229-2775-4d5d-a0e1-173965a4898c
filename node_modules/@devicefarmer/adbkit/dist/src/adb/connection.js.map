{"version": 3, "file": "connection.js", "sourceRoot": "", "sources": ["../../../src/adb/connection.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,yCAA2B;AAC3B,mCAAsC;AACtC,iDAAwE;AACxE,sDAA8B;AAC9B,kDAA0B;AAC1B,kDAAsB;AAEtB,wDAAgC;AAGhC,MAAM,KAAK,GAAG,IAAA,eAAC,EAAC,gBAAgB,CAAC,CAAC;AAElC,MAAqB,UAAW,SAAQ,qBAAY;IAMlD,YAAY,OAAuB;QACjC,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,OAAO,GAAG,OAAO,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;QACtC,sBAAsB;QACtB,sBAAsB;QACtB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;IAC7B,CAAC;IAEM,OAAO;QACZ,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACxC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAC7B,IAAI,CAAC,MAAM,GAAG,IAAI,gBAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACtC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;QACtD,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QAC9C,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;QAClD,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;QACtD,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,QAAiB,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC;QAE7E,OAAO,IAAI,kBAAQ,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACtC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YACrC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QACpC,CAAC,CAAC;aACC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;YACb,IAAI,GAAG,CAAC,IAAI,KAAK,cAAc,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;gBACtD,KAAK,CAAC,4DAA4D,CAAC,CAAC;gBACpE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;gBAC1B,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;oBAClC,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;gBACxB,CAAC,CAAC,CAAC;aACJ;iBAAM;gBACL,IAAI,CAAC,GAAG,EAAE,CAAC;gBACX,MAAM,GAAG,CAAC;aACX;QACH,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,EAAE;YACT,0EAA0E;YAC1E,wDAAwD;YACxD,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;oBAC9B,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;wBAC3D,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;qBACzB;gBACH,CAAC,CAAC,CAAC;aACJ;YACD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,SAAS;QACd,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAEM,GAAG;QACR,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;SACnB;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,KAAK,CAAC,IAAY,EAAE,QAAgC;QACzD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAA,cAAI,EAAC,IAAI,CAAC,EAAE,QAAQ,CAAC,CAAC;QACxC,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,WAAW;QAChB,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,IAAI,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;YAC1B,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;SAC1B;QACD,MAAM,IAAI,GAAa,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC;QACtF,KAAK,CAAC,4BAA4B,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACzE,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IAC9B,CAAC;IAEO,KAAK,CAAC,IAAc,EAAE,OAAO;QACnC,KAAK,CAAC,QAAQ,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACpD,OAAO,kBAAQ,CAAC,SAAS,CAMvB,wBAAQ,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,GAAa,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IACzD,CAAC;CAGF;AA/FD,6BA+FC"}