{"version": 3, "file": "DeviceClient.d.ts", "sourceRoot": "", "sources": ["../../../src/adb/DeviceClient.ts"], "names": [], "mappings": ";AACA,OAAO,MAAM,MAAM,6BAA6B,CAAC;AACjD,OAAO,UAAU,MAAM,cAAc,CAAC;AACtC,OAAO,IAAI,MAAM,QAAQ,CAAC;AAE1B,OAAO,QAAQ,MAAM,aAAa,CAAC;AAyCnC,OAAO,OAAO,MAAM,YAAY,CAAC;AACjC,OAAO,OAAO,MAAM,YAAY,CAAC;AACjC,OAAO,oBAAoB,MAAM,yBAAyB,CAAC;AAC3D,OAAO,mBAAmB,MAAM,wBAAwB,CAAC;AACzD,OAAO,QAAQ,MAAM,UAAU,CAAC;AAChC,OAAO,EAAE,MAAM,EAAE,MAAM,QAAQ,CAAC;AAChC,OAAO,KAAK,MAAM,cAAc,CAAC;AACjC,OAAO,KAAK,MAAM,cAAc,CAAC;AACjC,OAAO,YAAY,MAAM,qBAAqB,CAAC;AAC/C,OAAO,EAAE,UAAU,EAAE,MAAM,IAAI,CAAC;AAChC,OAAO,YAAY,MAAM,qBAAqB,CAAC;AAC/C,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,EAAE,QAAQ,EAAE,MAAM,aAAa,CAAC;AACvC,OAAO,yBAAyB,MAAM,8BAA8B,CAAC;AACrE,OAAO,YAAY,MAAM,iBAAiB,CAAC;AAC3C,OAAO,WAAW,MAAM,eAAe,CAAC;AACxC,OAAO,cAAc,MAAM,mBAAmB,CAAC;AAC/C,OAAO,MAAM,MAAM,UAAU,CAAC;AAM9B,MAAM,CAAC,OAAO,OAAO,YAAY;aACH,MAAM,EAAE,MAAM;aAAkB,MAAM,EAAE,MAAM;gBAA9C,MAAM,EAAE,MAAM,EAAkB,MAAM,EAAE,MAAM;IAI1E;;;;OAIG;IACI,WAAW,IAAI,QAAQ,CAAC,MAAM,CAAC;IAItC;;;OAGG;IACI,aAAa,IAAI,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;IAGxD;;;;OAIG;IACI,QAAQ,IAAI,QAAQ,CAAC,MAAM,CAAC;IAInC;;;;OAIG;IACI,aAAa,IAAI,QAAQ,CAAC,UAAU,CAAC;IAI5C;;;;;;;;;;OAUG;IACI,WAAW,IAAI,QAAQ,CAAC,QAAQ,CAAC;IAIxC;;;;;OAKG;IACI,WAAW,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC;IAItD;;;;;;OAMG;IACI,gBAAgB,CAAC,KAAK,SAAU,GAAG,QAAQ,CAAC,MAAM,CAAC;IAU1D;;;;;;;;;;;;;OAaG;IACI,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC;IAIhE;;;;;;;OAOG;IACI,YAAY,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;IAI1C;;;;;;;;OAQG;IACI,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC;IAGhE;;;;;;OAMG;IACI,YAAY,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;IAI1C;;OAEG;IACH,OAAO,CAAC,UAAU;IAIlB;;OAEG;IACI,SAAS,IAAI,QAAQ,CAAC,UAAU,CAAC;IAIxC;;;;;;OAMG;IACI,KAAK,CAAC,OAAO,EAAE,MAAM,GAAG,SAAS,CAAC,YAAY,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC;IAIzE;;;;OAIG;IACI,MAAM,IAAI,QAAQ,CAAC,OAAO,CAAC;IAIlC;;;;OAIG;IACI,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC;IAInC;;;;OAIG;IACI,IAAI,IAAI,QAAQ,CAAC,OAAO,CAAC;IAIhC;;;;;;;;;;;;;;;OAeG;IACI,SAAS,IAAI,QAAQ,CAAC,WAAW,CAAC;IAIzC;;;;;;;;OAQG;IACI,WAAW,CAAC,MAAM,SAAQ,GAAG,QAAQ,CAAC,yBAAyB,CAAC;IAIvE;;;;;;;;OAQG;IACI,SAAS,IAAI,QAAQ,CAAC,MAAM,CAAC;IASpC;;;;;;OAMG;IACI,SAAS,CAAC,IAAI,EAAE,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;IAIhD;;;;;;OAMG;IACI,OAAO,CAAC,IAAI,EAAE,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;IAI9C;;;;;;;SAOK;IACE,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;IAI7D;;;;;;;;OAQG;IACI,UAAU,CAAC,IAAI,SAAO,GAAG,QAAQ,CAAC,MAAM,CAAC;IAoBhD;;;;;;;;;OASG;IACI,UAAU,CAAC,OAAO,GAAE;QAAE,KAAK,CAAC,EAAE,OAAO,CAAA;KAAO,GAAG,QAAQ,CAAC,MAAM,CAAC;IAMtE;;;;;;;;;;;;;;;;;OAiBG;IACI,YAAY,IAAI,QAAQ,CAAC,QAAQ,CAAC;IAIzC;;;;;;OAMG;IACI,KAAK,CAAC,GAAG,EAAE,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC;IAI5C;;;;;;;OAOG;IACI,OAAO,CAAC,GAAG,EAAE,MAAM,GAAG,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC;IAiB3D;;;;;;;OAOG;IACI,aAAa,CAAC,GAAG,EAAE,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC;IAUpD;;;;;OAKG;IACI,SAAS,CAAC,GAAG,EAAE,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC;IAIhD;;;;;;OAMG;IACI,WAAW,CAAC,GAAG,EAAE,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC;IAIlD;;;;OAIG;IACI,aAAa,CAAC,OAAO,EAAE,oBAAoB,GAAG,QAAQ,CAAC,OAAO,CAAC;IAStE;;;OAGG;IACI,YAAY,CAAC,OAAO,EAAE,mBAAmB,GAAG,QAAQ,CAAC,OAAO,CAAC;IAcpE;;;;OAIG;IACI,WAAW,IAAI,QAAQ,CAAC,IAAI,CAAC;IAIpC;;;;;;;;;SASK;IACE,IAAI,CAAC,IAAI,EAAE,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC;IAI1C;;;;;OAKG;IACI,OAAO,CAAC,IAAI,EAAE,MAAM,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC;IAI/C;;;;;;OAMG;IACI,IAAI,CAAC,IAAI,EAAE,MAAM,GAAG,QAAQ,CAAC,YAAY,CAAC;IAIjD;;;;;;OAMG;IACI,IAAI,CAAC,QAAQ,EAAE,MAAM,GAAG,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,MAAM,GAAG,QAAQ,CAAC,YAAY,CAAC;IAI/F;;;;;OAKG;IACI,KAAK,CAAC,IAAI,SAAO,GAAG,QAAQ,CAAC,MAAM,CAAC;IAI3C;;;;OAIG;IACI,GAAG,IAAI,QAAQ,CAAC,OAAO,CAAC;IAI/B;;;;OAIG;IACI,gBAAgB,IAAI,QAAQ,CAAC,OAAO,CAAC;IAI5C;;;;OAIG;IACI,aAAa,IAAI,QAAQ,CAAC,MAAM,CAAC;CAGzC"}