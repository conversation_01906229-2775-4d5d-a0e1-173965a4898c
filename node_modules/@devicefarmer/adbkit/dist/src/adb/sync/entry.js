"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
const stats_1 = __importDefault(require("./stats"));
class Entry extends stats_1.default {
    constructor(name, mode, size, mtime) {
        super(mode, size, mtime);
        this.name = name;
    }
    toString() {
        return this.name;
    }
}
module.exports = Entry;
//# sourceMappingURL=entry.js.map