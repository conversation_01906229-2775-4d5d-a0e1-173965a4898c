"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
const Fs = __importStar(require("fs"));
class Stats extends Fs.Stats {
    constructor(mode, size, mtime) {
        super();
        this.mode = mode;
        this.size = size;
        this.mtime = new Date(mtime * 1000);
    }
}
exports.default = Stats;
// The following constant were extracted from `man 2 stat` on Ubuntu 12.10.
Stats.S_IFMT = 0o170000; // bit mask for the file type bit fields
Stats.S_IFSOCK = 0o140000; // socket
Stats.S_IFLNK = 0o120000; // symbolic link
Stats.S_IFREG = 0o100000; // regular file
Stats.S_IFBLK = 0o060000; // block device
Stats.S_IFDIR = 0o040000; // directory
Stats.S_IFCHR = 0o020000; // character device
Stats.S_IFIFO = 0o010000; // FIFO
Stats.S_ISUID = 0o004000; // set UID bit
Stats.S_ISGID = 0o002000; // set-group-ID bit (see below)
Stats.S_ISVTX = 0o001000; // sticky bit (see below)
Stats.S_IRWXU = 0o0700; // mask for file owner permissions
Stats.S_IRUSR = 0o0400; // owner has read permission
Stats.S_IWUSR = 0o0200; // owner has write permission
Stats.S_IXUSR = 0o0100; // owner has execute permission
Stats.S_IRWXG = 0o0070; // mask for group permissions
Stats.S_IRGRP = 0o0040; // group has read permission
//# sourceMappingURL=stats.js.map