"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const protocol_1 = __importDefault(require("../../protocol"));
const command_1 = __importDefault(require("../../command"));
class ListReversesCommand extends command_1.default {
    execute() {
        this._send('reverse:list-forward');
        return this.parser.readAscii(4).then((reply) => {
            switch (reply) {
                case protocol_1.default.OKAY:
                    return this.parser.readValue().then((value) => {
                        return this._parseReverses(value);
                    });
                case protocol_1.default.FAIL:
                    return this.parser.readError();
                default:
                    return this.parser.unexpected(reply, 'OKAY or FAIL');
            }
        });
    }
    _parseReverses(value) {
        const reverses = [];
        const ref = value.toString().split('\n');
        for (let i = 0, len = ref.length; i < len; i++) {
            const reverse = ref[i];
            if (reverse) {
                const [, remote, local] = reverse.split(/\s+/);
                reverses.push({ remote, local });
            }
        }
        return reverses;
    }
}
exports.default = ListReversesCommand;
//# sourceMappingURL=listreverses.js.map