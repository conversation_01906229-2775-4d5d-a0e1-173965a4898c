"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const protocol_1 = __importDefault(require("../../protocol"));
const command_1 = __importDefault(require("../../command"));
class InstallCommand extends command_1.default {
    execute(apk) {
        this._send(`shell:pm install -r ${this._escapeCompat(apk)}`);
        return this.parser.readAscii(4).then((reply) => {
            switch (reply) {
                case protocol_1.default.OKAY:
                    return this.parser
                        .searchLine(/^(Success|Failure \[(.*?)\])$/)
                        .then(function (match) {
                        let code, err;
                        if (match[1] === 'Success') {
                            return true;
                        }
                        else {
                            code = match[2];
                            err = new Error(`${apk} could not be installed [${code}]`);
                            err.code = code;
                            throw err;
                        }
                    })
                        .finally(() => {
                        // Consume all remaining content to "naturally" close the
                        // connection.
                        return this.parser.readAll();
                    });
                case protocol_1.default.FAIL:
                    return this.parser.readError();
                default:
                    return this.parser.unexpected(reply, 'OKAY or FAIL');
            }
        });
    }
}
exports.default = InstallCommand;
//# sourceMappingURL=install.js.map