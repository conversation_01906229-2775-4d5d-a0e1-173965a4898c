{"version": 3, "file": "startactivity.js", "sourceRoot": "", "sources": ["../../../../../src/adb/command/host-transport/startactivity.ts"], "names": [], "mappings": ";;;;AAAA,8DAAsC;AACtC,0DAAkC;AAClC,4DAAoC;AAKpC,MAAM,QAAQ,GAAG,eAAe,CAAC;AAEjC,MAAM,WAAW,GAAG;IAClB,MAAM,EAAE,GAAG;IACX,IAAI,EAAE,IAAI;IACV,IAAI,EAAE,GAAG;IACT,GAAG,EAAE,GAAG;IACR,IAAI,EAAE,GAAG;IACT,KAAK,EAAE,GAAG;IACV,GAAG,EAAE,GAAG;IACR,SAAS,EAAE,IAAI;CAChB,CAAC;AAEF,MAAM,oBAAqB,SAAQ,iBAAgB;IACjD,OAAO,CAAC,OAA6B;QACnC,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QACvC,IAAI,OAAO,CAAC,KAAK,EAAE;YACjB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACjB;QACD,IAAI,OAAO,CAAC,IAAI,EAAE;YAChB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACjB;QACD,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,KAAK,CAAC,EAAE;YACtC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;SACjD;QACD,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,IAAI,CAAC,OAAe,EAAE,IAA4B;QAChD,IAAI,CAAC,KAAK,CAAC,YAAY,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACpD,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;YAC7C,QAAQ,KAAK,EAAE;gBACb,KAAK,kBAAQ,CAAC,IAAI;oBAChB,OAAO,IAAI,CAAC,MAAM;yBACf,UAAU,CAAC,QAAQ,CAAC;yBACpB,OAAO,CAAC,GAAG,EAAE;wBACZ,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;oBAC3B,CAAC,CAAC;yBACD,IAAI,CAAC,UAAU,KAAK;wBACnB,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC5B,CAAC,CAAC;yBACD,KAAK,CAAC,gBAAM,CAAC,iBAAiB,EAAE;wBAC/B,OAAO,IAAI,CAAC;oBACd,CAAC,CAAC,CAAC;gBACP,KAAK,kBAAQ,CAAC,IAAI;oBAChB,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;gBACjC;oBACE,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;aACxD;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAES,WAAW,CAAC,OAA6B;QACjD,MAAM,IAAI,GAA2B,EAAE,CAAC;QACxC,IAAI,OAAO,CAAC,MAAM,EAAE;YAClB,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;SAClD;QACD,IAAI,OAAO,CAAC,MAAM,EAAE;YAClB,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;SAC/C;QACD,IAAI,OAAO,CAAC,IAAI,EAAE;YAChB,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;SAC7C;QACD,IAAI,OAAO,CAAC,QAAQ,EAAE;YACpB,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;SACjD;QACD,IAAI,OAAO,CAAC,QAAQ,EAAE;YACpB,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBACnC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;oBACpC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;gBACjD,CAAC,CAAC,CAAC;aACJ;iBAAM;gBACL,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;aACjD;SACF;QACD,IAAI,OAAO,CAAC,SAAS,EAAE;YACrB,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;SAClD;QACD,IAAI,OAAO,CAAC,KAAK,EAAE;YACjB,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;SAC9C;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,iCAAiC;IACzB,aAAa,CAAC,MAA6B;QACjD,IAAI,CAAC,MAAM,EAAE;YACX,OAAO,EAAE,CAAC;SACX;QACD,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACzB,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;gBAClC,OAAO,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;YAClD,CAAC,EAAE,EAAE,CAAC,CAAC;SACR;aAAM;YACL,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;gBAC7C,OAAO,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC9D,CAAC,EAAE,EAAE,CAAC,CAAC;SACR;IACH,CAAC;IAEO,iBAAiB,CAAC,GAAW,EAAE,KAAiB;QACtD,IAAI,OAAO,GAAG;YACZ,GAAG,EAAE,GAAG;YACR,IAAI,EAAE,EAAY;YAClB,8DAA8D;YAC9D,KAAK,EAAE,SAAgB;SACxB,CAAC;QACF,IAAI,KAAK,KAAK,IAAI,EAAE;YAClB,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC;SACvB;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAC/B,MAAM,IAAI,KAAK,CACb,mCAAmC,GAAG,0HAA0H,CACjK,CAAC;SACH;aAAM;YACL,QAAQ,OAAO,KAAK,EAAE;gBACpB,KAAK,QAAQ;oBACX,OAAO,CAAC,IAAI,GAAG,QAAQ,CAAC;oBACxB,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;oBACtB,MAAM;gBACR,KAAK,SAAS;oBACZ,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC;oBACtB,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;oBACtB,MAAM;gBACR,KAAK,QAAQ;oBACX,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC;oBACrB,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;oBACtB,MAAM;gBACR,KAAK,QAAQ;oBACX,8DAA8D;oBAC9D,OAAO,GAAG,KAAY,CAAC;oBACvB,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC;aACrB;SACF;QACD,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;IACxC,CAAC;IAEO,gBAAgB,CAAC,KAAK;QAC5B,MAAM,IAAI,GAA2B,EAAE,CAAC;QACxC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;YACf,KAAK,CAAC,IAAI,GAAG,QAAQ,CAAC;SACvB;QACD,MAAM,IAAI,GAAG,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACrC,IAAI,CAAC,IAAI,EAAE;YACT,MAAM,IAAI,KAAK,CAAC,qBAAqB,KAAK,CAAC,IAAI,gBAAgB,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;SAC9E;QACD,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,EAAE;YACzB,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC;YACxB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;SACpC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YACrC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,GAAG,CAAC,CAAC;YACzB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;YACnC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;SAChD;aAAM;YACL,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC;YACxB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;YACnC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;SACtC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAED,iBAAS,oBAAoB,CAAC"}