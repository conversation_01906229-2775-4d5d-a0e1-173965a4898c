{"version": 3, "file": "framebuffer.js", "sourceRoot": "", "sources": ["../../../../../src/adb/command/host-transport/framebuffer.ts"], "names": [], "mappings": ";;;;;AAAA,iDAAsC;AACtC,kDAAsB;AACtB,kFAA0D;AAC1D,8DAAsC;AACtC,4DAAoC;AAMpC,MAAM,KAAK,GAAG,IAAA,eAAC,EAAC,yBAAyB,CAAC,CAAC;AAE3C,sDAAsD;AACtD,8DAA8D;AAC9D,MAAqB,kBAAmB,SAAQ,iBAAY;IAC1D,OAAO,CAAC,MAAc;QACpB,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QAC3B,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;YAC7C,QAAQ,KAAK,EAAE;gBACb,KAAK,kBAAQ,CAAC,IAAI;oBAChB,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;wBAC/C,IAAI,MAAiC,CAAC;wBACtC,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;wBACvC,QAAQ,MAAM,EAAE;4BACd,KAAK,KAAK;gCACR,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,EAA+B,CAAC;gCACxD,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;gCACnB,OAAO,MAAM,CAAC;4BAChB;gCACE,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAA8B,CAAC;gCAClE,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;gCACnB,OAAO,MAAM,CAAC;yBACjB;oBACH,CAAC,CAAC,CAAC;gBACL,KAAK,kBAAQ,CAAC,IAAI;oBAChB,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;gBACjC;oBACE,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;aACxD;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,QAAQ,CAAC,IAAqB,EAAE,MAAc,EAAE,GAAc;QAC5D,KAAK,CAAC,0CAA0C,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;QACxE,QAAQ,IAAI,CAAC,MAAM,EAAE;YACnB,KAAK,KAAK,CAAC;YACX,KAAK,MAAM;gBACT,MAAM;YACR;gBACE,0CAA0C;gBAC1C,KAAK,CAAC,0BAA0B,IAAI,CAAC,MAAM,yBAAyB,CAAC,CAAC;gBACtE,MAAM,SAAS,GAAG,IAAI,sBAAY,CAAC,IAAI,CAAC,CAAC;gBACzC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;gBACpB,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SAC3C;QACD,MAAM,IAAI,GAAG,IAAA,qBAAK,EAAC,IAAI,EAAE,CAAC,SAAS,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,GAAG,IAAI,CAAC,MAAM,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,CAAC,CAAC;QAClH,IAAI,GAAG,EAAE;YACP,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACtB;QACD,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED,YAAY,CAAC,MAAc;QACzB,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,MAAM,OAAO,GAAG,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC5C,IAAI,OAAO,KAAK,EAAE,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;SAC3D;QACD,MAAM,IAAI,CAAC,CAAC;QACZ,MAAM,GAAG,GAAG,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QACxC,MAAM,IAAI,CAAC,CAAC;QACZ,MAAM,IAAI,GAAG,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QACzC,MAAM,IAAI,CAAC,CAAC;QACZ,MAAM,KAAK,GAAG,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC1C,MAAM,IAAI,CAAC,CAAC;QACZ,MAAM,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC3C,MAAM,IAAI,CAAC,CAAC;QACZ,MAAM,UAAU,GAAG,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC/C,MAAM,IAAI,CAAC,CAAC;QACZ,MAAM,UAAU,GAAG,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC/C,MAAM,IAAI,CAAC,CAAC;QACZ,MAAM,WAAW,GAAG,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAChD,MAAM,IAAI,CAAC,CAAC;QACZ,MAAM,WAAW,GAAG,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAChD,MAAM,IAAI,CAAC,CAAC;QACZ,MAAM,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QACjD,MAAM,IAAI,CAAC,CAAC;QACZ,MAAM,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QACjD,MAAM,IAAI,CAAC,CAAC;QACZ,MAAM,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QACjD,MAAM,IAAI,CAAC,CAAC;QACZ,MAAM,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QACjD,IAAI,MAAM,GAAgB,WAAW,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;QAC5D,IAAI,GAAG,KAAK,EAAE,IAAI,YAAY,EAAE;YAC9B,MAAM,IAAI,GAAG,CAAC;SACf;QACD,OAAO;YACL,OAAO;YACP,GAAG;YACH,IAAI;YACJ,KAAK;YACL,MAAM;YACN,UAAU;YACV,UAAU;YACV,WAAW;YACX,WAAW;YACX,YAAY;YACZ,YAAY;YACZ,YAAY;YACZ,YAAY;YACZ,MAAM,EAAE,MAAqB;SAC9B,CAAC;IACJ,CAAC;CACF;AAnGD,qCAmGC"}