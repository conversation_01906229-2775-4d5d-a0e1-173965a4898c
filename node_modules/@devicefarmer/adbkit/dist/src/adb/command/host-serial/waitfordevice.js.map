{"version": 3, "file": "waitfordevice.js", "sourceRoot": "", "sources": ["../../../../../src/adb/command/host-serial/waitfordevice.ts"], "names": [], "mappings": ";;;;;AAAA,8DAAsC;AACtC,4DAAoC;AAGpC,MAAqB,oBAAqB,SAAQ,iBAAe;IAC/D,OAAO,CAAC,MAAc;QACpB,IAAI,CAAC,KAAK,CAAC,eAAe,MAAM,sBAAsB,CAAC,CAAC;QACxD,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;YAC7C,QAAQ,KAAK,EAAE;gBACb,KAAK,kBAAQ,CAAC,IAAI;oBAChB,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;wBAC7C,QAAQ,KAAK,EAAE;4BACb,KAAK,kBAAQ,CAAC,IAAI;gCAChB,OAAO,MAAM,CAAC;4BAChB,KAAK,kBAAQ,CAAC,IAAI;gCAChB,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;4BACjC;gCACE,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;yBACxD;oBACH,CAAC,CAAC,CAAC;gBACL,KAAK,kBAAQ,CAAC,IAAI;oBAChB,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;gBACjC;oBACE,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;aACxD;QACH,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAvBD,uCAuBC"}