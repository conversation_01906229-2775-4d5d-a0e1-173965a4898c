/// <reference types="node" />
import { EventEmitter } from 'events';
import Connection from './connection';
import TcpUsbServer from './tcpusb/server';
import Device from '../Device';
import Bluebird from 'bluebird';
import { ClientOptions } from '../ClientOptions';
import SocketOptions from '../SocketOptions';
import Tracker from './tracker';
import DeviceWithPath from '../DeviceWithPath';
import DeviceClient from './DeviceClient';
export default class Client extends EventEmitter {
    readonly options: ClientOptions;
    readonly host: string;
    readonly port: number | string;
    readonly bin: string;
    constructor({ host, port, bin }?: ClientOptions);
    createTcpUsbBridge(serial: string, options: SocketOptions): TcpUsbServer;
    connection(): Bluebird<Connection>;
    version(): Bluebird<number>;
    connect(host: string, port?: number): Bluebird<string>;
    disconnect(host: string, port?: number): Bluebird<DeviceClient>;
    listDevices(): Bluebird<Device[]>;
    listDevicesWithPaths(): Bluebird<DeviceWithPath[]>;
    trackDevices(): Bluebird<Tracker>;
    kill(): Bluebird<boolean>;
    getDevice(serial: string): DeviceClient;
}
//# sourceMappingURL=client.d.ts.map