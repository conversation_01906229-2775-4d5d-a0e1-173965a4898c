/// <reference types="node" />
import { EventEmitter } from 'events';
import { ChildProcess } from 'child_process';
import Parser from './parser';
import { Socket } from 'net';
import Bluebird from 'bluebird';
import { ClientOptions } from '../ClientOptions';
export default class Connection extends EventEmitter {
    socket: Socket;
    parser: Parser;
    private triedStarting;
    options: ClientOptions;
    constructor(options?: ClientOptions);
    connect(): Bluebird<Connection>;
    /**
     * added for Mock testing
     */
    getSocket(): unknown;
    end(): this;
    write(data: Buffer, callback?: (err?: Error) => void): this;
    startServer(): Bluebird<ChildProcess>;
    private _exec;
}
//# sourceMappingURL=connection.d.ts.map