"use strict";
// Generated by `npm run keycode` on Tue, 08 Dec 2020 12:17:57 GMT
// KeyEvent.java Copyright (C) 2006 The Android Open Source Project
Object.defineProperty(exports, "__esModule", { value: true });
exports.KeyCodes = void 0;
var KeyCodes;
(function (KeyCodes) {
    KeyCodes[KeyCodes["KEYCODE_UNKNOWN"] = 0] = "KEYCODE_UNKNOWN";
    KeyCodes[KeyCodes["KEYCODE_SOFT_LEFT"] = 1] = "KEYCODE_SOFT_LEFT";
    KeyCodes[KeyCodes["KEYCODE_SOFT_RIGHT"] = 2] = "KEYCODE_SOFT_RIGHT";
    KeyCodes[KeyCodes["KEYCODE_HOME"] = 3] = "KEYCODE_HOME";
    KeyCodes[KeyCodes["KEYCODE_BACK"] = 4] = "KEYCODE_BACK";
    KeyCodes[KeyCodes["KEYCODE_CALL"] = 5] = "KEYCODE_CALL";
    KeyCodes[KeyCodes["KEYCO<PERSON>_ENDCALL"] = 6] = "KEYCODE_ENDCALL";
    KeyCodes[KeyCodes["KEY<PERSON>DE_0"] = 7] = "KEYCODE_0";
    KeyCodes[KeyCodes["KEYCODE_1"] = 8] = "KEYCODE_1";
    KeyCodes[KeyCodes["KEYCODE_2"] = 9] = "KEYCODE_2";
    KeyCodes[KeyCodes["KEYCODE_3"] = 10] = "KEYCODE_3";
    KeyCodes[KeyCodes["KEYCODE_4"] = 11] = "KEYCODE_4";
    KeyCodes[KeyCodes["KEYCODE_5"] = 12] = "KEYCODE_5";
    KeyCodes[KeyCodes["KEYCODE_6"] = 13] = "KEYCODE_6";
    KeyCodes[KeyCodes["KEYCODE_7"] = 14] = "KEYCODE_7";
    KeyCodes[KeyCodes["KEYCODE_8"] = 15] = "KEYCODE_8";
    KeyCodes[KeyCodes["KEYCODE_9"] = 16] = "KEYCODE_9";
    KeyCodes[KeyCodes["KEYCODE_STAR"] = 17] = "KEYCODE_STAR";
    KeyCodes[KeyCodes["KEYCODE_POUND"] = 18] = "KEYCODE_POUND";
    KeyCodes[KeyCodes["KEYCODE_DPAD_UP"] = 19] = "KEYCODE_DPAD_UP";
    KeyCodes[KeyCodes["KEYCODE_DPAD_DOWN"] = 20] = "KEYCODE_DPAD_DOWN";
    KeyCodes[KeyCodes["KEYCODE_DPAD_LEFT"] = 21] = "KEYCODE_DPAD_LEFT";
    KeyCodes[KeyCodes["KEYCODE_DPAD_RIGHT"] = 22] = "KEYCODE_DPAD_RIGHT";
    KeyCodes[KeyCodes["KEYCODE_DPAD_CENTER"] = 23] = "KEYCODE_DPAD_CENTER";
    KeyCodes[KeyCodes["KEYCODE_VOLUME_UP"] = 24] = "KEYCODE_VOLUME_UP";
    KeyCodes[KeyCodes["KEYCODE_VOLUME_DOWN"] = 25] = "KEYCODE_VOLUME_DOWN";
    KeyCodes[KeyCodes["KEYCODE_POWER"] = 26] = "KEYCODE_POWER";
    KeyCodes[KeyCodes["KEYCODE_CAMERA"] = 27] = "KEYCODE_CAMERA";
    KeyCodes[KeyCodes["KEYCODE_CLEAR"] = 28] = "KEYCODE_CLEAR";
    KeyCodes[KeyCodes["KEYCODE_A"] = 29] = "KEYCODE_A";
    KeyCodes[KeyCodes["KEYCODE_B"] = 30] = "KEYCODE_B";
    KeyCodes[KeyCodes["KEYCODE_C"] = 31] = "KEYCODE_C";
    KeyCodes[KeyCodes["KEYCODE_D"] = 32] = "KEYCODE_D";
    KeyCodes[KeyCodes["KEYCODE_E"] = 33] = "KEYCODE_E";
    KeyCodes[KeyCodes["KEYCODE_F"] = 34] = "KEYCODE_F";
    KeyCodes[KeyCodes["KEYCODE_G"] = 35] = "KEYCODE_G";
    KeyCodes[KeyCodes["KEYCODE_H"] = 36] = "KEYCODE_H";
    KeyCodes[KeyCodes["KEYCODE_I"] = 37] = "KEYCODE_I";
    KeyCodes[KeyCodes["KEYCODE_J"] = 38] = "KEYCODE_J";
    KeyCodes[KeyCodes["KEYCODE_K"] = 39] = "KEYCODE_K";
    KeyCodes[KeyCodes["KEYCODE_L"] = 40] = "KEYCODE_L";
    KeyCodes[KeyCodes["KEYCODE_M"] = 41] = "KEYCODE_M";
    KeyCodes[KeyCodes["KEYCODE_N"] = 42] = "KEYCODE_N";
    KeyCodes[KeyCodes["KEYCODE_O"] = 43] = "KEYCODE_O";
    KeyCodes[KeyCodes["KEYCODE_P"] = 44] = "KEYCODE_P";
    KeyCodes[KeyCodes["KEYCODE_Q"] = 45] = "KEYCODE_Q";
    KeyCodes[KeyCodes["KEYCODE_R"] = 46] = "KEYCODE_R";
    KeyCodes[KeyCodes["KEYCODE_S"] = 47] = "KEYCODE_S";
    KeyCodes[KeyCodes["KEYCODE_T"] = 48] = "KEYCODE_T";
    KeyCodes[KeyCodes["KEYCODE_U"] = 49] = "KEYCODE_U";
    KeyCodes[KeyCodes["KEYCODE_V"] = 50] = "KEYCODE_V";
    KeyCodes[KeyCodes["KEYCODE_W"] = 51] = "KEYCODE_W";
    KeyCodes[KeyCodes["KEYCODE_X"] = 52] = "KEYCODE_X";
    KeyCodes[KeyCodes["KEYCODE_Y"] = 53] = "KEYCODE_Y";
    KeyCodes[KeyCodes["KEYCODE_Z"] = 54] = "KEYCODE_Z";
    KeyCodes[KeyCodes["KEYCODE_COMMA"] = 55] = "KEYCODE_COMMA";
    KeyCodes[KeyCodes["KEYCODE_PERIOD"] = 56] = "KEYCODE_PERIOD";
    KeyCodes[KeyCodes["KEYCODE_ALT_LEFT"] = 57] = "KEYCODE_ALT_LEFT";
    KeyCodes[KeyCodes["KEYCODE_ALT_RIGHT"] = 58] = "KEYCODE_ALT_RIGHT";
    KeyCodes[KeyCodes["KEYCODE_SHIFT_LEFT"] = 59] = "KEYCODE_SHIFT_LEFT";
    KeyCodes[KeyCodes["KEYCODE_SHIFT_RIGHT"] = 60] = "KEYCODE_SHIFT_RIGHT";
    KeyCodes[KeyCodes["KEYCODE_TAB"] = 61] = "KEYCODE_TAB";
    KeyCodes[KeyCodes["KEYCODE_SPACE"] = 62] = "KEYCODE_SPACE";
    KeyCodes[KeyCodes["KEYCODE_SYM"] = 63] = "KEYCODE_SYM";
    KeyCodes[KeyCodes["KEYCODE_EXPLORER"] = 64] = "KEYCODE_EXPLORER";
    KeyCodes[KeyCodes["KEYCODE_ENVELOPE"] = 65] = "KEYCODE_ENVELOPE";
    KeyCodes[KeyCodes["KEYCODE_ENTER"] = 66] = "KEYCODE_ENTER";
    KeyCodes[KeyCodes["KEYCODE_DEL"] = 67] = "KEYCODE_DEL";
    KeyCodes[KeyCodes["KEYCODE_GRAVE"] = 68] = "KEYCODE_GRAVE";
    KeyCodes[KeyCodes["KEYCODE_MINUS"] = 69] = "KEYCODE_MINUS";
    KeyCodes[KeyCodes["KEYCODE_EQUALS"] = 70] = "KEYCODE_EQUALS";
    KeyCodes[KeyCodes["KEYCODE_LEFT_BRACKET"] = 71] = "KEYCODE_LEFT_BRACKET";
    KeyCodes[KeyCodes["KEYCODE_RIGHT_BRACKET"] = 72] = "KEYCODE_RIGHT_BRACKET";
    KeyCodes[KeyCodes["KEYCODE_BACKSLASH"] = 73] = "KEYCODE_BACKSLASH";
    KeyCodes[KeyCodes["KEYCODE_SEMICOLON"] = 74] = "KEYCODE_SEMICOLON";
    KeyCodes[KeyCodes["KEYCODE_APOSTROPHE"] = 75] = "KEYCODE_APOSTROPHE";
    KeyCodes[KeyCodes["KEYCODE_SLASH"] = 76] = "KEYCODE_SLASH";
    KeyCodes[KeyCodes["KEYCODE_AT"] = 77] = "KEYCODE_AT";
    KeyCodes[KeyCodes["KEYCODE_NUM"] = 78] = "KEYCODE_NUM";
    KeyCodes[KeyCodes["KEYCODE_HEADSETHOOK"] = 79] = "KEYCODE_HEADSETHOOK";
    KeyCodes[KeyCodes["KEYCODE_FOCUS"] = 80] = "KEYCODE_FOCUS";
    KeyCodes[KeyCodes["KEYCODE_PLUS"] = 81] = "KEYCODE_PLUS";
    KeyCodes[KeyCodes["KEYCODE_MENU"] = 82] = "KEYCODE_MENU";
    KeyCodes[KeyCodes["KEYCODE_NOTIFICATION"] = 83] = "KEYCODE_NOTIFICATION";
    KeyCodes[KeyCodes["KEYCODE_SEARCH"] = 84] = "KEYCODE_SEARCH";
    KeyCodes[KeyCodes["KEYCODE_MEDIA_PLAY_PAUSE"] = 85] = "KEYCODE_MEDIA_PLAY_PAUSE";
    KeyCodes[KeyCodes["KEYCODE_MEDIA_STOP"] = 86] = "KEYCODE_MEDIA_STOP";
    KeyCodes[KeyCodes["KEYCODE_MEDIA_NEXT"] = 87] = "KEYCODE_MEDIA_NEXT";
    KeyCodes[KeyCodes["KEYCODE_MEDIA_PREVIOUS"] = 88] = "KEYCODE_MEDIA_PREVIOUS";
    KeyCodes[KeyCodes["KEYCODE_MEDIA_REWIND"] = 89] = "KEYCODE_MEDIA_REWIND";
    KeyCodes[KeyCodes["KEYCODE_MEDIA_FAST_FORWARD"] = 90] = "KEYCODE_MEDIA_FAST_FORWARD";
    KeyCodes[KeyCodes["KEYCODE_MUTE"] = 91] = "KEYCODE_MUTE";
    KeyCodes[KeyCodes["KEYCODE_PAGE_UP"] = 92] = "KEYCODE_PAGE_UP";
    KeyCodes[KeyCodes["KEYCODE_PAGE_DOWN"] = 93] = "KEYCODE_PAGE_DOWN";
    KeyCodes[KeyCodes["KEYCODE_PICTSYMBOLS"] = 94] = "KEYCODE_PICTSYMBOLS";
    KeyCodes[KeyCodes["KEYCODE_SWITCH_CHARSET"] = 95] = "KEYCODE_SWITCH_CHARSET";
    KeyCodes[KeyCodes["KEYCODE_BUTTON_A"] = 96] = "KEYCODE_BUTTON_A";
    KeyCodes[KeyCodes["KEYCODE_BUTTON_B"] = 97] = "KEYCODE_BUTTON_B";
    KeyCodes[KeyCodes["KEYCODE_BUTTON_C"] = 98] = "KEYCODE_BUTTON_C";
    KeyCodes[KeyCodes["KEYCODE_BUTTON_X"] = 99] = "KEYCODE_BUTTON_X";
    KeyCodes[KeyCodes["KEYCODE_BUTTON_Y"] = 100] = "KEYCODE_BUTTON_Y";
    KeyCodes[KeyCodes["KEYCODE_BUTTON_Z"] = 101] = "KEYCODE_BUTTON_Z";
    KeyCodes[KeyCodes["KEYCODE_BUTTON_L1"] = 102] = "KEYCODE_BUTTON_L1";
    KeyCodes[KeyCodes["KEYCODE_BUTTON_R1"] = 103] = "KEYCODE_BUTTON_R1";
    KeyCodes[KeyCodes["KEYCODE_BUTTON_L2"] = 104] = "KEYCODE_BUTTON_L2";
    KeyCodes[KeyCodes["KEYCODE_BUTTON_R2"] = 105] = "KEYCODE_BUTTON_R2";
    KeyCodes[KeyCodes["KEYCODE_BUTTON_THUMBL"] = 106] = "KEYCODE_BUTTON_THUMBL";
    KeyCodes[KeyCodes["KEYCODE_BUTTON_THUMBR"] = 107] = "KEYCODE_BUTTON_THUMBR";
    KeyCodes[KeyCodes["KEYCODE_BUTTON_START"] = 108] = "KEYCODE_BUTTON_START";
    KeyCodes[KeyCodes["KEYCODE_BUTTON_SELECT"] = 109] = "KEYCODE_BUTTON_SELECT";
    KeyCodes[KeyCodes["KEYCODE_BUTTON_MODE"] = 110] = "KEYCODE_BUTTON_MODE";
    KeyCodes[KeyCodes["KEYCODE_ESCAPE"] = 111] = "KEYCODE_ESCAPE";
    KeyCodes[KeyCodes["KEYCODE_FORWARD_DEL"] = 112] = "KEYCODE_FORWARD_DEL";
    KeyCodes[KeyCodes["KEYCODE_CTRL_LEFT"] = 113] = "KEYCODE_CTRL_LEFT";
    KeyCodes[KeyCodes["KEYCODE_CTRL_RIGHT"] = 114] = "KEYCODE_CTRL_RIGHT";
    KeyCodes[KeyCodes["KEYCODE_CAPS_LOCK"] = 115] = "KEYCODE_CAPS_LOCK";
    KeyCodes[KeyCodes["KEYCODE_SCROLL_LOCK"] = 116] = "KEYCODE_SCROLL_LOCK";
    KeyCodes[KeyCodes["KEYCODE_META_LEFT"] = 117] = "KEYCODE_META_LEFT";
    KeyCodes[KeyCodes["KEYCODE_META_RIGHT"] = 118] = "KEYCODE_META_RIGHT";
    KeyCodes[KeyCodes["KEYCODE_FUNCTION"] = 119] = "KEYCODE_FUNCTION";
    KeyCodes[KeyCodes["KEYCODE_SYSRQ"] = 120] = "KEYCODE_SYSRQ";
    KeyCodes[KeyCodes["KEYCODE_BREAK"] = 121] = "KEYCODE_BREAK";
    KeyCodes[KeyCodes["KEYCODE_MOVE_HOME"] = 122] = "KEYCODE_MOVE_HOME";
    KeyCodes[KeyCodes["KEYCODE_MOVE_END"] = 123] = "KEYCODE_MOVE_END";
    KeyCodes[KeyCodes["KEYCODE_INSERT"] = 124] = "KEYCODE_INSERT";
    KeyCodes[KeyCodes["KEYCODE_FORWARD"] = 125] = "KEYCODE_FORWARD";
    KeyCodes[KeyCodes["KEYCODE_MEDIA_PLAY"] = 126] = "KEYCODE_MEDIA_PLAY";
    KeyCodes[KeyCodes["KEYCODE_MEDIA_PAUSE"] = 127] = "KEYCODE_MEDIA_PAUSE";
    KeyCodes[KeyCodes["KEYCODE_MEDIA_CLOSE"] = 128] = "KEYCODE_MEDIA_CLOSE";
    KeyCodes[KeyCodes["KEYCODE_MEDIA_EJECT"] = 129] = "KEYCODE_MEDIA_EJECT";
    KeyCodes[KeyCodes["KEYCODE_MEDIA_RECORD"] = 130] = "KEYCODE_MEDIA_RECORD";
    KeyCodes[KeyCodes["KEYCODE_F1"] = 131] = "KEYCODE_F1";
    KeyCodes[KeyCodes["KEYCODE_F2"] = 132] = "KEYCODE_F2";
    KeyCodes[KeyCodes["KEYCODE_F3"] = 133] = "KEYCODE_F3";
    KeyCodes[KeyCodes["KEYCODE_F4"] = 134] = "KEYCODE_F4";
    KeyCodes[KeyCodes["KEYCODE_F5"] = 135] = "KEYCODE_F5";
    KeyCodes[KeyCodes["KEYCODE_F6"] = 136] = "KEYCODE_F6";
    KeyCodes[KeyCodes["KEYCODE_F7"] = 137] = "KEYCODE_F7";
    KeyCodes[KeyCodes["KEYCODE_F8"] = 138] = "KEYCODE_F8";
    KeyCodes[KeyCodes["KEYCODE_F9"] = 139] = "KEYCODE_F9";
    KeyCodes[KeyCodes["KEYCODE_F10"] = 140] = "KEYCODE_F10";
    KeyCodes[KeyCodes["KEYCODE_F11"] = 141] = "KEYCODE_F11";
    KeyCodes[KeyCodes["KEYCODE_F12"] = 142] = "KEYCODE_F12";
    KeyCodes[KeyCodes["KEYCODE_NUM_LOCK"] = 143] = "KEYCODE_NUM_LOCK";
    KeyCodes[KeyCodes["KEYCODE_NUMPAD_0"] = 144] = "KEYCODE_NUMPAD_0";
    KeyCodes[KeyCodes["KEYCODE_NUMPAD_1"] = 145] = "KEYCODE_NUMPAD_1";
    KeyCodes[KeyCodes["KEYCODE_NUMPAD_2"] = 146] = "KEYCODE_NUMPAD_2";
    KeyCodes[KeyCodes["KEYCODE_NUMPAD_3"] = 147] = "KEYCODE_NUMPAD_3";
    KeyCodes[KeyCodes["KEYCODE_NUMPAD_4"] = 148] = "KEYCODE_NUMPAD_4";
    KeyCodes[KeyCodes["KEYCODE_NUMPAD_5"] = 149] = "KEYCODE_NUMPAD_5";
    KeyCodes[KeyCodes["KEYCODE_NUMPAD_6"] = 150] = "KEYCODE_NUMPAD_6";
    KeyCodes[KeyCodes["KEYCODE_NUMPAD_7"] = 151] = "KEYCODE_NUMPAD_7";
    KeyCodes[KeyCodes["KEYCODE_NUMPAD_8"] = 152] = "KEYCODE_NUMPAD_8";
    KeyCodes[KeyCodes["KEYCODE_NUMPAD_9"] = 153] = "KEYCODE_NUMPAD_9";
    KeyCodes[KeyCodes["KEYCODE_NUMPAD_DIVIDE"] = 154] = "KEYCODE_NUMPAD_DIVIDE";
    KeyCodes[KeyCodes["KEYCODE_NUMPAD_MULTIPLY"] = 155] = "KEYCODE_NUMPAD_MULTIPLY";
    KeyCodes[KeyCodes["KEYCODE_NUMPAD_SUBTRACT"] = 156] = "KEYCODE_NUMPAD_SUBTRACT";
    KeyCodes[KeyCodes["KEYCODE_NUMPAD_ADD"] = 157] = "KEYCODE_NUMPAD_ADD";
    KeyCodes[KeyCodes["KEYCODE_NUMPAD_DOT"] = 158] = "KEYCODE_NUMPAD_DOT";
    KeyCodes[KeyCodes["KEYCODE_NUMPAD_COMMA"] = 159] = "KEYCODE_NUMPAD_COMMA";
    KeyCodes[KeyCodes["KEYCODE_NUMPAD_ENTER"] = 160] = "KEYCODE_NUMPAD_ENTER";
    KeyCodes[KeyCodes["KEYCODE_NUMPAD_EQUALS"] = 161] = "KEYCODE_NUMPAD_EQUALS";
    KeyCodes[KeyCodes["KEYCODE_NUMPAD_LEFT_PAREN"] = 162] = "KEYCODE_NUMPAD_LEFT_PAREN";
    KeyCodes[KeyCodes["KEYCODE_NUMPAD_RIGHT_PAREN"] = 163] = "KEYCODE_NUMPAD_RIGHT_PAREN";
    KeyCodes[KeyCodes["KEYCODE_VOLUME_MUTE"] = 164] = "KEYCODE_VOLUME_MUTE";
    KeyCodes[KeyCodes["KEYCODE_INFO"] = 165] = "KEYCODE_INFO";
    KeyCodes[KeyCodes["KEYCODE_CHANNEL_UP"] = 166] = "KEYCODE_CHANNEL_UP";
    KeyCodes[KeyCodes["KEYCODE_CHANNEL_DOWN"] = 167] = "KEYCODE_CHANNEL_DOWN";
    KeyCodes[KeyCodes["KEYCODE_ZOOM_IN"] = 168] = "KEYCODE_ZOOM_IN";
    KeyCodes[KeyCodes["KEYCODE_ZOOM_OUT"] = 169] = "KEYCODE_ZOOM_OUT";
    KeyCodes[KeyCodes["KEYCODE_TV"] = 170] = "KEYCODE_TV";
    KeyCodes[KeyCodes["KEYCODE_WINDOW"] = 171] = "KEYCODE_WINDOW";
    KeyCodes[KeyCodes["KEYCODE_GUIDE"] = 172] = "KEYCODE_GUIDE";
    KeyCodes[KeyCodes["KEYCODE_DVR"] = 173] = "KEYCODE_DVR";
    KeyCodes[KeyCodes["KEYCODE_BOOKMARK"] = 174] = "KEYCODE_BOOKMARK";
    KeyCodes[KeyCodes["KEYCODE_CAPTIONS"] = 175] = "KEYCODE_CAPTIONS";
    KeyCodes[KeyCodes["KEYCODE_SETTINGS"] = 176] = "KEYCODE_SETTINGS";
    KeyCodes[KeyCodes["KEYCODE_TV_POWER"] = 177] = "KEYCODE_TV_POWER";
    KeyCodes[KeyCodes["KEYCODE_TV_INPUT"] = 178] = "KEYCODE_TV_INPUT";
    KeyCodes[KeyCodes["KEYCODE_STB_POWER"] = 179] = "KEYCODE_STB_POWER";
    KeyCodes[KeyCodes["KEYCODE_STB_INPUT"] = 180] = "KEYCODE_STB_INPUT";
    KeyCodes[KeyCodes["KEYCODE_AVR_POWER"] = 181] = "KEYCODE_AVR_POWER";
    KeyCodes[KeyCodes["KEYCODE_AVR_INPUT"] = 182] = "KEYCODE_AVR_INPUT";
    KeyCodes[KeyCodes["KEYCODE_PROG_RED"] = 183] = "KEYCODE_PROG_RED";
    KeyCodes[KeyCodes["KEYCODE_PROG_GREEN"] = 184] = "KEYCODE_PROG_GREEN";
    KeyCodes[KeyCodes["KEYCODE_PROG_YELLOW"] = 185] = "KEYCODE_PROG_YELLOW";
    KeyCodes[KeyCodes["KEYCODE_PROG_BLUE"] = 186] = "KEYCODE_PROG_BLUE";
    KeyCodes[KeyCodes["KEYCODE_APP_SWITCH"] = 187] = "KEYCODE_APP_SWITCH";
    KeyCodes[KeyCodes["KEYCODE_BUTTON_1"] = 188] = "KEYCODE_BUTTON_1";
    KeyCodes[KeyCodes["KEYCODE_BUTTON_2"] = 189] = "KEYCODE_BUTTON_2";
    KeyCodes[KeyCodes["KEYCODE_BUTTON_3"] = 190] = "KEYCODE_BUTTON_3";
    KeyCodes[KeyCodes["KEYCODE_BUTTON_4"] = 191] = "KEYCODE_BUTTON_4";
    KeyCodes[KeyCodes["KEYCODE_BUTTON_5"] = 192] = "KEYCODE_BUTTON_5";
    KeyCodes[KeyCodes["KEYCODE_BUTTON_6"] = 193] = "KEYCODE_BUTTON_6";
    KeyCodes[KeyCodes["KEYCODE_BUTTON_7"] = 194] = "KEYCODE_BUTTON_7";
    KeyCodes[KeyCodes["KEYCODE_BUTTON_8"] = 195] = "KEYCODE_BUTTON_8";
    KeyCodes[KeyCodes["KEYCODE_BUTTON_9"] = 196] = "KEYCODE_BUTTON_9";
    KeyCodes[KeyCodes["KEYCODE_BUTTON_10"] = 197] = "KEYCODE_BUTTON_10";
    KeyCodes[KeyCodes["KEYCODE_BUTTON_11"] = 198] = "KEYCODE_BUTTON_11";
    KeyCodes[KeyCodes["KEYCODE_BUTTON_12"] = 199] = "KEYCODE_BUTTON_12";
    KeyCodes[KeyCodes["KEYCODE_BUTTON_13"] = 200] = "KEYCODE_BUTTON_13";
    KeyCodes[KeyCodes["KEYCODE_BUTTON_14"] = 201] = "KEYCODE_BUTTON_14";
    KeyCodes[KeyCodes["KEYCODE_BUTTON_15"] = 202] = "KEYCODE_BUTTON_15";
    KeyCodes[KeyCodes["KEYCODE_BUTTON_16"] = 203] = "KEYCODE_BUTTON_16";
    KeyCodes[KeyCodes["KEYCODE_LANGUAGE_SWITCH"] = 204] = "KEYCODE_LANGUAGE_SWITCH";
    KeyCodes[KeyCodes["KEYCODE_MANNER_MODE"] = 205] = "KEYCODE_MANNER_MODE";
    KeyCodes[KeyCodes["KEYCODE_3D_MODE"] = 206] = "KEYCODE_3D_MODE";
    KeyCodes[KeyCodes["KEYCODE_CONTACTS"] = 207] = "KEYCODE_CONTACTS";
    KeyCodes[KeyCodes["KEYCODE_CALENDAR"] = 208] = "KEYCODE_CALENDAR";
    KeyCodes[KeyCodes["KEYCODE_MUSIC"] = 209] = "KEYCODE_MUSIC";
    KeyCodes[KeyCodes["KEYCODE_CALCULATOR"] = 210] = "KEYCODE_CALCULATOR";
    KeyCodes[KeyCodes["KEYCODE_ZENKAKU_HANKAKU"] = 211] = "KEYCODE_ZENKAKU_HANKAKU";
    KeyCodes[KeyCodes["KEYCODE_EISU"] = 212] = "KEYCODE_EISU";
    KeyCodes[KeyCodes["KEYCODE_MUHENKAN"] = 213] = "KEYCODE_MUHENKAN";
    KeyCodes[KeyCodes["KEYCODE_HENKAN"] = 214] = "KEYCODE_HENKAN";
    KeyCodes[KeyCodes["KEYCODE_KATAKANA_HIRAGANA"] = 215] = "KEYCODE_KATAKANA_HIRAGANA";
    KeyCodes[KeyCodes["KEYCODE_YEN"] = 216] = "KEYCODE_YEN";
    KeyCodes[KeyCodes["KEYCODE_RO"] = 217] = "KEYCODE_RO";
    KeyCodes[KeyCodes["KEYCODE_KANA"] = 218] = "KEYCODE_KANA";
    KeyCodes[KeyCodes["KEYCODE_ASSIST"] = 219] = "KEYCODE_ASSIST";
    KeyCodes[KeyCodes["KEYCODE_BRIGHTNESS_DOWN"] = 220] = "KEYCODE_BRIGHTNESS_DOWN";
    KeyCodes[KeyCodes["KEYCODE_BRIGHTNESS_UP"] = 221] = "KEYCODE_BRIGHTNESS_UP";
    KeyCodes[KeyCodes["KEYCODE_MEDIA_AUDIO_TRACK"] = 222] = "KEYCODE_MEDIA_AUDIO_TRACK";
    KeyCodes[KeyCodes["KEYCODE_SLEEP"] = 223] = "KEYCODE_SLEEP";
    KeyCodes[KeyCodes["KEYCODE_WAKEUP"] = 224] = "KEYCODE_WAKEUP";
    KeyCodes[KeyCodes["KEYCODE_PAIRING"] = 225] = "KEYCODE_PAIRING";
    KeyCodes[KeyCodes["KEYCODE_MEDIA_TOP_MENU"] = 226] = "KEYCODE_MEDIA_TOP_MENU";
    KeyCodes[KeyCodes["KEYCODE_11"] = 227] = "KEYCODE_11";
    KeyCodes[KeyCodes["KEYCODE_12"] = 228] = "KEYCODE_12";
    KeyCodes[KeyCodes["KEYCODE_LAST_CHANNEL"] = 229] = "KEYCODE_LAST_CHANNEL";
    KeyCodes[KeyCodes["KEYCODE_TV_DATA_SERVICE"] = 230] = "KEYCODE_TV_DATA_SERVICE";
    KeyCodes[KeyCodes["KEYCODE_VOICE_ASSIST"] = 231] = "KEYCODE_VOICE_ASSIST";
    KeyCodes[KeyCodes["KEYCODE_TV_RADIO_SERVICE"] = 232] = "KEYCODE_TV_RADIO_SERVICE";
    KeyCodes[KeyCodes["KEYCODE_TV_TELETEXT"] = 233] = "KEYCODE_TV_TELETEXT";
    KeyCodes[KeyCodes["KEYCODE_TV_NUMBER_ENTRY"] = 234] = "KEYCODE_TV_NUMBER_ENTRY";
    KeyCodes[KeyCodes["KEYCODE_TV_TERRESTRIAL_ANALOG"] = 235] = "KEYCODE_TV_TERRESTRIAL_ANALOG";
    KeyCodes[KeyCodes["KEYCODE_TV_TERRESTRIAL_DIGITAL"] = 236] = "KEYCODE_TV_TERRESTRIAL_DIGITAL";
    KeyCodes[KeyCodes["KEYCODE_TV_SATELLITE"] = 237] = "KEYCODE_TV_SATELLITE";
    KeyCodes[KeyCodes["KEYCODE_TV_SATELLITE_BS"] = 238] = "KEYCODE_TV_SATELLITE_BS";
    KeyCodes[KeyCodes["KEYCODE_TV_SATELLITE_CS"] = 239] = "KEYCODE_TV_SATELLITE_CS";
    KeyCodes[KeyCodes["KEYCODE_TV_SATELLITE_SERVICE"] = 240] = "KEYCODE_TV_SATELLITE_SERVICE";
    KeyCodes[KeyCodes["KEYCODE_TV_NETWORK"] = 241] = "KEYCODE_TV_NETWORK";
    KeyCodes[KeyCodes["KEYCODE_TV_ANTENNA_CABLE"] = 242] = "KEYCODE_TV_ANTENNA_CABLE";
    KeyCodes[KeyCodes["KEYCODE_TV_INPUT_HDMI_1"] = 243] = "KEYCODE_TV_INPUT_HDMI_1";
    KeyCodes[KeyCodes["KEYCODE_TV_INPUT_HDMI_2"] = 244] = "KEYCODE_TV_INPUT_HDMI_2";
    KeyCodes[KeyCodes["KEYCODE_TV_INPUT_HDMI_3"] = 245] = "KEYCODE_TV_INPUT_HDMI_3";
    KeyCodes[KeyCodes["KEYCODE_TV_INPUT_HDMI_4"] = 246] = "KEYCODE_TV_INPUT_HDMI_4";
    KeyCodes[KeyCodes["KEYCODE_TV_INPUT_COMPOSITE_1"] = 247] = "KEYCODE_TV_INPUT_COMPOSITE_1";
    KeyCodes[KeyCodes["KEYCODE_TV_INPUT_COMPOSITE_2"] = 248] = "KEYCODE_TV_INPUT_COMPOSITE_2";
    KeyCodes[KeyCodes["KEYCODE_TV_INPUT_COMPONENT_1"] = 249] = "KEYCODE_TV_INPUT_COMPONENT_1";
    KeyCodes[KeyCodes["KEYCODE_TV_INPUT_COMPONENT_2"] = 250] = "KEYCODE_TV_INPUT_COMPONENT_2";
    KeyCodes[KeyCodes["KEYCODE_TV_INPUT_VGA_1"] = 251] = "KEYCODE_TV_INPUT_VGA_1";
    KeyCodes[KeyCodes["KEYCODE_TV_AUDIO_DESCRIPTION"] = 252] = "KEYCODE_TV_AUDIO_DESCRIPTION";
    KeyCodes[KeyCodes["KEYCODE_TV_AUDIO_DESCRIPTION_MIX_UP"] = 253] = "KEYCODE_TV_AUDIO_DESCRIPTION_MIX_UP";
    KeyCodes[KeyCodes["KEYCODE_TV_AUDIO_DESCRIPTION_MIX_DOWN"] = 254] = "KEYCODE_TV_AUDIO_DESCRIPTION_MIX_DOWN";
    KeyCodes[KeyCodes["KEYCODE_TV_ZOOM_MODE"] = 255] = "KEYCODE_TV_ZOOM_MODE";
    KeyCodes[KeyCodes["KEYCODE_TV_CONTENTS_MENU"] = 256] = "KEYCODE_TV_CONTENTS_MENU";
    KeyCodes[KeyCodes["KEYCODE_TV_MEDIA_CONTEXT_MENU"] = 257] = "KEYCODE_TV_MEDIA_CONTEXT_MENU";
    KeyCodes[KeyCodes["KEYCODE_TV_TIMER_PROGRAMMING"] = 258] = "KEYCODE_TV_TIMER_PROGRAMMING";
    KeyCodes[KeyCodes["KEYCODE_HELP"] = 259] = "KEYCODE_HELP";
    KeyCodes[KeyCodes["KEYCODE_NAVIGATE_PREVIOUS"] = 260] = "KEYCODE_NAVIGATE_PREVIOUS";
    KeyCodes[KeyCodes["KEYCODE_NAVIGATE_NEXT"] = 261] = "KEYCODE_NAVIGATE_NEXT";
    KeyCodes[KeyCodes["KEYCODE_NAVIGATE_IN"] = 262] = "KEYCODE_NAVIGATE_IN";
    KeyCodes[KeyCodes["KEYCODE_NAVIGATE_OUT"] = 263] = "KEYCODE_NAVIGATE_OUT";
    KeyCodes[KeyCodes["KEYCODE_STEM_PRIMARY"] = 264] = "KEYCODE_STEM_PRIMARY";
    KeyCodes[KeyCodes["KEYCODE_STEM_1"] = 265] = "KEYCODE_STEM_1";
    KeyCodes[KeyCodes["KEYCODE_STEM_2"] = 266] = "KEYCODE_STEM_2";
    KeyCodes[KeyCodes["KEYCODE_STEM_3"] = 267] = "KEYCODE_STEM_3";
    KeyCodes[KeyCodes["KEYCODE_DPAD_UP_LEFT"] = 268] = "KEYCODE_DPAD_UP_LEFT";
    KeyCodes[KeyCodes["KEYCODE_DPAD_DOWN_LEFT"] = 269] = "KEYCODE_DPAD_DOWN_LEFT";
    KeyCodes[KeyCodes["KEYCODE_DPAD_UP_RIGHT"] = 270] = "KEYCODE_DPAD_UP_RIGHT";
    KeyCodes[KeyCodes["KEYCODE_DPAD_DOWN_RIGHT"] = 271] = "KEYCODE_DPAD_DOWN_RIGHT";
    KeyCodes[KeyCodes["KEYCODE_MEDIA_SKIP_FORWARD"] = 272] = "KEYCODE_MEDIA_SKIP_FORWARD";
    KeyCodes[KeyCodes["KEYCODE_MEDIA_SKIP_BACKWARD"] = 273] = "KEYCODE_MEDIA_SKIP_BACKWARD";
    KeyCodes[KeyCodes["KEYCODE_MEDIA_STEP_FORWARD"] = 274] = "KEYCODE_MEDIA_STEP_FORWARD";
    KeyCodes[KeyCodes["KEYCODE_MEDIA_STEP_BACKWARD"] = 275] = "KEYCODE_MEDIA_STEP_BACKWARD";
    KeyCodes[KeyCodes["KEYCODE_SOFT_SLEEP"] = 276] = "KEYCODE_SOFT_SLEEP";
    KeyCodes[KeyCodes["KEYCODE_CUT"] = 277] = "KEYCODE_CUT";
    KeyCodes[KeyCodes["KEYCODE_COPY"] = 278] = "KEYCODE_COPY";
    KeyCodes[KeyCodes["KEYCODE_PASTE"] = 279] = "KEYCODE_PASTE";
    KeyCodes[KeyCodes["KEYCODE_SYSTEM_NAVIGATION_UP"] = 280] = "KEYCODE_SYSTEM_NAVIGATION_UP";
    KeyCodes[KeyCodes["KEYCODE_SYSTEM_NAVIGATION_DOWN"] = 281] = "KEYCODE_SYSTEM_NAVIGATION_DOWN";
    KeyCodes[KeyCodes["KEYCODE_SYSTEM_NAVIGATION_LEFT"] = 282] = "KEYCODE_SYSTEM_NAVIGATION_LEFT";
    KeyCodes[KeyCodes["KEYCODE_SYSTEM_NAVIGATION_RIGHT"] = 283] = "KEYCODE_SYSTEM_NAVIGATION_RIGHT";
    KeyCodes[KeyCodes["KEYCODE_ALL_APPS"] = 284] = "KEYCODE_ALL_APPS";
    KeyCodes[KeyCodes["KEYCODE_REFRESH"] = 285] = "KEYCODE_REFRESH";
    KeyCodes[KeyCodes["KEYCODE_THUMBS_UP"] = 286] = "KEYCODE_THUMBS_UP";
    KeyCodes[KeyCodes["KEYCODE_THUMBS_DOWN"] = 287] = "KEYCODE_THUMBS_DOWN";
    KeyCodes[KeyCodes["KEYCODE_PROFILE_SWITCH"] = 288] = "KEYCODE_PROFILE_SWITCH";
})(KeyCodes = exports.KeyCodes || (exports.KeyCodes = {}));
//# sourceMappingURL=keycode.js.map