"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const events_1 = require("events");
const connection_1 = __importDefault(require("./connection"));
const host_1 = require("./command/host");
const server_1 = __importDefault(require("./tcpusb/server"));
const DeviceClient_1 = __importDefault(require("./DeviceClient"));
class Client extends events_1.EventEmitter {
    constructor({ host = '127.0.0.1', port = 5037, bin = 'adb' } = { port: 5037 }) {
        super();
        this.host = host;
        this.port = port;
        this.bin = bin;
        this.options = { host, port, bin };
    }
    createTcpUsbBridge(serial, options) {
        return new server_1.default(this, serial, options);
    }
    connection() {
        const connection = new connection_1.default(this.options);
        // Reemit unhandled connection errors, so they can be handled externally.
        // If not handled at all, these will crash node.
        connection.on('error', (err) => this.emit('error', err));
        return connection.connect();
    }
    version() {
        return this.connection().then((conn) => new host_1.HostVersionCommand(conn).execute());
    }
    connect(host, port = 5555) {
        if (host.indexOf(':') !== -1) {
            const [h, portString] = host.split(':', 2);
            host = h;
            const parsed = parseInt(portString, 10);
            if (!isNaN(parsed)) {
                port = parsed;
            }
        }
        return this.connection().then((conn) => new host_1.HostConnectCommand(conn).execute(host, port));
    }
    disconnect(host, port = 5555) {
        if (host.indexOf(':') !== -1) {
            const [h, portString] = host.split(':', 2);
            host = h;
            const parsed = parseInt(portString, 10);
            if (!isNaN(parsed)) {
                port = parsed;
            }
        }
        return this.connection()
            .then((conn) => new host_1.HostDisconnectCommand(conn).execute(host, port))
            .then((deviceId) => new DeviceClient_1.default(this, deviceId));
    }
    listDevices() {
        return this.connection().then((conn) => new host_1.HostDevicesCommand(conn).execute());
    }
    listDevicesWithPaths() {
        return this.connection().then((conn) => new host_1.HostDevicesWithPathsCommand(conn).execute());
    }
    trackDevices() {
        return this.connection().then((conn) => new host_1.HostTrackDevicesCommand(conn).execute());
    }
    kill() {
        return this.connection().then((conn) => new host_1.HostKillCommand(conn).execute());
    }
    getDevice(serial) {
        return new DeviceClient_1.default(this, serial);
    }
}
exports.default = Client;
//# sourceMappingURL=client.js.map