{"name": "image-size", "version": "1.1.1", "description": "get dimensions of any image file", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist", "bin/image-size.js"], "engines": {"node": ">=16.x"}, "packageManager": "yarn@4.0.2", "bin": "bin/image-size.js", "scripts": {"lint": "eslint --ext .ts,.js bin lib specs", "format": "prettier --write lib specs", "test": "nyc mocha", "clean": "rm -rf dist docs", "generate-docs": "typedoc", "build": "tsc", "prepack": "yarn clean && yarn build"}, "keywords": ["image", "size", "dimensions", "resolution", "width", "height", "avif", "bmp", "cur", "gif", "heic", "heif", "icns", "ico", "jpeg", "png", "psd", "svg", "tga", "tiff", "webp"], "repository": "git://github.com/image-size/image-size.git", "author": "netroy <<EMAIL>> (http://netroy.in/)", "license": "MIT", "devDependencies": {"@types/chai": "4.3.11", "@types/glob": "8.1.0", "@types/mocha": "10.0.6", "@types/node": "18.19.3", "@types/sinon": "17.0.2", "@typescript-eslint/eslint-plugin": "6.16.0", "@typescript-eslint/parser": "6.16.0", "chai": "4.3.10", "eslint": "8.56.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.1.2", "glob": "10.3.10", "mocha": "10.2.0", "nyc": "15.1.0", "prettier": "3.1.1", "sinon": "17.0.1", "ts-node": "10.9.2", "typedoc": "0.25.4", "typescript": "5.3.3"}, "nyc": {"include": "lib", "exclude": "specs/*.spec.ts"}, "dependencies": {"queue": "6.0.2"}}