{"name": "addons-scanner-utils", "version": "9.10.1", "description": "Various addons related helpers to build CLIs.", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist/**/*"], "author": "Mozilla Add-ons Team", "license": "MPL-2.0", "dependencies": {"@types/yauzl": "2.10.3", "common-tags": "1.8.2", "first-chunk-stream": "3.0.0", "strip-bom-stream": "4.0.0", "upath": "2.0.1", "yauzl": "2.10.0"}, "peerDependencies": {"body-parser": "1.20.2", "express": "4.18.3", "node-fetch": "2.6.11", "safe-compare": "1.1.4"}, "peerDependenciesMeta": {"body-parser": {"optional": true}, "node-fetch": {"optional": true}, "express": {"optional": true}, "safe-compare": {"optional": true}}, "devDependencies": {"@types/common-tags": "^1.8.0", "@types/express": "4.17.21", "@types/jest": "^29.0.0", "@types/node": "^20.1.1", "@types/node-fetch": "^2.6.4", "@types/safe-compare": "^1.1.0", "@types/sinon": "^17.0.1", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^6.7.0", "body-parser": "1.20.2", "eslint": "^8.1.0", "eslint-config-amo": "^5.0.0", "eslint-plugin-amo": "^1.10.2", "express": "4.18.3", "jest": "^29.0.0", "node-fetch": "2.6.11", "prettier": "2.8.8", "pretty-quick": "^3.0.0", "rimraf": "^5.0.0", "safe-compare": "1.1.4", "sinon": "^17.0.0", "supertest": "^6.0.0", "ts-jest": "^29.0.0", "type-coverage": "^2.3.0", "typescript": "^5.0.2"}, "scripts": {"eslint": "eslint --ext ts --ext js src/", "lint": "yarn eslint", "prepack": "rimraf dist/ && tsc --outDir dist/ && rimraf -g 'dist/**/*.spec.*' 'dist/*.spec.*'", "prettier": "prettier --write '**'", "prettier-ci": "prettier --list-different '**' || (echo '\n\nThis failure means you did not run `yarn prettier-dev` before committing\n\n' && exit 1)", "prettier-dev": "pretty-quick --branch master", "test": "jest", "test-ci": "yarn test --coverage", "type-coverage": "type-coverage", "typecheck": "tsc --noEmit"}, "homepage": "https://github.com/mozilla/addons-scanner-utils", "repository": {"type": "git", "url": "git://github.com/mozilla/addons-scanner-utils.git"}, "bugs": {"url": "http://github.com/mozilla/addons-scanner-utils/issues"}, "typeCoverage": {"atLeast": 97}}