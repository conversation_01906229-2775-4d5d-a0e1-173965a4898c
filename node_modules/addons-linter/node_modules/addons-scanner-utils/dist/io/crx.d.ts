/// <reference types="node" />
/// <reference types="node" />
import defaultFs from 'fs';
import yauzl, { ZipFile } from 'yauzl';
import { IOBaseConstructorParams } from './base';
import { Xpi } from './xpi';
export declare function defaultParseCRX(buf: Buffer): Buffer;
type CrxConstructorParams = IOBaseConstructorParams & {
    fs?: typeof defaultFs;
    parseCRX?: typeof defaultParseCRX;
    zipLib?: typeof yauzl;
};
export declare class Crx extends Xpi {
    fs: typeof defaultFs;
    parseCRX: typeof defaultParseCRX;
    constructor({ filePath, stderr, fs, parseCRX, zipLib, }: CrxConstructorParams);
    open(): Promise<ZipFile>;
}
export {};
