/// <reference types="node" />
/// <reference types="node" />
import { Readable } from 'stream';
import yauzl, { Entry, ZipFile } from 'yauzl';
import { IOBaseConstructorParams, IOBase } from './base';
export type Files = {
    [filename: string]: Entry;
};
type XpiConstructorParams = IOBaseConstructorParams & {
    autoClose?: boolean;
    zipLib?: typeof yauzl;
};
export declare class Xpi extends IOBase {
    autoClose: boolean;
    files: Files;
    processed: boolean;
    zipLib: typeof yauzl;
    zipfile: ZipFile | undefined;
    constructor({ autoClose, filePath, stderr, zipLib, }: XpiConstructorParams);
    open(): Promise<ZipFile>;
    handleEntry(entry: Entry, reject: (error: Error) => void): void;
    getFiles(_onEventsSubscribed?: () => void): Promise<Files>;
    checkPath(path: string): void;
    getFileAsStream(path: string): Promise<Readable>;
    getFileAsString(path: string): Promise<string>;
    getChunkAsBuffer(path: string, chunkLength: number): Promise<Buffer>;
    close(): void;
}
export {};
