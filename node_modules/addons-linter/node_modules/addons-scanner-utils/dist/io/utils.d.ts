/// <reference types="node" />
import fs from 'fs';
import { Stderr } from '../stdio';
export declare const lstat: typeof fs.lstat.__promisify__;
export declare const readFile: typeof fs.readFile.__promisify__;
export declare const readdir: typeof fs.readdir.__promisify__;
export type WalkPromiseOptions = {
    shouldIncludePath?: (_path: string, isDirectory: boolean) => boolean;
    stderr: Stderr;
};
export declare function walkPromise(curPath: string, { shouldIncludePath, stderr }: WalkPromiseOptions): Promise<{
    [path: string]: {
        size: number;
    };
}>;
export declare function checkFileExists(filePath: string, { _lstat }?: {
    _lstat?: typeof fs.lstat.__promisify__ | undefined;
}): Promise<fs.Stats>;
