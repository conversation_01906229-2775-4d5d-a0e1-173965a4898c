/// <reference types="node" />
import { Readable } from 'stream';
import { ZipFile, RandomAccessReader } from 'yauzl';
import { Stderr, Stdout } from './stdio';
export declare const createFakeStdout: () => Stdout;
export declare const createFakeStderr: () => Stderr;
export declare const readStringFromStream: (readStream: Readable, encoding: string | undefined) => Promise<string>;
export declare const createFakeFsStats: ({ isFile, isDirectory, }?: {
    isFile?: boolean | undefined;
    isDirectory?: boolean | undefined;
}) => {
    isDirectory: () => boolean;
    isFile: () => boolean;
};
declare class FakeRandomAccessReader extends RandomAccessReader {
}
export declare const createFakeZipFile: ({ autoClose, centralDirectoryOffset, comment, decodeStrings, entryCount, fileSize, lazyEntries, reader, validateEntrySizes, }?: {
    autoClose?: boolean | undefined;
    centralDirectoryOffset?: number | undefined;
    comment?: string | undefined;
    decodeStrings?: boolean | undefined;
    entryCount?: number | undefined;
    fileSize?: number | undefined;
    lazyEntries?: boolean | undefined;
    reader?: FakeRandomAccessReader | undefined;
    validateEntrySizes?: boolean | undefined;
}) => ZipFile;
export {};
