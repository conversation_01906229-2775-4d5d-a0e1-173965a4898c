export type Stderr = {
    debug: (message: string) => void;
    error: (message: string) => void;
    info: (message: string) => void;
};
type CreateConsoleStderrParams = {
    _console?: typeof console;
    programName: string;
    verboseLevel: number;
};
export declare const createConsoleStderr: ({ _console, programName, verboseLevel, }: CreateConsoleStderrParams) => Stderr;
export type InMemoryStderr = Stderr & {
    messages: {
        debug: string[];
        error: string[];
        info: string[];
    };
};
export declare const createInMemoryStderr: () => InMemoryStderr;
export type Stdout = {
    write: (message: string) => void;
};
export declare const createConsoleStdout: ({ _console }?: {
    _console?: Console | undefined;
}) => Stdout;
export type InMemoryStdout = Stdout & {
    output: string;
};
export declare const createInMemoryStdout: () => InMemoryStdout;
export {};
