/**
 * @fileoverview Rule to flag use of with statement
 * <AUTHOR>
 */

"use strict";

//------------------------------------------------------------------------------
// Rule Definition
//------------------------------------------------------------------------------

/** @type {import('../shared/types').Rule} */
module.exports = {
    meta: {
        type: "suggestion",

        docs: {
            description: "Disallow `with` statements",
            recommended: true,
            url: "https://eslint.org/docs/latest/rules/no-with"
        },

        schema: [],

        messages: {
            unexpectedWith: "Unexpected use of 'with' statement."
        }
    },

    create(context) {

        return {
            WithStatement(node) {
                context.report({ node, messageId: "unexpectedWith" });
            }
        };

    }
};
