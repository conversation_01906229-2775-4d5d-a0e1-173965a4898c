{"name": "addons-linter", "version": "6.28.0", "description": "Mozilla Add-ons Linter", "main": "dist/addons-linter.js", "bin": {"addons-linter": "bin/addons-linter"}, "engines": {"node": ">=16.0.0"}, "browserslist": ["node >=16.0.0"], "scripts": {"build": "node scripts/build-locales && webpack --bail --stats-error-details true --color --config webpack.config.js", "eslint": "eslint bin/* scripts/* .", "extract-locales": "webpack --bail --stats-error-details true --color --config webpack.l10n.config.babel.js", "test": "jest --runInBand --watch 'tests/.*'", "test-coverage": "jest --runInBand --coverage --watch 'tests/.*'", "test-once": "jest --runInBand", "test-coverage-once": "jest --runInBand --coverage", "test-ci": "npm run test-coverage-once", "test-integration": "jest --runInBand --config=jest.integration.config.js", "test-integration-linter": "npm run test-integration -- tests/integration/addons-linter", "test-integration:production": "node tests/integration/run-as-production-env.js test-integration tests/integration/addons-linter", "lint": "npm run eslint", "prettier": "prettier --write '**'", "prettier-ci": "prettier --list-different '**' || (echo '\n\nThis failure means you did not run `npm run prettier-dev` before committing\n\n' && exit 1)", "prettier-dev": "pretty-quick --branch master", "build-rules": "scripts/build-rules && cp node_modules/github-markdown-css/github-markdown.css docs/github-markdown.css", "webext-test-functional": "scripts/webext-test-functional", "smoke-test-eslint-version-conflicts": "scripts/smoke-test-eslint-version-conflicts", "update-hashes": "scripts/dispensary > src/dispensary/hashes.txt"}, "repository": {"type": "git", "url": "git+https://github.com/mozilla/addons-linter.git"}, "author": "Mozilla Add-ons Team", "license": "MPL-2.0", "bugs": {"url": "https://github.com/mozilla/addons-linter/issues"}, "homepage": "https://github.com/mozilla/addons-linter#readme", "dependencies": {"@fluent/syntax": "0.19.0", "@mdn/browser-compat-data": "5.5.29", "addons-moz-compare": "1.3.0", "addons-scanner-utils": "9.10.1", "ajv": "8.13.0", "chalk": "4.1.2", "cheerio": "1.0.0-rc.12", "columnify": "1.6.0", "common-tags": "1.8.2", "deepmerge": "4.3.1", "eslint": "8.57.0", "eslint-plugin-no-unsanitized": "4.0.2", "eslint-visitor-keys": "4.0.0", "espree": "10.0.1", "esprima": "4.0.1", "fast-json-patch": "3.1.1", "glob": "10.4.1", "image-size": "1.1.1", "is-mergeable-object": "1.1.1", "jed": "1.1.1", "json-merge-patch": "1.0.2", "os-locale": "5.0.0", "pino": "8.20.0", "relaxed-json": "1.0.3", "semver": "7.6.2", "sha.js": "2.4.11", "source-map-support": "0.5.21", "tosource": "1.0.0", "upath": "2.0.1", "yargs": "17.7.2", "yauzl": "2.10.0"}, "devDependencies": {"@babel/cli": "7.24.6", "@babel/core": "7.24.6", "@babel/eslint-parser": "7.24.6", "@babel/plugin-proposal-class-properties": "7.18.6", "@babel/plugin-proposal-decorators": "7.24.6", "@babel/plugin-proposal-export-namespace-from": "7.18.9", "@babel/plugin-proposal-function-sent": "7.24.6", "@babel/plugin-proposal-numeric-separator": "7.18.6", "@babel/plugin-proposal-throw-expressions": "7.24.6", "@babel/preset-env": "7.24.6", "@babel/register": "7.24.6", "async": "3.2.5", "babel-core": "7.0.0-bridge.0", "babel-gettext-extractor": "github:willdu<PERSON>/babel-gettext-extractor#5.1.0", "babel-jest": "29.7.0", "babel-loader": "9.1.3", "comment-json": "4.2.3", "eslint-config-amo": "5.10.0", "eslint-plugin-amo": "1.24.0", "github-markdown-css": "5.5.1", "gunzip-maybe": "1.4.2", "hashish": "0.0.4", "jest": "29.7.0", "lodash.clonedeep": "4.5.0", "lodash.ismatchwith": "4.4.0", "markdown-it": "14.1.0", "markdown-it-anchor": "8.6.7", "markdown-it-footnote": "4.0.0", "natural-compare-lite": "1.4.0", "node-fetch": "2.6.11", "po2json": "mikeedwards/po2json#51e2310485bbe35e9e57f2eee238185459ca0eab", "prettier": "2.8.8", "pretty-quick": "3.3.1", "raw-loader": "4.0.2", "replace-in-file": "7.2.0", "shelljs": "0.8.5", "sinon": "18.0.0", "tar": "6.2.1", "tar-fs": "3.0.6", "tmp-promise": "3.0.3", "webpack": "5.91.0", "webpack-cli": "5.1.4", "webpack-node-externals": "3.0.0", "yazl": "2.5.1"}}