module.exports = { domain:"messages",
  locale_data:{ messages:{ "":{ domain:"messages",
        plural_forms:"nplurals=1; plural=0;",
        lang:"zh_CN" },
      "Validation Summary:":[ "验证概要：" ],
      Code:[ "代码" ],
      Message:[ "消息" ],
      Description:[ "描述" ],
      File:[ "文件" ],
      Line:[ "行" ],
      Column:[ "列" ],
      "Invalid manifest version range requested: --min-manifest-version (currently set to %(minManifestVersion)s) should not be greater than --max-manifest-version (currently set to %(maxManifestVersion)s).":[ "请求的清单版本范围无效：--min-manifest-version（当前设置 %(minManifestVersion)s）不应大于 --max-manifest-version（已设为 %(maxManifestVersion)s）。" ],
      "Your FTL is not valid.":[ "您的 FTL 文件无效。" ],
      "Your FTL file could not be parsed.":[ "您的 FTL 文件无法解析。" ],
      "Remote scripts are not allowed as per the Add-on Policies.":[ "依照附加组件政策，不允许执行远程脚本。" ],
      "Please include all scripts in the add-on. For more information, refer to https://mzl.la/2uEOkYp.":[ "请在附加组件中包含所有脚本。更多详细信息，请参阅 https://mzl.la/2uEOkYp。" ],
      "Inline scripts blocked by default":[ "内联脚本默认被屏蔽" ],
      "Default CSP rules prevent inline JavaScript from running (https://mzl.la/2pn32nd).":[ "默认 CSP 规则阻止内联 JavaScript 运行 (https://mzl.la/2pn32nd)。" ],
      "Unadvised 3rd-party JS library":[ "不建议使用的第三方 JS 库" ],
      "Your add-on uses a JavaScript library we do not recommend. Read more: https://bit.ly/1TRIyZY":[ "您的插件使用我们不推荐的 JavaScript 库。阅读更多：https://bit.ly/1TRIyZY" ],
      "Known JS library detected":[ "检测到已知 JS 库" ],
      "JavaScript libraries are discouraged for simple add-ons, but are generally accepted.":[ "我们不鼓励将 JavaScript 库用于简单的附加组件，但这样通常也会被接受。" ],
      "Due to both security and performance concerns, this may not be set using dynamic values which have not been adequately sanitized. This can lead to security issues or fairly serious performance degradation.":[ "出于安全和性能方面的考虑，可能无法使用未充分清理的动态值来设置它。这可能会导致安全问题或相当严重的性能下降。" ],
      "{{api}} is not supported":[ "不支持 {{api}}" ],
      "This API has not been implemented by Firefox.":[ "Firefox 尚未实现此 API。" ],
      "\"{{api}}\" has been removed in Manifest Version 3 (`manifest_version` property)":[ "“{{api}}”已从 Manifest V3 中删除（“manifest_version”属性）" ],
      "{{api}} is deprecated":[ "{{api}} 已弃用" ],
      "This API has been deprecated by Firefox.":[ "Firefox 已弃用此 API。" ],
      "Content script file could not be found.":[ "无法找到内容脚本文件。" ],
      "\"{{api}}\" is deprecated or unimplemented":[ "“{{api}}”已弃用或尚未实现" ],
      "Content script file could not be found":[ "无法找到内容脚本文件" ],
      "\"%(api)s\" can cause issues when loaded temporarily":[ "临时加载“%(api)s”可能会导致问题" ],
      "This API can cause issues when loaded temporarily using about:debugging in Firefox unless you specify \"browser_specific_settings.gecko.id\" in the manifest. Please see: https://mzl.la/2hizK4a for more.":[ "在 Firefox 中使用 about:debugging 临时加载此 API 时可能会导致问题，除非您在清单中指定“browser_specific_settings.gecko.id”。请参阅：https://mzl.la/2hizK4a 了解更多信息。" ],
      "{{api}} is not supported in Firefox version {{minVersion}}":[ "Firefox 版本 {{minVersion}} 不支持 {{api}}" ],
      "This API is not implemented by the given minimum Firefox version":[ "给定的最低 Firefox 版本尚未实现此 API" ],
      "{{api}} is not supported in Firefox for Android version {{minVersion}}":[ "适用于 Android 的 Firefox 版本 {{minVersion}} 不支持 {{api}}" ],
      "This API is not implemented by the given minimum Firefox for Android version":[ "给定的最低适用于 Android 的 Firefox 版本尚未实现此 API" ],
      "Content script file name should not be empty.":[ "内容脚本文件名不应为空。" ],
      "Content script file name should not be empty":[ "内容脚本文件名不应为空" ],
      "\"%(method)s\" called with a non-literal uri":[ "使用非文字 uri 调用了“%(method)s”" ],
      "Calling \"%(method)s\" with variable parameters can result in potential security vulnerabilities if the variable contains a remote URI. Consider using 'window.open' with the 'chrome=no' flag.":[ "如果变量包含远程 URI，则使用可变参数调用“%(method)s”可能会导致潜在的安全漏洞。请考虑使用带有“chrome=no”标志的“window.open”。" ],
      "\"%(method)s\" called with non-local URI":[ "使用非本地 URI 调用了 “%(method)s”" ],
      "Calling \"%(method)s\" with a non-local URI will result in the dialog being opened with chrome privileges.":[ "使用非本地 URI 调用“%(method)s”将导致使用 chrome 权限打开对话框。" ],
      "JavaScript syntax error":[ "JavaScript 语法错误" ],
      "There is a JavaScript syntax error in your code, which might be related to some experimental JavaScript features that aren't an official part of the language specification and therefore not supported yet. The validation cannot continue on this file.":[ "您的代码中存在 JavaScript 语法错误，这可能与某些实验性 JavaScript 功能有关，这些功能不是语言规范的官方标准，因此尚不受支持。无法继续对此文件进行验证。" ],
      "Evaluation of strings as code can lead to security vulnerabilities and performance issues, even in the most innocuous of circumstances. Please avoid using `eval` and the `Function` constructor when at all possible.":[ "将字符串解析成代码可能会导致安全漏洞和性能问题，即使在最无害的情况下也是如此。请尽可能避免使用“eval”和“Function”构造函数。" ],
      "setTimeout, setInterval and execScript functions should be called only with function expressions as their first argument":[ "setTimeout、setInterval 和 execScript 函数只能以函数表达式作为它们的第一个参数来调用" ],
      "Unexpected global passed as an argument":[ "意外的将 global 传递为一个参数" ],
      "Passing a global as an argument is not recommended. Please make this a var instead.":[ "不建议将全局变量作为参数传递。请改为将其设为 var。" ],
      "Use of document.write strongly discouraged.":[ "强烈不建议使用 document.write。" ],
      "document.write will fail in many circumstances when used in extensions, and has potentially severe security repercussions when used improperly. Therefore, it should not be used.":[ "在扩展中使用 document.write 在许多情况下会失败，而且在使用不当时可能会产生严重的安全影响。因此，不应使用它。" ],
      "Banned 3rd-party JS library":[ "被禁止的第三方 JS 库" ],
      "Your add-on uses a JavaScript library we consider unsafe. Read more: https://bit.ly/1TRIyZY":[ "您的附加组件使用我们认为不安全的 JavaScript 库。阅读更多：https://bit.ly/1TRIyZY" ],
      "Your JSON contains block comments.":[ "您的 JSON 包含块注释。" ],
      "Only line comments (comments beginning with \"//\") are allowed in JSON files. Please remove block comments (comments beginning with \"/*\")":[ "JSON 文件中只允许行注释（以“//”开头的注释）。请移除块注释（以“/*”开头的注释）" ],
      "Duplicate keys are not allowed in JSON files.":[ "JSON 文件中不允许有重复的键。" ],
      "Duplicate key found in JSON file.":[ "在 JSON 文件中发现重复键值。" ],
      "Your JSON is not valid.":[ "您的 JSON 无效。" ],
      "Your JSON file could not be parsed.":[ "无法解析您的 JSON 文件。" ],
      "Reserved filename found.":[ "发现保留的文件名。" ],
      "Files whose names are reserved have been found in the add-on. Please refrain from using them and rename your files.":[ "在附加组件中发现使用了保留名称的文件。请不要使用它们，并重命名您的文件。" ],
      "The package is invalid. It may contain entries using invalid characters, as an example using '\\' as a path separator is not allowed in Firefox. Try to recreate your add-on package (ZIP) and make sure all entries are using '/' as the path separator.":[ "软件包不合规。它可能包含有无效字符，例如，在 Firefox 中不允许使用“\\”作为路径分隔符。请尝试重新创建您的附加组件包 (ZIP) 并确保所有条目都使用“/”作为路径分隔符。" ],
      "We were unable to decompress the zip file.":[ "我们无法解压缩该 zip 文件。" ],
      "manifest.json was not found":[ "未找到 manifest.json" ],
      "No manifest.json was found at the root of the extension. The package file must be a ZIP of the extension's files themselves, not of the containing directory. See: https://mzl.la/2r2McKv for more on packaging.":[ "在扩展的根目录下未找到 manifest.json。软件包文件必须是扩展文件本身的 ZIP，而不是包含其目录的 ZIP。有关软件包的更多信息，请参阅：https://mzl.la/2r2McKv。" ],
      "File is too large to parse.":[ "文件太大而未解析。" ],
      "This file is not binary and is too large to parse. Files larger than %(maxFileSizeToParseMB)sMB will not be parsed. Consider moving large lists of data out of JavaScript files and into JSON files, or splitting very large files into smaller ones.":[ "此文件不是二进制文件，并且太大而无法解析。大于 %(maxFileSizeToParseMB)sMB 的文件将不会被解析。请考虑将大型数据列表从 JavaScript 文件中移出并放入 JSON 文件中，或者将非常大的文件拆分为较小的文件。" ],
      "Hidden file flagged":[ "隐藏文件已被标记" ],
      "Hidden files complicate the review process and can contain sensitive information about the system that generated the add-on. Please modify the packaging process so that these files aren't included.":[ "隐藏文件使审查过程复杂化，并且可能包含有关生成附加组件系统的敏感信息。请修改打包过程，使之不包含这些文件。" ],
      "Package contains duplicate entries":[ "包包含重复的项" ],
      "Flagged filename found":[ "找到已标记的文件名" ],
      "Files were found that are either unnecessary or have been included unintentionally. They should be removed.":[ "软件包中有不需要的文件或无意中包含的文件。请删除之。" ],
      "The package contains multiple entries with the same name. This practice has been banned. Try unzipping and re-zipping your add-on package and try again.":[ "该软件包包含多个相同名称的条目。这种做法已被禁止。请尝试解压缩并重新压缩您的附加组件包，然后重试。" ],
      "Flagged file extensions found":[ "找到已标记的文件扩展名" ],
      "Flagged file type found":[ "找到已标记的文件类型" ],
      "Files whose names end with flagged extensions have been found in the add-on. The extension of these files are flagged because they usually identify binary components. Please see https://bit.ly/review-policy for more information on the binary content review process.":[ "在附加组件中发现了以带标记的扩展名为结尾的文件。这些文件的扩展名被标记，因为它们通常标识二进制组件。有关二进制内容审查流程的更多信息，请参阅 https://bit.ly/review-policy。" ],
      "Package already signed":[ "包已被签名" ],
      "Add-ons which are already signed will be re-signed when published on AMO. This will replace any existing signatures on the add-on.":[ "已签名的附加组件将在 AMO 上发布时重新签名。这将替换附加组件上的已有签名。" ],
      "Firefox add-ons are not allowed to run coin miners.":[ "Firefox 附加组件不得进行挖矿。" ],
      "We do not allow coinminer scripts to be run inside WebExtensions. See https://github.com/mozilla/addons-linter/issues/1643 for more details.":[ "我们不允许 coinminer 脚本在 WebExtensions 中运行。有关详细信息，请参阅 https://github.com/mozilla/addons-linter/issues/1643。" ],
      "String name is reserved for a predefined message":[ "字符串名称已为预定义消息保留" ],
      "String names starting with @@ get translated to built-in constants (https://mzl.la/2BL9ZjE).":[ "以 @@ 开头的字符串名称被转换为内置常量 (https://mzl.la/2BL9ZjE)。" ],
      "String name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2Eztyi5).":[ "字符串名称应仅包含字母数字字符、_ 和 @ (https://mzl.la/2Eztyi5)。" ],
      "Placeholder for message is missing":[ "消息占位符缺失" ],
      "A placeholder used in the message is not defined.":[ "消息中已使用的一个占位符未定义。" ],
      "Placeholder name contains invalid characters":[ "占位符名称包含无效字符" ],
      "Placeholder name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2ExbYez).":[ "占位符名称应仅包含字母数字字符、_ 和 @ (https://mzl.la/2ExbYez)。" ],
      "Placeholder is missing the content property":[ "占位符缺少内容属性" ],
      "A placeholder needs a content property defining the replacement of it (https://mzl.la/2DT1MQd)":[ "占位符需要一个内容属性来定义它的替换 (https://mzl.la/2DT1MQd)" ],
      "Translation string is missing the message property":[ "翻译字符串缺少消息属性" ],
      "No \"message\" message property is set for a string (https://mzl.la/2DSBTjA).":[ "一个字符串没有设置 \"message\" 消息属性 (https://mzl.la/2DSBTjA)。" ],
      "The field is required.":[ "该字段必填。" ],
      "See https://mzl.la/1ZOhoEN (MDN Docs) for more information.":[ "详细信息请参阅 https://mzl.la/1ZOhoEN (MDN 文档)。" ],
      "The permission type is unsupported.":[ "不支持该权限类型。" ],
      "See https://mzl.la/1R1n1t0 (MDN Docs) for more information.":[ "详细信息请参阅 https://mzl.la/1R1n1t0 (MDN 文档)。" ],
      "See https://mzl.la/2Qn0fWC (MDN Docs) for more information.":[ "详细信息请参阅 https://mzl.la/2Qn0fWC （MDN 文档）。" ],
      "See https://mzl.la/3Woeqv4 (MDN Docs) for more information.":[ "更多详情，请参阅 https://mzl.la/3Woeqv4（MDN 文档）。" ],
      "Unknown permission.":[ "未知权限。" ],
      "%(instancePath)s: the following privileged permissions are only allowed in privileged extensions: %(privilegedPermissions)s.":[ "%(instancePath)s：以下权限仅在特权扩展中允许：%(privilegedPermissions)s。" ],
      "Invalid host permission.":[ "主机权限无效。" ],
      "Invalid install origin.":[ "安装来源无效。" ],
      "Invalid install origin. A valid origin has - only - a scheme, hostname and optional port. See https://mzl.la/3TEbqbE (MDN Docs) for more information.":[ "安装来源无效。有效的来源只有一个方案、主机名和可选端口。更多详细信息，请参阅 https://mzl.la/3TEbqbE（MDN 文档）。" ],
      "The field is invalid.":[ "该字段无效。" ],
      "\"manifest_version\" in the manifest.json is not a valid value":[ "manifest.json 中的 \"manifest_version\" 不是有效的值" ],
      "See https://mzl.la/20PenXl (MDN Docs) for more information.":[ "详细信息请参阅 https://mzl.la/20PenXl (MDN文档)。" ],
      "\"%(property)s\" allows remote code execution in manifest.json":[ "\"%(property)s\" 允许在 manifest.json 中远程执行代码" ],
      "A custom \"%(property)s\" needs additional review.":[ "自定义“%(property)s”需要额外审核。" ],
      "\"%(property)s\" allows 'eval', which has strong security and performance implications.":[ "“%(property)s”允许“eval”，这意味着它有很强的安全和性能影响。" ],
      "In most cases the same result can be achieved differently, therefore it is generally prohibited":[ "在大多数情况下，此结果可以用不同的方法可以得到，因此它通常被禁止" ],
      "The \"name\" property must be a string with no leading/trailing whitespaces.":[ "“name”属性必须是一个前后都没有空格的字符串。" ],
      "See http://mzl.la/1STmr48 (MDN Docs) for more information.":[ "详细信息请参阅 http://mzl.la/1STmr48 (MDN 文档)。" ],
      "\"update_url\" is not allowed.":[ "不应含有“update_url”。" ],
      "\"applications.gecko.update_url\" or \"browser_specific_settings.gecko.update_url\" are not allowed for Mozilla-hosted add-ons.":[ "Mozilla 托管的附加组件不允许使用“applications.gecko.update_url”或“browser_specific_settings.gecko.update_url”。" ],
      "The \"update_url\" property is not used by Firefox.":[ "Firefox 不使用 \"update_url\" 属性。" ],
      "The \"update_url\" is not used by Firefox in the root of a manifest; your add-on will be updated via the Add-ons site and not your \"update_url\". See: https://mzl.la/25zqk4O":[ "Firefox 不在清单的根目录中使用“update_url”；您的附加组件将通过附加组件站点更新，而不是您的“update_url”。请参阅：https://mzl.la/25zqk4O" ],
      "\"strict_max_version\" not required.":[ "\"strict_max_version\" 不是必需的。" ],
      "\"strict_max_version\" shouldn't be used unless the add-on is expected not to work with future versions of Firefox.":[ "不应使用“strict_max_version”，除非预计该附加组件不能与未来版本的 Firefox 一起使用。" ],
      "Manifest Version 3 is not fully supported on Firefox for Android.":[ "Android 版 Firefox 尚未完全支持 Manifest 版本 3。" ],
      "No \"%(property)s\" property found in manifest.json":[ "在 manifest.json 中找不到“%(property)s”属性" ],
      "\"%(property)s\" is required":[ "\"%(property)s\" 是必需的" ],
      "An icon defined in the manifest could not be found in the package.":[ "manifest 中定义的一个图标未在包中找到。" ],
      "Icon could not be found at \"%(path)s\".":[ "无法在“%(path)s”找到图标。" ],
      "A background script defined in the manifest could not be found.":[ "未找到清单文件中定义的后台脚本。" ],
      "A background page defined in the manifest could not be found.":[ "未找到清单文件中定义的背景页面。" ],
      "Background script could not be found at \"%(path)s\".":[ "无法在“%(path)s”找到后台脚本。" ],
      "Background page could not be found at \"%(path)s\".":[ "无法在“%(path)s”找到后台页面。" ],
      "A content script defined in the manifest could not be found.":[ "未找到清单文件中定义的内容脚本。" ],
      "A content script css file defined in the manifest could not be found.":[ "未找到清单文件中定义的内容脚本 CSS 文件。" ],
      "Content script defined in the manifest could not be found at \"%(path)s\".":[ "manifest 中定义的一个内容脚本无法在“%(path)s”找到。" ],
      "Content script css file defined in the manifest could not be found at \"%(path)s\".":[ "manifest 中定义的一个内容脚本 css 文件无法在“%(path)s”找到。" ],
      "A dictionary file defined in the manifest could not be found.":[ "未找到清单文件中定义的字典文件。" ],
      "Dictionary file defined in the manifest could not be found at \"%(path)s\".":[ "未在“%(path)s”找到清单文件中定义的字典文件。" ],
      "The manifest contains multiple dictionaries.":[ "清单文件中包含多个字典。" ],
      "Multiple dictionaries were defined in the manifest, which is unsupported.":[ "不支持在清单文件中定义多个字典。" ],
      "The manifest contains a dictionaries object, but it is empty.":[ "清单文件中包含字典对象，但是为空。" ],
      "A dictionaries object was defined in the manifest, but it was empty.":[ "清单文件中定义了字典对象，但是为空。" ],
      "The manifest contains a dictionary but no id property.":[ "清单文件中包含字典对象，但无 ID 属性。" ],
      "A dictionary was found in the manifest, but there was no id set.":[ "清单文件中找到字典，但未设置 ID。" ],
      "Please refer to https://github.com/mozilla-extensions/xpi-manifest to learn more about privileged extensions and signing.":[ "请参阅 https://github.com/mozilla-extensions/xpi-manifest 以了解有关特权扩展和签名的更多信息。" ],
      "Forbidden content found in add-on.":[ "附加组件含有违禁内容。" ],
      "This add-on contains forbidden content.":[ "此附加组件包含被禁止的内容。" ],
      "Icons must be square.":[ "图标必须为正方形。" ],
      "Icon at \"%(path)s\" must be square.":[ "位于“%(path)s”的图标必须为正方形。" ],
      "The size of the icon does not match the manifest.":[ "图标的大小与清单不匹配。" ],
      "Expected icon at \"%(path)s\" to be %(expected)d pixels wide but was %(actual)d.":[ "“%(path)s”处的预期图标像素宽为 %(expected)d，但实际为 %(actual)d。" ],
      "\"%(fieldName)s\" is ignored for non-privileged add-ons.":[ "对非特权附加组件将忽略“%(fieldName)s”。" ],
      "Corrupt image file":[ "图像损坏" ],
      "Expected icon file at \"%(path)s\" is corrupted":[ "应该位于“%(path)s”的图标文件已损坏" ],
      "This property has been deprecated.":[ "此属性已弃用。" ],
      "This theme LWT alias has been removed in Firefox 70.":[ "此主题的 LWT 别名已在 Firefox 70 中移除。" ],
      "See https://mzl.la/2T11Lkc (MDN Docs) for more information.":[ "详细信息请参阅 https://mzl.la/2T11Lkc (MDN 文档)。" ],
      "Theme image for \"%(type)s\" could not be found at \"%(path)s\"":[ "未在“%(path)s”找到“%(type)s”的主题图像" ],
      "\"%(fieldName)s\" manifest field is only used for privileged and temporarily installed extensions.":[ "“%(fieldName)s”清单字段仅用于特权和临时安装的扩展。" ],
      "Corrupted theme image file":[ "损坏的主题图像文件" ],
      "Theme image file at \"%(path)s\" is corrupted":[ "位于“%(path)s”的主题图像文件已损坏" ],
      "Theme image file has an unsupported file extension":[ "该主题图像文件使用不支持的扩展名" ],
      "Theme image file at \"%(path)s\" has an unsupported file extension":[ "位于“%(path)s”的主题图像文件使用不支持的扩展名" ],
      "Theme image file has an unsupported mime type":[ "该主题图像使用不支持的 MIME 类型" ],
      "Theme image file at \"%(path)s\" has the unsupported mime type \"%(mime)s\"":[ "位于“%(path)s”的主题图像文件使用不支持的 MIME 类型：%(mime)s" ],
      "Theme image file mime type does not match its file extension":[ "该主题图像文件的 MIME 类型与其扩展名不匹配" ],
      "Theme image file extension at \"%(path)s\" does not match its actual mime type \"%(mime)s\"":[ "位于“%(path)s”的主题图像文件的 MIME 类型（%(mime)s）与其扩展名不匹配" ],
      "The \"default_locale\" is missing localizations.":[ "“default_locale”缺少本地化内容。" ],
      "The \"default_locale\" value is specified in the manifest, but no matching \"messages.json\" in the \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "清单中指定有 “default_locale” 值，但“_locales”目录中不存在匹配的“messages.json”。请参阅：https://mzl.la/2hjcaEE" ],
      "The \"default_locale\" is missing but \"_locales\" exist.":[ "“_locales”存在但缺少“default_locale”。" ],
      "The \"default_locale\" value is not specifed in the manifest, but a \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "清单中未指定“default_locale”值，但存在“_locales”目录。请参阅：https://mzl.la/2hjcaEE" ],
      "Unsupported image extension":[ "不支持的图像扩展名" ],
      "Icons should be one of JPG/JPEG, WebP, GIF, PNG or SVG.":[ "图标应该为 JPG/JPEG、WebP、GIFF、PNG 或 SVG 文件。" ],
      "\"applications\" property overridden by \"browser_specific_settings\" property":[ "“applications”属性已被“browser_specific_settings”属性覆盖" ],
      "The \"applications\" property is being ignored because it is superseded by the \"browser_specific_settings\" property which is also defined in your manifest. Consider removing applications.":[ "“applications”属性被忽略，因为它已被清单中定义的“browser_specific_settings”属性取代。请考虑删除应用程序。" ],
      "Empty language directory":[ "空的语言目录" ],
      "messages.json file missing in \"%(path)s\"":[ "“%(path)s”中缺少 messages.json 文件" ],
      "Manifest key not supported by the specified minimum Firefox version":[ "指定的最低 Firefox 版本不支持清单文件标识值" ],
      "\"strict_min_version\" requires Firefox %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "“strict_min_version”要求 Firefox %(minVersion)s，它是在版本 %(versionAdded)s 引入对“%(key)s”的支持之前发布的。" ],
      "\"%(fieldName)s\" is not supported in manifest versions %(versionRange)s.":[ "清单版本 %(versionRange)s 不支持 “%(fieldName)s”。" ],
      "Permission not supported by the specified minimum Firefox version":[ "指定的最低 Firefox 版本不支持权限" ],
      "\"%(fieldName)s\" is not supported.":[ "不支持“%(fieldName)s”。" ],
      "Manifest key not supported by the specified minimum Firefox for Android version":[ "指定的最低适用于 Android 的 Firefox 版本，不支持清单文件标识值" ],
      "\"strict_min_version\" requires Firefox for Android %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "“strict_min_version”要求 Firefox for Android %(minVersion)s，它是在版本 %(versionAdded)s 引入对“%(key)s”的支持之前发布的。" ],
      "Permission not supported by the specified minimum Firefox for Android version":[ "指定的最低适用于 Android 的 Firefox 版本，不支持权限" ],
      "Linking to \"addons.mozilla.org\" is not allowed":[ "不允许链接到“addons.mozilla.org”" ],
      "Links directing to \"addons.mozilla.org\" are not allowed to be used for homepage":[ "不允许将指向“addons.mozilla.org”的链接用于主页" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above":[ "“%(permission)s”权限要求将“strict_min_version”设置为“%(minFirefoxVersion)s”或更高" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above. Please update your manifest.json version to specify a minimum Firefox version.":[ "“%(permission)s”权限要求将“strict_min_version”设置为“%(minFirefoxVersion)s”或更高版本。请更新您的 manifest.json 版本以指定最低 Firefox 版本。" ],
      "The extension ID is required in Manifest Version 3 and above.":[ "Manifest Version 3 及以上版本中要求扩展 ID。" ],
      "See https://mzl.la/3PLZYdo for more information.":[ "请访问 https://mzl.la/3PLZYdo 以了解更多信息。" ],
      "%(instancePath)s: Privileged extensions should declare privileged permissions.":[ "%(instancePath)s：特权扩展应该声明特权权限。" ],
      "This extension does not declare any privileged permission. It does not need to be signed with the privileged certificate. Please upload it directly to https://addons.mozilla.org/.":[ "此扩展不声明任何特权权限。它不需要使用特权证书进行签名。请直接上传至 https://addons.mozilla.org/。" ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for privileged extensions.":[ "%(instancePath)s：特权扩展需要“mozillaAddons”权限。" ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for extensions that include privileged manifest fields.":[ "%(instancePath)s：包含特权清单字段的扩展需要“mozillaAddons”权限。" ],
      "%(instancePath)s: privileged manifest fields are only allowed in privileged extensions.":[ "%(instancePath)s：特权清单字段仅允许在特权扩展中使用。" ],
      "This extension does not include the \"mozillaAddons\" permission, which is required for privileged extensions.":[ "此扩展不包括特权扩展所需的“mozillaAddons”权限。" ],
      "Cannot use actions in hidden add-ons.":[ "无法在隐藏的附加组件中使用操作。" ],
      "The hidden and browser_action/page_action (or action in Manifest Version 3 and above) properties are mutually exclusive.":[ "hidden 和 browser_action/page_action（或清单版本 3 及更高版本中的操作）属性是互斥的。" ],
      "Use \"browser_specific_settings\" instead of \"applications\".":[ "请使用“browser_specific_settings”，不要使用“applications”。" ],
      "The \"applications\" property in the manifest is deprecated and will no longer be accepted in Manifest Version 3 and above.":[ "清单中的“应用程序”属性已弃用，清单版本 3 及更高版本将不再接受该属性。" ],
      "\"applications\" is no longer allowed in Manifest Version 3 and above.":[ "清单版本 3 及更高版本中不再允许使用“应用程序”。" ],
      "The \"applications\" property in the manifest is no longer allowed in Manifest Version 3 and above. Use \"browser_specific_settings\" instead.":[ "清单版本 3 及更高版本不再允许清单中的“应用程序”属性。请改用“browser_specific_settings”。" ],
      "The version string should be simplified because it won't be compatible with Manifest Version 3 and above.":[ "请简化版本字符串，因为它与 Manifest Version 3 及更高版本不兼容。" ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros will no longer be allowed. Letters will no longer be allowed either. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "版本应该是一个由 1 到 4 个数字组成的字符串，用点分隔。每个数字最多应包含 9 位数字，并且不再允许使用前导数字 0，也将不再允许使用字母。更多详细信息，请参阅 https://mzl.la/3h3mCRu（MDN 文档）。" ],
      "The version string should be simplified.":[ "版本字符串应该被简化。" ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros are not allowed. Letters are no longer allowed. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "版本应该是一个由 1 到 4 个数字组成的字符串，用点分隔。每个数字最多应包含 9 位数字，并且不允许使用前导数字 0，也不再允许使用字母。有关详细信息，请参阅 https://mzl.la/3h3mCRu（MDN 文档）。" ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported in manifest versions %(versionRange)s.":[ "/%(fieldName)s：清单版本 %(versionRange)s 不支持“%(permissionName)s”。" ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported.":[ "/%(fieldName)s：不支持“%(permissionName)s”。" ] } } }