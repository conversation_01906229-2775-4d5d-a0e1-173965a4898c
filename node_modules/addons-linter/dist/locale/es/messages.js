module.exports = { domain:"messages",
  locale_data:{ messages:{ "":{ domain:"messages",
        plural_forms:"nplurals=2; plural=(n != 1);",
        lang:"es" },
      "Validation Summary:":[ "Resumen de la validación:" ],
      Code:[ "Código" ],
      Message:[ "Mensaje" ],
      Description:[ "Descripción" ],
      File:[ "Archivo" ],
      Line:[ "Línea" ],
      Column:[ "Columna" ],
      "Invalid manifest version range requested: --min-manifest-version (currently set to %(minManifestVersion)s) should not be greater than --max-manifest-version (currently set to %(maxManifestVersion)s).":[ "" ],
      "Your FTL is not valid.":[ "Tu FTL no es válido." ],
      "Your FTL file could not be parsed.":[ "Tu archivo FTL no se ha podido interpretar." ],
      "Remote scripts are not allowed as per the Add-on Policies.":[ "Los scripts remotos no están permitidos por las políticas de complementos." ],
      "Please include all scripts in the add-on. For more information, refer to https://mzl.la/2uEOkYp.":[ "" ],
      "Inline scripts blocked by default":[ "Los scripts incrustados están bloqueados por defecto" ],
      "Default CSP rules prevent inline JavaScript from running (https://mzl.la/2pn32nd).":[ "" ],
      "Unadvised 3rd-party JS library":[ "Biblioteca JS de terceros desaconsejada" ],
      "Your add-on uses a JavaScript library we do not recommend. Read more: https://bit.ly/1TRIyZY":[ "" ],
      "Known JS library detected":[ "Detectada biblioteca JS conocida" ],
      "JavaScript libraries are discouraged for simple add-ons, but are generally accepted.":[ "" ],
      "Due to both security and performance concerns, this may not be set using dynamic values which have not been adequately sanitized. This can lead to security issues or fairly serious performance degradation.":[ "" ],
      "{{api}} is not supported":[ "{{api}} no compatible" ],
      "This API has not been implemented by Firefox.":[ "Firefox no ha implementado esta API." ],
      "\"{{api}}\" has been removed in Manifest Version 3 (`manifest_version` property)":[ "" ],
      "Content script file could not be found.":[ "No se puede encontrar el archivo script de contenido." ],
      "\"{{api}}\" is deprecated or unimplemented":[ "" ],
      "Content script file could not be found":[ "No se puede encontrar el archivo script de contenido" ],
      "\"%(api)s\" can cause issues when loaded temporarily":[ "" ],
      "This API can cause issues when loaded temporarily using about:debugging in Firefox unless you specify \"browser_specific_settings.gecko.id\" in the manifest. Please see: https://mzl.la/2hizK4a for more.":[ "" ],
      "{{api}} is not supported in Firefox version {{minVersion}}":[ "{{api}} no es compatible con la versión {{minVersion}} de Firefox" ],
      "This API is not implemented by the given minimum Firefox version":[ "La versión mínima de Firefox no ha implementado esta API" ],
      "{{api}} is not supported in Firefox for Android version {{minVersion}}":[ "{{api}} no es compatible con la versión {{minVersion}} de Firefox para Android" ],
      "This API is not implemented by the given minimum Firefox for Android version":[ "La versión mínima de Firefox para Android no ha implementado esta API" ],
      "Content script file name should not be empty.":[ "El nombre del archivo script de contenido no puede estar vacío." ],
      "Content script file name should not be empty":[ "El nombre del archivo script de contenido no puede estar vacío" ],
      "\"%(method)s\" called with a non-literal uri":[ "" ],
      "Calling \"%(method)s\" with variable parameters can result in potential security vulnerabilities if the variable contains a remote URI. Consider using 'window.open' with the 'chrome=no' flag.":[ "" ],
      "\"%(method)s\" called with non-local URI":[ "" ],
      "Calling \"%(method)s\" with a non-local URI will result in the dialog being opened with chrome privileges.":[ "" ],
      "JavaScript syntax error":[ "error de sintaxis de JavaScript" ],
      "There is a JavaScript syntax error in your code, which might be related to some experimental JavaScript features that aren't an official part of the language specification and therefore not supported yet. The validation cannot continue on this file.":[ "" ],
      "Evaluation of strings as code can lead to security vulnerabilities and performance issues, even in the most innocuous of circumstances. Please avoid using `eval` and the `Function` constructor when at all possible.":[ "" ],
      "setTimeout, setInterval and execScript functions should be called only with function expressions as their first argument":[ "" ],
      "Unexpected global passed as an argument":[ "Variable global inesperada pasada como argumento" ],
      "Passing a global as an argument is not recommended. Please make this a var instead.":[ "" ],
      "Use of document.write strongly discouraged.":[ "Se desaconseja el uso de document.write." ],
      "document.write will fail in many circumstances when used in extensions, and has potentially severe security repercussions when used improperly. Therefore, it should not be used.":[ "" ],
      "Banned 3rd-party JS library":[ "Biblioteca JS de terceros prohibida" ],
      "Your add-on uses a JavaScript library we consider unsafe. Read more: https://bit.ly/1TRIyZY":[ "" ],
      "Your JSON contains block comments.":[ "Tu archivo JSON incluye comentarios multilínea." ],
      "Only line comments (comments beginning with \"//\") are allowed in JSON files. Please remove block comments (comments beginning with \"/*\")":[ "" ],
      "Duplicate keys are not allowed in JSON files.":[ "No se pueden duplicar claves en los archivos JSON." ],
      "Duplicate key found in JSON file.":[ "" ],
      "Your JSON is not valid.":[ "Tu JSON no es válido." ],
      "Your JSON file could not be parsed.":[ "No se puede analizar tu archivo JSON." ],
      "Reserved filename found.":[ "Se encontró un nombre de archivo reservado." ],
      "Files whose names are reserved have been found in the add-on. Please refrain from using them and rename your files.":[ "" ],
      "The package is invalid. It may contain entries using invalid characters, as an example using '\\' as a path separator is not allowed in Firefox. Try to recreate your add-on package (ZIP) and make sure all entries are using '/' as the path separator.":[ "" ],
      "We were unable to decompress the zip file.":[ "No pudimos descomprimir el archivo zip." ],
      "manifest.json was not found":[ "no se encontró manifest.json" ],
      "No manifest.json was found at the root of the extension. The package file must be a ZIP of the extension's files themselves, not of the containing directory. See: https://mzl.la/2r2McKv for more on packaging.":[ "" ],
      "File is too large to parse.":[ "Archivo demasiado largo para su análisis." ],
      "This file is not binary and is too large to parse. Files larger than %(maxFileSizeToParseMB)sMB will not be parsed. Consider moving large lists of data out of JavaScript files and into JSON files, or splitting very large files into smaller ones.":[ "" ],
      "Hidden file flagged":[ "Se destacó un archivo oculto" ],
      "Hidden files complicate the review process and can contain sensitive information about the system that generated the add-on. Please modify the packaging process so that these files aren't included.":[ "" ],
      "Package contains duplicate entries":[ "El paquete incluye algunas entradas duplicadas" ],
      "Flagged filename found":[ "Se encontró un nombre de archivo destacado" ],
      "Files were found that are either unnecessary or have been included unintentionally. They should be removed.":[ "" ],
      "The package contains multiple entries with the same name. This practice has been banned. Try unzipping and re-zipping your add-on package and try again.":[ "" ],
      "Flagged file extensions found":[ "Se encontraron extensiones de archivo destacadas" ],
      "Flagged file type found":[ "Se encontró un tipo de archivo destacado" ],
      "Files whose names end with flagged extensions have been found in the add-on. The extension of these files are flagged because they usually identify binary components. Please see https://bit.ly/review-policy for more information on the binary content review process.":[ "" ],
      "Package already signed":[ "Paquete ya firmado" ],
      "Add-ons which are already signed will be re-signed when published on AMO. This will replace any existing signatures on the add-on.":[ "" ],
      "Firefox add-ons are not allowed to run coin miners.":[ "Los complementos de Firefox no tienen permitido minar criptomonedas." ],
      "We do not allow coinminer scripts to be run inside WebExtensions. See https://github.com/mozilla/addons-linter/issues/1643 for more details.":[ "" ],
      "String name is reserved for a predefined message":[ "El nombre de la cadena está reservado para un mensaje predefinido" ],
      "String names starting with @@ get translated to built-in constants (https://mzl.la/2BL9ZjE).":[ "" ],
      "String name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2Eztyi5).":[ "" ],
      "Placeholder for message is missing":[ "Falta el marcador de posición para mensajes" ],
      "A placeholder used in the message is not defined.":[ "No se definió el marcador de posición que se utilizó en el mensaje." ],
      "Placeholder name contains invalid characters":[ "El nombre del marcador de posición contiene caracteres no válidos" ],
      "Placeholder name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2ExbYez).":[ "" ],
      "Placeholder is missing the content property":[ "Al marcador de posición le faltan propiedades de contenido" ],
      "A placeholder needs a content property defining the replacement of it (https://mzl.la/2DT1MQd)":[ "" ],
      "Translation string is missing the message property":[ "Falta la propiedad del mensaje en la cadena de traducción" ],
      "No \"message\" message property is set for a string (https://mzl.la/2DSBTjA).":[ "No se configuró ninguna propiedad de mensaje en la cadena (https://mzl.la/2DSBTjA)." ],
      "The field is required.":[ "Este campo es obligatorio." ],
      "See https://mzl.la/1ZOhoEN (MDN Docs) for more information.":[ "Para más información, consulta https://mzl.la/1ZOhoEN (documento de MDN)." ],
      "The permission type is unsupported.":[ "No se admite el tipo de permiso." ],
      "See https://mzl.la/1R1n1t0 (MDN Docs) for more information.":[ "Para más información, consulta https://mzl.la/1R1n1t0 (documento de MDN)." ],
      "See https://mzl.la/3Woeqv4 (MDN Docs) for more information.":[ "" ],
      "Unknown permission.":[ "Permiso desconocido." ],
      "%(instancePath)s: the following privileged permissions are only allowed in privileged extensions: %(privilegedPermissions)s.":[ "" ],
      "Invalid install origin. A valid origin has - only - a scheme, hostname and optional port. See https://mzl.la/3TEbqbE (MDN Docs) for more information.":[ "" ],
      "The field is invalid.":[ "El campo no es válido." ],
      "\"manifest_version\" in the manifest.json is not a valid value":[ "\"manifest_version\" en el archivo manifest.json no tiene un valor válido" ],
      "See https://mzl.la/20PenXl (MDN Docs) for more information.":[ "Para más información, consulta https://mzl.la/20PenXl (documento de MDN)." ],
      "\"%(property)s\" allows remote code execution in manifest.json":[ "" ],
      "A custom \"%(property)s\" needs additional review.":[ "" ],
      "\"%(property)s\" allows 'eval', which has strong security and performance implications.":[ "" ],
      "In most cases the same result can be achieved differently, therefore it is generally prohibited":[ "" ],
      "The \"name\" property must be a string with no leading/trailing whitespaces.":[ "" ],
      "See http://mzl.la/1STmr48 (MDN Docs) for more information.":[ "Para más información, consulta http://mzl.la/1STmr48 (documento de MDN)." ],
      "\"update_url\" is not allowed.":[ "no se permite \"update_url\"." ],
      "\"applications.gecko.update_url\" or \"browser_specific_settings.gecko.update_url\" are not allowed for Mozilla-hosted add-ons.":[ "" ],
      "The \"update_url\" property is not used by Firefox.":[ "Firefox no utiliza la propiedad \"update_url\"." ],
      "The \"update_url\" is not used by Firefox in the root of a manifest; your add-on will be updated via the Add-ons site and not your \"update_url\". See: https://mzl.la/25zqk4O":[ "" ],
      "\"strict_max_version\" not required.":[ "\"strict_max_version\" no es obligatoria." ],
      "\"strict_max_version\" shouldn't be used unless the add-on is expected not to work with future versions of Firefox.":[ "" ],
      "Manifest Version 3 is not fully supported on Firefox for Android.":[ "" ],
      "No \"%(property)s\" property found in manifest.json":[ "" ],
      "\"%(property)s\" is required":[ "" ],
      "An icon defined in the manifest could not be found in the package.":[ "No se pudo encontrar en el paquete un icono definido en el manifiesto." ],
      "Icon could not be found at \"%(path)s\".":[ "No se pudo encontrar el icono en \"%(path)s\"." ],
      "A background script defined in the manifest could not be found.":[ "No se pudo encontrar el script de fondo definido en el manifiesto." ],
      "A background page defined in the manifest could not be found.":[ "No se pudo encontrar la página de fondo definida en el manifiesto." ],
      "Background script could not be found at \"%(path)s\".":[ "No se pudo encontrar el script de segundo plano en \"%(path)s\"." ],
      "Background page could not be found at \"%(path)s\".":[ "No se pudo encontrar la página en segundo plano en \"%(path)s\"." ],
      "A content script defined in the manifest could not be found.":[ "No se pudo encontrar el script de contenido definido en el manifiesto." ],
      "A content script css file defined in the manifest could not be found.":[ "No se pudo encontrar el archivo css del script de contenido definido en el manifiesto." ],
      "Content script defined in the manifest could not be found at \"%(path)s\".":[ "El script de contenido definido en el manifiesto no se pudo encontrar en \"%(path)s\"." ],
      "Content script css file defined in the manifest could not be found at \"%(path)s\".":[ "No se encontró en \"%(path)s\" el archivo css de script de contenido definido en el manifiesto." ],
      "A dictionary file defined in the manifest could not be found.":[ "No se pudo encontrar el archivo de diccionario definido en el manifiesto." ],
      "Dictionary file defined in the manifest could not be found at \"%(path)s\".":[ "No se pudo encontrar el archivo de diccionario definido en el manifiesto en la ubicación \"%(path)s\"." ],
      "The manifest contains multiple dictionaries.":[ "El manifesto incluye varios diccionarios." ],
      "Multiple dictionaries were defined in the manifest, which is unsupported.":[ "Se definieron varios diccionarios en el manifiesto y eso es incompatible." ],
      "The manifest contains a dictionaries object, but it is empty.":[ "El manifesto incluye un objeto en forma de diccionario, pero está vacío." ],
      "A dictionaries object was defined in the manifest, but it was empty.":[ "Se definió un objeto en forma de diccionario en el manifesto, pero estaba vacío." ],
      "The manifest contains a dictionary but no id property.":[ "El manifesto incluye un diccionario, pero ninguna propiedad de identificación." ],
      "A dictionary was found in the manifest, but there was no id set.":[ "Se encontró un diccionario en el manifiesto, pero no se estableció ninguna identificación." ],
      "Please refer to https://github.com/mozilla-extensions/xpi-manifest to learn more about privileged extensions and signing.":[ "" ],
      "Forbidden content found in add-on.":[ "Se encontró contenido prohibido en el complemento." ],
      "This add-on contains forbidden content.":[ "Este complemento incluye contenido prohibido." ],
      "Icons must be square.":[ "Los iconos deben ser cuadrados." ],
      "Icon at \"%(path)s\" must be square.":[ "El icono en \"%(path)s\" debe ser cuadrado." ],
      "The size of the icon does not match the manifest.":[ "El tamaño del icono no coincide con el del manifiesto." ],
      "Expected icon at \"%(path)s\" to be %(expected)d pixels wide but was %(actual)d.":[ "" ],
      "\"%(fieldName)s\" is ignored for non-privileged add-ons.":[ "" ],
      "Corrupt image file":[ "Archivo de imagen corrupto" ],
      "Expected icon file at \"%(path)s\" is corrupted":[ "El archivo de icono previsto en \"%(path)s\" está corrupto" ],
      "This property has been deprecated.":[ "" ],
      "This theme LWT alias has been removed in Firefox 70.":[ "Este alias del tema LWS se ha eliminado en Firefox 70." ],
      "See https://mzl.la/2T11Lkc (MDN Docs) for more information.":[ "Para más información, consulta https://mzl.la/2T11Lkc (MDN Docs)." ],
      "Theme image for \"%(type)s\" could not be found at \"%(path)s\"":[ "No se pudo encontrar la imagen del tema \"%(type)s\" en \"%(path)s\"" ],
      "\"%(fieldName)s\" manifest field is only used for privileged and temporarily installed extensions.":[ "" ],
      "Corrupted theme image file":[ "Archivo de imagen de tema corrupto" ],
      "Theme image file at \"%(path)s\" is corrupted":[ "El archivo de imagen de tema en \"%(path)s\" está corrupto" ],
      "Theme image file has an unsupported file extension":[ "El archivo de imagen del tema tiene una extensión de archivo no compatible" ],
      "Theme image file at \"%(path)s\" has an unsupported file extension":[ "El archivo de imagen del tema ubicado en \"%(path)s\" tiene una extensión de archivo no compatible" ],
      "Theme image file has an unsupported mime type":[ "El archivo de imagen del tema tiene un tipo MIME no compatible" ],
      "Theme image file at \"%(path)s\" has the unsupported mime type \"%(mime)s\"":[ "El archivo de imagen del tema ubicado en \"%(path)s\" tiene un tipo \"%(mime)s\" no compatible" ],
      "Theme image file mime type does not match its file extension":[ "El tipo de mime del archivo de imagen del tema no coincide con su extensión de archivo" ],
      "Theme image file extension at \"%(path)s\" does not match its actual mime type \"%(mime)s\"":[ "La extensión del archivo de imagen del tema en \"%(path)s\" no coincide con su tipo mimereal \"%(mime)s\"" ],
      "The \"default_locale\" is missing localizations.":[ "Faltan traducciones en \"default_locale\"." ],
      "The \"default_locale\" value is specified in the manifest, but no matching \"messages.json\" in the \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "" ],
      "The \"default_locale\" is missing but \"_locales\" exist.":[ "Falta \"default_locale\", pero sí existe \"_locales\"." ],
      "The \"default_locale\" value is not specifed in the manifest, but a \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "" ],
      "Unsupported image extension":[ "Extensión de imagen no admitida" ],
      "Icons should be one of JPG/JPEG, WebP, GIF, PNG or SVG.":[ "Los iconos deberían ser JPG/JPEG, WebP, GIF, PNG o SVG." ],
      "\"applications\" property overridden by \"browser_specific_settings\" property":[ "" ],
      "The \"applications\" property is being ignored because it is superseded by the \"browser_specific_settings\" property which is also defined in your manifest. Consider removing applications.":[ "" ],
      "Empty language directory":[ "Directorio de idioma vacío" ],
      "messages.json file missing in \"%(path)s\"":[ "No existe el archivo messages.json en \"%(path)s\"" ],
      "Manifest key not supported by the specified minimum Firefox version":[ "La versión mínima especificada por Firefox no admite la clave de manifiesto" ],
      "\"strict_min_version\" requires Firefox %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "" ],
      "\"%(fieldName)s\" is not supported in manifest versions %(versionRange)s.":[ "" ],
      "Permission not supported by the specified minimum Firefox version":[ "La versión mínima especificada por Firefox no admite el permiso" ],
      "\"%(fieldName)s\" is not supported.":[ "" ],
      "Manifest key not supported by the specified minimum Firefox for Android version":[ "La versión mínima especificada por Firefox para Android no admite la clave de manifiesto" ],
      "\"strict_min_version\" requires Firefox for Android %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "" ],
      "Permission not supported by the specified minimum Firefox for Android version":[ "La versión mínima especificada por Firefox para Android no admite el permiso" ],
      "Linking to \"addons.mozilla.org\" is not allowed":[ "" ],
      "Links directing to \"addons.mozilla.org\" are not allowed to be used for homepage":[ "" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above":[ "" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above. Please update your manifest.json version to specify a minimum Firefox version.":[ "" ],
      "The extension ID is required in Manifest Version 3 and above.":[ "" ],
      "See https://mzl.la/3PLZYdo for more information.":[ "" ],
      "%(instancePath)s: Privileged extensions should declare privileged permissions.":[ "" ],
      "This extension does not declare any privileged permission. It does not need to be signed with the privileged certificate. Please upload it directly to https://addons.mozilla.org/.":[ "" ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for privileged extensions.":[ "" ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for extensions that include privileged manifest fields.":[ "" ],
      "%(instancePath)s: privileged manifest fields are only allowed in privileged extensions.":[ "" ],
      "This extension does not include the \"mozillaAddons\" permission, which is required for privileged extensions.":[ "" ],
      "Cannot use actions in hidden add-ons.":[ "" ],
      "The hidden and browser_action/page_action (or action in Manifest Version 3 and above) properties are mutually exclusive.":[ "" ],
      "Use \"browser_specific_settings\" instead of \"applications\".":[ "" ],
      "The \"applications\" property in the manifest is deprecated and will no longer be accepted in Manifest Version 3 and above.":[ "" ],
      "\"applications\" is no longer allowed in Manifest Version 3 and above.":[ "" ],
      "The \"applications\" property in the manifest is no longer allowed in Manifest Version 3 and above. Use \"browser_specific_settings\" instead.":[ "" ],
      "The version string should be simplified because it won't be compatible with Manifest Version 3 and above.":[ "" ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros will no longer be allowed. Letters will no longer be allowed either. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "" ],
      "The version string should be simplified.":[ "" ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros are not allowed. Letters are no longer allowed. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "" ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported in manifest versions %(versionRange)s.":[ "" ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported.":[ "" ] } } }