module.exports = { domain:"messages",
  locale_data:{ messages:{ "":{ domain:"messages",
        plural_forms:"nplurals=1; plural=0;",
        lang:"zh_TW" },
      "Validation Summary:":[ "驗證摘要:" ],
      Code:[ "程式碼" ],
      Message:[ "訊息" ],
      Description:[ "描述" ],
      File:[ "檔案" ],
      Line:[ "行" ],
      Column:[ "欄" ],
      "Invalid manifest version range requested: --min-manifest-version (currently set to %(minManifestVersion)s) should not be greater than --max-manifest-version (currently set to %(maxManifestVersion)s).":[ "安裝資訊檔中要求的版本範圍無效：--min-manifest-version（目前設定為 %(minManifestVersion)s）不應大於 --max-manifest-version（目前設定為 %(maxManifestVersion)s）。" ],
      "Your FTL is not valid.":[ "您的 FTL 無效。" ],
      "Your FTL file could not be parsed.":[ "無法剖析您的 FTL 檔案。" ],
      "Remote scripts are not allowed as per the Add-on Policies.":[ "依照附加元件政策，不允許遠端指令碼。" ],
      "Please include all scripts in the add-on. For more information, refer to https://mzl.la/2uEOkYp.":[ "請包含附加元件當中使用到的所有指令碼。若需更多資訊，請參考 https://mzl.la/2uEOkYp。" ],
      "Inline scripts blocked by default":[ "預設封鎖行內指令碼" ],
      "Default CSP rules prevent inline JavaScript from running (https://mzl.la/2pn32nd).":[ "預設 CSP 規則阻止行內 JavaScript 執行（https://mzl.la/2pn32nd）。" ],
      "Unadvised 3rd-party JS library":[ "不建議使用的第三方 JS 程式庫" ],
      "Your add-on uses a JavaScript library we do not recommend. Read more: https://bit.ly/1TRIyZY":[ "您的附加元件使用我們不建議使用的 JavaScript 程式庫。更多資訊：https://bit.ly/1TRIyZY" ],
      "Known JS library detected":[ "偵測到已知的 JS 程式庫" ],
      "JavaScript libraries are discouraged for simple add-ons, but are generally accepted.":[ "不建議在簡單的附加元件中使用 JavaScript 程式庫，但一般來說都會接受。" ],
      "Due to both security and performance concerns, this may not be set using dynamic values which have not been adequately sanitized. This can lead to security issues or fairly serious performance degradation.":[ "由於安全性與效能考量，不可設定未充分檢查過的動態值。未充分檢查可能會導致安全性問題，或嚴重拖慢效能。" ],
      "{{api}} is not supported":[ "不支援 {{api}}" ],
      "This API has not been implemented by Firefox.":[ "Firefox 尚未實作此 API。" ],
      "\"{{api}}\" has been removed in Manifest Version 3 (`manifest_version` property)":[ "Manifest Version 3 當中已移除「{{api}}」（`manifest_version` 屬性）" ],
      "{{api}} is deprecated":[ "{{api}} 已棄用" ],
      "This API has been deprecated by Firefox.":[ "Firefox 已棄用此 API。" ],
      "Content script file could not be found.":[ "找不到內容指令碼檔案。" ],
      "\"{{api}}\" is deprecated or unimplemented":[ "「{{api}}」已棄用，或尚未實作" ],
      "Content script file could not be found":[ "找不到內容指令碼檔案" ],
      "\"%(api)s\" can cause issues when loaded temporarily":[ "暫時載入附加元件時，「%(api)s」可能會造成問題" ],
      "This API can cause issues when loaded temporarily using about:debugging in Firefox unless you specify \"browser_specific_settings.gecko.id\" in the manifest. Please see: https://mzl.la/2hizK4a for more.":[ "除非您在安裝資訊中指定「browser_specific_settings.gecko.id」，否則當您在 Firefox 的 about:debuggung 暫時載入附加元件時，此 API 可能會造成問題。若需更多資訊，請參考： https://mzl.la/2hizK4a。" ],
      "{{api}} is not supported in Firefox version {{minVersion}}":[ "Firefox 版本 {{minVersion}} 不支援 {{api}}" ],
      "This API is not implemented by the given minimum Firefox version":[ "指定的最小 Firefox 版本尚未實作此 API。" ],
      "{{api}} is not supported in Firefox for Android version {{minVersion}}":[ "Firefox for Android 版本 {{minVersion}} 不支援 {{api}}" ],
      "This API is not implemented by the given minimum Firefox for Android version":[ "指定的最小 Firefox for Android 版本尚未實做此 API。" ],
      "Content script file name should not be empty.":[ "內容指令碼檔案名稱不應為空白。" ],
      "Content script file name should not be empty":[ "內容指令碼檔案名稱不應為空白" ],
      "\"%(method)s\" called with a non-literal uri":[ "使用非 literal URI 呼叫「%(method)s」" ],
      "Calling \"%(method)s\" with variable parameters can result in potential security vulnerabilities if the variable contains a remote URI. Consider using 'window.open' with the 'chrome=no' flag.":[ "使用變數參數呼叫「%(method)s」時，可能會因變數內含有遠端 URI 造成安全性漏洞，請考慮改用「window.open」並包含「chrome=no」旗標。" ],
      "\"%(method)s\" called with non-local URI":[ "使用非本機 URI 呼叫「%(method)s」" ],
      "Calling \"%(method)s\" with a non-local URI will result in the dialog being opened with chrome privileges.":[ "使用非本機 URI 呼叫「%(method)s」將造成開啟的對話框含有 chrome 權限。" ],
      "JavaScript syntax error":[ "JavaScript 語法錯誤" ],
      "There is a JavaScript syntax error in your code, which might be related to some experimental JavaScript features that aren't an official part of the language specification and therefore not supported yet. The validation cannot continue on this file.":[ "您的程式碼中包含 JavaScript 語法錯誤，可能是因為有些實驗性的 JavaScript 功能尚未成為語言的正式標準，尚不受支援所導致。無法繼續驗證此檔案。" ],
      "Evaluation of strings as code can lead to security vulnerabilities and performance issues, even in the most innocuous of circumstances. Please avoid using `eval` and the `Function` constructor when at all possible.":[ "將字串作為程式碼來求值，就算是在最小的情況下也可能造成安全性漏洞或效能問題。請盡可能避免使用`eval` 與 `Function` 建構子。" ],
      "setTimeout, setInterval and execScript functions should be called only with function expressions as their first argument":[ "呼叫 setTimeout、setInterval 及 execScript 函數時，應只將函數表達式放在它們的第一個參數" ],
      "Unexpected global passed as an argument":[ "未預期的全域變數被作為引數傳遞" ],
      "Passing a global as an argument is not recommended. Please make this a var instead.":[ "不建議將全域變數作為參數傳入，請將其設定為 var 變數。" ],
      "Use of document.write strongly discouraged.":[ "非常不建議使用 document.write。" ],
      "document.write will fail in many circumstances when used in extensions, and has potentially severe security repercussions when used improperly. Therefore, it should not be used.":[ "大部分情況下，於擴充套件中使用 document.write 時，都會失敗，且在誤用時可能會有嚴重的安全性問題，因此不該使用此方法。" ],
      "Banned 3rd-party JS library":[ "被禁止的第三方 JS 程式庫" ],
      "Your add-on uses a JavaScript library we consider unsafe. Read more: https://bit.ly/1TRIyZY":[ "您的附加元件使用我們認為不安全的 JavaScript 程式庫。更多資訊：https://bit.ly/1TRIyZY" ],
      "Your JSON contains block comments.":[ "您的 JSON 包含區塊注解。" ],
      "Only line comments (comments beginning with \"//\") are allowed in JSON files. Please remove block comments (comments beginning with \"/*\")":[ "JSON 檔案僅支援單行註解（每一行以「//」開頭的註解）。請移除區塊註解（以「/*」開頭的註解）" ],
      "Duplicate keys are not allowed in JSON files.":[ "JSON 檔案中不允許使用重複的鍵值。" ],
      "Duplicate key found in JSON file.":[ "在 JSON 檔案中發現重複的 key。" ],
      "Your JSON is not valid.":[ "您的 JSON 無效。" ],
      "Your JSON file could not be parsed.":[ "無法剖析您的 JSON 檔案。" ],
      "Reserved filename found.":[ "找到保留的檔案名稱。" ],
      "Files whose names are reserved have been found in the add-on. Please refrain from using them and rename your files.":[ "在附加元件中找到保留名稱的檔案。請勿使用該名稱，並更名這些檔案。" ],
      "The package is invalid. It may contain entries using invalid characters, as an example using '\\' as a path separator is not allowed in Firefox. Try to recreate your add-on package (ZIP) and make sure all entries are using '/' as the path separator.":[ "封裝無效。可能是因為當中包含無效字元的項目，例如 Firefox 不允許使用「\\」作為路徑分隔符號。請嘗試重新建立您的附加元件封裝（ZIP 檔）並確保所有項目都使用「/」作為路徑分隔符號。" ],
      "We were unable to decompress the zip file.":[ "無法解壓縮 zip 檔案。" ],
      "manifest.json was not found":[ "找不到 manifest.json" ],
      "No manifest.json was found at the root of the extension. The package file must be a ZIP of the extension's files themselves, not of the containing directory. See: https://mzl.la/2r2McKv for more on packaging.":[ "未在擴充套件根目錄找到 manifest.json。封裝檔案必須是直接包含套件檔案的 ZIP 壓縮檔，最上層不含目錄。若需有關套件的更多資訊，請參考 https://mzl.la/2r2McKv。" ],
      "File is too large to parse.":[ "檔案太大無法剖析。" ],
      "This file is not binary and is too large to parse. Files larger than %(maxFileSizeToParseMB)sMB will not be parsed. Consider moving large lists of data out of JavaScript files and into JSON files, or splitting very large files into smaller ones.":[ "這個檔案不是二進位檔案，且太大無法剖析。不會剖析超過 %(maxFileSizeToParseMB)sMB 的檔案。請考慮將大型資料清單從 JavaScript 檔案移動到 JSON 檔案，或分割成多個小檔案。" ],
      "Hidden file flagged":[ "標示了隱藏檔案" ],
      "Hidden files complicate the review process and can contain sensitive information about the system that generated the add-on. Please modify the packaging process so that these files aren't included.":[ "隱藏檔案將造成審核過程變得複雜，有時也會包含產生附加元件的電腦的敏感資訊。請修改您的封裝檔案產製過程，讓這些檔案不包含在封裝檔中。" ],
      "Package contains duplicate entries":[ "封包包含重複項目" ],
      "Flagged filename found":[ "找到被標記的檔案名稱" ],
      "Files were found that are either unnecessary or have been included unintentionally. They should be removed.":[ "找到非必要的檔案，或是意外加入的檔案，應該移除。" ],
      "The package contains multiple entries with the same name. This practice has been banned. Try unzipping and re-zipping your add-on package and try again.":[ "封裝檔案包含多個相同名稱的項目內容，已封鎖這個做法。請嘗試解壓縮並重新壓縮附加元件封裝後再試一次。" ],
      "Flagged file extensions found":[ "找到被標記的檔案副檔名" ],
      "Flagged file type found":[ "找到被標記的檔案類型" ],
      "Files whose names end with flagged extensions have been found in the add-on. The extension of these files are flagged because they usually identify binary components. Please see https://bit.ly/review-policy for more information on the binary content review process.":[ "在附加元件中找到名稱結尾為被標記的擴充套件檔案。之所以會標記這些檔案的副檔名，是因為它們通常是二進位元件。若需有關二進位內容審核程序的更多資訊，請參考 https://bit.ly/review-policy。" ],
      "Package already signed":[ "封包已簽名" ],
      "Add-ons which are already signed will be re-signed when published on AMO. This will replace any existing signatures on the add-on.":[ "已簽署過的附加元件，在 AMO 上架時會被重新簽署。將取代附加元件中所有已有的簽章。" ],
      "Firefox add-ons are not allowed to run coin miners.":[ "Firefox 附加元件中不允許進行挖礦。" ],
      "We do not allow coinminer scripts to be run inside WebExtensions. See https://github.com/mozilla/addons-linter/issues/1643 for more details.":[ "我們不允許在 WebExtension 中包含採礦指令碼。若需更多資訊，請參考 https://github.com/mozilla/addons-linter/issues/1643。" ],
      "String name is reserved for a predefined message":[ "字串名稱是為預設訊息保留的名稱" ],
      "String names starting with @@ get translated to built-in constants (https://mzl.la/2BL9ZjE).":[ "以 @@ 開頭的字串名稱，將被轉換為內建常數（https://mzl.la/2BL9ZjE）。" ],
      "String name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2Eztyi5).":[ "字串名稱應只包含字母、數字、_ 及 @（https://mzl.la/2Eztyi5）。" ],
      "Placeholder for message is missing":[ "缺少訊息的預留位置" ],
      "A placeholder used in the message is not defined.":[ "未定義訊息中使用的預留位置。" ],
      "Placeholder name contains invalid characters":[ "預留位置名稱包含無效字元" ],
      "Placeholder name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2ExbYez).":[ "Placeholder 名稱應只包含字母、數字、_ 及 @（https://mzl.la/2ExbYez）。" ],
      "Placeholder is missing the content property":[ "缺少預留位置的內容屬性" ],
      "A placeholder needs a content property defining the replacement of it (https://mzl.la/2DT1MQd)":[ "Placeholder 需要有一個 content 屬性定義如何被取代（https://mzl.la/2TT1MDd）" ],
      "Translation string is missing the message property":[ "翻譯字串缺少訊息屬性" ],
      "No \"message\" message property is set for a string (https://mzl.la/2DSBTjA).":[ "字串中，未為訊息設定「message」屬性（https://mzl.la/2DSBTjA）。" ],
      "The field is required.":[ "此欄位是必要的。" ],
      "See https://mzl.la/1ZOhoEN (MDN Docs) for more information.":[ "請參考 https://mzl.la/1ZOhoEN（MDN 文件）上的更多資訊。" ],
      "The permission type is unsupported.":[ "不受支援的權限類型。" ],
      "See https://mzl.la/1R1n1t0 (MDN Docs) for more information.":[ "請參考 https://mzl.la/1R1n1t0（MDN 文件）上的更多資訊。" ],
      "See https://mzl.la/2Qn0fWC (MDN Docs) for more information.":[ "請參考 https://mzl.la/2Qn0fWC（MDN 文件）上的更多資訊。" ],
      "See https://mzl.la/3Woeqv4 (MDN Docs) for more information.":[ "請參考 https://mzl.la/3Woeqv4（MDN 文件）上的更多資訊。" ],
      "Unknown permission.":[ "權限未知。" ],
      "%(instancePath)s: the following privileged permissions are only allowed in privileged extensions: %(privilegedPermissions)s.":[ "%(instancePath)s：下列特殊權限僅能在特殊擴充套件中使用：%(privilegedPermissions)s。" ],
      "Invalid host permission.":[ "host 權限無效。" ],
      "Invalid install origin.":[ "安裝來源無效。" ],
      "Invalid install origin. A valid origin has - only - a scheme, hostname and optional port. See https://mzl.la/3TEbqbE (MDN Docs) for more information.":[ "安裝來源無效。有效的來源「僅」能有一組 scheme、主機名稱，與通訊埠（選填）。若需更多資訊，請參考 https://mzl.la/3TEbqbE（MDN 文件）" ],
      "The field is invalid.":[ "欄位無效。" ],
      "\"manifest_version\" in the manifest.json is not a valid value":[ "manifest.json 當中的「manifest_version」不是有效值" ],
      "See https://mzl.la/20PenXl (MDN Docs) for more information.":[ "請參考 https://mzl.la/20PenXl（MDN 文件）上的更多資訊。" ],
      "\"%(property)s\" allows remote code execution in manifest.json":[ "manifest.json 當中的「%(property)s」允許遠端執行程式碼" ],
      "A custom \"%(property)s\" needs additional review.":[ "自訂的「%(property)s」需要額外審核。" ],
      "\"%(property)s\" allows 'eval', which has strong security and performance implications.":[ "「%(property)s」允許「eval」，有重大的安全性與效能風險。" ],
      "In most cases the same result can be achieved differently, therefore it is generally prohibited":[ "大部分情況下都可以用其他替代方式達到此目的，所以一般來說都禁止使用" ],
      "The \"name\" property must be a string with no leading/trailing whitespaces.":[ "「name」屬性必須是前後沒有空白的字串。" ],
      "See http://mzl.la/1STmr48 (MDN Docs) for more information.":[ "請參考 http://mzl.la/1STmr48（MDN 文件）上的更多資訊。" ],
      "\"update_url\" is not allowed.":[ "不允許「update_url」。" ],
      "\"applications.gecko.update_url\" or \"browser_specific_settings.gecko.update_url\" are not allowed for Mozilla-hosted add-ons.":[ "由 Mozilla 代管的附加元件不允許使用「applications.gecko.update_url」與「browser_specific_settings.gecko.update_url」。" ],
      "The \"update_url\" property is not used by Firefox.":[ "Firefox 不使用「update_url」屬性。" ],
      "The \"update_url\" is not used by Firefox in the root of a manifest; your add-on will be updated via the Add-ons site and not your \"update_url\". See: https://mzl.la/25zqk4O":[ "Firefox 不使用安裝資訊檔根層級的「update_url」；您的附加元件將透過附加元件網站而非「update_url」更新。詳見：https://mzl.la/25zqk4O" ],
      "\"strict_max_version\" not required.":[ "「strict_max_version」不是必要的。" ],
      "\"strict_max_version\" shouldn't be used unless the add-on is expected not to work with future versions of Firefox.":[ "除非本來就預期此附加元件與未來版本的 Firefox 不相容，否則不應使用「strict_max_version」。" ],
      "Manifest Version 3 is not fully supported on Firefox for Android.":[ "Firefox for Android 未完整支援 Manifest Version 3。" ],
      "No \"%(property)s\" property found in manifest.json":[ "在 manifest.json 中找不到「%(property)s」屬性" ],
      "\"%(property)s\" is required":[ "「%(property)s」必填" ],
      "An icon defined in the manifest could not be found in the package.":[ "無法在封裝檔案找到安裝資訊中定義的圖示。" ],
      "Icon could not be found at \"%(path)s\".":[ "無法在「%(path)s」找到圖示。" ],
      "A background script defined in the manifest could not be found.":[ "找不到安裝資訊檔中定義的背景指令碼。" ],
      "A background page defined in the manifest could not be found.":[ "找不到安裝資訊檔中定義的背景頁面。" ],
      "Background script could not be found at \"%(path)s\".":[ "無法在「%(path)s」找到背景指令碼。" ],
      "Background page could not be found at \"%(path)s\".":[ "無法在「%(path)s」找到背景頁面。" ],
      "A content script defined in the manifest could not be found.":[ "找不到安裝資訊檔中定義的內容指令碼。" ],
      "A content script css file defined in the manifest could not be found.":[ "找不到安裝資訊檔中定義的內容指令碼 CSS 檔案。" ],
      "Content script defined in the manifest could not be found at \"%(path)s\".":[ "無法在「%(path)s」找到安裝資訊檔中定義的內容指令碼。" ],
      "Content script css file defined in the manifest could not be found at \"%(path)s\".":[ "無法在「%(path)s」找到安裝資訊檔中定義的內容指令碼 CSS 檔案。" ],
      "A dictionary file defined in the manifest could not be found.":[ "找不到安裝資訊檔中定義的字典檔案。" ],
      "Dictionary file defined in the manifest could not be found at \"%(path)s\".":[ "無法在「%(path)s」找到安裝資訊檔中定義的字典檔案。" ],
      "The manifest contains multiple dictionaries.":[ "安裝資訊檔中包含多個字典。" ],
      "Multiple dictionaries were defined in the manifest, which is unsupported.":[ "不支援在安裝資訊檔中定義多個字典。" ],
      "The manifest contains a dictionaries object, but it is empty.":[ "安裝資訊檔中包含字典物件，但是是空白的。" ],
      "A dictionaries object was defined in the manifest, but it was empty.":[ "安裝資訊檔中定義了字典物件，但是是空白的。" ],
      "The manifest contains a dictionary but no id property.":[ "安裝資訊中包含字典，但沒有 ID 屬性。" ],
      "A dictionary was found in the manifest, but there was no id set.":[ "在安裝資訊檔中找到字典，但沒有設定 ID。" ],
      "Please refer to https://github.com/mozilla-extensions/xpi-manifest to learn more about privileged extensions and signing.":[ "若需更多資訊，請參考 https://github.com/mozilla-extensions/xpi-manifest 了解取得權限的擴充套件與簽章的相關資訊。" ],
      "Forbidden content found in add-on.":[ "附加元件中出現被禁止的內容。" ],
      "This add-on contains forbidden content.":[ "此附加元件包含被禁止的內容。" ],
      "Icons must be square.":[ "圖示必須是正方形。" ],
      "Icon at \"%(path)s\" must be square.":[ "位於「%(path)s」的圖示應為正方形。" ],
      "The size of the icon does not match the manifest.":[ "圖示大小與安裝資訊檔不同。" ],
      "Expected icon at \"%(path)s\" to be %(expected)d pixels wide but was %(actual)d.":[ "預期位於「%(path)s」的圖示應有 %(expected)d 像素寬，但實際上是 %(actual)d。" ],
      "\"%(fieldName)s\" is ignored for non-privileged add-ons.":[ "不需要權限的附加元件將忽略「%(fieldName)s」。" ],
      "Corrupt image file":[ "圖檔已損壞" ],
      "Expected icon file at \"%(path)s\" is corrupted":[ "位於「%(path)s」的圖示檔案已損壞。" ],
      "This property has been deprecated.":[ "此屬性已棄用。" ],
      "This theme LWT alias has been removed in Firefox 70.":[ "此佈景主題的 LWT 別名，已於 Firefox 70 移除。" ],
      "See https://mzl.la/2T11Lkc (MDN Docs) for more information.":[ "請參考 https://mzl.la/2T11Lkc（MDN 文件）的更多資訊。" ],
      "Theme image for \"%(type)s\" could not be found at \"%(path)s\"":[ "無法在「%(path)s」找到「%(type)s」的佈景主題圖片" ],
      "\"%(fieldName)s\" manifest field is only used for privileged and temporarily installed extensions.":[ "安裝資訊檔當中的「%(fieldName)s」欄位，僅在需要權限或暫時安裝的擴充套件當中使用。" ],
      "Corrupted theme image file":[ "損壞的佈景主題圖檔" ],
      "Theme image file at \"%(path)s\" is corrupted":[ "位於「%(path)s」的佈景主題圖檔已毀損" ],
      "Theme image file has an unsupported file extension":[ "此圖片使用不支援的副檔名" ],
      "Theme image file at \"%(path)s\" has an unsupported file extension":[ "位於「%(path)s」的佈景主題圖檔，副檔名不支援" ],
      "Theme image file has an unsupported mime type":[ "佈景主題圖檔的 MIME 類型不受支援" ],
      "Theme image file at \"%(path)s\" has the unsupported mime type \"%(mime)s\"":[ "位於「%(path)s」的佈景主題圖檔，其 MIME 類型「%(mime)s」不受支援" ],
      "Theme image file mime type does not match its file extension":[ "佈景主題圖檔的 MIME 類型，與其副檔名不符" ],
      "Theme image file extension at \"%(path)s\" does not match its actual mime type \"%(mime)s\"":[ "位於「%(path)s」的佈景主題圖檔，其 MIME 類型「%(mime)s」與副檔名不符" ],
      "The \"default_locale\" is missing localizations.":[ "「default_locale」缺少在地化內容。" ],
      "The \"default_locale\" value is specified in the manifest, but no matching \"messages.json\" in the \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "安裝資訊檔中指定了「default_locale」，但「_locales」目錄中沒有符合的「messages.json」。詳見：https://mzl.la/2hjCAee" ],
      "The \"default_locale\" is missing but \"_locales\" exist.":[ "缺少「default_locale」但有「_locales」存在。" ],
      "The \"default_locale\" value is not specifed in the manifest, but a \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "安裝資訊檔中未指定「default_locale」，但存在「_locales」目錄。詳見：https://mzl.la/2hjCAee" ],
      "Unsupported image extension":[ "不支援的圖片副檔名" ],
      "Icons should be one of JPG/JPEG, WebP, GIF, PNG or SVG.":[ "圖示格式應為 JPG/JPEG、WebP、GIF、PNG 或 SVG 任一。" ],
      "\"applications\" property overridden by \"browser_specific_settings\" property":[ "「applications」屬性被「browser_specific_settings」屬性覆蓋" ],
      "The \"applications\" property is being ignored because it is superseded by the \"browser_specific_settings\" property which is also defined in your manifest. Consider removing applications.":[ "將忽略「applications」屬性，它已被安裝資訊檔中也有定義的「browser_specific_settings」屬性取代。請考慮移除「applications」屬性。" ],
      "Empty language directory":[ "語言目錄空白" ],
      "messages.json file missing in \"%(path)s\"":[ "無法在「%(path)s」找到 messages.json 檔案。" ],
      "Manifest key not supported by the specified minimum Firefox version":[ "指定的最小 Firefox 版本，不支援安裝資訊鍵值" ],
      "\"strict_min_version\" requires Firefox %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "「strict_min_version」需要在 %(versionAdded)s 版，引入「%(key)s」功能支援前所發行的 Firefox %(minVersion)s 版才能使用。" ],
      "\"%(fieldName)s\" is not supported in manifest versions %(versionRange)s.":[ "安裝資訊檔版本 %(versionRange)s 不支援「%(fieldName)s」。" ],
      "Permission not supported by the specified minimum Firefox version":[ "指定的最小 Firefox 版本，不支援權限" ],
      "\"%(fieldName)s\" is not supported.":[ "不支援「%(fieldName)s」。" ],
      "Manifest key not supported by the specified minimum Firefox for Android version":[ "指定的最小 Firefox for Android 版本，不支援安裝資訊鍵值" ],
      "\"strict_min_version\" requires Firefox for Android %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "「strict_min_version」需要在 %(versionAdded)s 版，引入「%(key)s」功能支援前所發行的 Firefox for Android %(minVersion)s 版才能使用。" ],
      "Permission not supported by the specified minimum Firefox for Android version":[ "指定的最小 Firefox for Android 版本，不支援權限" ],
      "Linking to \"addons.mozilla.org\" is not allowed":[ "不允許連結到「addons.mozilla.org」" ],
      "Links directing to \"addons.mozilla.org\" are not allowed to be used for homepage":[ "不允許將首頁連結指到「addons.mozilla.org」" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above":[ "「%(permission)s」權限要求「strict_min_version」設定為「%(minFirefoxVersion)s」或更新版本" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above. Please update your manifest.json version to specify a minimum Firefox version.":[ "「%(permission)s」權限要求「strict_min_version」設定為「%(minFirefoxVersion)s」或更新版本。請將您 manifest.json 當中的版本更改為最小支援的 Firefox 版本。" ],
      "The extension ID is required in Manifest Version 3 and above.":[ "Manifest 第 3 版或更新版本必填擴充套件 ID。" ],
      "See https://mzl.la/3PLZYdo for more information.":[ "若需更多資訊，請參考 https://mzl.la/3PLZYdo。" ],
      "%(instancePath)s: Privileged extensions should declare privileged permissions.":[ "%(instancePath)s: 特殊權限擴充套件應該宣告需要的特殊權限。" ],
      "This extension does not declare any privileged permission. It does not need to be signed with the privileged certificate. Please upload it directly to https://addons.mozilla.org/.":[ "此擴充套件未宣告任何特殊權限，無須使用特殊憑證簽署，請直接上傳到 https://addons.mozilla.org/ 即可。" ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for privileged extensions.":[ "%(instancePath)s：特殊權限的擴充套件必須要有「mozillaAddons」權限。" ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for extensions that include privileged manifest fields.":[ "%(instancePath)s：安裝資訊中，包含特殊權限欄位的擴充套件，必須要有「mozillaAddons」權限。" ],
      "%(instancePath)s: privileged manifest fields are only allowed in privileged extensions.":[ "%(instancePath)s：特殊權限的安裝資訊檔欄位，僅允許使用於特殊權限的擴充套件中。" ],
      "This extension does not include the \"mozillaAddons\" permission, which is required for privileged extensions.":[ "此擴充套件並未包含特殊權限擴充套件所必須的「mozillaAddons」權限。" ],
      "Cannot use actions in hidden add-ons.":[ "無法在隱藏的附加元件中使用 actions。" ],
      "The hidden and browser_action/page_action (or action in Manifest Version 3 and above) properties are mutually exclusive.":[ "hidden 與 browser_action/page_action（或 Manifest Version 3 以上版本中的 action）屬性互斥。" ],
      "Use \"browser_specific_settings\" instead of \"applications\".":[ "請改用「browser_specific_settings」來取代「applications」。" ],
      "The \"applications\" property in the manifest is deprecated and will no longer be accepted in Manifest Version 3 and above.":[ "已棄用安裝資訊檔中的「applications」屬性，於 Manifest Version 3 起將不再接受。" ],
      "\"applications\" is no longer allowed in Manifest Version 3 and above.":[ "Manifest Version 3 起不再允許使用「applications」。" ],
      "The \"applications\" property in the manifest is no longer allowed in Manifest Version 3 and above. Use \"browser_specific_settings\" instead.":[ "Manifest Version 3 起將不再允許於安裝資訊檔中使用「applications」屬性，請改用「browser_specific_settings」。" ],
      "The version string should be simplified because it won't be compatible with Manifest Version 3 and above.":[ "版本字串於 Manifest Version 3 起不再相容，請簡化，" ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros will no longer be allowed. Letters will no longer be allowed either. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "版本號應該為 1~4 組數字所組成的字串，每組之間用小數點分隔。每組數字最多僅能有九位數，前方不再允許補 0 也不可再包含字母。若需更多資訊請參考 https://mzl.la/3h3mCRu（MDN 文件）。" ],
      "The version string should be simplified.":[ "應簡化版本字串。" ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros are not allowed. Letters are no longer allowed. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "版本號應該為 1~4 組數字所組成的字串，每組之間用小數點分隔。每組數字最多僅能有九位數，前方不允許補 0 也不可再包含字母。若需更多資訊請參考 https://mzl.la/3h3mCRu（MDN 文件）。" ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported in manifest versions %(versionRange)s.":[ "/%(fieldName)s：安裝資訊檔版本 %(versionRange)s 不支援「%(permissionName)s」。" ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported.":[ "/%(fieldName)s：不支援「%(permissionName)s」。" ] } } }