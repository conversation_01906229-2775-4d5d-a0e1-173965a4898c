module.exports = { domain:"messages",
  locale_data:{ messages:{ "":{ domain:"messages",
        plural_forms:"nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);",
        lang:"sl" },
      "Validation Summary:":[ "Povzetek preverjanja:" ],
      Code:[ "Koda" ],
      Message:[ "Sporočilo" ],
      Description:[ "Opis" ],
      File:[ "Datoteka" ],
      Line:[ "Vrstica" ],
      Column:[ "Stolpec" ],
      "Invalid manifest version range requested: --min-manifest-version (currently set to %(minManifestVersion)s) should not be greater than --max-manifest-version (currently set to %(maxManifestVersion)s).":[ "Zahtevan obseg različic manifesta je neveljaven: --min-manifest-version (trenutno nastavljen na %(minManifestVersion)s) ne sme biti večji od --max-manifest-version (trenutno nastavljen na %(maxManifestVersion)s)." ],
      "Your FTL is not valid.":[ "Vaš FTL ni veljaven." ],
      "Your FTL file could not be parsed.":[ "Vaše datoteke FTL ni bilo mogoče razčleniti." ],
      "Remote scripts are not allowed as per the Add-on Policies.":[ "Pravilnik dodatkov ne dovoljuje oddaljenih skriptov." ],
      "Please include all scripts in the add-on. For more information, refer to https://mzl.la/2uEOkYp.":[ "V dodatek vključite vse skripte. Za več informacij si oglejte https://mzl.la/2uEOkYp." ],
      "Inline scripts blocked by default":[ "Notranji skripti so privzeto zavrnjeni." ],
      "Default CSP rules prevent inline JavaScript from running (https://mzl.la/2pn32nd).":[ "Privzeta pravila CSP preprečujejo izvajanje znotrajvrstičnega JavaScripta (https://mzl.la/2pn32nd)." ],
      "Unadvised 3rd-party JS library":[ "Nepriporočena knjižnica JS tretje osebe" ],
      "Your add-on uses a JavaScript library we do not recommend. Read more: https://bit.ly/1TRIyZY":[ "Vaš dodatek uporablja knjižnico JavaScript, ki je ne priporočamo. Več na: https://bit.ly/1TRIyZY" ],
      "Known JS library detected":[ "Zaznana je znana knjižnica JS" ],
      "JavaScript libraries are discouraged for simple add-ons, but are generally accepted.":[ "Knjižnice JavaScript niso priporočljive za preproste dodatke, vendar jih v splošnem sprejemamo." ],
      "Due to both security and performance concerns, this may not be set using dynamic values which have not been adequately sanitized. This can lead to security issues or fairly serious performance degradation.":[ "Zaradi pomislekov glede varnosti in zmogljivosti tega morda ne bo mogoče nastaviti z uporabo dinamičnih vrednosti, ki niso bile ustrezno prečiščene. To lahko povzroči varnostne težave ali dokaj resno poslabšanje delovanja." ],
      "{{api}} is not supported":[ "{{api}} ni podprt" ],
      "This API has not been implemented by Firefox.":[ "Firefox ne podpira tega API-ja." ],
      "\"{{api}}\" has been removed in Manifest Version 3 (`manifest_version` property)":[ "\"{{api}}\" je bil odstranjen v različici manifesta 3 (lastnost 'manifest_version')" ],
      "{{api}} is deprecated":[ "{{api}} ni več podprt" ],
      "This API has been deprecated by Firefox.":[ "Firefox je opustil ta API." ],
      "Content script file could not be found.":[ "Datoteke skripta vsebine ni bilo mogoče najti." ],
      "\"{{api}}\" is deprecated or unimplemented":[ "\"{{api}}\" je zastarel ali neuveljavljen" ],
      "Content script file could not be found":[ "Datoteke skripta vsebine ni bilo mogoče najti." ],
      "\"%(api)s\" can cause issues when loaded temporarily":[ "\"%(api)s\" lahko povzroča težave, če je naložena začasno" ],
      "This API can cause issues when loaded temporarily using about:debugging in Firefox unless you specify \"browser_specific_settings.gecko.id\" in the manifest. Please see: https://mzl.la/2hizK4a for more.":[ "Ta API lahko povzroči težave pri začasnem nalaganju z uporabo about:debugging v Firefoxu, razen če v manifestu navedete \"browser_specific_settings.gecko.id\". Več na: https://mzl.la/2hizK4a." ],
      "{{api}} is not supported in Firefox version {{minVersion}}":[ "{{api}} ni podprt v različici Firefox {{minVersion}}" ],
      "This API is not implemented by the given minimum Firefox version":[ "Ta API ni uveljavljen v najnižji podprti različici Firefoxa" ],
      "{{api}} is not supported in Firefox for Android version {{minVersion}}":[ "{{api}} ni podprt v različici Firefox za Android {{minVersion}}" ],
      "This API is not implemented by the given minimum Firefox for Android version":[ "Ta API ni uveljavljen v najnižji podprti različici Firefoxa za Android" ],
      "Content script file name should not be empty.":[ "Ime datoteke skripta vsebine ne sme biti prazno." ],
      "Content script file name should not be empty":[ "Ime datoteke skripta vsebine ne sme biti prazno." ],
      "\"%(method)s\" called with a non-literal uri":[ "\"%(method)s\" klicana z nedobesednim URI" ],
      "Calling \"%(method)s\" with variable parameters can result in potential security vulnerabilities if the variable contains a remote URI. Consider using 'window.open' with the 'chrome=no' flag.":[ "Klicanje \"%(method)s\" s parametri spremenljivke lahko povzroči morebitne varnostne ranljivosti, če spremenljivka vsebuje oddaljeni URI. Razmislite o uporabi 'window.open' z zastavico 'chrome=no'." ],
      "\"%(method)s\" called with non-local URI":[ "\"%(method)s\" klicana z nekrajevnim URI" ],
      "Calling \"%(method)s\" with a non-local URI will result in the dialog being opened with chrome privileges.":[ "Klicanje \"%(method)s\" z nekrajevnim URI bo povzročilo, da se bo pogovorno okno odprlo s pravicami brskalnika." ],
      "JavaScript syntax error":[ "Napaka skladnje JavaScripta." ],
      "There is a JavaScript syntax error in your code, which might be related to some experimental JavaScript features that aren't an official part of the language specification and therefore not supported yet. The validation cannot continue on this file.":[ "V vaši kodi je prišlo do sintaktične napake JavaScripta, ki je lahko povezana z nekaterimi poskusnimi funkcijami JavaScripta, ki niso uradni del jezikovne specifikacije in zato še niso podprte. Preverjanja te datoteke ni mogoče nadaljevati." ],
      "Evaluation of strings as code can lead to security vulnerabilities and performance issues, even in the most innocuous of circumstances. Please avoid using `eval` and the `Function` constructor when at all possible.":[ "Vrednotenje nizov kot kode lahko povzroči varnostne ranljivosti in težave z delovanjem, tudi v najbolj neškodljivih okoliščinah. Izogibajte se uporabi `eval` in konstruktorja `Function`, kadar je le mogoče." ],
      "setTimeout, setInterval and execScript functions should be called only with function expressions as their first argument":[ "Funkcije setTimeout, setInterval in execScript lahko kličete samo s funkcijskimi izrazi kot prvim argumentom" ],
      "Unexpected global passed as an argument":[ "Globalna spremenljivka nepričakovano podana kot argument" ],
      "Passing a global as an argument is not recommended. Please make this a var instead.":[ "Posredovanje globalne spremenljivke kot argumenta ni priporočljivo. Raje uporabite \"var\"." ],
      "Use of document.write strongly discouraged.":[ "Uporaba document.write se močno odsvetuje." ],
      "document.write will fail in many circumstances when used in extensions, and has potentially severe security repercussions when used improperly. Therefore, it should not be used.":[ "document.write ne deluje v mnogo primerih, če se uporablja v razširitvah, in ima lahko resne varnostne posledice, če se uporablja nepravilno. Zato je najbolje, da se ga ne uporablja." ],
      "Banned 3rd-party JS library":[ "Prepovedana knjižnica JS tretje osebe" ],
      "Your add-on uses a JavaScript library we consider unsafe. Read more: https://bit.ly/1TRIyZY":[ "Vaš dodatek uporablja knjižnico JavaScript, ki jo štejemo za nevarno. Več na: https://bit.ly/1TRIyZY" ],
      "Your JSON contains block comments.":[ "Vaša datoteka JSON vsebuje bloke komentarjev." ],
      "Only line comments (comments beginning with \"//\") are allowed in JSON files. Please remove block comments (comments beginning with \"/*\")":[ "V datotekah JSON so dovoljeni samo vrstični komentarji (ki se začenjajo z \"//\"). Odstranite komentarje v blokih (ki se začenjajo z \"/*\")" ],
      "Duplicate keys are not allowed in JSON files.":[ "V datotekah JSON podvojeni ključi niso dovoljeni." ],
      "Duplicate key found in JSON file.":[ "V datoteki JSON je najden podvojen ključ." ],
      "Your JSON is not valid.":[ "Vaša datoteka JSON ni veljavna." ],
      "Your JSON file could not be parsed.":[ "Vaše datoteke JSON ni bilo mogoče razčleniti." ],
      "Reserved filename found.":[ "Najdeno rezervirano ime datoteke." ],
      "Files whose names are reserved have been found in the add-on. Please refrain from using them and rename your files.":[ "V dodatku so bile najdene datoteke z rezerviranimi imeni. Prosimo, da teh imen ne uporabljate in svoje datoteke preimenujete." ],
      "The package is invalid. It may contain entries using invalid characters, as an example using '\\' as a path separator is not allowed in Firefox. Try to recreate your add-on package (ZIP) and make sure all entries are using '/' as the path separator.":[ "Paket je neveljaven. Morda vsebuje vnose, ki uporabljajo neveljavne znake, na primer uporaba '\\' kot ločila poti v Firefoxu ni dovoljena. Poskusite znova ustvariti paket (ZIP) in se prepričajte, da vsi vnosi kot ločilo poti uporabljajo '/'." ],
      "We were unable to decompress the zip file.":[ "Datoteke zip ni bilo mogoče razširiti." ],
      "manifest.json was not found":[ "Datoteke manifest.json ni bilo mogoče najti" ],
      "No manifest.json was found at the root of the extension. The package file must be a ZIP of the extension's files themselves, not of the containing directory. See: https://mzl.la/2r2McKv for more on packaging.":[ "V korenu razširitve ni bilo mogoče najti datoteke manifest.json. Datoteka paketa mora biti ZIP same datoteke razširitve in ne mape, ki jo vsebuje. Več o embalaži na: https://mzl.la/2r2McKv." ],
      "File is too large to parse.":[ "Datoteka je prevelika za razčlenjevanje." ],
      "This file is not binary and is too large to parse. Files larger than %(maxFileSizeToParseMB)sMB will not be parsed. Consider moving large lists of data out of JavaScript files and into JSON files, or splitting very large files into smaller ones.":[ "Ta datoteka ni binarna in je prevelika za razčlenjevanje. Datoteke, večje od %(maxFileSizeToParseMB)sMB, ne bodo razčlenjene. Razmislite o premikanju velikih seznamov podatkov iz datotek JavaScript v datoteke JSON ali o razdelitvi zelo velikih datotek na manjše." ],
      "Hidden file flagged":[ "Skrita datoteka označena" ],
      "Hidden files complicate the review process and can contain sensitive information about the system that generated the add-on. Please modify the packaging process so that these files aren't included.":[ "Skrite datoteke otežijo postopek pregleda in lahko vsebujejo občutljive podatke o sistemu, ki je ustvaril dodatek. Spremenite postopek pakiranja, da te datoteke ne bodo vključene." ],
      "Package contains duplicate entries":[ "Paket vsebuje podvojene vnose" ],
      "Flagged filename found":[ "Najdeno označeno ime datoteke" ],
      "Files were found that are either unnecessary or have been included unintentionally. They should be removed.":[ "Najdene so bile datoteke, ki so nepotrebne ali nenamerno vključene. Treba jih je odstraniti." ],
      "The package contains multiple entries with the same name. This practice has been banned. Try unzipping and re-zipping your add-on package and try again.":[ "Paket vsebuje več vnosov z istim imenom. Ta praksa je prepovedana. Poskusite razpakirati in znova zapakirati paket dodatka ter poskusiti znova." ],
      "Flagged file extensions found":[ "Najdene označene končnice datotek" ],
      "Flagged file type found":[ "Najdena označena vrsta datoteke" ],
      "Files whose names end with flagged extensions have been found in the add-on. The extension of these files are flagged because they usually identify binary components. Please see https://bit.ly/review-policy for more information on the binary content review process.":[ "V dodatku so bile najdene datoteke, katerih imena se končujejo z eno od pripon, kakršne običajno pomenijo binarne komponente. Za več informacij o postopku pregleda binarne vsebine obiščite https://bit.ly/review-policy." ],
      "Package already signed":[ "Paket je že podpisan" ],
      "Add-ons which are already signed will be re-signed when published on AMO. This will replace any existing signatures on the add-on.":[ "Dodatki, ki so že podpisani, bodo ob objavi na AMO znova podpisani. To bo zamenjalo vse obstoječe podpise v dodatku." ],
      "Firefox add-ons are not allowed to run coin miners.":[ "Dodatkom za Firefox ni dovoljeno rudariti kriptovalut." ],
      "We do not allow coinminer scripts to be run inside WebExtensions. See https://github.com/mozilla/addons-linter/issues/1643 for more details.":[ "Znotraj WebExtensions ne dovolimo izvajanja skriptov coinminer. Za podrobnosti si oglejte https://github.com/mozilla/addons-linter/issues/1643." ],
      "String name is reserved for a predefined message":[ "Ime niza je rezervirano za vnaprej določeno sporočilo" ],
      "String names starting with @@ get translated to built-in constants (https://mzl.la/2BL9ZjE).":[ "Imena nizov, ki se začnejo z @@, se prevedejo v vgrajene konstante (https://mzl.la/2BL9ZjE)." ],
      "String name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2Eztyi5).":[ "Ime niza naj vsebuje le alfanumerične znake, _ in @ (https://mzl.la/2Eztyi5)." ],
      "Placeholder for message is missing":[ "Manjka označba mesta sporočila" ],
      "A placeholder used in the message is not defined.":[ "Označba mesta, uporabljena v sporočilu, ni določena." ],
      "Placeholder name contains invalid characters":[ "Ime označbe mesta vsebuje neveljavne znake" ],
      "Placeholder name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2ExbYez).":[ "Ime lahko vsebuje le alfanumerične znake, _ in @ (https://mzl.la/2ExbYez)." ],
      "Placeholder is missing the content property":[ "Označbi mesta manjka lastnost vsebine" ],
      "A placeholder needs a content property defining the replacement of it (https://mzl.la/2DT1MQd)":[ "Ograda mesta potrebuje lastnost vsebine, ki določa njegovo zamenjavo (https://mzl.la/2DT1MQd)" ],
      "Translation string is missing the message property":[ "Nizu prevoda manjka lastnost sporočila" ],
      "No \"message\" message property is set for a string (https://mzl.la/2DSBTjA).":[ "Za niz ni bilo nastavljeno nobeno sporočilo \"message\" (https://mzl.la/2DSBTjA)." ],
      "The field is required.":[ "Polje je zahtevano." ],
      "See https://mzl.la/1ZOhoEN (MDN Docs) for more information.":[ "Za več informacij glejte https://mzl.la/1ZOhoEN (dokumente na MDN-u)." ],
      "The permission type is unsupported.":[ "Vrsta dovoljenja ni podprta." ],
      "See https://mzl.la/1R1n1t0 (MDN Docs) for more information.":[ "Za več informacij glejte https://mzl.la/1R1n1t0 (dokumente na MDN-u)." ],
      "See https://mzl.la/2Qn0fWC (MDN Docs) for more information.":[ "Za več informacij glejte https://mzl.la/2Qn0fWC (dokumente na MDN-u)." ],
      "See https://mzl.la/3Woeqv4 (MDN Docs) for more information.":[ "Za več informacij glejte https://mzl.la/3Woeqv4 (dokumente na MDN)." ],
      "Unknown permission.":[ "Neznano dovoljenje." ],
      "%(instancePath)s: the following privileged permissions are only allowed in privileged extensions: %(privilegedPermissions)s.":[ "%(instancePath)s: naslednja privilegirana dovoljenja so dovoljena samo v privilegiranih razširitvah: %(privilegedPermissions)s." ],
      "Invalid host permission.":[ "Neveljavno dovoljenje gostitelja." ],
      "Invalid install origin.":[ "Neveljavno poreklo namestitve." ],
      "Invalid install origin. A valid origin has - only - a scheme, hostname and optional port. See https://mzl.la/3TEbqbE (MDN Docs) for more information.":[ "Neveljaven izvor namestitve. Veljaven izvor ima – samo – shemo, ime gostitelja in neobvezna vrata. Za več informacij glejte https://mzl.la/3TEbqbE (MDN Docs)." ],
      "The field is invalid.":[ "Polje je neveljavno." ],
      "\"manifest_version\" in the manifest.json is not a valid value":[ "\"manifest_version\" v datoteki manifest.json ni veljavna vrednost." ],
      "See https://mzl.la/20PenXl (MDN Docs) for more information.":[ "Za več informacij glejte https://mzl.la/20PenXl (dokumente na MDN-u)." ],
      "\"%(property)s\" allows remote code execution in manifest.json":[ "\"%(property)s\" dovoljuje oddaljeno izvajanje kode v datoteki manifest.json" ],
      "A custom \"%(property)s\" needs additional review.":[ "Lastnost po meri \"%(property)s\" potrebuje dodaten pregled." ],
      "\"%(property)s\" allows 'eval', which has strong security and performance implications.":[ "\"%(property)s\" dovoljuje \"eval\", ki močno vpliva na varnost in zmogljivost." ],
      "In most cases the same result can be achieved differently, therefore it is generally prohibited":[ "V večini primerov je mogoče enako doseči na drugačen način, zato je to na splošno prepovedano" ],
      "The \"name\" property must be a string with no leading/trailing whitespaces.":[ "Lastnost \"name\" mora biti niz brez presledkov na začetku ali koncu." ],
      "See http://mzl.la/1STmr48 (MDN Docs) for more information.":[ "Za več informacij glejte http://mzl.la/1STmr48 (dokumente na MDN-u)." ],
      "\"update_url\" is not allowed.":[ "\"update_url\" ni dovoljen." ],
      "\"applications.gecko.update_url\" or \"browser_specific_settings.gecko.update_url\" are not allowed for Mozilla-hosted add-ons.":[ "\"applications.gecko.update_url\" ali \"browser_specific_settings.gecko.update_url\" nista dovoljena za dodatke, ki gostujejo pri Mozilli." ],
      "The \"update_url\" property is not used by Firefox.":[ "Lastnosti \"update_url\" Firefox ne uporablja." ],
      "The \"update_url\" is not used by Firefox in the root of a manifest; your add-on will be updated via the Add-ons site and not your \"update_url\". See: https://mzl.la/25zqk4O":[ "Firefox ne uporablja \"update_url\" v korenu manifesta; vaš dodatek bo posodobljen na strani z dodatki in ne na \"update_url\". Glej: https://mzl.la/25zqk4O" ],
      "\"strict_max_version\" not required.":[ "\"strict_max_version\" ni zahtevan." ],
      "\"strict_max_version\" shouldn't be used unless the add-on is expected not to work with future versions of Firefox.":[ "\"strict_max_version\" se ne sme uporabljati, razen če se pričakuje, da dodatek s prihodnjimi različicami Firefoxa ne bo deloval." ],
      "Manifest Version 3 is not fully supported on Firefox for Android.":[ "Firefox za Android ne podpira v celoti različice manifesta 3." ],
      "No \"%(property)s\" property found in manifest.json":[ "V datoteki manifest.json ni bilo mogoče najti lastnosti \"%(property)s\"" ],
      "\"%(property)s\" is required":[ "Lastnost \"%(property)s\" je obvezna" ],
      "An icon defined in the manifest could not be found in the package.":[ "V paketu ni bilo mogoče najti ikone, določene v manifestu." ],
      "Icon could not be found at \"%(path)s\".":[ "V mapi \"%(path)s\" ni bilo mogoče najti ikone." ],
      "A background script defined in the manifest could not be found.":[ "Skripta ozadja, določenega v manifestu, ni bilo mogoče najti." ],
      "A background page defined in the manifest could not be found.":[ "Strani ozadja, določene v manifestu, ni bilo mogoče najti." ],
      "Background script could not be found at \"%(path)s\".":[ "V mapi \"%(path)s\" ni bilo mogoče najti skripta ozadja." ],
      "Background page could not be found at \"%(path)s\".":[ "V mapi \"%(path)s\" ni bilo mogoče najti strani ozadja." ],
      "A content script defined in the manifest could not be found.":[ "Skripta vsebine, določenega v manifestu, ni bilo mogoče najti." ],
      "A content script css file defined in the manifest could not be found.":[ "Datoteke css skripta vsebine, določene v manifestu, ni bilo mogoče najti." ],
      "Content script defined in the manifest could not be found at \"%(path)s\".":[ "V mapi \"%(path)s\" ni bilo mogoče najti skripta vsebine, določenega v manifestu." ],
      "Content script css file defined in the manifest could not be found at \"%(path)s\".":[ "V mapi \"%(path)s\" ni bilo mogoče najti datoteke css skripta vsebine, določene v manifestu." ],
      "A dictionary file defined in the manifest could not be found.":[ "Datoteke slovarja, določene v manifestu, ni bilo mogoče najti." ],
      "Dictionary file defined in the manifest could not be found at \"%(path)s\".":[ "V mapi \"%(path)s\" ni bilo mogoče najti datoteke slovarja, določene v manifestu." ],
      "The manifest contains multiple dictionaries.":[ "Manifest vsebuje več slovarjev." ],
      "Multiple dictionaries were defined in the manifest, which is unsupported.":[ "V manifestu je bilo določenih več slovarjev, kar ni podprto." ],
      "The manifest contains a dictionaries object, but it is empty.":[ "Manifest vsebuje predmet slovarjev, vendar je prazen." ],
      "A dictionaries object was defined in the manifest, but it was empty.":[ "V manifestu je bil opredeljen predmet slovarjev, vendar je bil prazen." ],
      "The manifest contains a dictionary but no id property.":[ "Manifest vsebuje predmet slovarjev, vendar nima lastnosti id." ],
      "A dictionary was found in the manifest, but there was no id set.":[ "V manifestu je bil najden slovar, ki nima nastavljenega id." ],
      "Please refer to https://github.com/mozilla-extensions/xpi-manifest to learn more about privileged extensions and signing.":[ "Za več informacij o privilegiranih razširitvah in podpisovanju si oglejte https://github.com/mozilla-extensions/xpi-manifest." ],
      "Forbidden content found in add-on.":[ "V dodatku je bila najdena prepovedana vsebina." ],
      "This add-on contains forbidden content.":[ "Ta dodatek vsebuje prepovedano vsebino." ],
      "Icons must be square.":[ "Ikone morajo biti kvadratne." ],
      "Icon at \"%(path)s\" must be square.":[ "Ikona v mapi \"%(path)s\" mora biti kvadratna." ],
      "The size of the icon does not match the manifest.":[ "Velikost ikone se ne ujema z manifestom." ],
      "Expected icon at \"%(path)s\" to be %(expected)d pixels wide but was %(actual)d.":[ "Pričakovana velikost ikone na \"%(path)s\" je bila %(expected)d slikovnih pik, dejanska pa %(actual)d." ],
      "\"%(fieldName)s\" is ignored for non-privileged add-ons.":[ "\"%(fieldName)s\" je prezrt za dodatke brez pravic." ],
      "Corrupt image file":[ "Pokvarjena slikovna datoteka" ],
      "Expected icon file at \"%(path)s\" is corrupted":[ "Pričakovana datoteka ikone v mapi \"%(path)s\" je pokvarjena" ],
      "This property has been deprecated.":[ "Ta lastnost je opuščena." ],
      "This theme LWT alias has been removed in Firefox 70.":[ "Ta vzdevek LWT teme je bil odstranjen v Firefoxu 70." ],
      "See https://mzl.la/2T11Lkc (MDN Docs) for more information.":[ "Za več informacij glejte https://mzl.la/2T11Lkc (dokumente na omrežju MDN)." ],
      "Theme image for \"%(type)s\" could not be found at \"%(path)s\"":[ "V mapi \"%(path)s\" ni bilo mogoče najti slike teme za \"%(type)s\"" ],
      "\"%(fieldName)s\" manifest field is only used for privileged and temporarily installed extensions.":[ "Polje manifesta \"%(fieldName)s\" se uporablja samo za privilegirane in začasno nameščene razširitve." ],
      "Corrupted theme image file":[ "Pokvarjena slikovna datoteka teme" ],
      "Theme image file at \"%(path)s\" is corrupted":[ "Slika teme v mapi \"%(path)s\" je pokvarjena" ],
      "Theme image file has an unsupported file extension":[ "Slikovna datoteka teme ima nepodprto pripono" ],
      "Theme image file at \"%(path)s\" has an unsupported file extension":[ "Slikovna datoteka na \"%(path)s\" ima nepodprto pripono" ],
      "Theme image file has an unsupported mime type":[ "Slikovna datoteka teme ima nepodprto vrsto mime" ],
      "Theme image file at \"%(path)s\" has the unsupported mime type \"%(mime)s\"":[ "Slikovna datoteka na \"%(path)s\" ima nepodprto vrsto mime \"%(mime)s\"" ],
      "Theme image file mime type does not match its file extension":[ "Vrsta mime slikovne datoteke se ne ujema s pripono datoteke" ],
      "Theme image file extension at \"%(path)s\" does not match its actual mime type \"%(mime)s\"":[ "Vrsta mime slikovne datoteke na \"%(path)s\" se ne ujema z njeno dejansko vrsto mime \"%(mime)s\"" ],
      "The \"default_locale\" is missing localizations.":[ "V \"default_locale\" manjkajo lokalizacije." ],
      "The \"default_locale\" value is specified in the manifest, but no matching \"messages.json\" in the \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "Vrednost \"default_locale\" je navedena v manifestu, vendar v imeniku \"_locale\" ne obstaja ujemajoča se datoteka \"messages.json\". Glej: https://mzl.la/2hjcaEE" ],
      "The \"default_locale\" is missing but \"_locales\" exist.":[ "\"default_locale\" manjka, vendar obstaja \"_locales\"." ],
      "The \"default_locale\" value is not specifed in the manifest, but a \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "Vrednost \"default_locale\" ni navedena v manifestu, vendar pa mapa \"_locale\" obstaja. Glej: https://mzl.la/2hjcaEE" ],
      "Unsupported image extension":[ "Nepodprta pripona slike" ],
      "Icons should be one of JPG/JPEG, WebP, GIF, PNG or SVG.":[ "Ikone morajo biti vrste JPG/JPEG, WebP, GIF, PNG ali SVG." ],
      "\"applications\" property overridden by \"browser_specific_settings\" property":[ "Lastnost \"applications\" je preglašena z lastnostjo \"browser_specific_settings\"" ],
      "The \"applications\" property is being ignored because it is superseded by the \"browser_specific_settings\" property which is also defined in your manifest. Consider removing applications.":[ "Lastnost \"applications\" je prezrta, ker je nadomeščena z lastnostjo \"browser_specific_settings\", ki je prav tako definirana v vašem manifestu. Razmislite o odstranitvi aplikacij." ],
      "Empty language directory":[ "Prazna jezikovna mapa" ],
      "messages.json file missing in \"%(path)s\"":[ "V mapi \"%(path)s\" manjka datoteka messages.json." ],
      "Manifest key not supported by the specified minimum Firefox version":[ "Ključ manifesta ni podprt v najnižji podprti različici Firefoxa" ],
      "\"strict_min_version\" requires Firefox %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "\"strict_min_version\" zahteva Firefox %(minVersion)s, ki je bil izdan, preden je različica %(versionAdded)s uvedla podporo za \"%(key)s\"." ],
      "\"%(fieldName)s\" is not supported in manifest versions %(versionRange)s.":[ "\"%(fieldName)s\" ni podprto v različicah manifesta %(versionRange)s." ],
      "Permission not supported by the specified minimum Firefox version":[ "Dovoljenje ni podprto v najnižji podprti različici Firefoxa" ],
      "\"%(fieldName)s\" is not supported.":[ "\"%(fieldName)s\" ni podprt." ],
      "Manifest key not supported by the specified minimum Firefox for Android version":[ "Ključ manifesta ni podprt v najnižji podprti različici Firefoxa za Android" ],
      "\"strict_min_version\" requires Firefox for Android %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "\"strict_min_version\" zahteva Firefox za Android %(minVersion)s, ki je bil izdan, preden je različica %(versionAdded)s uvedla podporo za \"%(key)s\"." ],
      "Permission not supported by the specified minimum Firefox for Android version":[ "Dovoljenje ni podprto v najnižji podprti različici Firefoxa za Android" ],
      "Linking to \"addons.mozilla.org\" is not allowed":[ "Povezovanje na \"addons.mozilla.org\" ni dovoljeno" ],
      "Links directing to \"addons.mozilla.org\" are not allowed to be used for homepage":[ "Za domačo stran ni dovoljeno uporabljati povezav, ki usmerjajo na \"addons.mozilla.org\"" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above":[ "Za dovoljenje \"%(permission)s\" mora biti \"strict_min_version\" nastavljena na različico \"%(minFirefoxVersion)s\" ali novejšo" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above. Please update your manifest.json version to specify a minimum Firefox version.":[ "Dovoljenje \"%(permission)s\" zahteva, da je \"strict_min_version\" nastavljen na \"%(minFirefoxVersion)s\" ali višje. Posodobite svojo različico manifest.json, da bo navedla najnižjo različico Firefoxa." ],
      "The extension ID is required in Manifest Version 3 and above.":[ "ID razširitve je obvezen v različici manifesta 3 in novejših." ],
      "See https://mzl.la/3PLZYdo for more information.":[ "Za več informacij glejte https://mzl.la/3PLZYdo" ],
      "%(instancePath)s: Privileged extensions should declare privileged permissions.":[ "%(instancePath)s: Privilegirane razširitve morajo navesti privilegirana dovoljenja." ],
      "This extension does not declare any privileged permission. It does not need to be signed with the privileged certificate. Please upload it directly to https://addons.mozilla.org/.":[ "Ta razširitev ne navaja nobenih privilegiranih dovoljenj. Ni ga treba podpisati s privilegiranim digitalnim potrdilom. Naložite ga neposredno na https://addons.mozilla.org/." ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for privileged extensions.":[ "%(instancePath)s: Za privilegirane razširitve je potrebno dovoljenje \"mozillaAddons\"." ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for extensions that include privileged manifest fields.":[ "%(instancePath)s: Za razširitve, ki vključujejo polja manifesta s posebnimi pravicami, so potrebna dovoljenja \"mozillaAddons\"." ],
      "%(instancePath)s: privileged manifest fields are only allowed in privileged extensions.":[ "%(instancePath)s: privilegirana polja manifesta so dovoljena samo v privilegiranih razširitvah." ],
      "This extension does not include the \"mozillaAddons\" permission, which is required for privileged extensions.":[ "Ta razširitev ne vključuje dovoljenja \"mozillaAddons\", ki je zahtevano za privilegirane razširitve." ],
      "Cannot use actions in hidden add-ons.":[ "V skritih dodatkih ni mogoče uporabljati dejanj." ],
      "The hidden and browser_action/page_action (or action in Manifest Version 3 and above) properties are mutually exclusive.":[ "Lastnosti \"hidden\" in \"browser_action\"/\"page_action\" (oz. \"action\" v manifestu različice 3 in novejših) se medsebojno izključujeta." ],
      "Use \"browser_specific_settings\" instead of \"applications\".":[ "Namesto \"applications\" uporabite \"browser_specific_settings\"." ],
      "The \"applications\" property in the manifest is deprecated and will no longer be accepted in Manifest Version 3 and above.":[ "Lastnost \"applications\" v manifestu je zastarela in v manifestu različice 3 in novejših ne bo več sprejeta." ],
      "\"applications\" is no longer allowed in Manifest Version 3 and above.":[ "Lastnost \"applications\" v Manifestu 3 in novejših različicah ni več dovoljena." ],
      "The \"applications\" property in the manifest is no longer allowed in Manifest Version 3 and above. Use \"browser_specific_settings\" instead.":[ "Lastnost \"applications\" v manifestu različice 3 in novejših ni več dovoljena. Namesto nje uporabite \"browser_specific_settings\"." ],
      "The version string should be simplified because it won't be compatible with Manifest Version 3 and above.":[ "Niz različice je treba poenostaviti, sicer ne bo združljiv z Manifestom 3 in novejšimi različicami." ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros will no longer be allowed. Letters will no longer be allowed either. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "Različica naj bo niz z 1 do 4 številkami, ločenimi s piko. Vsaka številka naj bo sestavljena iz največ 9 števk, ničle na začetku niso več dovoljene. Dovoljene niso več niti črke. Za več informacij glejte https://mzl.la/3h3mCRu (dokumentacija MDN)." ],
      "The version string should be simplified.":[ "Niz različice je treba poenostaviti." ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros are not allowed. Letters are no longer allowed. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "Različica naj bo niz z 1 do 4 številkami, ločenimi s piko. Vsaka številka naj bo sestavljena iz največ 9 števk. Ničle na začetku in črke niso več dovoljene. Za več informacij glejte https://mzl.la/3h3mCRu (dokumentacija MDN)." ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported in manifest versions %(versionRange)s.":[ "/%(fieldName)s: dovoljenje \"%(permissionName)s\" ni podprto v različicah manifesta %(versionRange)s." ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported.":[ "/%(fieldName)s: dovoljenje \"%(permissionName)s\" ni podprto." ] } } }