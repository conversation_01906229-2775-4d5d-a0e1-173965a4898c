module.exports = { domain:"messages",
  locale_data:{ messages:{ "":{ domain:"messages",
        plural_forms:"nplurals=1; plural=0;",
        lang:"id" },
      "Validation Summary:":[ "Ringkasan <PERSON>idasi:" ],
      Code:[ "Kode" ],
      Message:[ "<PERSON><PERSON>" ],
      Description:[ "Deskripsi" ],
      File:[ "Berkas" ],
      Line:[ "Baris" ],
      Column:[ "Kolom" ],
      "Invalid manifest version range requested: --min-manifest-version (currently set to %(minManifestVersion)s) should not be greater than --max-manifest-version (currently set to %(maxManifestVersion)s).":[ "" ],
      "Your FTL is not valid.":[ "FTL Anda tidak valid." ],
      "Your FTL file could not be parsed.":[ "File FTL Anda tidak dapat diurai." ],
      "Remote scripts are not allowed as per the Add-on Policies.":[ "Skrip jarak jauh tidak diperkenankan menurut Kebijakan Pengaya." ],
      "Please include all scripts in the add-on. For more information, refer to https://mzl.la/2uEOkYp.":[ "" ],
      "Inline scripts blocked by default":[ "Skrip selarik berkelanjutan diblokir" ],
      "Default CSP rules prevent inline JavaScript from running (https://mzl.la/2pn32nd).":[ "" ],
      "Unadvised 3rd-party JS library":[ "Pustaka JS pihak ke-3 yang tidak dianjurkan" ],
      "Your add-on uses a JavaScript library we do not recommend. Read more: https://bit.ly/1TRIyZY":[ "" ],
      "Known JS library detected":[ "Pustaka JS yang diketahui terdeteksi" ],
      "JavaScript libraries are discouraged for simple add-ons, but are generally accepted.":[ "" ],
      "Due to both security and performance concerns, this may not be set using dynamic values which have not been adequately sanitized. This can lead to security issues or fairly serious performance degradation.":[ "" ],
      "{{api}} is not supported":[ "{{api}} tidak didukung" ],
      "This API has not been implemented by Firefox.":[ "API ini belum diimplementasikan oleh Firefox." ],
      "\"{{api}}\" has been removed in Manifest Version 3 (`manifest_version` property)":[ "" ],
      "Content script file could not be found.":[ "Berkas skrip konten tidak dapat ditemukan." ],
      "\"{{api}}\" is deprecated or unimplemented":[ "" ],
      "Content script file could not be found":[ "Berkas skrip konten tidak dapat ditemukan" ],
      "\"%(api)s\" can cause issues when loaded temporarily":[ "" ],
      "This API can cause issues when loaded temporarily using about:debugging in Firefox unless you specify \"browser_specific_settings.gecko.id\" in the manifest. Please see: https://mzl.la/2hizK4a for more.":[ "" ],
      "{{api}} is not supported in Firefox version {{minVersion}}":[ "{{api}} tidak didukung di Firefox versi {{minVersion}}" ],
      "This API is not implemented by the given minimum Firefox version":[ "API ini belum diimplementasikan pada versi Firefox minimum yang diberikan" ],
      "{{api}} is not supported in Firefox for Android version {{minVersion}}":[ "{{api}} tidak didukung di Firefox untuk Android versi {{minVersion}}" ],
      "This API is not implemented by the given minimum Firefox for Android version":[ "API ini belum diimplementasikan pada versi Firefox untuk Android minimum yang diberikan" ],
      "Content script file name should not be empty.":[ "Nama berkas skrip konten tidak boleh kosong." ],
      "Content script file name should not be empty":[ "Nama berkas skrip konten tidak boleh kosong" ],
      "\"%(method)s\" called with a non-literal uri":[ "" ],
      "Calling \"%(method)s\" with variable parameters can result in potential security vulnerabilities if the variable contains a remote URI. Consider using 'window.open' with the 'chrome=no' flag.":[ "" ],
      "\"%(method)s\" called with non-local URI":[ "" ],
      "Calling \"%(method)s\" with a non-local URI will result in the dialog being opened with chrome privileges.":[ "" ],
      "JavaScript syntax error":[ "Galat sintaksis JavaScript" ],
      "There is a JavaScript syntax error in your code, which might be related to some experimental JavaScript features that aren't an official part of the language specification and therefore not supported yet. The validation cannot continue on this file.":[ "" ],
      "Evaluation of strings as code can lead to security vulnerabilities and performance issues, even in the most innocuous of circumstances. Please avoid using `eval` and the `Function` constructor when at all possible.":[ "" ],
      "setTimeout, setInterval and execScript functions should be called only with function expressions as their first argument":[ "" ],
      "Unexpected global passed as an argument":[ "Variabel global tiba-tiba diluluskan sebagai argumen" ],
      "Passing a global as an argument is not recommended. Please make this a var instead.":[ "" ],
      "Use of document.write strongly discouraged.":[ "Penggunaan document.write sangat tidak dianjurkan." ],
      "document.write will fail in many circumstances when used in extensions, and has potentially severe security repercussions when used improperly. Therefore, it should not be used.":[ "" ],
      "Banned 3rd-party JS library":[ "Pustaka JS pihak ke-3 yang dilarang" ],
      "Your add-on uses a JavaScript library we consider unsafe. Read more: https://bit.ly/1TRIyZY":[ "" ],
      "Your JSON contains block comments.":[ "JSON Anda mengandung komentar pemblokiran." ],
      "Only line comments (comments beginning with \"//\") are allowed in JSON files. Please remove block comments (comments beginning with \"/*\")":[ "" ],
      "Duplicate keys are not allowed in JSON files.":[ "Kunci duplikat tidak diizinkan dalam berkas JSON." ],
      "Duplicate key found in JSON file.":[ "" ],
      "Your JSON is not valid.":[ "JSON Anda tidak valid." ],
      "Your JSON file could not be parsed.":[ "File JSON Anda tidak bisa diurai." ],
      "Reserved filename found.":[ "Nama-berkas cadangan ditemukan." ],
      "Files whose names are reserved have been found in the add-on. Please refrain from using them and rename your files.":[ "" ],
      "The package is invalid. It may contain entries using invalid characters, as an example using '\\' as a path separator is not allowed in Firefox. Try to recreate your add-on package (ZIP) and make sure all entries are using '/' as the path separator.":[ "" ],
      "We were unable to decompress the zip file.":[ "Kami tidak dapat mendekompresi berkas yang di-zip." ],
      "manifest.json was not found":[ "manifest.json tidak ditemukan" ],
      "No manifest.json was found at the root of the extension. The package file must be a ZIP of the extension's files themselves, not of the containing directory. See: https://mzl.la/2r2McKv for more on packaging.":[ "" ],
      "File is too large to parse.":[ "Berkas terlalu besar untuk diurai." ],
      "This file is not binary and is too large to parse. Files larger than %(maxFileSizeToParseMB)sMB will not be parsed. Consider moving large lists of data out of JavaScript files and into JSON files, or splitting very large files into smaller ones.":[ "" ],
      "Hidden file flagged":[ "Berkas tersembunyi yang ditandai" ],
      "Hidden files complicate the review process and can contain sensitive information about the system that generated the add-on. Please modify the packaging process so that these files aren't included.":[ "" ],
      "Package contains duplicate entries":[ "Paket mengandung entri ganda" ],
      "Flagged filename found":[ "Nama berkas yang ditandai ditemukan" ],
      "Files were found that are either unnecessary or have been included unintentionally. They should be removed.":[ "" ],
      "The package contains multiple entries with the same name. This practice has been banned. Try unzipping and re-zipping your add-on package and try again.":[ "" ],
      "Flagged file extensions found":[ "Ekstensi berkas yang ditandai ditemukan" ],
      "Flagged file type found":[ "Jenis berkas yang ditandai ditemukan" ],
      "Files whose names end with flagged extensions have been found in the add-on. The extension of these files are flagged because they usually identify binary components. Please see https://bit.ly/review-policy for more information on the binary content review process.":[ "" ],
      "Package already signed":[ "Paket yang sudah ditandatangani" ],
      "Add-ons which are already signed will be re-signed when published on AMO. This will replace any existing signatures on the add-on.":[ "" ],
      "Firefox add-ons are not allowed to run coin miners.":[ "Pengaya Firefox tidak diizinkan untuk menjalankan penambang koin." ],
      "We do not allow coinminer scripts to be run inside WebExtensions. See https://github.com/mozilla/addons-linter/issues/1643 for more details.":[ "" ],
      "String name is reserved for a predefined message":[ "Nama string disiapkan untuk pesan yang telah ditentukan" ],
      "String names starting with @@ get translated to built-in constants (https://mzl.la/2BL9ZjE).":[ "" ],
      "String name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2Eztyi5).":[ "" ],
      "Placeholder for message is missing":[ "Placeholder untuk pesan hilang" ],
      "A placeholder used in the message is not defined.":[ "Placeholder yang digunakan dalam pesan tidak ditentukan." ],
      "Placeholder name contains invalid characters":[ "Nama placeholder berisi karakter yang tidak sah" ],
      "Placeholder name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2ExbYez).":[ "" ],
      "Placeholder is missing the content property":[ "Placeholder tidak memiliki properti konten" ],
      "A placeholder needs a content property defining the replacement of it (https://mzl.la/2DT1MQd)":[ "" ],
      "Translation string is missing the message property":[ "String terjemahan tidak memiliki properti pesan" ],
      "No \"message\" message property is set for a string (https://mzl.la/2DSBTjA).":[ "Tidak ada properti pesan \"message\" yang ditentukan untuk string (https://mzl.la/2DSBTjA)." ],
      "The field is required.":[ "Bidang yang wajib diisi." ],
      "See https://mzl.la/1ZOhoEN (MDN Docs) for more information.":[ "Lihat https://mzl.la/1ZOhoEN (MDN Docs) untuk informasi lebih lanjut." ],
      "The permission type is unsupported.":[ "Tipe izin tidak didukung." ],
      "See https://mzl.la/1R1n1t0 (MDN Docs) for more information.":[ "Lihat https://mzl.la/1R1n1t0 (MDN Docs) untuk informasi lebih lanjut." ],
      "See https://mzl.la/3Woeqv4 (MDN Docs) for more information.":[ "" ],
      "Unknown permission.":[ "Izin tidak dikenal." ],
      "%(instancePath)s: the following privileged permissions are only allowed in privileged extensions: %(privilegedPermissions)s.":[ "" ],
      "Invalid install origin. A valid origin has - only - a scheme, hostname and optional port. See https://mzl.la/3TEbqbE (MDN Docs) for more information.":[ "" ],
      "The field is invalid.":[ "Bidang ini tidak valid." ],
      "\"manifest_version\" in the manifest.json is not a valid value":[ "\"manifest_version\" dalam manifest.json bukan nilai yang valid" ],
      "See https://mzl.la/20PenXl (MDN Docs) for more information.":[ "Lihat https://mzl.la/20PenXl (Dokumen MDN) untuk informasi lebih lanjut." ],
      "\"%(property)s\" allows remote code execution in manifest.json":[ "" ],
      "A custom \"%(property)s\" needs additional review.":[ "" ],
      "\"%(property)s\" allows 'eval', which has strong security and performance implications.":[ "" ],
      "In most cases the same result can be achieved differently, therefore it is generally prohibited":[ "" ],
      "The \"name\" property must be a string with no leading/trailing whitespaces.":[ "" ],
      "See http://mzl.la/1STmr48 (MDN Docs) for more information.":[ "Lihat http://mzl.la/1STmr48 (MDN Docs) untuk informasi lebih lanjut." ],
      "\"update_url\" is not allowed.":[ "\"update_url\" tidak diizinkan." ],
      "\"applications.gecko.update_url\" or \"browser_specific_settings.gecko.update_url\" are not allowed for Mozilla-hosted add-ons.":[ "" ],
      "The \"update_url\" property is not used by Firefox.":[ "Properti \"update_url\" tidak digunakan oleh Firefox." ],
      "The \"update_url\" is not used by Firefox in the root of a manifest; your add-on will be updated via the Add-ons site and not your \"update_url\". See: https://mzl.la/25zqk4O":[ "" ],
      "\"strict_max_version\" not required.":[ "\"strict_max_version\" tidak diperlukan." ],
      "\"strict_max_version\" shouldn't be used unless the add-on is expected not to work with future versions of Firefox.":[ "" ],
      "Manifest Version 3 is not fully supported on Firefox for Android.":[ "" ],
      "No \"%(property)s\" property found in manifest.json":[ "" ],
      "\"%(property)s\" is required":[ "" ],
      "An icon defined in the manifest could not be found in the package.":[ "Ikon yang ditentukan di manifes tidak ditemukan di dalam paket." ],
      "Icon could not be found at \"%(path)s\".":[ "Ikon tidak ditemukan dalam \"%(path)s\"." ],
      "A background script defined in the manifest could not be found.":[ "Skrip latar belakang yang ditentukan di manifes tidak ditemukan." ],
      "A background page defined in the manifest could not be found.":[ "Laman latar belakang yang ditentukan di manifes tidak ditemukan." ],
      "Background script could not be found at \"%(path)s\".":[ "Skrip latar tidak dapat ditemukan di \"%(path)s\"." ],
      "Background page could not be found at \"%(path)s\".":[ "Laman latar belakang tidak dapat ditemukan dalam \"%(path)s\"." ],
      "A content script defined in the manifest could not be found.":[ "Skrip konten yang ditentukan di manifes tidak ditemukan." ],
      "A content script css file defined in the manifest could not be found.":[ "Berkas css skrip konten yang ditentukan di manifes tidak ditemukan." ],
      "Content script defined in the manifest could not be found at \"%(path)s\".":[ "Skrip konten yang ditentukan di manifes tidak ditemukan di \"%(path)s\"." ],
      "Content script css file defined in the manifest could not be found at \"%(path)s\".":[ "Berkas css skrip konten yang ditentukan di manifes tidak ditemukan di \"%(path)s\"." ],
      "A dictionary file defined in the manifest could not be found.":[ "Berkas kamus yang ditentukan di manifes tidak ditemukan." ],
      "Dictionary file defined in the manifest could not be found at \"%(path)s\".":[ "Berkas kamus yang ditentukan di manifes tidak ditemukan di \"%(path)s\"." ],
      "The manifest contains multiple dictionaries.":[ "Manifes berisi berbagai kamus." ],
      "Multiple dictionaries were defined in the manifest, which is unsupported.":[ "Kamus ganda terdefinisi di dalam manifest, dan ini tidak didukung." ],
      "The manifest contains a dictionaries object, but it is empty.":[ "Manifes berisikan obyek kamus, namun tidak ada isinya." ],
      "A dictionaries object was defined in the manifest, but it was empty.":[ "Objek kamus didefinisikan di dalam manifes, namun kosong." ],
      "The manifest contains a dictionary but no id property.":[ "Manifes berisikan kamus tanpa properti id." ],
      "A dictionary was found in the manifest, but there was no id set.":[ "Kamus ditemukan di dalam manifes, namun tidak ada id yang diatur." ],
      "Please refer to https://github.com/mozilla-extensions/xpi-manifest to learn more about privileged extensions and signing.":[ "" ],
      "Forbidden content found in add-on.":[ "Konten yang dilarang ditemukan di pengaya." ],
      "This add-on contains forbidden content.":[ "Pengaya ini berisi konten yang dilarang." ],
      "Icons must be square.":[ "Ikon harus persegi." ],
      "Icon at \"%(path)s\" must be square.":[ "Ikon di \"%(path)s\" harus persegi." ],
      "The size of the icon does not match the manifest.":[ "Ukuran ikon tidak cocok dengan manifesnya." ],
      "Expected icon at \"%(path)s\" to be %(expected)d pixels wide but was %(actual)d.":[ "" ],
      "\"%(fieldName)s\" is ignored for non-privileged add-ons.":[ "" ],
      "Corrupt image file":[ "Berkas gambar rusak" ],
      "Expected icon file at \"%(path)s\" is corrupted":[ "Berkas ikon yang diharapkan di %(path)s rusak" ],
      "This property has been deprecated.":[ "" ],
      "This theme LWT alias has been removed in Firefox 70.":[ "Alias LWT tema ini telah dihapus di Firefox 70." ],
      "See https://mzl.la/2T11Lkc (MDN Docs) for more information.":[ "Lihat https://mzl.la/2T11Lkc (MDN Docs) untuk informasi lebih lanjut." ],
      "Theme image for \"%(type)s\" could not be found at \"%(path)s\"":[ "Gambar tema untuk \"%(type)s\" tidak dapat ditemukan di \"%(path)s\"" ],
      "\"%(fieldName)s\" manifest field is only used for privileged and temporarily installed extensions.":[ "" ],
      "Corrupted theme image file":[ "Berkas gambar tema rusak" ],
      "Theme image file at \"%(path)s\" is corrupted":[ "Berkas gambar tema di \"%(path)s\" rusak" ],
      "Theme image file has an unsupported file extension":[ "Berkas gambar tema memiliki ekstensi berkas yang tidak didukung" ],
      "Theme image file at \"%(path)s\" has an unsupported file extension":[ "Berkas gambar tema di \"%(path)s\" memiliki ekstensi berkas yang tidak didukung" ],
      "Theme image file has an unsupported mime type":[ "Berkas gambar tema memiliki tipe mime yang tidak didukung" ],
      "Theme image file at \"%(path)s\" has the unsupported mime type \"%(mime)s\"":[ "Berkas gambar tema pada \"%(path)s\" memiliki mime tipe \"%(mime)s\" yang tidak didukung" ],
      "Theme image file mime type does not match its file extension":[ "Jenis mime berkas gambar tema tidak cocok dengan ekstensi berkasnya" ],
      "Theme image file extension at \"%(path)s\" does not match its actual mime type \"%(mime)s\"":[ "Ekstensi berkas gambar tema di \"%(path)s\" tidak cocok dengan mime jenis \"%(mime)s\" yang sebenarnya" ],
      "The \"default_locale\" is missing localizations.":[ "\"default_locale\" tidak memiliki pelokalan." ],
      "The \"default_locale\" value is specified in the manifest, but no matching \"messages.json\" in the \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "" ],
      "The \"default_locale\" is missing but \"_locales\" exist.":[ "Ada \"_locales\", tetapi \"default_locale\" tidak ditemukan." ],
      "The \"default_locale\" value is not specifed in the manifest, but a \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "" ],
      "Unsupported image extension":[ "Ekstensi gambar tidak didukung" ],
      "Icons should be one of JPG/JPEG, WebP, GIF, PNG or SVG.":[ "Ikon harus salah satu dari JPG/JPEG, WebP, GIF, PNG, atau SVG." ],
      "\"applications\" property overridden by \"browser_specific_settings\" property":[ "" ],
      "The \"applications\" property is being ignored because it is superseded by the \"browser_specific_settings\" property which is also defined in your manifest. Consider removing applications.":[ "" ],
      "Empty language directory":[ "Direktori bahasa kosong" ],
      "messages.json file missing in \"%(path)s\"":[ "Berkas messages.json tidak ada dalam \"%(path)s\"" ],
      "Manifest key not supported by the specified minimum Firefox version":[ "Kunci manifes tidak didukung oleh versi Firefox minimum yang dispesifikasikan" ],
      "\"strict_min_version\" requires Firefox %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "" ],
      "\"%(fieldName)s\" is not supported in manifest versions %(versionRange)s.":[ "" ],
      "Permission not supported by the specified minimum Firefox version":[ "Izin tidak didukung oleh versi Firefox minimum yang dispesifikasikan" ],
      "\"%(fieldName)s\" is not supported.":[ "" ],
      "Manifest key not supported by the specified minimum Firefox for Android version":[ "Kunci manifes tidak didukung oleh versi Firefox untuk Android minimum yang dispesifikasikan" ],
      "\"strict_min_version\" requires Firefox for Android %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "" ],
      "Permission not supported by the specified minimum Firefox for Android version":[ "Izin tidak didukung oleh versi Firefox untuk Android minimum yang dispesifikasikan" ],
      "Linking to \"addons.mozilla.org\" is not allowed":[ "" ],
      "Links directing to \"addons.mozilla.org\" are not allowed to be used for homepage":[ "" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above":[ "" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above. Please update your manifest.json version to specify a minimum Firefox version.":[ "" ],
      "The extension ID is required in Manifest Version 3 and above.":[ "" ],
      "See https://mzl.la/3PLZYdo for more information.":[ "" ],
      "%(instancePath)s: Privileged extensions should declare privileged permissions.":[ "" ],
      "This extension does not declare any privileged permission. It does not need to be signed with the privileged certificate. Please upload it directly to https://addons.mozilla.org/.":[ "" ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for privileged extensions.":[ "" ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for extensions that include privileged manifest fields.":[ "" ],
      "%(instancePath)s: privileged manifest fields are only allowed in privileged extensions.":[ "" ],
      "This extension does not include the \"mozillaAddons\" permission, which is required for privileged extensions.":[ "" ],
      "Cannot use actions in hidden add-ons.":[ "" ],
      "The hidden and browser_action/page_action (or action in Manifest Version 3 and above) properties are mutually exclusive.":[ "" ],
      "Use \"browser_specific_settings\" instead of \"applications\".":[ "" ],
      "The \"applications\" property in the manifest is deprecated and will no longer be accepted in Manifest Version 3 and above.":[ "" ],
      "\"applications\" is no longer allowed in Manifest Version 3 and above.":[ "" ],
      "The \"applications\" property in the manifest is no longer allowed in Manifest Version 3 and above. Use \"browser_specific_settings\" instead.":[ "" ],
      "The version string should be simplified because it won't be compatible with Manifest Version 3 and above.":[ "" ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros will no longer be allowed. Letters will no longer be allowed either. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "" ],
      "The version string should be simplified.":[ "" ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros are not allowed. Letters are no longer allowed. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "" ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported in manifest versions %(versionRange)s.":[ "" ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported.":[ "" ] } } }