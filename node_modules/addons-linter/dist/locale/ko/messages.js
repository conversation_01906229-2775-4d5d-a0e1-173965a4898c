module.exports = { domain:"messages",
  locale_data:{ messages:{ "":{ domain:"messages",
        plural_forms:"nplurals=1; plural=0;",
        lang:"ko" },
      "Validation Summary:":[ "유효성 검증 요약:" ],
      Code:[ "코드" ],
      Message:[ "메시지" ],
      Description:[ "설명" ],
      File:[ "파일" ],
      Line:[ "행" ],
      Column:[ "열" ],
      "Invalid manifest version range requested: --min-manifest-version (currently set to %(minManifestVersion)s) should not be greater than --max-manifest-version (currently set to %(maxManifestVersion)s).":[ "" ],
      "Your FTL is not valid.":[ "FTL이 유효하지 않습니다." ],
      "Your FTL file could not be parsed.":[ "FTL 파일을 파싱하지 못했습니다." ],
      "Remote scripts are not allowed as per the Add-on Policies.":[ "부가기능 정책에 따라 외부 스크립트는 허용되지 않습니다." ],
      "Please include all scripts in the add-on. For more information, refer to https://mzl.la/2uEOkYp.":[ "" ],
      "Inline scripts blocked by default":[ "기본적으로 인라인 스크립트 차단" ],
      "Default CSP rules prevent inline JavaScript from running (https://mzl.la/2pn32nd).":[ "" ],
      "Unadvised 3rd-party JS library":[ "권장되지 않는 타사 JS 라이브러리" ],
      "Your add-on uses a JavaScript library we do not recommend. Read more: https://bit.ly/1TRIyZY":[ "" ],
      "Known JS library detected":[ "알려진 JS 라이브러리 감지됨" ],
      "JavaScript libraries are discouraged for simple add-ons, but are generally accepted.":[ "" ],
      "Due to both security and performance concerns, this may not be set using dynamic values which have not been adequately sanitized. This can lead to security issues or fairly serious performance degradation.":[ "" ],
      "{{api}} is not supported":[ "{{api}}는 지원되지 않음" ],
      "This API has not been implemented by Firefox.":[ "이 API는 Firefox에서 구현되지 않았습니다." ],
      "\"{{api}}\" has been removed in Manifest Version 3 (`manifest_version` property)":[ "\"{{api}}\"가 Manifest 버전 3(`manifest_version` 속성)에서 제거되었습니다." ],
      "{{api}} is deprecated":[ "{{api}}는 더 이상 사용되지 않습니다." ],
      "This API has been deprecated by Firefox.":[ "이 API는 Firefox에서 더 이상 사용되지 않습니다." ],
      "Content script file could not be found.":[ "컨텐츠 스크립트 파일을 찾을 수 없습니다." ],
      "\"{{api}}\" is deprecated or unimplemented":[ "\"{{api}}\"는 더 이상 사용되지 않거나 구현되지 않았습니다." ],
      "Content script file could not be found":[ "컨텐츠 스크립트 파일을 찾을 수 없습니다" ],
      "\"%(api)s\" can cause issues when loaded temporarily":[ "" ],
      "This API can cause issues when loaded temporarily using about:debugging in Firefox unless you specify \"browser_specific_settings.gecko.id\" in the manifest. Please see: https://mzl.la/2hizK4a for more.":[ "" ],
      "{{api}} is not supported in Firefox version {{minVersion}}":[ "{{api}}는 Firefox 버전 {{minVersion}}에서 지원되지 않습니다" ],
      "This API is not implemented by the given minimum Firefox version":[ "이 API는 제공된 최소 Firefox 버전에서 구현되지 않습니다" ],
      "{{api}} is not supported in Firefox for Android version {{minVersion}}":[ "{{api}}는 안드로이드용 Firefox {{minVersion}}에서 지원되지 않습니다" ],
      "This API is not implemented by the given minimum Firefox for Android version":[ "이 API는 제공된 최소 안드로이드용 Firefox 버전에서 구현되지 않습니다" ],
      "Content script file name should not be empty.":[ "컨텐츠 스크립트 파일의 이름은 비어 있을 수 없습니다." ],
      "Content script file name should not be empty":[ "컨텐츠 스크립트 파일의 이름은 비어 있을 수 없습니다" ],
      "\"%(method)s\" called with a non-literal uri":[ "" ],
      "Calling \"%(method)s\" with variable parameters can result in potential security vulnerabilities if the variable contains a remote URI. Consider using 'window.open' with the 'chrome=no' flag.":[ "" ],
      "\"%(method)s\" called with non-local URI":[ "" ],
      "Calling \"%(method)s\" with a non-local URI will result in the dialog being opened with chrome privileges.":[ "" ],
      "JavaScript syntax error":[ "JavaScript 구문 오류" ],
      "There is a JavaScript syntax error in your code, which might be related to some experimental JavaScript features that aren't an official part of the language specification and therefore not supported yet. The validation cannot continue on this file.":[ "" ],
      "Evaluation of strings as code can lead to security vulnerabilities and performance issues, even in the most innocuous of circumstances. Please avoid using `eval` and the `Function` constructor when at all possible.":[ "" ],
      "setTimeout, setInterval and execScript functions should be called only with function expressions as their first argument":[ "" ],
      "Unexpected global passed as an argument":[ "예상치 못한 전역이 인수로 전달됨" ],
      "Passing a global as an argument is not recommended. Please make this a var instead.":[ "" ],
      "Use of document.write strongly discouraged.":[ "document.write는 사용하지 않는 것이 좋습니다." ],
      "document.write will fail in many circumstances when used in extensions, and has potentially severe security repercussions when used improperly. Therefore, it should not be used.":[ "" ],
      "Banned 3rd-party JS library":[ "금지된 타사 JS 라이브러리" ],
      "Your add-on uses a JavaScript library we consider unsafe. Read more: https://bit.ly/1TRIyZY":[ "부가 기능이 안전하지 않은 것으로 간주되는 JavaScript 라이브러리를 사용합니다. 자세한 정보: https://bit.ly/1TRIyZY" ],
      "Your JSON contains block comments.":[ "JSON에 블록 주석이 포함되어 있습니다." ],
      "Only line comments (comments beginning with \"//\") are allowed in JSON files. Please remove block comments (comments beginning with \"/*\")":[ "" ],
      "Duplicate keys are not allowed in JSON files.":[ "JSON 파일에서는 중복된 키가 허용될 수 없습니다." ],
      "Duplicate key found in JSON file.":[ "" ],
      "Your JSON is not valid.":[ "JSON 파일이 유효하지 않습니다." ],
      "Your JSON file could not be parsed.":[ "JSON 파일을 파싱하지 못했습니다." ],
      "Reserved filename found.":[ "예약된 파일명이 있습니다." ],
      "Files whose names are reserved have been found in the add-on. Please refrain from using them and rename your files.":[ "" ],
      "The package is invalid. It may contain entries using invalid characters, as an example using '\\' as a path separator is not allowed in Firefox. Try to recreate your add-on package (ZIP) and make sure all entries are using '/' as the path separator.":[ "" ],
      "We were unable to decompress the zip file.":[ "zip 파일을 압출 해제 할 수 없습니다." ],
      "manifest.json was not found":[ "manifest.json을 찾을 수 없습니다" ],
      "No manifest.json was found at the root of the extension. The package file must be a ZIP of the extension's files themselves, not of the containing directory. See: https://mzl.la/2r2McKv for more on packaging.":[ "" ],
      "File is too large to parse.":[ "분석하기에 파일 용량이 너무 큽니다." ],
      "This file is not binary and is too large to parse. Files larger than %(maxFileSizeToParseMB)sMB will not be parsed. Consider moving large lists of data out of JavaScript files and into JSON files, or splitting very large files into smaller ones.":[ "" ],
      "Hidden file flagged":[ "숨겨진 파일 플래그" ],
      "Hidden files complicate the review process and can contain sensitive information about the system that generated the add-on. Please modify the packaging process so that these files aren't included.":[ "" ],
      "Package contains duplicate entries":[ "패키지에 중복 된 항목이 있습니다" ],
      "Flagged filename found":[ "플래그 파일명 발견" ],
      "Files were found that are either unnecessary or have been included unintentionally. They should be removed.":[ "" ],
      "The package contains multiple entries with the same name. This practice has been banned. Try unzipping and re-zipping your add-on package and try again.":[ "" ],
      "Flagged file extensions found":[ "플래그 파일 확장자 발견" ],
      "Flagged file type found":[ "플래그 파일 형식 발견" ],
      "Files whose names end with flagged extensions have been found in the add-on. The extension of these files are flagged because they usually identify binary components. Please see https://bit.ly/review-policy for more information on the binary content review process.":[ "" ],
      "Package already signed":[ "이미 서명 된 패키지" ],
      "Add-ons which are already signed will be re-signed when published on AMO. This will replace any existing signatures on the add-on.":[ "" ],
      "Firefox add-ons are not allowed to run coin miners.":[ "Firefox 부가기능은 가상화폐 채굴 코드 운영을 허용하지 않습니다." ],
      "We do not allow coinminer scripts to be run inside WebExtensions. See https://github.com/mozilla/addons-linter/issues/1643 for more details.":[ "" ],
      "String name is reserved for a predefined message":[ "String 이름이 사전 정의된 메시지에 예약되어 있음" ],
      "String names starting with @@ get translated to built-in constants (https://mzl.la/2BL9ZjE).":[ "" ],
      "String name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2Eztyi5).":[ "" ],
      "Placeholder for message is missing":[ "메시지 자리 표시자가 없음" ],
      "A placeholder used in the message is not defined.":[ "메시지에 사용 된 자리 표시자가 정의되지 않았습니다." ],
      "Placeholder name contains invalid characters":[ "자리 표시자 이름에 잘못된 문자가 포함됨" ],
      "Placeholder name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2ExbYez).":[ "" ],
      "Placeholder is missing the content property":[ "자리 표시자에 콘텐츠 속성이 없음" ],
      "A placeholder needs a content property defining the replacement of it (https://mzl.la/2DT1MQd)":[ "" ],
      "Translation string is missing the message property":[ "번역 문자열에 메시지 속성이 없음" ],
      "No \"message\" message property is set for a string (https://mzl.la/2DSBTjA).":[ "문자열(https://mzl.la/2DSBTjA)에 \"message\" 메시지 속성이 설정되어 있지 않습니다." ],
      "The field is required.":[ "이 필드는 필수입니다." ],
      "See https://mzl.la/1ZOhoEN (MDN Docs) for more information.":[ "자세한 정보는 https://mzl.la/1ZOhoEN (MDN 문서)에서 볼 수 있습니다." ],
      "The permission type is unsupported.":[ "지원하지 않는 권한 타입입니다." ],
      "See https://mzl.la/1R1n1t0 (MDN Docs) for more information.":[ "자세한 정보는 https://mzl.la/1R1n1t0 (MDN 문서)에서 확인할 수 있습니다." ],
      "See https://mzl.la/2Qn0fWC (MDN Docs) for more information.":[ "자세한 정보는 https://mzl.la/2Qn0fWC (MDN 문서)에서 확인할 수 있습니다." ],
      "See https://mzl.la/3Woeqv4 (MDN Docs) for more information.":[ "" ],
      "Unknown permission.":[ "알 수 없는 권한입니다." ],
      "%(instancePath)s: the following privileged permissions are only allowed in privileged extensions: %(privilegedPermissions)s.":[ "" ],
      "Invalid host permission.":[ "잘못된 호스트 권한입니다." ],
      "Invalid install origin.":[ "설치 출처가 잘못되었습니다." ],
      "Invalid install origin. A valid origin has - only - a scheme, hostname and optional port. See https://mzl.la/3TEbqbE (MDN Docs) for more information.":[ "" ],
      "The field is invalid.":[ "필드가 유효하지 않습니다." ],
      "\"manifest_version\" in the manifest.json is not a valid value":[ "manifest.json의 \"manifest_version\"이 유효한 값이 아님" ],
      "See https://mzl.la/20PenXl (MDN Docs) for more information.":[ "자세한 정보는 https://mzl.la/20PenXl (MDN 문서)에서 확인할 수 있습니다." ],
      "\"%(property)s\" allows remote code execution in manifest.json":[ "" ],
      "A custom \"%(property)s\" needs additional review.":[ "" ],
      "\"%(property)s\" allows 'eval', which has strong security and performance implications.":[ "" ],
      "In most cases the same result can be achieved differently, therefore it is generally prohibited":[ "" ],
      "The \"name\" property must be a string with no leading/trailing whitespaces.":[ "" ],
      "See http://mzl.la/1STmr48 (MDN Docs) for more information.":[ "자세한 정보는 http://mzl.la/1STmr48 (MDN 문서)에서 확인할 수 있습니다." ],
      "\"update_url\" is not allowed.":[ "\"update_url\"은 허용되지 않습니다." ],
      "\"applications.gecko.update_url\" or \"browser_specific_settings.gecko.update_url\" are not allowed for Mozilla-hosted add-ons.":[ "" ],
      "The \"update_url\" property is not used by Firefox.":[ "Firefox에서는 \"update_url\" 속성을 사용하지 않습니다." ],
      "The \"update_url\" is not used by Firefox in the root of a manifest; your add-on will be updated via the Add-ons site and not your \"update_url\". See: https://mzl.la/25zqk4O":[ "" ],
      "\"strict_max_version\" not required.":[ "\"strict_max_version\"은 필요하지 않습니다." ],
      "\"strict_max_version\" shouldn't be used unless the add-on is expected not to work with future versions of Firefox.":[ "" ],
      "Manifest Version 3 is not fully supported on Firefox for Android.":[ "" ],
      "No \"%(property)s\" property found in manifest.json":[ "" ],
      "\"%(property)s\" is required":[ "" ],
      "An icon defined in the manifest could not be found in the package.":[ "매니페스트에 정의되어 있는 아이콘을 패키지에서 찾을 수 없습니다." ],
      "Icon could not be found at \"%(path)s\".":[ "\"%(path)s\"에서 아이콘을 찾을 수 없습니다." ],
      "A background script defined in the manifest could not be found.":[ "매니페스트에 정의된 백그라운드 스크립트를 찾을 수 없습니다." ],
      "A background page defined in the manifest could not be found.":[ "매니페스트에 정의된 백그라운드 페이지를 찾을 수 없습니다." ],
      "Background script could not be found at \"%(path)s\".":[ "\"%(path)s\"에서 백그라운드 스크립트를 찾을 수 없습니다." ],
      "Background page could not be found at \"%(path)s\".":[ "\"%(path)s\"에서 백그라운드 페이지를 찾을 수 없습니다." ],
      "A content script defined in the manifest could not be found.":[ "매니페스트에 정의된 콘텐트 스크립트를 찾을 수 없습니다." ],
      "A content script css file defined in the manifest could not be found.":[ "매니페스트에 정의된 콘텐트 스크립트 css 파일을 찾을 수 없습니다." ],
      "Content script defined in the manifest could not be found at \"%(path)s\".":[ "매니페스트에 정의된 콘텐트 스크립트를 \"%(path)s\"에서 찾을 수 없습니다." ],
      "Content script css file defined in the manifest could not be found at \"%(path)s\".":[ "매니페스트에 서언된 콘텐트 스크립트 CSS 파일을 \"%(path)s\"에서 찾을 수 없습니다." ],
      "A dictionary file defined in the manifest could not be found.":[ "매니페이스에 정의된 사전 파일을 찾을 수 없습니다." ],
      "Dictionary file defined in the manifest could not be found at \"%(path)s\".":[ "매니페이스에 정의된 사전 파일을 \"%(path)s\"에서 찾을 수 없습니다." ],
      "The manifest contains multiple dictionaries.":[ "매니페스트가 여러 개의 사전을 포함하고 있습니다." ],
      "Multiple dictionaries were defined in the manifest, which is unsupported.":[ "지원되지 않는 여러 개의 사전이 매니페스트에 정의되어 있습니다." ],
      "The manifest contains a dictionaries object, but it is empty.":[ "매니페스트가 사전 객체를 포함하고 있지만 비어 있습니다." ],
      "A dictionaries object was defined in the manifest, but it was empty.":[ "사전 객체는 매니페스트에 정의되었지만 비어 있었습니다." ],
      "The manifest contains a dictionary but no id property.":[ "매니페스트가 사전을 포함하고 있지만 id 속성이 없습니다." ],
      "A dictionary was found in the manifest, but there was no id set.":[ "매니페스트에서 사전이 발견되었지만, id set이 없습니다." ],
      "Please refer to https://github.com/mozilla-extensions/xpi-manifest to learn more about privileged extensions and signing.":[ "" ],
      "Forbidden content found in add-on.":[ "부가 기능에 금지된 콘텐츠가 있습니다." ],
      "This add-on contains forbidden content.":[ "이 부가 기능은 금지된 콘텐츠를 포함하고 있습니다." ],
      "Icons must be square.":[ "아이콘은 사각형이어야 합니다." ],
      "Icon at \"%(path)s\" must be square.":[ "\"%(path)s\"에 있는 아이콘은 사각형이어야 합니다." ],
      "The size of the icon does not match the manifest.":[ "아이콘의 크기가 매니페스트와 일치하지 않습니다." ],
      "Expected icon at \"%(path)s\" to be %(expected)d pixels wide but was %(actual)d.":[ "" ],
      "\"%(fieldName)s\" is ignored for non-privileged add-ons.":[ "" ],
      "Corrupt image file":[ "이미지 파일 손상됨" ],
      "Expected icon file at \"%(path)s\" is corrupted":[ "\"%(path)s\"에 있는 예상 아이콘 파일이 손상됨" ],
      "This property has been deprecated.":[ "이 속성은 더 이상 사용되지 않습니다." ],
      "This theme LWT alias has been removed in Firefox 70.":[ "이 테마 LWT 별칭은 Firefox 70 에서 제외되었습니다." ],
      "See https://mzl.la/2T11Lkc (MDN Docs) for more information.":[ "자세한 정보는 https://mzl.la/2T11Lkc (MDN 문서)에서 확인할 수 있습니다." ],
      "Theme image for \"%(type)s\" could not be found at \"%(path)s\"":[ "\"%(path)s\"에서 \"%(type)s\"의 테마 이미지를 찾을 수 없습니다." ],
      "\"%(fieldName)s\" manifest field is only used for privileged and temporarily installed extensions.":[ "" ],
      "Corrupted theme image file":[ "테마 이미지 파일 손상됨" ],
      "Theme image file at \"%(path)s\" is corrupted":[ "\"%(path)s\"의 테마 이미지 파일 손상됨" ],
      "Theme image file has an unsupported file extension":[ "테마 이미지 파일에 지원하지 않는 파일 확장자가 있음" ],
      "Theme image file at \"%(path)s\" has an unsupported file extension":[ "\"%(path)s\"의 테마 이미지 파일에 지원하지 않는 파일 확장자가 있음" ],
      "Theme image file has an unsupported mime type":[ "테마 이미지 파일에 지원하지 않는 MIME 타입이 있음" ],
      "Theme image file at \"%(path)s\" has the unsupported mime type \"%(mime)s\"":[ "\"%(path)s\"에 있는 테마 이미지 파일에 지원하지 않는 MIME 타입 \"%(mime)s\"이 있음" ],
      "Theme image file mime type does not match its file extension":[ "MIME 타입 테마 이미지 파일이 파일 확장자와 일치하지 않음" ],
      "Theme image file extension at \"%(path)s\" does not match its actual mime type \"%(mime)s\"":[ "\"%(path)s\"에 있는 테마 이미지 파일 확장자가 실제 MIME 타입 \"%(mime)s\"와 일치하지 않음" ],
      "The \"default_locale\" is missing localizations.":[ "\"default_locale\"은 현지화가 되어 있지 않습니다." ],
      "The \"default_locale\" value is specified in the manifest, but no matching \"messages.json\" in the \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "" ],
      "The \"default_locale\" is missing but \"_locales\" exist.":[ "\"default_locale\"은 존재하지 않지만 \"_locales\"는 존재합니다." ],
      "The \"default_locale\" value is not specifed in the manifest, but a \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "" ],
      "Unsupported image extension":[ "지원되지 않는 이미지 확장자" ],
      "Icons should be one of JPG/JPEG, WebP, GIF, PNG or SVG.":[ "아이콘은 JPG/JPEG, WebP, GIF, PNG 또는 SVG 중 하나여야 합니다." ],
      "\"applications\" property overridden by \"browser_specific_settings\" property":[ "\"browser_specific_settings\" 속성으로 재정의된 \"applications\" 속성" ],
      "The \"applications\" property is being ignored because it is superseded by the \"browser_specific_settings\" property which is also defined in your manifest. Consider removing applications.":[ "" ],
      "Empty language directory":[ "빈 언어 디렉터리" ],
      "messages.json file missing in \"%(path)s\"":[ "\"%(path)s\"에 messages.json 파일이 없음" ],
      "Manifest key not supported by the specified minimum Firefox version":[ "매니페스트 키가 명시된 최소 Firefox 버전에서 지원되지 않음" ],
      "\"strict_min_version\" requires Firefox %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "" ],
      "\"%(fieldName)s\" is not supported in manifest versions %(versionRange)s.":[ "" ],
      "Permission not supported by the specified minimum Firefox version":[ "명시된 최소 Firefox 버전에서 지원하지 않는 권한" ],
      "\"%(fieldName)s\" is not supported.":[ "" ],
      "Manifest key not supported by the specified minimum Firefox for Android version":[ "매니페스트 키가 명시된 최소 Android 용 Firefox 버전에서 지원되지 않음" ],
      "\"strict_min_version\" requires Firefox for Android %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "" ],
      "Permission not supported by the specified minimum Firefox for Android version":[ "명시된 최소 Android 용 Firefox 버전에서 지원하지 않는 권한" ],
      "Linking to \"addons.mozilla.org\" is not allowed":[ "\"addons.mozilla.org\"에 대한 링크는 허용되지 않습니다." ],
      "Links directing to \"addons.mozilla.org\" are not allowed to be used for homepage":[ "\"addons.mozilla.org\"로 연결되는 링크는 홈페이지에 사용할 수 없습니다." ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above":[ "" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above. Please update your manifest.json version to specify a minimum Firefox version.":[ "" ],
      "The extension ID is required in Manifest Version 3 and above.":[ "매니페스트 버전 3 이상에서는 확장 ID가 필요합니다." ],
      "See https://mzl.la/3PLZYdo for more information.":[ "자세한 정보는 https://mzl.la/3PLZYdo 에서 확인할 수 있습니다." ],
      "%(instancePath)s: Privileged extensions should declare privileged permissions.":[ "" ],
      "This extension does not declare any privileged permission. It does not need to be signed with the privileged certificate. Please upload it directly to https://addons.mozilla.org/.":[ "" ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for privileged extensions.":[ "" ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for extensions that include privileged manifest fields.":[ "" ],
      "%(instancePath)s: privileged manifest fields are only allowed in privileged extensions.":[ "" ],
      "This extension does not include the \"mozillaAddons\" permission, which is required for privileged extensions.":[ "" ],
      "Cannot use actions in hidden add-ons.":[ "" ],
      "The hidden and browser_action/page_action (or action in Manifest Version 3 and above) properties are mutually exclusive.":[ "" ],
      "Use \"browser_specific_settings\" instead of \"applications\".":[ "" ],
      "The \"applications\" property in the manifest is deprecated and will no longer be accepted in Manifest Version 3 and above.":[ "" ],
      "\"applications\" is no longer allowed in Manifest Version 3 and above.":[ "" ],
      "The \"applications\" property in the manifest is no longer allowed in Manifest Version 3 and above. Use \"browser_specific_settings\" instead.":[ "" ],
      "The version string should be simplified because it won't be compatible with Manifest Version 3 and above.":[ "" ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros will no longer be allowed. Letters will no longer be allowed either. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "" ],
      "The version string should be simplified.":[ "" ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros are not allowed. Letters are no longer allowed. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "" ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported in manifest versions %(versionRange)s.":[ "" ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported.":[ "" ] } } }