module.exports = { domain:"messages",
  locale_data:{ messages:{ "":{ domain:"messages",
        plural_forms:"nplurals=2; plural=(n != 1);",
        lang:"sv_SE" },
      "Validation Summary:":[ "Valideringsöversikt:" ],
      Code:[ "Kod" ],
      Message:[ "Meddelande" ],
      Description:[ "Beskrivning" ],
      File:[ "Fil" ],
      Line:[ "Rad" ],
      Column:[ "Kolumn" ],
      "Invalid manifest version range requested: --min-manifest-version (currently set to %(minManifestVersion)s) should not be greater than --max-manifest-version (currently set to %(maxManifestVersion)s).":[ "Ogiltigt manifestversionsområde begärt: --min-manifest-version (för närvarande inställt på %(minManifestVersion)s) bör inte vara större än --max-manifest-version (för närvarande inställt på %(maxManifestVersion)s)." ],
      "Your FTL is not valid.":[ "Din FTL är inte giltig." ],
      "Your FTL file could not be parsed.":[ "Din FTL-fil kunde inte tolkas." ],
      "Remote scripts are not allowed as per the Add-on Policies.":[ "Fjärrskript är inte tillåtna enligt tilläggspolicyn." ],
      "Please include all scripts in the add-on. For more information, refer to https://mzl.la/2uEOkYp.":[ "Inkludera alla skript i tillägget. För mer information, se https://mzl.la/2uEOkYp." ],
      "Inline scripts blocked by default":[ "Inline-skript blockeras som standard" ],
      "Default CSP rules prevent inline JavaScript from running (https://mzl.la/2pn32nd).":[ "Standard CSP-regler förhindrar att inline JavaScript körs (https://mzl.la/2pn32nd)." ],
      "Unadvised 3rd-party JS library":[ "Ej rekommenderat JS-biblioteket från tredje part" ],
      "Your add-on uses a JavaScript library we do not recommend. Read more: https://bit.ly/1TRIyZY":[ "Ditt tillägg använder ett JavaScript-bibliotek som vi inte rekommenderar. Läs mer: https://bit.ly/1TRIyZY" ],
      "Known JS library detected":[ "Känt JS-bibliotek upptäckt" ],
      "JavaScript libraries are discouraged for simple add-ons, but are generally accepted.":[ "JavaScript-bibliotek avråds för enkla tillägg, men är allmänt accepterade." ],
      "Due to both security and performance concerns, this may not be set using dynamic values which have not been adequately sanitized. This can lead to security issues or fairly serious performance degradation.":[ "På grund av både säkerhets- och prestandaproblem kanske detta inte ställs in med dynamiska värden som inte har sanerats tillräckligt. Detta kan leda till säkerhetsproblem eller ganska allvarlig prestandaförsämring." ],
      "{{api}} is not supported":[ "{{api}} stöds ej" ],
      "This API has not been implemented by Firefox.":[ "Detta API har inte implementerats av Firefox." ],
      "\"{{api}}\" has been removed in Manifest Version 3 (`manifest_version` property)":[ "\"{{api}}\" har tagits bort i Manifest Version 3 (egenskapen `manifest_version`)" ],
      "{{api}} is deprecated":[ "{{api}} är föråldrat" ],
      "This API has been deprecated by Firefox.":[ "Detta API har föråldrats av Firefox." ],
      "Content script file could not be found.":[ "Innehållsskriptfilen kunde inte hittas." ],
      "\"{{api}}\" is deprecated or unimplemented":[ "\"{{api}}\" är föråldrat eller ej implementerat" ],
      "Content script file could not be found":[ "Innehållsskriptfilen kunde inte hittas" ],
      "\"%(api)s\" can cause issues when loaded temporarily":[ "\"%(api)s\" kan orsaka problem när den laddas tillfälligt" ],
      "This API can cause issues when loaded temporarily using about:debugging in Firefox unless you specify \"browser_specific_settings.gecko.id\" in the manifest. Please see: https://mzl.la/2hizK4a for more.":[ "Detta API kan orsaka problem när det laddas tillfälligt med about:debugging i Firefox om du inte anger \"browser_specific_settings.gecko.id\" i manifestet. Se: https://mzl.la/2hizK4a för mer." ],
      "{{api}} is not supported in Firefox version {{minVersion}}":[ "{{api}} stöds inte i Firefox version {{minVersion}}" ],
      "This API is not implemented by the given minimum Firefox version":[ "Detta API har inte implementerats den givna lägsta Firefox-versionen" ],
      "{{api}} is not supported in Firefox for Android version {{minVersion}}":[ "{{api}} stöds inte i Firefox för Android version {{minVersion}}" ],
      "This API is not implemented by the given minimum Firefox for Android version":[ "Detta API har inte implementerats av den givna lägsta Firefox-versionen för Android" ],
      "Content script file name should not be empty.":[ "Innehållsskriptfilnamnet ska inte vara tomt." ],
      "Content script file name should not be empty":[ "Innehållsskriptfilnamnet ska inte vara tomt" ],
      "\"%(method)s\" called with a non-literal uri":[ "\"%(method)s\" anropas med en icke-bokstavlig uri" ],
      "Calling \"%(method)s\" with variable parameters can result in potential security vulnerabilities if the variable contains a remote URI. Consider using 'window.open' with the 'chrome=no' flag.":[ "Att anropa \"%(method)s\" med variabla parametrar kan resultera i potentiella säkerhetsbrister om variabeln innehåller en fjärr-URI. Överväg att använda 'window.open' med flaggan 'chrome=no'." ],
      "\"%(method)s\" called with non-local URI":[ "\"%(method)s\" anropas med icke-lokal URI" ],
      "Calling \"%(method)s\" with a non-local URI will result in the dialog being opened with chrome privileges.":[ "Att anropa \"%(method)s\" med en icke-lokal URI kommer att resultera i att dialogrutan öppnas med chrome-privilegier." ],
      "JavaScript syntax error":[ "JavaScript-syntaxfel" ],
      "There is a JavaScript syntax error in your code, which might be related to some experimental JavaScript features that aren't an official part of the language specification and therefore not supported yet. The validation cannot continue on this file.":[ "Det finns ett JavaScript-syntaxfel i din kod, som kan vara relaterat till vissa experimentella JavaScript-funktioner som inte är en officiell del av språkspecifikationen och därför inte stöds ännu. Valideringen kan inte fortsätta på den här filen." ],
      "Evaluation of strings as code can lead to security vulnerabilities and performance issues, even in the most innocuous of circumstances. Please avoid using `eval` and the `Function` constructor when at all possible.":[ "Utvärdering av strängar som kod kan leda till säkerhetsbrister och prestandaproblem, även under de mest ofarliga omständigheter. Undvik att använda \"eval\" och \"Function\"-konstruktorn när det är möjligt." ],
      "setTimeout, setInterval and execScript functions should be called only with function expressions as their first argument":[ "Funktionerna setTimeout, setInterval och execScript ska endast anropas med funktionsuttryck som första argument" ],
      "Unexpected global passed as an argument":[ "Oväntad global skickad som ett argument" ],
      "Passing a global as an argument is not recommended. Please make this a var instead.":[ "Att skicka en global som argument rekommenderas inte. Gör detta till en var istället." ],
      "Use of document.write strongly discouraged.":[ "Användning av document.write rekommenderas inte." ],
      "document.write will fail in many circumstances when used in extensions, and has potentially severe security repercussions when used improperly. Therefore, it should not be used.":[ "document.write kommer att misslyckas i många fall när det används i tillägg och har potentiellt allvarliga säkerhetsmässiga återverkningar när den används felaktigt. Därför bör den inte användas." ],
      "Banned 3rd-party JS library":[ "Förbjudet JS-bibliotek från tredje part" ],
      "Your add-on uses a JavaScript library we consider unsafe. Read more: https://bit.ly/1TRIyZY":[ "Ditt tillägg använder ett JavaScript-bibliotek som vi anser vara osäkert. Läs mer: https://bit.ly/1TRIyZY" ],
      "Your JSON contains block comments.":[ "Din JSON innehåller blockkommentarer." ],
      "Only line comments (comments beginning with \"//\") are allowed in JSON files. Please remove block comments (comments beginning with \"/*\")":[ "Endast radkommentarer (kommentarer som börjar med \"//\") är tillåtna i JSON-filer. Ta bort blockkommentarer (kommentarer som börjar med \"/*\")" ],
      "Duplicate keys are not allowed in JSON files.":[ "Dubble nycklar är inte tillåtna i JSON-filer." ],
      "Duplicate key found in JSON file.":[ "Dubblettnyckel hittades i JSON-filen." ],
      "Your JSON is not valid.":[ "Din JSON är inte giltig." ],
      "Your JSON file could not be parsed.":[ "Din JSON-fil kunde inte tolkas." ],
      "Reserved filename found.":[ "Reserverat filnamn hittades." ],
      "Files whose names are reserved have been found in the add-on. Please refrain from using them and rename your files.":[ "Filer vars namn är reserverade har hittats i tillägget. Vänligen avstå från att använda dem och byt namn på dina filer." ],
      "The package is invalid. It may contain entries using invalid characters, as an example using '\\' as a path separator is not allowed in Firefox. Try to recreate your add-on package (ZIP) and make sure all entries are using '/' as the path separator.":[ "Paketet är ogiltigt. Det kan innehålla poster med ogiltiga tecken, som ett exempel är det inte tillåtet att använda '\\' som sökvägsavgränsare i Firefox. Försök att återskapa ditt tilläggspaket (ZIP) och se till att alla poster använder '/' som sökvägsavgränsare." ],
      "We were unable to decompress the zip file.":[ "Vi kunde inte dekomprimera zip-filen." ],
      "manifest.json was not found":[ "manifest.json hittades inte" ],
      "No manifest.json was found at the root of the extension. The package file must be a ZIP of the extension's files themselves, not of the containing directory. See: https://mzl.la/2r2McKv for more on packaging.":[ "Ingen manifest.json hittades i roten av tillägget. Paketfilen måste vara en ZIP-fil med filtilläggets filer själva, inte av den innehållande katalogen. Se: https://mzl.la/2r2McKv för mer om packningar." ],
      "File is too large to parse.":[ "Filen är för stor för att tolkas." ],
      "This file is not binary and is too large to parse. Files larger than %(maxFileSizeToParseMB)sMB will not be parsed. Consider moving large lists of data out of JavaScript files and into JSON files, or splitting very large files into smaller ones.":[ "Den här filen är inte binär och är för stor för att tolkas. Filer större än %(maxFileSizeToParseMB)s MB kommer inte att tolkas. Överväg att flytta stora listor med data från JavaScript-filer och till JSON-filer eller dela upp mycket stora filer i mindre." ],
      "Hidden file flagged":[ "Dold fil flaggad" ],
      "Hidden files complicate the review process and can contain sensitive information about the system that generated the add-on. Please modify the packaging process so that these files aren't included.":[ "Dolda filer komplicerar granskningen och kan innehålla känslig information om systemet som genererade tillägget. Ändra paketeringsprocessen så att dessa filer inte ingår." ],
      "Package contains duplicate entries":[ "Paket innehåller dubbla poster" ],
      "Flagged filename found":[ "Flaggat filnamn hittades" ],
      "Files were found that are either unnecessary or have been included unintentionally. They should be removed.":[ "Filer hittades som antingen är onödiga eller som har inkluderats oavsiktligt. De bör tas bort." ],
      "The package contains multiple entries with the same name. This practice has been banned. Try unzipping and re-zipping your add-on package and try again.":[ "Paketet innehåller flera poster med samma namn. Denna praxis har förbjudits. Försök att packa upp och zippa om ditt tilläggspaket och försök igen." ],
      "Flagged file extensions found":[ "Flaggad filändelse hittades" ],
      "Flagged file type found":[ "Flaggad filtyp hittades" ],
      "Files whose names end with flagged extensions have been found in the add-on. The extension of these files are flagged because they usually identify binary components. Please see https://bit.ly/review-policy for more information on the binary content review process.":[ "Filer vars namn slutar med flaggade tillägg har hittats i tillägget. Tillägget av dessa filer flaggas eftersom de vanligtvis identifierar binära komponenter. Se https://bit.ly/review-policy för mer information om processen för granskning av binärt innehåll." ],
      "Package already signed":[ "Paket redan signerat" ],
      "Add-ons which are already signed will be re-signed when published on AMO. This will replace any existing signatures on the add-on.":[ "Tillägg som redan är signerade kommer att återsigneras när de publiceras på AMO. Detta kommer att ersätta alla befintliga signaturer för tillägget." ],
      "Firefox add-ons are not allowed to run coin miners.":[ "Firefox tillägg får inte köra coin-miners." ],
      "We do not allow coinminer scripts to be run inside WebExtensions. See https://github.com/mozilla/addons-linter/issues/1643 for more details.":[ "Vi tillåter inte att coinminer-skript körs inuti WebExtensions. Se https://github.com/mozilla/addons-linter/issues/1643 för mer information." ],
      "String name is reserved for a predefined message":[ "Strängnamn är reserverat för ett fördefinierat meddelande" ],
      "String names starting with @@ get translated to built-in constants (https://mzl.la/2BL9ZjE).":[ "Strängnamn som börjar med @@ översätts till inbyggda konstanter (https://mzl.la/2BL9ZjE)." ],
      "String name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2Eztyi5).":[ "Strängnamn ska bara innehålla alfanumeriska tecken, _ och @ (https://mzl.la/2Eztyi5)." ],
      "Placeholder for message is missing":[ "Platshållare för meddelande saknas" ],
      "A placeholder used in the message is not defined.":[ "En platshållare som används i meddelandet är inte definierad." ],
      "Placeholder name contains invalid characters":[ "Namn på platshållare innehåller ogiltiga tecken" ],
      "Placeholder name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2ExbYez).":[ "Platshållarens namn bör endast innehålla alfanumeriska tecken, _ och @ (https://mzl.la/2ExbYez)." ],
      "Placeholder is missing the content property":[ "Platshållaren saknar innehållsegenskapen" ],
      "A placeholder needs a content property defining the replacement of it (https://mzl.la/2DT1MQd)":[ "En platshållare behöver en innehållsegenskap som definierar ersättningen av den (https://mzl.la/2DT1MQd)" ],
      "Translation string is missing the message property":[ "Översättningssträngen saknar meddelandegenskapen" ],
      "No \"message\" message property is set for a string (https://mzl.la/2DSBTjA).":[ "Ingen meddelandeegenskap \"message\" är satt för en sträng (https://mzl.la/2DSBTjA)." ],
      "The field is required.":[ "Fältet är obligatoriskt." ],
      "See https://mzl.la/1ZOhoEN (MDN Docs) for more information.":[ "Se https://mzl.la/1ZOhoEN (MDN Docs) för mer information." ],
      "The permission type is unsupported.":[ "Behörighetstyp stöds inte." ],
      "See https://mzl.la/1R1n1t0 (MDN Docs) for more information.":[ "Se https://mzl.la/1R1n1t0 (MDN Docs) för mer information." ],
      "See https://mzl.la/2Qn0fWC (MDN Docs) for more information.":[ "Se https://mzl.la/2Qn0fWC (MDN Docs) för mer information." ],
      "See https://mzl.la/3Woeqv4 (MDN Docs) for more information.":[ "Se https://mzl.la/3Woeqv4 (MDN Docs) för mer information." ],
      "Unknown permission.":[ "Okänd behörighet." ],
      "%(instancePath)s: the following privileged permissions are only allowed in privileged extensions: %(privilegedPermissions)s.":[ "%(instancePath)s: följande privilegierade behörigheter är endast tillåtna i privilegierade tillägg: %(privilegedPermissions)s." ],
      "Invalid host permission.":[ "Ogiltig värdbehörighet." ],
      "Invalid install origin.":[ "Ogiltigt installationsursprung." ],
      "Invalid install origin. A valid origin has - only - a scheme, hostname and optional port. See https://mzl.la/3TEbqbE (MDN Docs) for more information.":[ "Ogiltigt installationsursprung. Ett giltigt ursprung har - endast - ett schema, värdnamn och valfri port. Se https://mzl.la/3TEbqbE (MDN Docs) för mer information." ],
      "The field is invalid.":[ "Fältet är ogiltigt." ],
      "\"manifest_version\" in the manifest.json is not a valid value":[ "\"manifest_version\" i manifest.json har inte ett giltigt värde" ],
      "See https://mzl.la/20PenXl (MDN Docs) for more information.":[ "Se https://mzl.la/20PenXl (MDN Docs) för mer information." ],
      "\"%(property)s\" allows remote code execution in manifest.json":[ "\"%(property)s\" tillåter fjärrkörning av kod i manifest.json" ],
      "A custom \"%(property)s\" needs additional review.":[ "En anpassad \"%(property)s\" behöver ytterligare granskning." ],
      "\"%(property)s\" allows 'eval', which has strong security and performance implications.":[ "\"%(property)s\" tillåter \"eval\", vilket har starka säkerhets- och prestandaimplikationer." ],
      "In most cases the same result can be achieved differently, therefore it is generally prohibited":[ "I de flesta fall kan samma resultat uppnås på olika sätt, därför är det generellt förbjudet" ],
      "The \"name\" property must be a string with no leading/trailing whitespaces.":[ "Egenskapen \"name\" måste vara en sträng utan inledande/efterföljande blanksteg." ],
      "See http://mzl.la/1STmr48 (MDN Docs) for more information.":[ "Se http://mzl.la/1STmr48 (MDN Docs) för mer information." ],
      "\"update_url\" is not allowed.":[ "\"update_url\" är inte tillåtet." ],
      "\"applications.gecko.update_url\" or \"browser_specific_settings.gecko.update_url\" are not allowed for Mozilla-hosted add-ons.":[ "\"applications.gecko.update_url\" eller \"browser_specific_settings.gecko.update_url\" är inte tillåtna för tillägg på Mozilla." ],
      "The \"update_url\" property is not used by Firefox.":[ "Egenskapen \"update_url\" används inte av Firefox." ],
      "The \"update_url\" is not used by Firefox in the root of a manifest; your add-on will be updated via the Add-ons site and not your \"update_url\". See: https://mzl.la/25zqk4O":[ "\"update_url\" används inte av Firefox i roten av ett manifest; ditt tillägg kommer att uppdateras via tilläggswebbplatsen och inte din \"update_url\". Se: https://mzl.la/25zqk4O" ],
      "\"strict_max_version\" not required.":[ "\"strict_max_version\" krävs inte." ],
      "\"strict_max_version\" shouldn't be used unless the add-on is expected not to work with future versions of Firefox.":[ "\"strict_max_version\" bör inte användas om inte tillägget ska sluta fungera med framtida versioner av Firefox." ],
      "Manifest Version 3 is not fully supported on Firefox for Android.":[ "Manifest version 3 stöds inte fullt ut på Firefox för Android." ],
      "No \"%(property)s\" property found in manifest.json":[ "Ingen \"%(property)s\"-egenskap hittades i manifest.json" ],
      "\"%(property)s\" is required":[ "\"%(property)s\" krävs" ],
      "An icon defined in the manifest could not be found in the package.":[ "En ikon definierad i manifestet kunde inte hittas i paketet." ],
      "Icon could not be found at \"%(path)s\".":[ "Ikonen kunde inte hittas på \"%(path)s\"." ],
      "A background script defined in the manifest could not be found.":[ "Ett bakgrundsskript definierat i manifestet kunde inte hittas." ],
      "A background page defined in the manifest could not be found.":[ "En bakgrundssida definierad i manifestet kunde inte hittas." ],
      "Background script could not be found at \"%(path)s\".":[ "Bakgrundsskript kunde inte hittas på \"%(path)s\"." ],
      "Background page could not be found at \"%(path)s\".":[ "Bakgrundssidan kunde inte hittas på \"%(path)s\"." ],
      "A content script defined in the manifest could not be found.":[ "Ett innehållsskript som definierats i manifestet kunde inte hittas." ],
      "A content script css file defined in the manifest could not be found.":[ "En css-fil med innehållsskript definierat i manifestet kunde inte hittas." ],
      "Content script defined in the manifest could not be found at \"%(path)s\".":[ "Innehållsskript definierat i manifestet kunde inte hittas på \"%(path)s\"." ],
      "Content script css file defined in the manifest could not be found at \"%(path)s\".":[ "Innehållsskriptets css-fil definierat i manifestet kunde inte hittas på \"%(path)s\"." ],
      "A dictionary file defined in the manifest could not be found.":[ "En ordboksfil som definierats i manifestet kunde inte hittas." ],
      "Dictionary file defined in the manifest could not be found at \"%(path)s\".":[ "Ordbokfil definierad i manifestet kunde inte hittas på \"%(path)s\"." ],
      "The manifest contains multiple dictionaries.":[ "Manifestet innehåller flera ordböcker." ],
      "Multiple dictionaries were defined in the manifest, which is unsupported.":[ "Flera ordböcker definierades i manifestet, vilket inte stöds." ],
      "The manifest contains a dictionaries object, but it is empty.":[ "Manifestet innehåller ett ordboksobjekt, men det är tomt." ],
      "A dictionaries object was defined in the manifest, but it was empty.":[ "Ett ordboksobjekt definierades i manifestet, men det var tomt." ],
      "The manifest contains a dictionary but no id property.":[ "Manifestet innehåller en ordbok men ingen ID-egenskap." ],
      "A dictionary was found in the manifest, but there was no id set.":[ "En ordbok hittades i manifestet, men det fanns ingen angiven ID." ],
      "Please refer to https://github.com/mozilla-extensions/xpi-manifest to learn more about privileged extensions and signing.":[ "Se https://github.com/mozilla-extensions/xpi-manifest för att lära dig mer om privilegierade tillägg och signering." ],
      "Forbidden content found in add-on.":[ "Förbjudet innehåll hittades i tillägget." ],
      "This add-on contains forbidden content.":[ "Detta tillägg innehåller förbjudet innehåll." ],
      "Icons must be square.":[ "Ikoner måste vara kvadratiska." ],
      "Icon at \"%(path)s\" must be square.":[ "Ikonen på \"%(path)s\" måste vara kvadratisk." ],
      "The size of the icon does not match the manifest.":[ "Ikonens storlek matchar inte manifestet." ],
      "Expected icon at \"%(path)s\" to be %(expected)d pixels wide but was %(actual)d.":[ "Förväntade ikonen vid \"%(path)s\" att vara %(expected)d pixlar bred men var %(actual)d." ],
      "\"%(fieldName)s\" is ignored for non-privileged add-ons.":[ "\"%(fieldName)s\" ignoreras för icke-privilegierade tillägg." ],
      "Corrupt image file":[ "Skadad bildfil" ],
      "Expected icon file at \"%(path)s\" is corrupted":[ "Förväntad ikonfil på \"%(path)s\" är skadad" ],
      "This property has been deprecated.":[ "Den här egenskapen är föråldrad." ],
      "This theme LWT alias has been removed in Firefox 70.":[ "Detta tema LWT-alias har tagits bort i Firefox 70." ],
      "See https://mzl.la/2T11Lkc (MDN Docs) for more information.":[ "Se https://mzl.la/2T11Lkc (MDN Docs) för mer information." ],
      "Theme image for \"%(type)s\" could not be found at \"%(path)s\"":[ "Temabild för \"%(type)s\" kunde inte hittas på \"%(path)s\"" ],
      "\"%(fieldName)s\" manifest field is only used for privileged and temporarily installed extensions.":[ "Manifestfältet \"%(fieldName)s\" används endast för privilegierade och tillfälligt installerade tillägg." ],
      "Corrupted theme image file":[ "Skadad temabildfil" ],
      "Theme image file at \"%(path)s\" is corrupted":[ "Temabildfil \"%(path)s\" är skadad" ],
      "Theme image file has an unsupported file extension":[ "Temabildfil har ett filändelse som inte stöds" ],
      "Theme image file at \"%(path)s\" has an unsupported file extension":[ "Temabildfil på \"%(path)s\" har en filändelse som inte stöds" ],
      "Theme image file has an unsupported mime type":[ "Temabildfil har en mime-typ som inte stöds" ],
      "Theme image file at \"%(path)s\" has the unsupported mime type \"%(mime)s\"":[ "Temabildfil på \"%(path)s\" har en mime-typ \"%(mime)s\" som inte stöds" ],
      "Theme image file mime type does not match its file extension":[ "Mime-typ för temabildfil matchar inte filändelsen" ],
      "Theme image file extension at \"%(path)s\" does not match its actual mime type \"%(mime)s\"":[ "Filändelsen för temabildfil på \"%(path)s\" matchar inte med den verkliga mime-typen \"%(mime)s\"" ],
      "The \"default_locale\" is missing localizations.":[ "\"default_locale\" saknar lokaliseringar." ],
      "The \"default_locale\" value is specified in the manifest, but no matching \"messages.json\" in the \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "Värdet \"default_locale\" anges i manifestet, men det finns inga matchande \"messages.json\" i katalogen \"_locales\". Se: https://mzl.la/2hjcaEE" ],
      "The \"default_locale\" is missing but \"_locales\" exist.":[ "\"default_locale\" saknas men \"_locales\" existerar." ],
      "The \"default_locale\" value is not specifed in the manifest, but a \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "Värdet \"default_locale\" är inte specificerat i manifestet, men det finns en \"_locales\"-katalog. Se: https://mzl.la/2hjcaEE" ],
      "Unsupported image extension":[ "Filändelse för bild stöds ej" ],
      "Icons should be one of JPG/JPEG, WebP, GIF, PNG or SVG.":[ "Ikoner bör vara av JPG/JPEG, WebP, GIFF, PNG eller SVG." ],
      "\"applications\" property overridden by \"browser_specific_settings\" property":[ "Egenskapen \"applications\" åsidosatt av egenskapen \"browser_specific_settings\"" ],
      "The \"applications\" property is being ignored because it is superseded by the \"browser_specific_settings\" property which is also defined in your manifest. Consider removing applications.":[ "Egenskapen \"applications\" ignoreras eftersom den ersätts av egenskapen \"browser_specific_settings\" som också definieras i ditt manifest. Överväg att ta bort applikationer." ],
      "Empty language directory":[ "Tom språkkatalog" ],
      "messages.json file missing in \"%(path)s\"":[ "messages.json fil saknas i \"%(path)s\"" ],
      "Manifest key not supported by the specified minimum Firefox version":[ "Manifest-nyckel stöds inte av den angivna lägsta Firefox-versionen" ],
      "\"strict_min_version\" requires Firefox %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "\"strict_min_version\" kräver Firefox %(minVersion)s, som släpptes innan version %(versionAdded)s introducerade stöd för \"%(key)s\"." ],
      "\"%(fieldName)s\" is not supported in manifest versions %(versionRange)s.":[ "\"%(fieldName)s\" stöds inte i manifestversionerna %(versionRange)s." ],
      "Permission not supported by the specified minimum Firefox version":[ "Behörighet stöds inte av den angivna lägsta Firefox-versionen" ],
      "\"%(fieldName)s\" is not supported.":[ "\"%(fieldName)s\" stöds inte." ],
      "Manifest key not supported by the specified minimum Firefox for Android version":[ "Manifest-nyckel stöds inte av den angivna lägsta versionen av Firefox för Android" ],
      "\"strict_min_version\" requires Firefox for Android %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "\"strict_min_version\" kräver Firefox för Android %(minVersion)s, som släpptes innan version %(versionAdded)s introducerade stöd för \"%(key)s\"." ],
      "Permission not supported by the specified minimum Firefox for Android version":[ "Behörighet stöds inte av den angivna lägsta Firefox-versionen för Android" ],
      "Linking to \"addons.mozilla.org\" is not allowed":[ "Det är inte tillåtet att länka till \"addons.mozilla.org\"." ],
      "Links directing to \"addons.mozilla.org\" are not allowed to be used for homepage":[ "Länkar som leder till \"addons.mozilla.org\" är inte tillåtna att användas för hemsidan" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above":[ "Behörigheten \"%(permission)s\" kräver att \"strict_min_version\" är inställd på \"%(minFirefoxVersion)s\" eller högre" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above. Please update your manifest.json version to specify a minimum Firefox version.":[ "Behörigheten \"%(permission)s\" kräver att \"strict_min_version\" är inställd på \"%(minFirefoxVersion)s\" eller högre. Uppdatera din manifest.json-version för att ange en minsta Firefox-version." ],
      "The extension ID is required in Manifest Version 3 and above.":[ "Tilläggs-ID krävs i Manifest version 3 och senare." ],
      "See https://mzl.la/3PLZYdo for more information.":[ "Se https://mzl.la/3PLZYdo för mer information." ],
      "%(instancePath)s: Privileged extensions should declare privileged permissions.":[ "%(instancePath)s: Privilegerade tillägg bör deklarera privilegierade behörigheter." ],
      "This extension does not declare any privileged permission. It does not need to be signed with the privileged certificate. Please upload it directly to https://addons.mozilla.org/.":[ "Detta tillägg deklarerar inte någon privilegierad behörighet. Det behöver inte signeras med det privilegierade certifikatet. Ladda upp den direkt till https://addons.mozilla.org/." ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for privileged extensions.":[ "%(instancePath)s: Behörigheten \"mozillaAddons\" krävs för privilegierade tillägg." ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for extensions that include privileged manifest fields.":[ "%(instancePath)s: Behörigheten \"mozillaAddons\" krävs för tillägg som inkluderar privilegierade manifestfält." ],
      "%(instancePath)s: privileged manifest fields are only allowed in privileged extensions.":[ "%(instancePath)s: privilegierade manifestfält är endast tillåtna i privilegierade tillägg." ],
      "This extension does not include the \"mozillaAddons\" permission, which is required for privileged extensions.":[ "Det här tillägget inkluderar inte behörigheten \"mozillaAddons\", som krävs för privilegierade tillägg." ],
      "Cannot use actions in hidden add-ons.":[ "Kan inte använda åtgärder i dolda tillägg." ],
      "The hidden and browser_action/page_action (or action in Manifest Version 3 and above) properties are mutually exclusive.":[ "Egenskaperna hidden och browser_action/page_action (eller action i Manifest version 3 och högre) utesluter varandra." ],
      "Use \"browser_specific_settings\" instead of \"applications\".":[ "Använd \"browser_specific_settings\" istället för \"applications\"." ],
      "The \"applications\" property in the manifest is deprecated and will no longer be accepted in Manifest Version 3 and above.":[ "Egenskapen \"apps\" i manifestet är föråldrad och kommer inte längre att accepteras i Manifest version 3 och senare." ],
      "\"applications\" is no longer allowed in Manifest Version 3 and above.":[ "\"applikationer\" är inte längre tillåtna i Manifest version 3 och högre." ],
      "The \"applications\" property in the manifest is no longer allowed in Manifest Version 3 and above. Use \"browser_specific_settings\" instead.":[ "Egenskapen \"apps\" i manifestet är inte längre tillåten i Manifest version 3 och senare. Använd \"browser_specific_settings\" istället." ],
      "The version string should be simplified because it won't be compatible with Manifest Version 3 and above.":[ "Versionssträngen bör förenklas eftersom den inte kommer att vara kompatibel med Manifest version 3 och högre." ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros will no longer be allowed. Letters will no longer be allowed either. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "Versionen ska vara en sträng med 1 till 4 nummer separerade med punkter. Varje nummer bör ha upp till 9 siffror och inledande nollor kommer inte längre att tillåtas. Bokstäver kommer inte heller att tillåtas längre. Se https://mzl.la/3h3mCRu (MDN Docs) för mer information." ],
      "The version string should be simplified.":[ "Versionssträngen bör förenklas." ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros are not allowed. Letters are no longer allowed. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "Versionen ska vara en sträng med 1 till 4 nummer separerade med punkter. Varje nummer bör ha upp till 9 siffror och inledande nollor är inte tillåtna. Bokstäver är inte längre tillåtna. Se https://mzl.la/3h3mCRu (MDN Docs) för mer information." ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported in manifest versions %(versionRange)s.":[ "/%(fieldName)s: \"%(permissionName)s\" stöds inte i manifestversionerna %(versionRange)s." ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported.":[ "/%(fieldName)s: \"%(permissionName)s\" stöds inte." ] } } }