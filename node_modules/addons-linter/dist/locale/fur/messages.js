module.exports = { domain:"messages",
  locale_data:{ messages:{ "":{ domain:"messages",
        plural_forms:"nplurals=2; plural=(n != 1);",
        lang:"fur" },
      "Validation Summary:":[ "Sintesi de convalide:" ],
      Code:[ "Codi<PERSON>" ],
      Message:[ "<PERSON><PERSON><PERSON>" ],
      Description:[ "Descrizion" ],
      File:[ "File" ],
      Line:[ "Rie" ],
      Column:[ "Colone" ],
      "Invalid manifest version range requested: --min-manifest-version (currently set to %(minManifestVersion)s) should not be greater than --max-manifest-version (currently set to %(maxManifestVersion)s).":[ "Domandât interval di versions dal manifest no valit: --min-manifest-version (in chest moment metût a %(minManifestVersion)s) nol à di jessi plui grant di --max-manifest-version (in chest moment metût a %(maxManifestVersion)s)." ],
      "Your FTL is not valid.":[ "Il to file FTL nol è valit." ],
      "Your FTL file could not be parsed.":[ "Nol è pussibil analizâ il to file FTL." ],
      "Remote scripts are not allowed as per the Add-on Policies.":[ "Lant daûr des politichis dai components adizionâi, i scripts esternis/lontans no son ametûts." ],
      "Please include all scripts in the add-on. For more information, refer to https://mzl.la/2uEOkYp.":[ "Inclût ducj i scripts tal component adizionâl. Par vê plui informazions, fâs riferiment a https://mzl.la/2uEOkYp." ],
      "Inline scripts blocked by default":[ "I scripts in linie a son blocâts tant che impostazion predefinide" ],
      "Default CSP rules prevent inline JavaScript from running (https://mzl.la/2pn32nd).":[ "Lis regulis CSP predefinidis a impedissin la esecuzion di JavaScript in linie (https://mzl.la/2pn32nd)." ],
      "Unadvised 3rd-party JS library":[ "Librarie JS di tiercis parts disconseade" ],
      "Your add-on uses a JavaScript library we do not recommend. Read more: https://bit.ly/1TRIyZY":[ "Il to component adizionâl al dopre une librarie JavaScript che no conseìn. Lei di plui achì: https://bit.ly/1TRIyZY" ],
      "Known JS library detected":[ "Rilevade librarie JS cognossude" ],
      "JavaScript libraries are discouraged for simple add-ons, but are generally accepted.":[ "Lis librariis JavaScript a son disconseadis pai components adizionâi sempliçs, ma pal plui a son acetadis." ],
      "Due to both security and performance concerns, this may not be set using dynamic values which have not been adequately sanitized. This can lead to security issues or fairly serious performance degradation.":[ "Par motîfs di sigurece e prestazions, nol è pussibil configurâ chest doprant valôrs dinamics che no son stâts purificâts in mût adeguât. Chest al pues puartâ a problemis di sigurece o une penze degradazion des prestazions." ],
      "{{api}} is not supported":[ "{{api}} nol è supuartât" ],
      "This API has not been implemented by Firefox.":[ "Cheste API no je stade implementade di Firefox." ],
      "\"{{api}}\" has been removed in Manifest Version 3 (`manifest_version` property)":[ "“{{api}}” al è stât gjavât de version 3 dal manifest (proprietât “manifest_version”)" ],
      "{{api}} is deprecated":[ "{{api}} al è deplorât" ],
      "This API has been deprecated by Firefox.":[ "Cheste API e je stade deplorade di Firefox." ],
      "Content script file could not be found.":[ "Impussibil cjatâ il file content script." ],
      "\"{{api}}\" is deprecated or unimplemented":[ "“{{api}}” al è deplorât o nol è implementât" ],
      "Content script file could not be found":[ "Impussibil cjatâ il file content script" ],
      "\"%(api)s\" can cause issues when loaded temporarily":[ "\"$(api)s\" e pues causâ problemis se e ven cjariade in mût temporani" ],
      "This API can cause issues when loaded temporarily using about:debugging in Firefox unless you specify \"browser_specific_settings.gecko.id\" in the manifest. Please see: https://mzl.la/2hizK4a for more.":[ "Cheste API e pues causâ problemis se e ven cjariade in mût temporani doprant about:debugging in Firefox, gjavant il câs di specificâ “browser_specific_settings.gecko.id” tal manifest. Par altris informazions, consultâ: https://mzl.la/2hizK4a." ],
      "{{api}} is not supported in Firefox version {{minVersion}}":[ "{{api}} no je supuartade te version {{minVersion}} di Firefox" ],
      "This API is not implemented by the given minimum Firefox version":[ "Cheste API no je stade implementade de version minime indicade di Firefox" ],
      "{{api}} is not supported in Firefox for Android version {{minVersion}}":[ "{{api}} no je supuartade te version {{minVersion}} di Firefox par Android" ],
      "This API is not implemented by the given minimum Firefox for Android version":[ "Cheste API no je implementade de version minime indicade di Firefox par Android" ],
      "Content script file name should not be empty.":[ "Il non dal file content script nol pues jessi vueit." ],
      "Content script file name should not be empty":[ "Il non dal file content script nol pues jessi vueit" ],
      "\"%(method)s\" called with a non-literal uri":[ "“%(method)s” clamât cuntun URI no-leterâl" ],
      "Calling \"%(method)s\" with variable parameters can result in potential security vulnerabilities if the variable contains a remote URI. Consider using 'window.open' with the 'chrome=no' flag.":[ "La clamade di “%(method)s” cun parametris variabii e pues puartâ a vulnerabilitâts potenziâls di sigurece se la variabile e conten un URI lontan. Considere la utilizazion di “window.open” cu la opzion “chrome=no”." ],
      "\"%(method)s\" called with non-local URI":[ "“%(method)s” clamât cun URI no-locâl" ],
      "Calling \"%(method)s\" with a non-local URI will result in the dialog being opened with chrome privileges.":[ "Se al ven clamât “%(method)s” cuntun URI no-locâl, il dialic che si vierzarà al varà i privileçs di chrome." ],
      "JavaScript syntax error":[ "Erôr di sintassi JavaScript" ],
      "There is a JavaScript syntax error in your code, which might be related to some experimental JavaScript features that aren't an official part of the language specification and therefore not supported yet. The validation cannot continue on this file.":[ "Tal to codiç al è un erôr di sintassi JavaScript, che al podarès jessi colegât a funzions JavaScript sperimentâls che no son part uficiâl des specifichis dal lengaç di programazion e duncje no son ancjemò supuartadis. Nol è pussibil continuâ la convalide su chest file." ],
      "Evaluation of strings as code can lead to security vulnerabilities and performance issues, even in the most innocuous of circumstances. Please avoid using `eval` and the `Function` constructor when at all possible.":[ "La valutazion di stringhis come codiç al pues puartâ a vulnerabilitâts di sigurece e problemis di prestazions, ancje tes circostancis plui inocuis. Se pussibil evite di doprâ “eval“ e il costrutôr “Function”." ],
      "setTimeout, setInterval and execScript functions should be called only with function expressions as their first argument":[ "Lis funzions setTimeout, setInterval e execScript a scugnin jessi clamadis nome cun espressions di funzion tant che lôr prin argoment" ],
      "Unexpected global passed as an argument":[ "Variabile globâl inspietade passade tant che argoment" ],
      "Passing a global as an argument is not recommended. Please make this a var instead.":[ "Nol è conseât passâ un “global” tant che argoment. Al so puest rindilu un “var”." ],
      "Use of document.write strongly discouraged.":[ "Al è une vore disconseât doprâ document.write." ],
      "document.write will fail in many circumstances when used in extensions, and has potentially severe security repercussions when used improperly. Therefore, it should not be used.":[ "document.write nol funzionarà in tantis circostancis cuant che al ven doprât tes estensions e al podarès vê ripercussions grivis su la sigurece se al ven doprât mâl. Duncje, nol sarès di doprâ." ],
      "Banned 3rd-party JS library":[ "Librarie JS di tiercis parts bandidis" ],
      "Your add-on uses a JavaScript library we consider unsafe. Read more: https://bit.ly/1TRIyZY":[ "Il to component adizionâl al dopre une librarie JavaScript che no considerìn sigure. Lei di plui in merit: https://bit.ly/1TRIyZY" ],
      "Your JSON contains block comments.":[ "Il file JSON al conten coments di bloc." ],
      "Only line comments (comments beginning with \"//\") are allowed in JSON files. Please remove block comments (comments beginning with \"/*\")":[ "Tai files JSON a son consintûts dome coments di rie (i coments che a scomencin cun “//”). Gjave i blocs di coments (coments che a scomencin cun “/*”)" ],
      "Duplicate keys are not allowed in JSON files.":[ "Lis clâfs duplicadis no son consintudis tai files JSON." ],
      "Duplicate key found in JSON file.":[ "E je stade cjatade une clâf duplicade tal file JSON." ],
      "Your JSON is not valid.":[ "Il to file JSON nol è valit." ],
      "Your JSON file could not be parsed.":[ "Nol è pussibil analizâ il to file JSON." ],
      "Reserved filename found.":[ "Al è stât cjatât un non di file riservât." ],
      "Files whose names are reserved have been found in the add-on. Please refrain from using them and rename your files.":[ "Tal component adizionâl al è stât cjatât che cualchi file al à un non riservât. Par plasê strategniti dal doprâ chescj nons e cambie il non dai tiei files." ],
      "The package is invalid. It may contain entries using invalid characters, as an example using '\\' as a path separator is not allowed in Firefox. Try to recreate your add-on package (ZIP) and make sure all entries are using '/' as the path separator.":[ "Il pachet nol è valit. Al è pussibil che al contegni vôs che a doprin caratars no valits, par esempli doprâ '\\' come separadôr di percors nol è consintût in Firefox. Prove a tornâ a creâ il pachet dal to component adizionâl (ZIP) e verifiche che dutis lis vôs a doprin '/' come separadôr di percors." ],
      "We were unable to decompress the zip file.":[ "No sin rivâts a decomprimi il file zip." ],
      "manifest.json was not found":[ "Impussibil cjatâ manifest.json" ],
      "No manifest.json was found at the root of the extension. The package file must be a ZIP of the extension's files themselves, not of the containing directory. See: https://mzl.la/2r2McKv for more on packaging.":[ "Nol è stât cjatât nissun file manifest.json te lidrîs de estension. Il file dal pachet al à di jessi un ZIP dai files stès de estension, no de cartele ce ju conten. Viôt: https://mzl.la/2r2McKv par savê di plui sui pachets." ],
      "File is too large to parse.":[ "Il file al è masse grant par jessi analizât." ],
      "This file is not binary and is too large to parse. Files larger than %(maxFileSizeToParseMB)sMB will not be parsed. Consider moving large lists of data out of JavaScript files and into JSON files, or splitting very large files into smaller ones.":[ "Chest file nol è binari e al è masse grant par jessi analizât. I files plui grancj di %(maxFileSizeToParseMB)s MB no vignaran analizâts. Considere la idee di spostâ listis grandis di dâts fûr dai files di JavaScript in files JSON, o dividi i files une vore grancj in files plui piçui." ],
      "Hidden file flagged":[ "Segnalât file platât" ],
      "Hidden files complicate the review process and can contain sensitive information about the system that generated the add-on. Please modify the packaging process so that these files aren't included.":[ "I files platâts a complichin il procès di revision e a puedin contignî informazions sensibilis sul sisteme che al à gjenerât il component adizionâl. Par plasê modifiche il procès di creazion dai pachets in mût che chescj files no sedin includûts." ],
      "Package contains duplicate entries":[ "Il pachet al conten vôs doplis" ],
      "Flagged filename found":[ "Rilevât non di file segnalât" ],
      "Files were found that are either unnecessary or have been included unintentionally. They should be removed.":[ "A son stâts rilevâts files che no coventin o includûts cence volê. A varessin di sei gjavâts." ],
      "The package contains multiple entries with the same name. This practice has been banned. Try unzipping and re-zipping your add-on package and try again.":[ "Il pachet al conten plui vôs cul stes non. Cheste pratiche e je stade vietade. Prove a decomprimi e tornâ a comprimi il pachet dal component adizionâl e po' tornâ a provâ." ],
      "Flagged file extensions found":[ "Rilevadis estensions di file segnaladis" ],
      "Flagged file type found":[ "Rilevât gjenar di file segnalât" ],
      "Files whose names end with flagged extensions have been found in the add-on. The extension of these files are flagged because they usually identify binary components. Please see https://bit.ly/review-policy for more information on the binary content review process.":[ "Tal component adizionâl a son stâts cjatâts files cun nons che a finissin cun estensions segnaladis. La estension di chescj files e je segnalade parcè che di solit e identifiche components binaris. Viôt https://bit.ly/review-policy par vê plui informazions sul procès di revision dai contignûts binaris." ],
      "Package already signed":[ "Pachet za firmât" ],
      "Add-ons which are already signed will be re-signed when published on AMO. This will replace any existing signatures on the add-on.":[ "Une volte publicâts su AMO, i components adizionâi za firmâts a vignaran firmâts di gnûf. Chest al sostituirà dutis lis firmis esistentis tal component adizionâl." ],
      "Firefox add-ons are not allowed to run coin miners.":[ "La estrazion di criptovalude no je consintude tai components adizionâi par Firefox." ],
      "We do not allow coinminer scripts to be run inside WebExtensions. See https://github.com/mozilla/addons-linter/issues/1643 for more details.":[ "La esecuzion di script pe estrazion di criptovalude dentri di WebExtensions no je consintude. Par vê plui detais, viôt https://github.com/mozilla/addons-linter/issues/1643." ],
      "String name is reserved for a predefined message":[ "Il non stringhe al è riservât pa un messaç predefinît" ],
      "String names starting with @@ get translated to built-in constants (https://mzl.la/2BL9ZjE).":[ "I nons di stringhe che a scomencin par @@ a vegnin convertîts in costantis integradis (https://mzl.la/2BL9ZjE)." ],
      "String name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2Eztyi5).":[ "Il non de stringhe al à di contignî dome caratars alfanumerics, _ e @ (https://mzl.la/2Eztyi5)." ],
      "Placeholder for message is missing":[ "Al mancje il segnepuest pal messaç" ],
      "A placeholder used in the message is not defined.":[ "Un segnepuest doprât tal messaç nol è stât definît." ],
      "Placeholder name contains invalid characters":[ "Il non dal segnepuest al conten caratars no valits" ],
      "Placeholder name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2ExbYez).":[ "Il non dal segnepuest al à di contignî dome caratars alfanumerics, _ e @ (https://mzl.la/2ExbYez)." ],
      "Placeholder is missing the content property":[ "Al segnepuest  i mancje la proprietât “content“" ],
      "A placeholder needs a content property defining the replacement of it (https://mzl.la/2DT1MQd)":[ "Un segnepuest al à bisugne di une proprietât content che int definìs la sostituzion (https://mzl.la/2DT1MQd)" ],
      "Translation string is missing the message property":[ "Ae stringhe de traduzion i mancje la proprietât “message“" ],
      "No \"message\" message property is set for a string (https://mzl.la/2DSBTjA).":[ "Nissune proprietât “message“ e je configurade par une stringhe (https://mzl.la/2DSBTjA)." ],
      "The field is required.":[ "Cjamp obligatori." ],
      "See https://mzl.la/1ZOhoEN (MDN Docs) for more information.":[ "Viôt https://mzl.la/1ZOhoEN (Documentazion MDN) par vê plui informazions." ],
      "The permission type is unsupported.":[ "Il gjenar di permès nol è supuartât." ],
      "See https://mzl.la/1R1n1t0 (MDN Docs) for more information.":[ "Viôt https://mzl.la/1R1n1t0 (Documentazion MDN) par vê plui informazions." ],
      "See https://mzl.la/2Qn0fWC (MDN Docs) for more information.":[ "Viôt https://mzl.la/2Qn0fWC (Documentazion MDN) par vê plui informazions." ],
      "See https://mzl.la/3Woeqv4 (MDN Docs) for more information.":[ "Viôt https://mzl.la/3Woeqv4 (Documentazion MDN) par vê plui informazions." ],
      "Unknown permission.":[ "Permès no cognossût." ],
      "%(instancePath)s: the following privileged permissions are only allowed in privileged extensions: %(privilegedPermissions)s.":[ "%(instancePath)s: chescj permès a son consintûts dome tes estensions cun privileçs: %(privilegedPermissions)s." ],
      "Invalid host permission.":[ "“host permission“ no valit." ],
      "Invalid install origin.":[ "Origjin de instalazion no valide." ],
      "Invalid install origin. A valid origin has - only - a scheme, hostname and optional port. See https://mzl.la/3TEbqbE (MDN Docs) for more information.":[ "Origjin instalazion no valide. Une origjin valide e à dome un scheme, un non host e une puarte facoltative. Viôt https://mzl.la/3TEbqbE (Documentazion MDN) par vê plui informazions." ],
      "The field is invalid.":[ "Il cjamp nol è valit." ],
      "\"manifest_version\" in the manifest.json is not a valid value":[ "\"manifest_version\" tal file manifest.json nol è un valôr valit" ],
      "See https://mzl.la/20PenXl (MDN Docs) for more information.":[ "Viôt https://mzl.la/20PenXl (Documentazion MDN) par vê plui informazions." ],
      "\"%(property)s\" allows remote code execution in manifest.json":[ "“%(property)s” al permet la esecuzion di codiç di lontan in manifest.json" ],
      "A custom \"%(property)s\" needs additional review.":[ "Un “%(property)s” personalizât al à bisugne di une altre revision." ],
      "\"%(property)s\" allows 'eval', which has strong security and performance implications.":[ "“%(property)s” al permet “eval”, che al à implicazions significativis in tiermins di sigurece e prestazions." ],
      "In most cases the same result can be achieved differently, therefore it is generally prohibited":[ "Te plui part dai câs al è pussibil otignî il stes risultât in mût diviers, duncje in gjenerâl al è vietât" ],
      "The \"name\" property must be a string with no leading/trailing whitespaces.":[ "La proprietât “name” e scugne jessi une stringhe cence spazis vueits al inizi o ae fin." ],
      "See http://mzl.la/1STmr48 (MDN Docs) for more information.":[ "Viôt http://mzl.la/1STmr48 (Documentazion MDN) par vê plui informazions." ],
      "\"update_url\" is not allowed.":[ "\"update_url\" nol è consintût." ],
      "\"applications.gecko.update_url\" or \"browser_specific_settings.gecko.update_url\" are not allowed for Mozilla-hosted add-ons.":[ "“applications.gecko.update_url” o “browser_specific_settings.gecko.update_url” no son consintûts pai components adizionâi ospitâts di Mozilla." ],
      "The \"update_url\" property is not used by Firefox.":[ "La proprietât \"update_url\" no ven doprade di Firefox." ],
      "The \"update_url\" is not used by Firefox in the root of a manifest; your add-on will be updated via the Add-ons site and not your \"update_url\". See: https://mzl.la/25zqk4O":[ "\"update_url\" nol è doprât di Firefox te lidrîs di un manifest; il component adizionâl al vignarà inzornât midiant il sît dai components adizionâi e no midiant “update_url”. Viôt: https://mzl.la/25zqk4O" ],
      "\"strict_max_version\" not required.":[ "\"strict_max_version\" no necessari." ],
      "\"strict_max_version\" shouldn't be used unless the add-on is expected not to work with future versions of Firefox.":[ "No sta doprâ “strict_max_version” gjavât che no tu previodis che il component adizionâl nol funzioni cu lis versions futuris di Firefox." ],
      "Manifest Version 3 is not fully supported on Firefox for Android.":[ "La version manifest 3 no je supuartade dal dut su Firefox par Android." ],
      "No \"%(property)s\" property found in manifest.json":[ "No je stade cjatade nissune proprietât “%(property)s” in manifest.json" ],
      "\"%(property)s\" is required":[ "“%(property)s” al è obligatori" ],
      "An icon defined in the manifest could not be found in the package.":[ "Nol è stât pussibil cjatâ tal pachet une icone definide tal manifest." ],
      "Icon could not be found at \"%(path)s\".":[ "Impussibil cjatâ la icone in \"%(path)s\"." ],
      "A background script defined in the manifest could not be found.":[ "Nol è stât pussibil cjatâ un script di sotfont definît tal manifest." ],
      "A background page defined in the manifest could not be found.":[ "Impussibil cjatâ une pagjine di sotfont definide tal manifest." ],
      "Background script could not be found at \"%(path)s\".":[ "Impussibil cjatâ in \"%(path)s\" il script di sotfont." ],
      "Background page could not be found at \"%(path)s\".":[ "Impussibil cjatâ in \"%(path)s\" la pagjine di sotfont." ],
      "A content script defined in the manifest could not be found.":[ "Impussibil cjatâ un script di contignût definît tal manifest." ],
      "A content script css file defined in the manifest could not be found.":[ "Impussibil cjatâ il file css cuntun script di contignût definît tal manifest." ],
      "Content script defined in the manifest could not be found at \"%(path)s\".":[ "Impussibil cjatâ il script di contignût definît tal manifest in \"%(path)s\"." ],
      "Content script css file defined in the manifest could not be found at \"%(path)s\".":[ "Impussibil cjatâ tal manifest in \"%(path)s\" il file css dal script di contignût." ],
      "A dictionary file defined in the manifest could not be found.":[ "Impussibil cjatâ un file di dizionari definît tal manifest." ],
      "Dictionary file defined in the manifest could not be found at \"%(path)s\".":[ "Impussibil cjatâ il file di dizionari definît tal manifest in \"%(path)s\"." ],
      "The manifest contains multiple dictionaries.":[ "Il manifest al conten plui dizionaris." ],
      "Multiple dictionaries were defined in the manifest, which is unsupported.":[ "Tal manifest a son stâts cjatâts plui dizionaris, cheste opzion no je supuartade." ],
      "The manifest contains a dictionaries object, but it is empty.":[ "Il manifest al conten un ogjet dictionaries, ma al è vueit." ],
      "A dictionaries object was defined in the manifest, but it was empty.":[ "Tal manifest al jere definît un ogjet dictionaries, ma al jere vueit." ],
      "The manifest contains a dictionary but no id property.":[ "Il manifest al conten un dizionari ma nol à une proprietât ID." ],
      "A dictionary was found in the manifest, but there was no id set.":[ "Tal manifest al è stât cjatât un dizionari, ma nol jere configurât un ID." ],
      "Please refer to https://github.com/mozilla-extensions/xpi-manifest to learn more about privileged extensions and signing.":[ "Fâs riferiment a https://github.com/mozilla-extensions/xpi-manifest par savê di plui su lis estensions cun privileçs e su la firme." ],
      "Forbidden content found in add-on.":[ "Cjatât contignût vietât tal component adizionât." ],
      "This add-on contains forbidden content.":[ "Chest component adizionâl al conten contignût vietât." ],
      "Icons must be square.":[ "Lis iconis a scugnin jessi cuadradis." ],
      "Icon at \"%(path)s\" must be square.":[ "La icone in \"%(path)s\" e scugne jessi cuadrade." ],
      "The size of the icon does not match the manifest.":[ "La dimension de icone no corispuint al manifest." ],
      "Expected icon at \"%(path)s\" to be %(expected)d pixels wide but was %(actual)d.":[ "La icone “%(path)s” e varès di vê une largjece di %(expected)d pixels ma int veve %(actual)d)." ],
      "\"%(fieldName)s\" is ignored for non-privileged add-ons.":[ "“%(fieldName)s” al ven ignorât pai components adizionâi cence privileçs." ],
      "Corrupt image file":[ "File imagjin ruvinât" ],
      "Expected icon file at \"%(path)s\" is corrupted":[ "Il file icone previodût in \"%(path)s\" al è ruvinât" ],
      "This property has been deprecated.":[ "Cheste proprietât e je deplorade." ],
      "This theme LWT alias has been removed in Firefox 70.":[ "Chest alias dal teme LWS al è stât gjavât in Firefox 70." ],
      "See https://mzl.la/2T11Lkc (MDN Docs) for more information.":[ "Viôt https://mzl.la/2T11Lkc (Documentazion MDN) par vê plui informazions." ],
      "Theme image for \"%(type)s\" could not be found at \"%(path)s\"":[ "Impussibil cjatâ la imagjin dal teme “%(type)s” in “%(path)s”" ],
      "\"%(fieldName)s\" manifest field is only used for privileged and temporarily installed extensions.":[ "Il cjamp dal manifest “%(fieldName)s” al ven doprât dome pes estensions cun privileçs e instaladis in mût temporani." ],
      "Corrupted theme image file":[ "File imagjin dal teme ruvinât" ],
      "Theme image file at \"%(path)s\" is corrupted":[ "Il file imagjin dal teme in \"%(path)s\" al è ruvinât" ],
      "Theme image file has an unsupported file extension":[ "Il file imagjin dal teme al à une estension che no je supuartade" ],
      "Theme image file at \"%(path)s\" has an unsupported file extension":[ "Il file imagjin dal teme in \"%(path)s\" al à une estension che no je supuartade" ],
      "Theme image file has an unsupported mime type":[ "Il file imagjin dal teme al à un gjenar mime no supuartât" ],
      "Theme image file at \"%(path)s\" has the unsupported mime type \"%(mime)s\"":[ "Il file imagjin dal teme in \"%(path)s\" al à il gjenar mime \"%(mime)s\" che nol è supuartât" ],
      "Theme image file mime type does not match its file extension":[ "Il gjenar mime dal file imagjin dal teme nol corispuint ae estension dal file" ],
      "Theme image file extension at \"%(path)s\" does not match its actual mime type \"%(mime)s\"":[ "La estension dal file imagjin dal teme in “%(path)s” no corispuint al gjenar mime efetîf “%(mime)s”" ],
      "The \"default_locale\" is missing localizations.":[ "La traduzion par \"default_locale\" no je complete." ],
      "The \"default_locale\" value is specified in the manifest, but no matching \"messages.json\" in the \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "Il valôr “default_locale\" al è specificât tal manifest, ma nol esist nissun “messages.json” corispondent te cartele “_locales”. Viôt: https://mzl.la/2hjcaEE" ],
      "The \"default_locale\" is missing but \"_locales\" exist.":[ "Rilevât \"_locales\", ma no \"default_locale\"." ],
      "The \"default_locale\" value is not specifed in the manifest, but a \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "Il valôr “default_locale” nol è specificât tal manifest, ma e esist un cartele “_locales”. Viôt: https://mzl.la/2hjcaEE" ],
      "Unsupported image extension":[ "Estension di imagjin no supuartade" ],
      "Icons should be one of JPG/JPEG, WebP, GIF, PNG or SVG.":[ "Lis iconis a scugnin jessi intun di chescj formâts: JPG/JPEG, WebP, GIF, PNG o SVG." ],
      "\"applications\" property overridden by \"browser_specific_settings\" property":[ "Proprietât “applications” sostituide de proprietât “browser_specific_settings”" ],
      "The \"applications\" property is being ignored because it is superseded by the \"browser_specific_settings\" property which is also defined in your manifest. Consider removing applications.":[ "La proprietât “applications” e je stade ignorade parcè che e je stade sostituide de proprietât “browser_specific_settings”, ancje chê definide tal manifest. Valute la pussibilitât di gjavâ “applications”." ],
      "Empty language directory":[ "Cartele des lenghis vueide" ],
      "messages.json file missing in \"%(path)s\"":[ "Al mancje il file messages.json in \"%(path)s\"" ],
      "Manifest key not supported by the specified minimum Firefox version":[ "Il manifest key nol è supuartât te version minime di Firefox specificade" ],
      "\"strict_min_version\" requires Firefox %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "“strict_min_version” al à bisugne di Firefox %(minVersion)s, che al è stât publicât prime che la version %(versionAdded)s e introdusès il supuart par “%(key)s”." ],
      "\"%(fieldName)s\" is not supported in manifest versions %(versionRange)s.":[ "“%(fieldName)s” nol è supuartât tes versions dal manifest %(versionRange)s." ],
      "Permission not supported by the specified minimum Firefox version":[ "Il permès nol è supuartât de version minime di Firefox specificade" ],
      "\"%(fieldName)s\" is not supported.":[ "“%(fieldName)s” nol è supuartât." ],
      "Manifest key not supported by the specified minimum Firefox for Android version":[ "Il manifest key nol è supuartât te version minime di Firefox par Android specificade" ],
      "\"strict_min_version\" requires Firefox for Android %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "“strict_min_version” al à bisugne di Firefox per Android %(minVersion)s, che al è stât publicât prime che la version %(versionAdded)s e introdusès il supuart par “%(key)s”." ],
      "Permission not supported by the specified minimum Firefox for Android version":[ "Il permès nol è supuartât te version minime di Firefox per Android specificade" ],
      "Linking to \"addons.mozilla.org\" is not allowed":[ "Il colegament a “addons.mozilla.org” nol è consintût" ],
      "Links directing to \"addons.mozilla.org\" are not allowed to be used for homepage":[ "Nol è pussibil doprâ i colegaments che a pontin a “addons.mozilla.org” tant che pagjine iniziâl" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above":[ "Il permès “%(permission)s” al à bisugne che “strict_min_version” al sedi metût a “%(minFirefoxVersion)s” o superiôr" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above. Please update your manifest.json version to specify a minimum Firefox version.":[ "Il permès “%(permission)s” al à bisugne che “strict_min_version” al sedi metût a “%(minFirefoxVersion)s” o superiôr. Inzorne la version di manifest.json par specificâ une version minime di Firefox." ],
      "The extension ID is required in Manifest Version 3 and above.":[ "L'ID de estension al è obligatori te version 3 o sucessivis dal manifest." ],
      "See https://mzl.la/3PLZYdo for more information.":[ "Viôt https://mzl.la/3PLZYdo par vê plui informazions." ],
      "%(instancePath)s: Privileged extensions should declare privileged permissions.":[ "%(instancePath)s: lis estensions cun privileçs a scugnin declarâ permès cun privileçs." ],
      "This extension does not declare any privileged permission. It does not need to be signed with the privileged certificate. Please upload it directly to https://addons.mozilla.org/.":[ "Cheste estension no declare nissun permès cun privileçs. Nol è necessri firmâ cul certificât privilegjât. Cjamile in rêt dret su https://addons.mozilla.org/." ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for privileged extensions.":[ "%(instancePath)s: pes estensions cun privileçs al è necessari il permès “mozillaAddons”." ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for extensions that include privileged manifest fields.":[ "%(instancePath)s: il permès “mozillaAddons” al è necessari pes estensions che a includin cjamps dal manifest cun privileçs." ],
      "%(instancePath)s: privileged manifest fields are only allowed in privileged extensions.":[ "%(instancePath)s: i cjamps dal manifest cun privileçs a son consintûts dome tes estensions cun privileçs." ],
      "This extension does not include the \"mozillaAddons\" permission, which is required for privileged extensions.":[ "Cheste estension no inclût il permès  “mozillaAddons” necessari pes estensions cun privileçs." ],
      "Cannot use actions in hidden add-ons.":[ "Impussibil doprâ azions in components adizionâi platâts." ],
      "The hidden and browser_action/page_action (or action in Manifest Version 3 and above) properties are mutually exclusive.":[ "La proprietât “hidden” e “browser_action/page_action” (o “action” tes versions 3 e sucessivis dai manifescj) si escludin un cun chel altri." ],
      "Use \"browser_specific_settings\" instead of \"applications\".":[ "Dopre “browser_specific_settings” invezit che “applications”." ],
      "The \"applications\" property in the manifest is deprecated and will no longer be accepted in Manifest Version 3 and above.":[ "La proprietât “applications” tal manifest e je deplorade e no sarà plui acetade tes versions 3 e sucessivis dai manifescj." ],
      "\"applications\" is no longer allowed in Manifest Version 3 and above.":[ "“applications” nol è plui consintût tes versions 3 e sucessivis dai manifescj." ],
      "The \"applications\" property in the manifest is no longer allowed in Manifest Version 3 and above. Use \"browser_specific_settings\" instead.":[ "La proprietât “applications” tal manifest no je plui consintude tes versions 3 e sucessivis dai manifescj. Al so puest dopre “browser_specific_settings”." ],
      "The version string should be simplified because it won't be compatible with Manifest Version 3 and above.":[ "La stringhe de version e varès di jessi semplificade parcè che no sarà compatibile cu lis versions 3 e sucessivis dai manifescj." ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros will no longer be allowed. Letters will no longer be allowed either. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "La version e varès di jessi une stringhe fate di 1 a 4 numars separâts di ponts. Ogni numar al à di vê un manssim di 9 cifris e i zeros iniziâi no saran plui consintûts. Ancje lis letaris no saran plui consintudis. Par vê plui informazions viôt: https://mzl.la/3h3mCRu (Documentazion MDN)." ],
      "The version string should be simplified.":[ "La stringhe de version e scugne jessi semplificade." ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros are not allowed. Letters are no longer allowed. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "La version e varès di jessi une stringhe fate di 1 a 4 numars separâts di ponts. Ogni numar al à di vê fin a 9 cifris e i zeros iniziâi no son consintûts. Lis letaris no son plui consintudis. Par vê plui informazions, viôt https://mzl.la/3h3mCRu (Documentazion MDN)." ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported in manifest versions %(versionRange)s.":[ "/%(fieldName)s: “%(permissionName)s” nol è supuartât tes versions dal manifest %(versionRange)s." ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported.":[ "/%(fieldName)s: \"%(permissionName)s\" nol è supuartât." ] } } }