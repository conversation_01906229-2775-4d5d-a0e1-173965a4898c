module.exports = { domain:"messages",
  locale_data:{ messages:{ "":{ domain:"messages",
        plural_forms:"nplurals=2; plural=(n != 1);",
        lang:"nn_NO" },
      "Validation Summary:":[ "Valideringsoversikt:" ],
      Code:[ "Kode" ],
      Message:[ "Melding" ],
      Description:[ "Skildring" ],
      File:[ "Fil" ],
      Line:[ "Linje" ],
      Column:[ "Kolonne" ],
      "Invalid manifest version range requested: --min-manifest-version (currently set to %(minManifestVersion)s) should not be greater than --max-manifest-version (currently set to %(maxManifestVersion)s).":[ "" ],
      "Your FTL is not valid.":[ "FTL er ikkje gyldig." ],
      "Your FTL file could not be parsed.":[ "Klarte ikkje å tolke FTL-fila di." ],
      "Remote scripts are not allowed as per the Add-on Policies.":[ "Eksterne skript er ikkje tillatne ifølgje utvidingsretningslinjene." ],
      "Please include all scripts in the add-on. For more information, refer to https://mzl.la/2uEOkYp.":[ "" ],
      "Inline scripts blocked by default":[ "Inline-script er blokkerte som standard" ],
      "Default CSP rules prevent inline JavaScript from running (https://mzl.la/2pn32nd).":[ "" ],
      "Unadvised 3rd-party JS library":[ "Ikkje-tilrådd tredjeparts JS-bibliotek" ],
      "Your add-on uses a JavaScript library we do not recommend. Read more: https://bit.ly/1TRIyZY":[ "" ],
      "Known JS library detected":[ "Kjent JS-bibliotek oppdaga" ],
      "JavaScript libraries are discouraged for simple add-ons, but are generally accepted.":[ "" ],
      "Due to both security and performance concerns, this may not be set using dynamic values which have not been adequately sanitized. This can lead to security issues or fairly serious performance degradation.":[ "" ],
      "{{api}} is not supported":[ "{{api}} er ikkje støtta" ],
      "This API has not been implemented by Firefox.":[ "Denne API-en har ikkje blitt implementert av Firefox." ],
      "\"{{api}}\" has been removed in Manifest Version 3 (`manifest_version` property)":[ "\"{{api}}\" er tatt bort i Manifest Version 3 (eigenskapen `manifest_version`)" ],
      "{{api}} is deprecated":[ "{{api}} er forelda" ],
      "This API has been deprecated by Firefox.":[ "Denne API-en har blitt forelda av Firefox." ],
      "Content script file could not be found.":[ "Fann ikkje innhaldskriptfila." ],
      "\"{{api}}\" is deprecated or unimplemented":[ "\"{{api}}\" er forelda eller ikkje implementert" ],
      "Content script file could not be found":[ "Fann ikkje innhaldskriptfila" ],
      "\"%(api)s\" can cause issues when loaded temporarily":[ "" ],
      "This API can cause issues when loaded temporarily using about:debugging in Firefox unless you specify \"browser_specific_settings.gecko.id\" in the manifest. Please see: https://mzl.la/2hizK4a for more.":[ "" ],
      "{{api}} is not supported in Firefox version {{minVersion}}":[ "{{api}} er ikkje støtta i Firefox {{minVersion}}" ],
      "This API is not implemented by the given minimum Firefox version":[ "Denne API-en er ikkje implementert av den spesifiserte minimumsversjonen av Firefox" ],
      "{{api}} is not supported in Firefox for Android version {{minVersion}}":[ "{{api}} er ikkje støtta i Firefox for Android-versjon {{minVersion}}" ],
      "This API is not implemented by the given minimum Firefox for Android version":[ "Denne API-en har ikkje blitt implementert av den spesifiserte minimumsversjonen av Firefox for Android." ],
      "Content script file name should not be empty.":[ "Innhaldskriptfil-namnet skal ikkje vere tomt." ],
      "Content script file name should not be empty":[ "Innhaldsskript-filnamnet skal ikkje vere tomt" ],
      "\"%(method)s\" called with a non-literal uri":[ "" ],
      "Calling \"%(method)s\" with variable parameters can result in potential security vulnerabilities if the variable contains a remote URI. Consider using 'window.open' with the 'chrome=no' flag.":[ "" ],
      "\"%(method)s\" called with non-local URI":[ "" ],
      "Calling \"%(method)s\" with a non-local URI will result in the dialog being opened with chrome privileges.":[ "" ],
      "JavaScript syntax error":[ "JavaScript-syntaxfeil" ],
      "There is a JavaScript syntax error in your code, which might be related to some experimental JavaScript features that aren't an official part of the language specification and therefore not supported yet. The validation cannot continue on this file.":[ "" ],
      "Evaluation of strings as code can lead to security vulnerabilities and performance issues, even in the most innocuous of circumstances. Please avoid using `eval` and the `Function` constructor when at all possible.":[ "" ],
      "setTimeout, setInterval and execScript functions should be called only with function expressions as their first argument":[ "" ],
      "Unexpected global passed as an argument":[ "Uventa global sendt som eit argument" ],
      "Passing a global as an argument is not recommended. Please make this a var instead.":[ "" ],
      "Use of document.write strongly discouraged.":[ "Bruk av document.write er ikkje tilrådd." ],
      "document.write will fail in many circumstances when used in extensions, and has potentially severe security repercussions when used improperly. Therefore, it should not be used.":[ "" ],
      "Banned 3rd-party JS library":[ "Forbydt tredjeparts JS-bibliotek" ],
      "Your add-on uses a JavaScript library we consider unsafe. Read more: https://bit.ly/1TRIyZY":[ "" ],
      "Your JSON contains block comments.":[ "JSON-en din inneheld blokkkommentarar." ],
      "Only line comments (comments beginning with \"//\") are allowed in JSON files. Please remove block comments (comments beginning with \"/*\")":[ "" ],
      "Duplicate keys are not allowed in JSON files.":[ "Doble nøklar er ikkje tillatne i JSON-filer." ],
      "Duplicate key found in JSON file.":[ "" ],
      "Your JSON is not valid.":[ "JSON-en din er ugyldig." ],
      "Your JSON file could not be parsed.":[ "Klarte ikkje å tolke JSON-en din." ],
      "Reserved filename found.":[ "Reservert filnamn funne." ],
      "Files whose names are reserved have been found in the add-on. Please refrain from using them and rename your files.":[ "" ],
      "The package is invalid. It may contain entries using invalid characters, as an example using '\\' as a path separator is not allowed in Firefox. Try to recreate your add-on package (ZIP) and make sure all entries are using '/' as the path separator.":[ "" ],
      "We were unable to decompress the zip file.":[ "Vi klarte ikkje å pakke ut zip-fila." ],
      "manifest.json was not found":[ "manifest.json vart ikkje funne" ],
      "No manifest.json was found at the root of the extension. The package file must be a ZIP of the extension's files themselves, not of the containing directory. See: https://mzl.la/2r2McKv for more on packaging.":[ "" ],
      "File is too large to parse.":[ "Fila er for stor for å tolkast." ],
      "This file is not binary and is too large to parse. Files larger than %(maxFileSizeToParseMB)sMB will not be parsed. Consider moving large lists of data out of JavaScript files and into JSON files, or splitting very large files into smaller ones.":[ "" ],
      "Hidden file flagged":[ "Gøymd fil flagga" ],
      "Hidden files complicate the review process and can contain sensitive information about the system that generated the add-on. Please modify the packaging process so that these files aren't included.":[ "" ],
      "Package contains duplicate entries":[ "Pakke inneheld doble oppføringar" ],
      "Flagged filename found":[ "Flagga filnamn vart funne" ],
      "Files were found that are either unnecessary or have been included unintentionally. They should be removed.":[ "" ],
      "The package contains multiple entries with the same name. This practice has been banned. Try unzipping and re-zipping your add-on package and try again.":[ "" ],
      "Flagged file extensions found":[ "Flagga filutviding funne" ],
      "Flagged file type found":[ "Flagga filtype vart funne" ],
      "Files whose names end with flagged extensions have been found in the add-on. The extension of these files are flagged because they usually identify binary components. Please see https://bit.ly/review-policy for more information on the binary content review process.":[ "" ],
      "Package already signed":[ "Pakken er allereie signert" ],
      "Add-ons which are already signed will be re-signed when published on AMO. This will replace any existing signatures on the add-on.":[ "" ],
      "Firefox add-ons are not allowed to run coin miners.":[ "Firefox tillegg får ikkje køyre coin-miners." ],
      "We do not allow coinminer scripts to be run inside WebExtensions. See https://github.com/mozilla/addons-linter/issues/1643 for more details.":[ "" ],
      "String name is reserved for a predefined message":[ "Strengnamn er reservert for ei førdefiniert melding" ],
      "String names starting with @@ get translated to built-in constants (https://mzl.la/2BL9ZjE).":[ "" ],
      "String name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2Eztyi5).":[ "" ],
      "Placeholder for message is missing":[ "Plasshaldar for meldinga manglar" ],
      "A placeholder used in the message is not defined.":[ "Ein plasshaldar som vert nytta i meldinga er ikkje definert." ],
      "Placeholder name contains invalid characters":[ "Namn på plasshaldar inneheld ugyldige teikn" ],
      "Placeholder name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2ExbYez).":[ "" ],
      "Placeholder is missing the content property":[ "Plasshaldaren manglar innhaldseigenskapen" ],
      "A placeholder needs a content property defining the replacement of it (https://mzl.la/2DT1MQd)":[ "" ],
      "Translation string is missing the message property":[ "Omsettingsstrengen manglar meldingseigenskapen" ],
      "No \"message\" message property is set for a string (https://mzl.la/2DSBTjA).":[ "Ingen meldingseigenskap «message» er sett for ein streng (https://mzl.la/2DSBTjA)." ],
      "The field is required.":[ "Feltet er påkravd." ],
      "See https://mzl.la/1ZOhoEN (MDN Docs) for more information.":[ "Sjå https://mzl.la/1ZOhoEN (MDN Docs) for meir informasjon." ],
      "The permission type is unsupported.":[ "Kompetansetypen vert ikkje støtta." ],
      "See https://mzl.la/1R1n1t0 (MDN Docs) for more information.":[ "Sjå https://mzl.la/1R1n1t0 (MDN Docs) for meir informasjon." ],
      "See https://mzl.la/2Qn0fWC (MDN Docs) for more information.":[ "Sjå https://mzl.la/2Qn0fWC (MDN Docs) for meir informasjon." ],
      "See https://mzl.la/3Woeqv4 (MDN Docs) for more information.":[ "Sjå https://mzl.la/3Woeqv4 (MDN Docs) for meir informasjon." ],
      "Unknown permission.":[ "Ukjent løyve." ],
      "%(instancePath)s: the following privileged permissions are only allowed in privileged extensions: %(privilegedPermissions)s.":[ "" ],
      "Invalid host permission.":[ "Ugyldig vertsløyve." ],
      "Invalid install origin.":[ "Ugyldig installasjonsopphav." ],
      "Invalid install origin. A valid origin has - only - a scheme, hostname and optional port. See https://mzl.la/3TEbqbE (MDN Docs) for more information.":[ "" ],
      "The field is invalid.":[ "Feltet er ugyldig." ],
      "\"manifest_version\" in the manifest.json is not a valid value":[ "\"manifest_version\" i manifest.json er ikkje ein gyldig verdi" ],
      "See https://mzl.la/20PenXl (MDN Docs) for more information.":[ "Sjå https://mzl.la/20PenXl (MDN Docs) for meir informasjon." ],
      "\"%(property)s\" allows remote code execution in manifest.json":[ "" ],
      "A custom \"%(property)s\" needs additional review.":[ "" ],
      "\"%(property)s\" allows 'eval', which has strong security and performance implications.":[ "" ],
      "In most cases the same result can be achieved differently, therefore it is generally prohibited":[ "" ],
      "The \"name\" property must be a string with no leading/trailing whitespaces.":[ "" ],
      "See http://mzl.la/1STmr48 (MDN Docs) for more information.":[ "Sjå http://mzl.la/1STmr48 (MDN Docs) for meir informasjon." ],
      "\"update_url\" is not allowed.":[ "«update_url» er ikkje lov." ],
      "\"applications.gecko.update_url\" or \"browser_specific_settings.gecko.update_url\" are not allowed for Mozilla-hosted add-ons.":[ "" ],
      "The \"update_url\" property is not used by Firefox.":[ "Eigenskapen \"update_url\" vert ikkje brukt av Firefox." ],
      "The \"update_url\" is not used by Firefox in the root of a manifest; your add-on will be updated via the Add-ons site and not your \"update_url\". See: https://mzl.la/25zqk4O":[ "" ],
      "\"strict_max_version\" not required.":[ "«strict_max_versjon» ikkje påkravd." ],
      "\"strict_max_version\" shouldn't be used unless the add-on is expected not to work with future versions of Firefox.":[ "" ],
      "Manifest Version 3 is not fully supported on Firefox for Android.":[ "" ],
      "No \"%(property)s\" property found in manifest.json":[ "" ],
      "\"%(property)s\" is required":[ "" ],
      "An icon defined in the manifest could not be found in the package.":[ "Eit ikon definert i manifestet kunne ikkje finnast i pakka." ],
      "Icon could not be found at \"%(path)s\".":[ "Klarte ikkje å finne ikon på \"%(path)s\"." ],
      "A background script defined in the manifest could not be found.":[ "Klarte ikkje å finne eit bakgrunnsskript definiert i manifestet." ],
      "A background page defined in the manifest could not be found.":[ "Klarte ikkje å finne ei bakgrunnsside definiert i manifestet." ],
      "Background script could not be found at \"%(path)s\".":[ "Fann ikkje bakgrunnsskriptet på \"%(path)s\"." ],
      "Background page could not be found at \"%(path)s\".":[ "Fann ikkje bakgrunnssida på \"%(path)s\"." ],
      "A content script defined in the manifest could not be found.":[ "Klarte ikkje å finne eit innehaldskript som er definiert i manifestet." ],
      "A content script css file defined in the manifest could not be found.":[ "Klarte ikkje å finne ei css-fil med innhaldsskript som er definert i manifestet.." ],
      "Content script defined in the manifest could not be found at \"%(path)s\".":[ "Fann ikkje innhaldsskriptet definert i manifestet på \"%(path)s\"." ],
      "Content script css file defined in the manifest could not be found at \"%(path)s\".":[ "Fann ikkje innhaldsskriptet til css-fila definert i manifestet på «%(path)s»." ],
      "A dictionary file defined in the manifest could not be found.":[ "Klarte ikkje å finne ei ordboksfil som er definert i manifestet." ],
      "Dictionary file defined in the manifest could not be found at \"%(path)s\".":[ "Klarte ikkje å finne ordbokfil definiert i manifestet på \"%(path)s\"." ],
      "The manifest contains multiple dictionaries.":[ "Manifestet inneheld fleire ordbøker." ],
      "Multiple dictionaries were defined in the manifest, which is unsupported.":[ "Fleire ordbøker er definert i manifestet som ikkje er støtta." ],
      "The manifest contains a dictionaries object, but it is empty.":[ "Manifestet inneheld eit ordboksobjekt, men det er tomt." ],
      "A dictionaries object was defined in the manifest, but it was empty.":[ "Eit ordboksobjekt er definert i manifestet, men det var tomt." ],
      "The manifest contains a dictionary but no id property.":[ "Manifestet inneheld ei ordbok men ingen ID-eigenskap." ],
      "A dictionary was found in the manifest, but there was no id set.":[ "Ei ordbok vart funnen i manifestet, men ingen ID er spesifisert." ],
      "Please refer to https://github.com/mozilla-extensions/xpi-manifest to learn more about privileged extensions and signing.":[ "" ],
      "Forbidden content found in add-on.":[ "Forbydd innhald funne i utvidinga." ],
      "This add-on contains forbidden content.":[ "Denne utvidinga inneheld forbydd innhald." ],
      "Icons must be square.":[ "Ikon må vere kvadratiske." ],
      "Icon at \"%(path)s\" must be square.":[ "Ikona på \"%(path)s\" må vere kvadratiske." ],
      "The size of the icon does not match the manifest.":[ "Ikonstorleiken passar ikkje med manifestet." ],
      "Expected icon at \"%(path)s\" to be %(expected)d pixels wide but was %(actual)d.":[ "" ],
      "\"%(fieldName)s\" is ignored for non-privileged add-ons.":[ "" ],
      "Corrupt image file":[ "Skada bildefil" ],
      "Expected icon file at \"%(path)s\" is corrupted":[ "Forventa ikonfil på \"%(path)s\" er skadd" ],
      "This property has been deprecated.":[ "Denne eigenskapen er forelda." ],
      "This theme LWT alias has been removed in Firefox 70.":[ "Temaet LWT-alias er fjerna frå Firefox 70." ],
      "See https://mzl.la/2T11Lkc (MDN Docs) for more information.":[ "Sjå https://mzl.la/2T11Lkc (MDN Docs) for meir informasjon." ],
      "Theme image for \"%(type)s\" could not be found at \"%(path)s\"":[ "Klarte ikkje å finne temabilde for «%(type)s» på «%(path)s»" ],
      "\"%(fieldName)s\" manifest field is only used for privileged and temporarily installed extensions.":[ "" ],
      "Corrupted theme image file":[ "Skada temabildefil" ],
      "Theme image file at \"%(path)s\" is corrupted":[ "Temabildefil \"%(path)s\" er skadd" ],
      "Theme image file has an unsupported file extension":[ "Temabildefila har ei filending som ikkje er støtta." ],
      "Theme image file at \"%(path)s\" has an unsupported file extension":[ "Temabildefila på «%(path)s» har ei filending som ikkje er støtta" ],
      "Theme image file has an unsupported mime type":[ "Temabildefila har ein mime-type som ikkje er støtta" ],
      "Theme image file at \"%(path)s\" has the unsupported mime type \"%(mime)s\"":[ "Temabildefila på \"%(path)s\" har ei mime-type \"%(mime)s\" som ikkje er støtta" ],
      "Theme image file mime type does not match its file extension":[ "Mime-typen for temabildefila samsvarar ikkje med filendinga" ],
      "Theme image file extension at \"%(path)s\" does not match its actual mime type \"%(mime)s\"":[ "Filendinga for temabildefila på «%(path)s» samsvarar ikkje med den verkelege mime-typen «%(mime)s»" ],
      "The \"default_locale\" is missing localizations.":[ "«default_locale» manglar lokaliseringar." ],
      "The \"default_locale\" value is specified in the manifest, but no matching \"messages.json\" in the \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "" ],
      "The \"default_locale\" is missing but \"_locales\" exist.":[ "«default_locale» manglar men «_locales» finst." ],
      "The \"default_locale\" value is not specifed in the manifest, but a \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "" ],
      "Unsupported image extension":[ "Ustøtta bildefilending" ],
      "Icons should be one of JPG/JPEG, WebP, GIF, PNG or SVG.":[ "Ikon bør vere av typen JPG/JPEG, WebP, GIFF, PNG eller SVG." ],
      "\"applications\" property overridden by \"browser_specific_settings\" property":[ "Eigenskapen «applications» overkøyrt av eigenskapen \"browser_specific_settings\"" ],
      "The \"applications\" property is being ignored because it is superseded by the \"browser_specific_settings\" property which is also defined in your manifest. Consider removing applications.":[ "" ],
      "Empty language directory":[ "Tom språkkatalog" ],
      "messages.json file missing in \"%(path)s\"":[ "messages.json fil manglar i \"%(path)s\"" ],
      "Manifest key not supported by the specified minimum Firefox version":[ "Manifestnøkkel er ikkje støtta av den spesifiserte minimum Firefox-versjonen" ],
      "\"strict_min_version\" requires Firefox %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "" ],
      "\"%(fieldName)s\" is not supported in manifest versions %(versionRange)s.":[ "" ],
      "Permission not supported by the specified minimum Firefox version":[ "Løyve er ikkje støtta av den spesifiserte minimum Firefox-versjonen" ],
      "\"%(fieldName)s\" is not supported.":[ "" ],
      "Manifest key not supported by the specified minimum Firefox for Android version":[ "Manifestnøkkelen er ikkje støtta av den spesifiserte minimum Firefox for Android-versjonen" ],
      "\"strict_min_version\" requires Firefox for Android %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "" ],
      "Permission not supported by the specified minimum Firefox for Android version":[ "Løyve er ikkje  støtta av den spesifiserte minimum Firefox-versjonen for Android" ],
      "Linking to \"addons.mozilla.org\" is not allowed":[ "Det er ikkje tillate å lenke til «addons.mozilla.org»" ],
      "Links directing to \"addons.mozilla.org\" are not allowed to be used for homepage":[ "Lenker som leiar til «addons.mozilla.org» er ikkje tillatne å brukast for heimesida" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above":[ "" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above. Please update your manifest.json version to specify a minimum Firefox version.":[ "" ],
      "The extension ID is required in Manifest Version 3 and above.":[ "Utvidings-ID-en er påkravd i manifestversjon 3 og høgare." ],
      "See https://mzl.la/3PLZYdo for more information.":[ "Gå til https://mzl.la/3PLZYdo for meir informasjon." ],
      "%(instancePath)s: Privileged extensions should declare privileged permissions.":[ "" ],
      "This extension does not declare any privileged permission. It does not need to be signed with the privileged certificate. Please upload it directly to https://addons.mozilla.org/.":[ "" ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for privileged extensions.":[ "" ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for extensions that include privileged manifest fields.":[ "" ],
      "%(instancePath)s: privileged manifest fields are only allowed in privileged extensions.":[ "" ],
      "This extension does not include the \"mozillaAddons\" permission, which is required for privileged extensions.":[ "" ],
      "Cannot use actions in hidden add-ons.":[ "" ],
      "The hidden and browser_action/page_action (or action in Manifest Version 3 and above) properties are mutually exclusive.":[ "" ],
      "Use \"browser_specific_settings\" instead of \"applications\".":[ "" ],
      "The \"applications\" property in the manifest is deprecated and will no longer be accepted in Manifest Version 3 and above.":[ "" ],
      "\"applications\" is no longer allowed in Manifest Version 3 and above.":[ "" ],
      "The \"applications\" property in the manifest is no longer allowed in Manifest Version 3 and above. Use \"browser_specific_settings\" instead.":[ "" ],
      "The version string should be simplified because it won't be compatible with Manifest Version 3 and above.":[ "" ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros will no longer be allowed. Letters will no longer be allowed either. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "" ],
      "The version string should be simplified.":[ "" ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros are not allowed. Letters are no longer allowed. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "" ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported in manifest versions %(versionRange)s.":[ "/%(fieldName)s: \"%(permissionName)s\" er ikkje støtta i manifestversjonane %(versionRange)s." ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported.":[ "" ] } } }