module.exports = { domain:"messages",
  locale_data:{ messages:{ "":{ domain:"messages",
        plural_forms:"nplurals=1; plural=0;",
        lang:"th" },
      "Validation Summary:":[ "ข้อมูลสรุปการตรวจสอบความถูกต้อง:" ],
      Code:[ "โค้ด" ],
      Message:[ "ข้อความ" ],
      Description:[ "คำอธิบาย" ],
      File:[ "ไฟล์" ],
      Line:[ "บรรทัด" ],
      Column:[ "คอลัมน์" ],
      "Invalid manifest version range requested: --min-manifest-version (currently set to %(minManifestVersion)s) should not be greater than --max-manifest-version (currently set to %(maxManifestVersion)s).":[ "" ],
      "Your FTL is not valid.":[ "FTL ของคุณไม่ถูกต้อง" ],
      "Your FTL file could not be parsed.":[ "ไม่สามารถแยกวิเคราะห์ไฟล์ FTL ของคุณ" ],
      "Remote scripts are not allowed as per the Add-on Policies.":[ "ไม่อนุญาตให้ใช้สคริปต์ระยะไกลตามที่ระบุไว้ในนโยบายส่วนเสริม" ],
      "Please include all scripts in the add-on. For more information, refer to https://mzl.la/2uEOkYp.":[ "" ],
      "Inline scripts blocked by default":[ "สคริปต์แบบอินไลน์ถูกบล็อกตามค่าเริ่มต้น" ],
      "Default CSP rules prevent inline JavaScript from running (https://mzl.la/2pn32nd).":[ "" ],
      "Unadvised 3rd-party JS library":[ "ไลบรารี JS ของบุคคลที่สามที่ไม่แนะนำให้ใช้" ],
      "Your add-on uses a JavaScript library we do not recommend. Read more: https://bit.ly/1TRIyZY":[ "" ],
      "Known JS library detected":[ "ตรวจพบไลบรารี JS ที่รู้จัก" ],
      "JavaScript libraries are discouraged for simple add-ons, but are generally accepted.":[ "" ],
      "Due to both security and performance concerns, this may not be set using dynamic values which have not been adequately sanitized. This can lead to security issues or fairly serious performance degradation.":[ "" ],
      "{{api}} is not supported":[ "ไม่รองรับ {{api}}" ],
      "This API has not been implemented by Firefox.":[ "API นี้ยังไม่ได้ถูกนำมาใช้โดย Firefox" ],
      "\"{{api}}\" has been removed in Manifest Version 3 (`manifest_version` property)":[ "" ],
      "{{api}} is deprecated":[ "{{api}} เลิกใช้แล้ว" ],
      "This API has been deprecated by Firefox.":[ "API นี้เลิกใช้แล้วโดย Firefox" ],
      "Content script file could not be found.":[ "ไม่พบไฟล์สคริปต์เนื้อหา" ],
      "\"{{api}}\" is deprecated or unimplemented":[ "" ],
      "Content script file could not be found":[ "ไม่พบไฟล์สคริปต์เนื้อหา" ],
      "\"%(api)s\" can cause issues when loaded temporarily":[ "" ],
      "This API can cause issues when loaded temporarily using about:debugging in Firefox unless you specify \"browser_specific_settings.gecko.id\" in the manifest. Please see: https://mzl.la/2hizK4a for more.":[ "" ],
      "{{api}} is not supported in Firefox version {{minVersion}}":[ "ไม่รองรับ {{api}} ใน Firefox รุ่น {{minVersion}}" ],
      "This API is not implemented by the given minimum Firefox version":[ "API นี้ไม่ได้ถูกนำมาใช้โดย Firefox รุ่นต่ำสุดที่ระบุ" ],
      "{{api}} is not supported in Firefox for Android version {{minVersion}}":[ "ไม่รองรับ {{api}} ใน Firefox สำหรับ Android รุ่น {{minVersion}}" ],
      "This API is not implemented by the given minimum Firefox for Android version":[ "API นี้ไม่ได้ถูกนำมาใช้โดย Firefox สำหรับ Android รุ่นต่ำสุดที่ระบุ" ],
      "Content script file name should not be empty.":[ "ชื่อไฟล์สคริปต์เนื้อหาไม่ควรว่างเปล่า" ],
      "Content script file name should not be empty":[ "ชื่อไฟล์สคริปต์เนื้อหาไม่ควรว่างเปล่า" ],
      "\"%(method)s\" called with a non-literal uri":[ "" ],
      "Calling \"%(method)s\" with variable parameters can result in potential security vulnerabilities if the variable contains a remote URI. Consider using 'window.open' with the 'chrome=no' flag.":[ "" ],
      "\"%(method)s\" called with non-local URI":[ "" ],
      "Calling \"%(method)s\" with a non-local URI will result in the dialog being opened with chrome privileges.":[ "" ],
      "JavaScript syntax error":[ "ข้อผิดพลาดไวยากรณ์ JavaScript" ],
      "There is a JavaScript syntax error in your code, which might be related to some experimental JavaScript features that aren't an official part of the language specification and therefore not supported yet. The validation cannot continue on this file.":[ "" ],
      "Evaluation of strings as code can lead to security vulnerabilities and performance issues, even in the most innocuous of circumstances. Please avoid using `eval` and the `Function` constructor when at all possible.":[ "" ],
      "setTimeout, setInterval and execScript functions should be called only with function expressions as their first argument":[ "" ],
      "Unexpected global passed as an argument":[ "ตัวแปรส่วนกลางที่ไม่คาดคิดถูกส่งผ่านเป็นอาร์กิวเมนต์" ],
      "Passing a global as an argument is not recommended. Please make this a var instead.":[ "" ],
      "Use of document.write strongly discouraged.":[ "ไม่แนะนำให้ใช้ document.write เป็นอย่างยิ่ง" ],
      "document.write will fail in many circumstances when used in extensions, and has potentially severe security repercussions when used improperly. Therefore, it should not be used.":[ "" ],
      "Banned 3rd-party JS library":[ "ไลบรารี JS ของบุคคลที่สามที่ถูกห้าม" ],
      "Your add-on uses a JavaScript library we consider unsafe. Read more: https://bit.ly/1TRIyZY":[ "" ],
      "Your JSON contains block comments.":[ "JSON ของคุณมีความคิดเห็นเกี่ยวกับการปิดกั้น" ],
      "Only line comments (comments beginning with \"//\") are allowed in JSON files. Please remove block comments (comments beginning with \"/*\")":[ "" ],
      "Duplicate keys are not allowed in JSON files.":[ "ไม่อนุญาตให้ใช้คีย์ที่ซ้ำกันในไฟล์ JSON" ],
      "Duplicate key found in JSON file.":[ "" ],
      "Your JSON is not valid.":[ "JSON ของคุณไม่ถูกต้อง" ],
      "Your JSON file could not be parsed.":[ "ไม่สามารถแยกวิเคราะห์ไฟล์ JSON ของคุณได้" ],
      "Reserved filename found.":[ "พบชื่อไฟล์ที่สงวนไว้" ],
      "Files whose names are reserved have been found in the add-on. Please refrain from using them and rename your files.":[ "" ],
      "The package is invalid. It may contain entries using invalid characters, as an example using '\\' as a path separator is not allowed in Firefox. Try to recreate your add-on package (ZIP) and make sure all entries are using '/' as the path separator.":[ "" ],
      "We were unable to decompress the zip file.":[ "เราไม่สามารถคลายการบีบอัดไฟล์ zip ได้" ],
      "manifest.json was not found":[ "ไม่พบ manifest.json" ],
      "No manifest.json was found at the root of the extension. The package file must be a ZIP of the extension's files themselves, not of the containing directory. See: https://mzl.la/2r2McKv for more on packaging.":[ "" ],
      "File is too large to parse.":[ "ไฟล์มีขนาดใหญ่เกินกว่าที่จะแยกวิเคราะห์ได้" ],
      "This file is not binary and is too large to parse. Files larger than %(maxFileSizeToParseMB)sMB will not be parsed. Consider moving large lists of data out of JavaScript files and into JSON files, or splitting very large files into smaller ones.":[ "" ],
      "Hidden file flagged":[ "ตั้งค่าสถานะไฟล์ที่ซ่อนอยู่แล้ว" ],
      "Hidden files complicate the review process and can contain sensitive information about the system that generated the add-on. Please modify the packaging process so that these files aren't included.":[ "" ],
      "Package contains duplicate entries":[ "แพ็คเกจมีรายการที่ซ้ำกัน" ],
      "Flagged filename found":[ "พบชื่อไฟล์ที่ถูกตั้งค่าสถานะ" ],
      "Files were found that are either unnecessary or have been included unintentionally. They should be removed.":[ "" ],
      "The package contains multiple entries with the same name. This practice has been banned. Try unzipping and re-zipping your add-on package and try again.":[ "" ],
      "Flagged file extensions found":[ "พบนามสกุลไฟล์ที่ถูกตั้งค่าสถานะ" ],
      "Flagged file type found":[ "พบชนิดไฟล์ที่ถูกตั้งค่าสถานะ" ],
      "Files whose names end with flagged extensions have been found in the add-on. The extension of these files are flagged because they usually identify binary components. Please see https://bit.ly/review-policy for more information on the binary content review process.":[ "" ],
      "Package already signed":[ "แพ็คเกจถูกเซ็นชื่อแล้ว" ],
      "Add-ons which are already signed will be re-signed when published on AMO. This will replace any existing signatures on the add-on.":[ "" ],
      "Firefox add-ons are not allowed to run coin miners.":[ "ส่วนเสริมของ Firefox ไม่ได้รับอนุญาตให้เรียกใช้ตัวทำเหมืองเหรียญ" ],
      "We do not allow coinminer scripts to be run inside WebExtensions. See https://github.com/mozilla/addons-linter/issues/1643 for more details.":[ "" ],
      "String name is reserved for a predefined message":[ "ชื่อสตริงถูกสงวนไว้สำหรับข้อความที่กำหนดไว้ล่วงหน้า" ],
      "String names starting with @@ get translated to built-in constants (https://mzl.la/2BL9ZjE).":[ "" ],
      "String name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2Eztyi5).":[ "" ],
      "Placeholder for message is missing":[ "ตัวยึดตำแหน่งสำหรับข้อความขาดหายไป" ],
      "A placeholder used in the message is not defined.":[ "ตัวยึดตำแหน่งที่ใช้ในข้อความไม่ได้ถูกกำหนดไว้" ],
      "Placeholder name contains invalid characters":[ "ชื่อตัวยึดตำแหน่งมีอักขระที่ไม่ถูกต้อง" ],
      "Placeholder name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2ExbYez).":[ "" ],
      "Placeholder is missing the content property":[ "ตัวยึดตำแหน่งขาดคุณสมบัติเนื้อหา" ],
      "A placeholder needs a content property defining the replacement of it (https://mzl.la/2DT1MQd)":[ "" ],
      "Translation string is missing the message property":[ "สตริงการแปลขาดคุณสมบัติข้อความ" ],
      "No \"message\" message property is set for a string (https://mzl.la/2DSBTjA).":[ "ไม่ได้ตั้งค่าคุณสมบัติข้อความ \"message\" สำหรับสตริง (https://mzl.la/2DSBTjA)" ],
      "The field is required.":[ "จำเป็นต้องกรอกข้อมูลในช่องนี้" ],
      "See https://mzl.la/1ZOhoEN (MDN Docs) for more information.":[ "ดู https://mzl.la/1ZOhoEN (คู่มือ MDN) สำหรับข้อมูลเพิ่มเติม" ],
      "The permission type is unsupported.":[ "ไม่รองรับชนิดสิทธิอนุญาตนี้" ],
      "See https://mzl.la/1R1n1t0 (MDN Docs) for more information.":[ "ดู https://mzl.la/1R1n1t0 (คู่มือ MDN) สำหรับข้อมูลเพิ่มเติม" ],
      "See https://mzl.la/2Qn0fWC (MDN Docs) for more information.":[ "ดู https://mzl.la/2Qn0fWC (คู่มือ MDN) สำหรับข้อมูลเพิ่มเติม" ],
      "See https://mzl.la/3Woeqv4 (MDN Docs) for more information.":[ "" ],
      "Unknown permission.":[ "ไม่ทราบสิทธิอนุญาต" ],
      "%(instancePath)s: the following privileged permissions are only allowed in privileged extensions: %(privilegedPermissions)s.":[ "" ],
      "Invalid host permission.":[ "สิทธิอนุญาตโฮสต์ไม่ถูกต้อง" ],
      "Invalid install origin.":[ "ที่มาของการติดตั้งไม่ถูกต้อง" ],
      "Invalid install origin. A valid origin has - only - a scheme, hostname and optional port. See https://mzl.la/3TEbqbE (MDN Docs) for more information.":[ "" ],
      "The field is invalid.":[ "ช่องกรอกไม่ถูกต้อง" ],
      "\"manifest_version\" in the manifest.json is not a valid value":[ "\"manifest_version\" ที่อยู่ใน manifest.json ไม่ใช่ค่าที่ถูกต้อง" ],
      "See https://mzl.la/20PenXl (MDN Docs) for more information.":[ "ดู https://mzl.la/20PenXl (คู่มือ MDN) สำหรับข้อมูลเพิ่มเติม" ],
      "\"%(property)s\" allows remote code execution in manifest.json":[ "" ],
      "A custom \"%(property)s\" needs additional review.":[ "" ],
      "\"%(property)s\" allows 'eval', which has strong security and performance implications.":[ "" ],
      "In most cases the same result can be achieved differently, therefore it is generally prohibited":[ "" ],
      "The \"name\" property must be a string with no leading/trailing whitespaces.":[ "" ],
      "See http://mzl.la/1STmr48 (MDN Docs) for more information.":[ "ดู http://mzl.la/1STmr48 (คู่มือ MDN) สำหรับข้อมูลเพิ่มเติม" ],
      "\"update_url\" is not allowed.":[ "ไม่ได้รับอนุญาตให้ใช้ \"update_url\"" ],
      "\"applications.gecko.update_url\" or \"browser_specific_settings.gecko.update_url\" are not allowed for Mozilla-hosted add-ons.":[ "" ],
      "The \"update_url\" property is not used by Firefox.":[ "คุณสมบัติ \"update_url\" ไม่ได้ถูกใช้โดย Firefox" ],
      "The \"update_url\" is not used by Firefox in the root of a manifest; your add-on will be updated via the Add-ons site and not your \"update_url\". See: https://mzl.la/25zqk4O":[ "" ],
      "\"strict_max_version\" not required.":[ "ไม่จำเป็นต้องมี \"strict_max_version\"" ],
      "\"strict_max_version\" shouldn't be used unless the add-on is expected not to work with future versions of Firefox.":[ "" ],
      "Manifest Version 3 is not fully supported on Firefox for Android.":[ "" ],
      "No \"%(property)s\" property found in manifest.json":[ "" ],
      "\"%(property)s\" is required":[ "" ],
      "An icon defined in the manifest could not be found in the package.":[ "ไม่พบไอคอนที่กำหนดไว้ในไฟล์กำกับในแพ็คเกจ" ],
      "Icon could not be found at \"%(path)s\".":[ "ไม่พบไอคอนที่ \"%(path)s\"" ],
      "A background script defined in the manifest could not be found.":[ "ไม่พบสคริปต์เบื้องหลังที่กำหนดไว้ในไฟล์กำกับ" ],
      "A background page defined in the manifest could not be found.":[ "ไม่พบหน้าเบื้องหลังที่กำหนดไว้ในไฟล์กำกับ" ],
      "Background script could not be found at \"%(path)s\".":[ "ไม่พบสคริปต์เบื้องหลังที่ \"%(path)s\"" ],
      "Background page could not be found at \"%(path)s\".":[ "ไม่พบหน้าเบื้องหลังที่ \"%(path)s\"" ],
      "A content script defined in the manifest could not be found.":[ "ไม่พบสคริปต์เนื้อหาที่กำหนดไว้ในไฟล์กำกับ" ],
      "A content script css file defined in the manifest could not be found.":[ "ไม่พบไฟล์ CSS สคริปต์เนื้อหาที่กำหนดไว้ในไฟล์กำกับ" ],
      "Content script defined in the manifest could not be found at \"%(path)s\".":[ "ไม่พบสคริปต์เนื้อหาที่กำหนดไว้ในไฟล์กำกับที่ \"%(path)s\"" ],
      "Content script css file defined in the manifest could not be found at \"%(path)s\".":[ "ไม่พบไฟล์ CSS สคริปต์เนื้อหาที่กำหนดไว้ในไฟล์กำกับที่ \"%(path)s\"" ],
      "A dictionary file defined in the manifest could not be found.":[ "ไม่พบไฟล์พจนานุกรมที่กำหนดไว้ในไฟล์กำกับ" ],
      "Dictionary file defined in the manifest could not be found at \"%(path)s\".":[ "ไม่พบไฟล์พจนานุกรมที่กำหนดไว้ในไฟล์กำกับที่ \"%(path)s\"" ],
      "The manifest contains multiple dictionaries.":[ "ไฟล์กำกับประกอบด้วยพจนานุกรมหลายเล่ม" ],
      "Multiple dictionaries were defined in the manifest, which is unsupported.":[ "มีพจนานุกรมหลายเล่มที่กำหนดในไฟล์กำกับซึ่งไม่รองรับ" ],
      "The manifest contains a dictionaries object, but it is empty.":[ "ไฟล์กำกับประกอบด้วยวัตถุพจนานุกรม แต่ว่างเปล่า" ],
      "A dictionaries object was defined in the manifest, but it was empty.":[ "วัตถุพจนานุกรมถูกกำหนดในไฟล์กำกับ แต่ว่างเปล่า" ],
      "The manifest contains a dictionary but no id property.":[ "ไฟล์กำกับประกอบด้วยพจนานุกรม แต่ไม่มีคุณลักษณะ id" ],
      "A dictionary was found in the manifest, but there was no id set.":[ "พบพจนานุกรมในไฟล์กำกับ แต่ไม่ได้ตั้งค่า id ไว้" ],
      "Please refer to https://github.com/mozilla-extensions/xpi-manifest to learn more about privileged extensions and signing.":[ "" ],
      "Forbidden content found in add-on.":[ "พบเนื้อหาที่ไม่ได้รับอนุญาตในส่วนเสริม" ],
      "This add-on contains forbidden content.":[ "ส่วนเสริมนี้มีเนื้อหาที่ไม่ได้รับอนุญาต" ],
      "Icons must be square.":[ "ไอคอนต้องเป็นสี่เหลี่ยมจัตุรัส" ],
      "Icon at \"%(path)s\" must be square.":[ "ไอคอนที่ \"%(path)s\" ต้องเป็นสี่เหลี่ยมจัตุรัส" ],
      "The size of the icon does not match the manifest.":[ "ขนาดของไอคอนไม่ตรงกับไฟล์กำกับ" ],
      "Expected icon at \"%(path)s\" to be %(expected)d pixels wide but was %(actual)d.":[ "" ],
      "\"%(fieldName)s\" is ignored for non-privileged add-ons.":[ "" ],
      "Corrupt image file":[ "ไฟล์ภาพเสียหาย" ],
      "Expected icon file at \"%(path)s\" is corrupted":[ "ไฟล์ไอคอนที่คาดไว้ที่ \"%(path)s\" เสียหาย" ],
      "This property has been deprecated.":[ "" ],
      "This theme LWT alias has been removed in Firefox 70.":[ "นามแฝง LWT ของชุดตกแต่งนี้ได้ถูกเอาออกแล้วใน Firefox 70" ],
      "See https://mzl.la/2T11Lkc (MDN Docs) for more information.":[ "ดู https://mzl.la/2T11Lkc (คู่มือ MDN) สำหรับข้อมูลเพิ่มเติม" ],
      "Theme image for \"%(type)s\" could not be found at \"%(path)s\"":[ "ไม่พบภาพชุดตกแต่งสำหรับ \"%(type)s\" ที่ \"%(path)s\"" ],
      "\"%(fieldName)s\" manifest field is only used for privileged and temporarily installed extensions.":[ "" ],
      "Corrupted theme image file":[ "ไฟล์ภาพชุดตกแต่งเสียหาย" ],
      "Theme image file at \"%(path)s\" is corrupted":[ "ไฟล์ภาพชุดตกแต่งที่ \"%(path)s\" เสียหาย" ],
      "Theme image file has an unsupported file extension":[ "ไฟล์ภาพชุดตกแต่งมีนามสกุลไฟล์ที่ไม่รองรับ" ],
      "Theme image file at \"%(path)s\" has an unsupported file extension":[ "ไฟล์ภาพชุดตกแต่งที่ \"%(path)s\" มีนามสกุลไฟล์ที่ไม่รองรับ" ],
      "Theme image file has an unsupported mime type":[ "ไฟล์ภาพของชุดตกแต่งมีชนิด MIME ที่ไม่รองรับ" ],
      "Theme image file at \"%(path)s\" has the unsupported mime type \"%(mime)s\"":[ "ไฟล์ภาพของชุดตกแต่งที่ \"%(path)s\" มีชนิด mime ที่ไม่รองรับ \"%(mime)s\"" ],
      "Theme image file mime type does not match its file extension":[ "ชนิด mime ของไฟล์ภาพของชุดตกแต่งไม่ตรงกับนามสกุลไฟล์" ],
      "Theme image file extension at \"%(path)s\" does not match its actual mime type \"%(mime)s\"":[ "นามสกุลไฟล์ภาพของชุดตกแต่งที่ \"%(path)s\" ไม่ตรงกับชนิด mime จริง \"%(mime)s\"" ],
      "The \"default_locale\" is missing localizations.":[ "\"default_locale\" ขาดข้อมูลการแปลเป็นภาษาท้องถิ่น" ],
      "The \"default_locale\" value is specified in the manifest, but no matching \"messages.json\" in the \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "" ],
      "The \"default_locale\" is missing but \"_locales\" exist.":[ "\"default_locale\" ขาดหายไป แต่มี \"_locales\" อยู่" ],
      "The \"default_locale\" value is not specifed in the manifest, but a \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "" ],
      "Unsupported image extension":[ "ไม่รองรับนามสกุลไฟล์ภาพนี้" ],
      "Icons should be one of JPG/JPEG, WebP, GIF, PNG or SVG.":[ "ไอคอนควรมีนามสกุลไฟล์เป็น JPG/JPEG, WebP, GIF, PNG หรือ SVG" ],
      "\"applications\" property overridden by \"browser_specific_settings\" property":[ "" ],
      "The \"applications\" property is being ignored because it is superseded by the \"browser_specific_settings\" property which is also defined in your manifest. Consider removing applications.":[ "" ],
      "Empty language directory":[ "ไดเรกทอรีภาษาที่ว่างเปล่า" ],
      "messages.json file missing in \"%(path)s\"":[ "ไฟล์ messages.json ขาดหายไปใน \"%(path)s\"" ],
      "Manifest key not supported by the specified minimum Firefox version":[ "คีย์ไฟล์กำกับไม่รองรับโดย Firefox รุ่นขั้นต่ำที่ระบุ" ],
      "\"strict_min_version\" requires Firefox %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "" ],
      "\"%(fieldName)s\" is not supported in manifest versions %(versionRange)s.":[ "" ],
      "Permission not supported by the specified minimum Firefox version":[ "สิทธิอนุญาตไม่รองรับโดย Firefox รุ่นขั้นต่ำที่ระบุ" ],
      "\"%(fieldName)s\" is not supported.":[ "" ],
      "Manifest key not supported by the specified minimum Firefox for Android version":[ "คีย์ไฟล์กำกับไม่รองรับโดย Firefox สำหรับ Android รุ่นขั้นต่ำที่ระบุ" ],
      "\"strict_min_version\" requires Firefox for Android %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "" ],
      "Permission not supported by the specified minimum Firefox for Android version":[ "สิทธิอนุญาตไม่รองรับโดย Firefox สำหรับ Android รุ่นขั้นต่ำที่ระบุ" ],
      "Linking to \"addons.mozilla.org\" is not allowed":[ "" ],
      "Links directing to \"addons.mozilla.org\" are not allowed to be used for homepage":[ "" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above":[ "" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above. Please update your manifest.json version to specify a minimum Firefox version.":[ "" ],
      "The extension ID is required in Manifest Version 3 and above.":[ "" ],
      "See https://mzl.la/3PLZYdo for more information.":[ "" ],
      "%(instancePath)s: Privileged extensions should declare privileged permissions.":[ "" ],
      "This extension does not declare any privileged permission. It does not need to be signed with the privileged certificate. Please upload it directly to https://addons.mozilla.org/.":[ "" ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for privileged extensions.":[ "" ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for extensions that include privileged manifest fields.":[ "" ],
      "%(instancePath)s: privileged manifest fields are only allowed in privileged extensions.":[ "" ],
      "This extension does not include the \"mozillaAddons\" permission, which is required for privileged extensions.":[ "" ],
      "Cannot use actions in hidden add-ons.":[ "" ],
      "The hidden and browser_action/page_action (or action in Manifest Version 3 and above) properties are mutually exclusive.":[ "" ],
      "Use \"browser_specific_settings\" instead of \"applications\".":[ "" ],
      "The \"applications\" property in the manifest is deprecated and will no longer be accepted in Manifest Version 3 and above.":[ "" ],
      "\"applications\" is no longer allowed in Manifest Version 3 and above.":[ "" ],
      "The \"applications\" property in the manifest is no longer allowed in Manifest Version 3 and above. Use \"browser_specific_settings\" instead.":[ "" ],
      "The version string should be simplified because it won't be compatible with Manifest Version 3 and above.":[ "" ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros will no longer be allowed. Letters will no longer be allowed either. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "" ],
      "The version string should be simplified.":[ "" ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros are not allowed. Letters are no longer allowed. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "" ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported in manifest versions %(versionRange)s.":[ "" ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported.":[ "" ] } } }