module.exports = { domain:"messages",
  locale_data:{ messages:{ "":{ domain:"messages",
        plural_forms:"nplurals=2; plural=(n != 1);",
        lang:"fi" },
      "Validation Summary:":[ "Validoinnin yhteenveto:" ],
      Code:[ "<PERSON><PERSON><PERSON>" ],
      Message:[ "Viesti" ],
      Description:[ "Kuvaus" ],
      File:[ "Tiedosto" ],
      Line:[ "Rivi" ],
      Column:[ "Sarake" ],
      "Invalid manifest version range requested: --min-manifest-version (currently set to %(minManifestVersion)s) should not be greater than --max-manifest-version (currently set to %(maxManifestVersion)s).":[ "" ],
      "Your FTL is not valid.":[ "Määrittelemäsi FTL ei ole kelvollinen." ],
      "Your FTL file could not be parsed.":[ "FTL-tiedostoasi ei voitu jäsentää." ],
      "Remote scripts are not allowed as per the Add-on Policies.":[ "Lisäosakäytäntöjen vuoksi etäskriptejä ei sallita." ],
      "Please include all scripts in the add-on. For more information, refer to https://mzl.la/2uEOkYp.":[ "" ],
      "Inline scripts blocked by default":[ "Sisennetyt skriptit on oletusarvoisesti estetty" ],
      "Default CSP rules prevent inline JavaScript from running (https://mzl.la/2pn32nd).":[ "" ],
      "Unadvised 3rd-party JS library":[ "Ei suositeltu kolmannen osapuolen JS-kirjasto" ],
      "Your add-on uses a JavaScript library we do not recommend. Read more: https://bit.ly/1TRIyZY":[ "" ],
      "Known JS library detected":[ "Tunnettu JS-kirjasto havaittu" ],
      "JavaScript libraries are discouraged for simple add-ons, but are generally accepted.":[ "" ],
      "Due to both security and performance concerns, this may not be set using dynamic values which have not been adequately sanitized. This can lead to security issues or fairly serious performance degradation.":[ "" ],
      "{{api}} is not supported":[ "{{api}} ei ole tuettu" ],
      "This API has not been implemented by Firefox.":[ "Tätä API:a ei ole vielä toteutettu Firefoxissa." ],
      "\"{{api}}\" has been removed in Manifest Version 3 (`manifest_version` property)":[ "" ],
      "Content script file could not be found.":[ "Sisältökriptitiedostoa ei löytynyt." ],
      "\"{{api}}\" is deprecated or unimplemented":[ "" ],
      "Content script file could not be found":[ "Sisältökriptitiedostoa ei löytynyt" ],
      "\"%(api)s\" can cause issues when loaded temporarily":[ "" ],
      "This API can cause issues when loaded temporarily using about:debugging in Firefox unless you specify \"browser_specific_settings.gecko.id\" in the manifest. Please see: https://mzl.la/2hizK4a for more.":[ "" ],
      "{{api}} is not supported in Firefox version {{minVersion}}":[ "{{api}} ei ole tuettu Firefox-versiossa {{minVersion}}" ],
      "This API is not implemented by the given minimum Firefox version":[ "Tätä API:a ei ole vielä toteutettu annetussa Firefoxin minimiversiossa" ],
      "{{api}} is not supported in Firefox for Android version {{minVersion}}":[ "{{api}} ei ole tuettu Firefoxin Android-versiossa {{minVersion}}" ],
      "Content script file name should not be empty.":[ "Sisällön skriptitiedoston nimi ei saa olla tyhjä." ],
      "Content script file name should not be empty":[ "Sisällön skriptitiedoston nimi ei saa olla tyhjä" ],
      "\"%(method)s\" called with a non-literal uri":[ "" ],
      "Calling \"%(method)s\" with variable parameters can result in potential security vulnerabilities if the variable contains a remote URI. Consider using 'window.open' with the 'chrome=no' flag.":[ "" ],
      "\"%(method)s\" called with non-local URI":[ "" ],
      "Calling \"%(method)s\" with a non-local URI will result in the dialog being opened with chrome privileges.":[ "" ],
      "JavaScript syntax error":[ "JavaScript-syntaksivirhe" ],
      "There is a JavaScript syntax error in your code, which might be related to some experimental JavaScript features that aren't an official part of the language specification and therefore not supported yet. The validation cannot continue on this file.":[ "" ],
      "Evaluation of strings as code can lead to security vulnerabilities and performance issues, even in the most innocuous of circumstances. Please avoid using `eval` and the `Function` constructor when at all possible.":[ "" ],
      "setTimeout, setInterval and execScript functions should be called only with function expressions as their first argument":[ "" ],
      "Unexpected global passed as an argument":[ "" ],
      "Passing a global as an argument is not recommended. Please make this a var instead.":[ "" ],
      "Use of document.write strongly discouraged.":[ "Document.write:n käyttöä ei suositella." ],
      "document.write will fail in many circumstances when used in extensions, and has potentially severe security repercussions when used improperly. Therefore, it should not be used.":[ "" ],
      "Banned 3rd-party JS library":[ "Kielletty kolmannen osapuolen JS-kirjasto" ],
      "Your add-on uses a JavaScript library we consider unsafe. Read more: https://bit.ly/1TRIyZY":[ "" ],
      "Your JSON contains block comments.":[ "Määrittelemäsi JSON sisältää lohkokommentteja." ],
      "Only line comments (comments beginning with \"//\") are allowed in JSON files. Please remove block comments (comments beginning with \"/*\")":[ "" ],
      "Duplicate keys are not allowed in JSON files.":[ "Kahdennetut avaimet eivät ole sallittuja JSON-tiedostoissa." ],
      "Duplicate key found in JSON file.":[ "" ],
      "Your JSON is not valid.":[ "Määrittelemäsi JSON ei ole kelvollinen." ],
      "Your JSON file could not be parsed.":[ "JSON-tiedostoasi ei voitu jäsentää." ],
      "Reserved filename found.":[ "Varattu tiedostonimi löytyi." ],
      "Files whose names are reserved have been found in the add-on. Please refrain from using them and rename your files.":[ "" ],
      "The package is invalid. It may contain entries using invalid characters, as an example using '\\' as a path separator is not allowed in Firefox. Try to recreate your add-on package (ZIP) and make sure all entries are using '/' as the path separator.":[ "" ],
      "We were unable to decompress the zip file.":[ "Zip-tiedostoa ei voitu purkaa." ],
      "manifest.json was not found":[ "manifest.json-tiedostoa ei löytynyt" ],
      "No manifest.json was found at the root of the extension. The package file must be a ZIP of the extension's files themselves, not of the containing directory. See: https://mzl.la/2r2McKv for more on packaging.":[ "" ],
      "File is too large to parse.":[ "Tiedosto on liian suuri jäsennettäväksi." ],
      "This file is not binary and is too large to parse. Files larger than %(maxFileSizeToParseMB)sMB will not be parsed. Consider moving large lists of data out of JavaScript files and into JSON files, or splitting very large files into smaller ones.":[ "" ],
      "Hidden file flagged":[ "Piilotettu tiedosto merkitty" ],
      "Hidden files complicate the review process and can contain sensitive information about the system that generated the add-on. Please modify the packaging process so that these files aren't included.":[ "" ],
      "Package contains duplicate entries":[ "" ],
      "Flagged filename found":[ "" ],
      "Files were found that are either unnecessary or have been included unintentionally. They should be removed.":[ "" ],
      "The package contains multiple entries with the same name. This practice has been banned. Try unzipping and re-zipping your add-on package and try again.":[ "" ],
      "Flagged file extensions found":[ "" ],
      "Flagged file type found":[ "Merkitty tiedostotyyppi löytyi" ],
      "Files whose names end with flagged extensions have been found in the add-on. The extension of these files are flagged because they usually identify binary components. Please see https://bit.ly/review-policy for more information on the binary content review process.":[ "" ],
      "Package already signed":[ "Paketti on jo allekirjoitettu" ],
      "Add-ons which are already signed will be re-signed when published on AMO. This will replace any existing signatures on the add-on.":[ "" ],
      "Firefox add-ons are not allowed to run coin miners.":[ "Firefox-lisäosat eivät saa ajaa kolikonkaivajia." ],
      "We do not allow coinminer scripts to be run inside WebExtensions. See https://github.com/mozilla/addons-linter/issues/1643 for more details.":[ "" ],
      "String name is reserved for a predefined message":[ "" ],
      "String names starting with @@ get translated to built-in constants (https://mzl.la/2BL9ZjE).":[ "" ],
      "String name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2Eztyi5).":[ "" ],
      "Placeholder for message is missing":[ "Viestin paikkamerkki puuttuu" ],
      "A placeholder used in the message is not defined.":[ "" ],
      "Placeholder name contains invalid characters":[ "" ],
      "Placeholder name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2ExbYez).":[ "" ],
      "Placeholder is missing the content property":[ "" ],
      "A placeholder needs a content property defining the replacement of it (https://mzl.la/2DT1MQd)":[ "" ],
      "Translation string is missing the message property":[ "Käännösmerkkijonosta puuttuu \"message\" -ominaisuus" ],
      "No \"message\" message property is set for a string (https://mzl.la/2DSBTjA).":[ "Viestiominaisuutta \"message\" ei ole asetettu merkkijonolle (https://mzl.la/2DSBTjA)." ],
      "The field is required.":[ "Tämä kenttä vaaditaan." ],
      "See https://mzl.la/1ZOhoEN (MDN Docs) for more information.":[ "Katso lisätietoja osoitteesta https://mzl.la/1ZOhoEN (MDN-dokumentit)." ],
      "The permission type is unsupported.":[ "Käyttöoikeustyyppi ei ole tuettu." ],
      "See https://mzl.la/1R1n1t0 (MDN Docs) for more information.":[ "Katso lisätietoja osoitteesta https://mzl.la/1R1n1t0 (MDN-dokumentit)." ],
      "See https://mzl.la/3Woeqv4 (MDN Docs) for more information.":[ "" ],
      "Unknown permission.":[ "Tuntematon käyttöoikeus." ],
      "%(instancePath)s: the following privileged permissions are only allowed in privileged extensions: %(privilegedPermissions)s.":[ "" ],
      "Invalid install origin. A valid origin has - only - a scheme, hostname and optional port. See https://mzl.la/3TEbqbE (MDN Docs) for more information.":[ "" ],
      "The field is invalid.":[ "Kenttä on virheellinen." ],
      "\"manifest_version\" in the manifest.json is not a valid value":[ "\"manifest_version\" manifest.json-tiedostossa ei ole kelvollinen arvo" ],
      "See https://mzl.la/20PenXl (MDN Docs) for more information.":[ "Katso lisätietoja osoitteesta https://mzl.la/20PenXl (MDN-dokumentit)." ],
      "\"%(property)s\" allows remote code execution in manifest.json":[ "" ],
      "A custom \"%(property)s\" needs additional review.":[ "" ],
      "\"%(property)s\" allows 'eval', which has strong security and performance implications.":[ "" ],
      "In most cases the same result can be achieved differently, therefore it is generally prohibited":[ "" ],
      "The \"name\" property must be a string with no leading/trailing whitespaces.":[ "" ],
      "See http://mzl.la/1STmr48 (MDN Docs) for more information.":[ "Katso lisätietoja osoitteesta http://mzl.la/1STmr48 (MDN-dokumentit)." ],
      "\"update_url\" is not allowed.":[ "\"update_url\" ei ole sallittu." ],
      "\"applications.gecko.update_url\" or \"browser_specific_settings.gecko.update_url\" are not allowed for Mozilla-hosted add-ons.":[ "" ],
      "The \"update_url\" property is not used by Firefox.":[ "Firefox ei käytä \"update_url\"-ominaisuutta." ],
      "The \"update_url\" is not used by Firefox in the root of a manifest; your add-on will be updated via the Add-ons site and not your \"update_url\". See: https://mzl.la/25zqk4O":[ "" ],
      "\"strict_max_version\" not required.":[ "\"strict_max_version\" ei ole vaadittu." ],
      "\"strict_max_version\" shouldn't be used unless the add-on is expected not to work with future versions of Firefox.":[ "" ],
      "Manifest Version 3 is not fully supported on Firefox for Android.":[ "" ],
      "No \"%(property)s\" property found in manifest.json":[ "" ],
      "\"%(property)s\" is required":[ "" ],
      "An icon defined in the manifest could not be found in the package.":[ "Manifestissa määritettyä kuvaketta ei löytynyt paketista." ],
      "Icon could not be found at \"%(path)s\".":[ "Kuvaketta ei löytynyt polusta \"%(path)s\"." ],
      "Background script could not be found at \"%(path)s\".":[ "Taustaskriptiä ei löydetty polusta \"%(path)s\"." ],
      "Background page could not be found at \"%(path)s\".":[ "Taustasivua ei löydetty polusta \"%(path)s\"." ],
      "Content script defined in the manifest could not be found at \"%(path)s\".":[ "" ],
      "Content script css file defined in the manifest could not be found at \"%(path)s\".":[ "" ],
      "The manifest contains multiple dictionaries.":[ "" ],
      "Multiple dictionaries were defined in the manifest, which is unsupported.":[ "" ],
      "The manifest contains a dictionaries object, but it is empty.":[ "" ],
      "A dictionaries object was defined in the manifest, but it was empty.":[ "" ],
      "The manifest contains a dictionary but no id property.":[ "" ],
      "A dictionary was found in the manifest, but there was no id set.":[ "" ],
      "Please refer to https://github.com/mozilla-extensions/xpi-manifest to learn more about privileged extensions and signing.":[ "" ],
      "Forbidden content found in add-on.":[ "Lisäosasta löytyi kiellettyä sisältöä." ],
      "This add-on contains forbidden content.":[ "Tämä lisäosa sisältää kiellettyä sisältöä." ],
      "Icons must be square.":[ "Kuvakkeiden tulee olla muodoltaan neliöitä." ],
      "Icon at \"%(path)s\" must be square.":[ "Kuvakkeen polussa \"%(path)s\" tulee olla neliö." ],
      "The size of the icon does not match the manifest.":[ "Kuvakkeen koko ei vastaa manifestia." ],
      "Expected icon at \"%(path)s\" to be %(expected)d pixels wide but was %(actual)d.":[ "" ],
      "\"%(fieldName)s\" is ignored for non-privileged add-ons.":[ "" ],
      "Corrupt image file":[ "Vahingoittunut kuvatiedosto" ],
      "Expected icon file at \"%(path)s\" is corrupted":[ "Odotettu kuvatiedosto polussa \"%(path)s\" on vioittunut" ],
      "This property has been deprecated.":[ "" ],
      "This theme LWT alias has been removed in Firefox 70.":[ "" ],
      "See https://mzl.la/2T11Lkc (MDN Docs) for more information.":[ "Katso lisätietoja osoitteesta https://mzl.la/2T11Lkc (MDN-dokumentit)." ],
      "\"%(fieldName)s\" manifest field is only used for privileged and temporarily installed extensions.":[ "" ],
      "Theme image file has an unsupported file extension":[ "" ],
      "Theme image file at \"%(path)s\" has an unsupported file extension":[ "" ],
      "Theme image file has an unsupported mime type":[ "" ],
      "Theme image file at \"%(path)s\" has the unsupported mime type \"%(mime)s\"":[ "" ],
      "Theme image file mime type does not match its file extension":[ "" ],
      "Theme image file extension at \"%(path)s\" does not match its actual mime type \"%(mime)s\"":[ "" ],
      "The \"default_locale\" is missing localizations.":[ "Kohteesta \"default_locale\" puuttuu lokalisaatioita." ],
      "The \"default_locale\" value is specified in the manifest, but no matching \"messages.json\" in the \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "" ],
      "The \"default_locale\" is missing but \"_locales\" exist.":[ "\"default_locale\" puuttuu, mutta \"_locales\" on olemassa." ],
      "The \"default_locale\" value is not specifed in the manifest, but a \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "" ],
      "Unsupported image extension":[ "Kuvan tiedostotyyppiä ei tueta" ],
      "Icons should be one of JPG/JPEG, WebP, GIF, PNG or SVG.":[ "Kuvakkeen tulee olla muotoa JPG/JPEG, WebP, GIF, PNG tai SVG." ],
      "\"applications\" property overridden by \"browser_specific_settings\" property":[ "" ],
      "The \"applications\" property is being ignored because it is superseded by the \"browser_specific_settings\" property which is also defined in your manifest. Consider removing applications.":[ "" ],
      "Empty language directory":[ "Tyhjä kielihakemisto" ],
      "messages.json file missing in \"%(path)s\"":[ "messages.json -tiedosto puuttuu polusta \"%(path)s\"" ],
      "Manifest key not supported by the specified minimum Firefox version":[ "" ],
      "\"strict_min_version\" requires Firefox %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "" ],
      "\"%(fieldName)s\" is not supported in manifest versions %(versionRange)s.":[ "" ],
      "Permission not supported by the specified minimum Firefox version":[ "" ],
      "\"%(fieldName)s\" is not supported.":[ "" ],
      "Manifest key not supported by the specified minimum Firefox for Android version":[ "" ],
      "\"strict_min_version\" requires Firefox for Android %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "" ],
      "Permission not supported by the specified minimum Firefox for Android version":[ "" ],
      "Linking to \"addons.mozilla.org\" is not allowed":[ "" ],
      "Links directing to \"addons.mozilla.org\" are not allowed to be used for homepage":[ "" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above":[ "" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above. Please update your manifest.json version to specify a minimum Firefox version.":[ "" ],
      "The extension ID is required in Manifest Version 3 and above.":[ "" ],
      "See https://mzl.la/3PLZYdo for more information.":[ "" ],
      "%(instancePath)s: Privileged extensions should declare privileged permissions.":[ "" ],
      "This extension does not declare any privileged permission. It does not need to be signed with the privileged certificate. Please upload it directly to https://addons.mozilla.org/.":[ "" ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for privileged extensions.":[ "" ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for extensions that include privileged manifest fields.":[ "" ],
      "%(instancePath)s: privileged manifest fields are only allowed in privileged extensions.":[ "" ],
      "This extension does not include the \"mozillaAddons\" permission, which is required for privileged extensions.":[ "" ],
      "Cannot use actions in hidden add-ons.":[ "" ],
      "The hidden and browser_action/page_action (or action in Manifest Version 3 and above) properties are mutually exclusive.":[ "" ],
      "Use \"browser_specific_settings\" instead of \"applications\".":[ "" ],
      "The \"applications\" property in the manifest is deprecated and will no longer be accepted in Manifest Version 3 and above.":[ "" ],
      "\"applications\" is no longer allowed in Manifest Version 3 and above.":[ "" ],
      "The \"applications\" property in the manifest is no longer allowed in Manifest Version 3 and above. Use \"browser_specific_settings\" instead.":[ "" ],
      "The version string should be simplified because it won't be compatible with Manifest Version 3 and above.":[ "" ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros will no longer be allowed. Letters will no longer be allowed either. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "" ],
      "The version string should be simplified.":[ "" ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros are not allowed. Letters are no longer allowed. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "" ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported in manifest versions %(versionRange)s.":[ "" ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported.":[ "" ] } } }