module.exports = { domain:"messages",
  locale_data:{ messages:{ "":{ domain:"messages",
        plural_forms:"nplurals=2; plural=(n != 1);",
        lang:"hu" },
      "Validation Summary:":[ "Ellenőrzési összefoglaló:" ],
      Code:[ "Kód" ],
      Message:[ "Üzenet" ],
      Description:[ "Leírás" ],
      File:[ "Fájl" ],
      Line:[ "Sor" ],
      Column:[ "Oszlop" ],
      "Invalid manifest version range requested: --min-manifest-version (currently set to %(minManifestVersion)s) should not be greater than --max-manifest-version (currently set to %(maxManifestVersion)s).":[ "A jegyzékfájl kért verziótartománya érvénytelen: a --min-manifest-version (jelenleg érték: %(minManifestVersion)s) nem lehet nagyobb, mint a --max-manifest-version (jelenlegi ért<PERSON>k: %(maxManifestVersion)s)." ],
      "Your FTL is not valid.":[ "Az FTL érvénytelen." ],
      "Your FTL file could not be parsed.":[ "Az FTL fájl nem dolgozható fel." ],
      "Remote scripts are not allowed as per the Add-on Policies.":[ "A távoli parancsfájlok a kiegészítők irányelvei szerint nem engedélyezettek." ],
      "Please include all scripts in the add-on. For more information, refer to https://mzl.la/2uEOkYp.":[ "Adja meg az összes, a kiegészítőben lévő parancsfájlt. További információkért lásd: https://mzl.la/2uEOkYp." ],
      "Inline scripts blocked by default":[ "A soron belüli parancsfájlok alapértelmezetten blokkoltak" ],
      "Default CSP rules prevent inline JavaScript from running (https://mzl.la/2pn32nd).":[ "Az alapértelmezett CSP-szabályok megakadályozzák a beágyazott JavaScript futtatását (https://mzl.la/2pn32nd)." ],
      "Unadvised 3rd-party JS library":[ "Nem tanácsolt harmadik féltől származó JS könyvtár" ],
      "Your add-on uses a JavaScript library we do not recommend. Read more: https://bit.ly/1TRIyZY":[ "A kiegészítője egy általunk nem javasolt JavaScript programkönyvtárat használ. Bővebben: https://bit.ly/1TRIyZY" ],
      "Known JS library detected":[ "Ismert JS könyvtár észlelve" ],
      "JavaScript libraries are discouraged for simple add-ons, but are generally accepted.":[ "A JavaScript programkönyvtárak nem javasoltak az egyszerű kiegészítőknél, de általában elfogadottak." ],
      "Due to both security and performance concerns, this may not be set using dynamic values which have not been adequately sanitized. This can lead to security issues or fairly serious performance degradation.":[ "Biztonsági és teljesítménybeli okokból ez nem adható meg olyan dinamikus értékekkel, amelyek nem lettek megfelelően megtisztítva. Ez biztonsági problémákhoz, vagy komoly teljesítménycsökkenéshez vezethet." ],
      "{{api}} is not supported":[ "A(z) {{api}} nem támogatott" ],
      "This API has not been implemented by Firefox.":[ "Ez az API még nincs megvalósítva a Firefoxban." ],
      "\"{{api}}\" has been removed in Manifest Version 3 (`manifest_version` property)":[ "A(z) „{{api}}” el lett távolítva a leíró 3. verziójából (`manifest_version` tulajdonság)" ],
      "{{api}} is deprecated":[ "A(z) {{api}} elavult" ],
      "This API has been deprecated by Firefox.":[ "Ez az API elavult a Firefoxban." ],
      "Content script file could not be found.":[ "A tartalomparancsfájl nem található." ],
      "\"{{api}}\" is deprecated or unimplemented":[ "A(z) „{{api}}” elavult és nincs megvalósítva" ],
      "Content script file could not be found":[ "A tartalomparancsfájl nem található" ],
      "\"%(api)s\" can cause issues when loaded temporarily":[ "A(z) „%(api)s” problémákat okozhat, ha ideiglenesen betöltik" ],
      "This API can cause issues when loaded temporarily using about:debugging in Firefox unless you specify \"browser_specific_settings.gecko.id\" in the manifest. Please see: https://mzl.la/2hizK4a for more.":[ "Ez az API problémákat okozhat, ha a Firefox about:debugging használatával ideiglenesen betöltik, hacsak nem adja meg a „browser_specific_settings.gecko.id” értéket a jegyzékfájlban. További információkért lásd: https://mzl.la/2hizK4a." ],
      "{{api}} is not supported in Firefox version {{minVersion}}":[ "A(z) {{api}} nem támogatott a Firefox {{minVersion}} verzióban" ],
      "This API is not implemented by the given minimum Firefox version":[ "Ez az API még nincs megvalósítva a megadott minimális Firefox verzióban" ],
      "{{api}} is not supported in Firefox for Android version {{minVersion}}":[ "A(z) {{api}} nem támogatott a Firefox for Android {{minVersion}} verzióban" ],
      "This API is not implemented by the given minimum Firefox for Android version":[ "Ez az API még nincs megvalósítva a megadott minimális Firefox for Android verzióban" ],
      "Content script file name should not be empty.":[ "A tartalomparancsfájl neve nem lehet üres." ],
      "Content script file name should not be empty":[ "A tartalomparancsfájl neve nem lehet üres" ],
      "\"%(method)s\" called with a non-literal uri":[ "A(z) „%(method)s” nem URI literállal meghívva" ],
      "Calling \"%(method)s\" with variable parameters can result in potential security vulnerabilities if the variable contains a remote URI. Consider using 'window.open' with the 'chrome=no' flag.":[ "A(z) „%(method)s” változóparaméterekkel történő meghívása lehetséges biztonsági sebezhetőséget okozhat, ha a változó távoli URI-t tartalmaz. Fontolja meg a „window.open” metódus „chrome=no” jelzővel történő használatát." ],
      "\"%(method)s\" called with non-local URI":[ "A(z) „%(method)s” nem helyi URI-val meghívva" ],
      "Calling \"%(method)s\" with a non-local URI will result in the dialog being opened with chrome privileges.":[ "A(z) „%(method)s” nem helyi URI-val történő meghívása a chrome jogosultságával fogja megnyitni a párbeszédablakot." ],
      "JavaScript syntax error":[ "JavaScript szintaktikai hiba" ],
      "There is a JavaScript syntax error in your code, which might be related to some experimental JavaScript features that aren't an official part of the language specification and therefore not supported yet. The validation cannot continue on this file.":[ "JavaScript szintaktikai hiba van a kódjában, amely néhány kísérleti JavaScript funkcióhoz kötődhet, amelyek hivatalosan nem részei a nyelvi specifikációnak, ezért még nem támogatottak. Az ellenőrzés nem folytatódhat ennél a fájlnál." ],
      "Evaluation of strings as code can lead to security vulnerabilities and performance issues, even in the most innocuous of circumstances. Please avoid using `eval` and the `Function` constructor when at all possible.":[ "A karakterláncok kódként történő kiértékelése biztonsági sérülékenységekhez és teljesítménybeli problémákhoz vezethet, még a legártalmatlanabb körülmények között is. Lehetőleg kerülje az `eval` függvény és a `Function` konstruktor használatát." ],
      "setTimeout, setInterval and execScript functions should be called only with function expressions as their first argument":[ "A setTimeout, a setInterval és az execScript függvényeket csak függvénykifejezést tartalmazó első argumentummal szabad meghívni" ],
      "Unexpected global passed as an argument":[ "Nem várt argumentumként átadott globális változó" ],
      "Passing a global as an argument is not recommended. Please make this a var instead.":[ "A globális változók argumentumként történő átadása nem ajánlott. Tegye ezt változóvá." ],
      "Use of document.write strongly discouraged.":[ "A document.write használata erősen ellenjavallt." ],
      "document.write will fail in many circumstances when used in extensions, and has potentially severe security repercussions when used improperly. Therefore, it should not be used.":[ "A document.write sok esetben nem sikerül, ha kiegészítőkben használják, és nem megfelelő használat esetén súlyos biztonsági vonatkozásai lehetnek. Ezért nem szabad használni." ],
      "Banned 3rd-party JS library":[ "Tiltott harmadik féltől származó JS könyvtár" ],
      "Your add-on uses a JavaScript library we consider unsafe. Read more: https://bit.ly/1TRIyZY":[ "A kiegészítője egy általunk nem biztonságosnak tekintett JavaScript programkönyvtárat használ. Bővebben: https://bit.ly/1TRIyZY" ],
      "Your JSON contains block comments.":[ "A JSON megjegyzésblokkot tartalmaz." ],
      "Only line comments (comments beginning with \"//\") are allowed in JSON files. Please remove block comments (comments beginning with \"/*\")":[ "Csak a sorszintű megjegyzések („//” karakterekkel kezdődő megjegyzések) használhatók a JSON-fájlokban. Távolítsa el a blokkszintű megjegyzéseket („/*” karakterekkel kezdődő megjegyzések)." ],
      "Duplicate keys are not allowed in JSON files.":[ "Az ismétlődő kulcsok nem megengedettek a JSON fájlokban." ],
      "Duplicate key found in JSON file.":[ "Ismételt kulcs található a JSON-fájlban." ],
      "Your JSON is not valid.":[ "A JSON érvénytelen." ],
      "Your JSON file could not be parsed.":[ "Az JSON fájl nem dolgozható fel." ],
      "Reserved filename found.":[ "Fenntartott fájlnév találva." ],
      "Files whose names are reserved have been found in the add-on. Please refrain from using them and rename your files.":[ "Fenntartott nevű fájlok találhatók a kiegészítőben. Ttartózkodjon ezek használatától, és nevezze át a fájlokat." ],
      "The package is invalid. It may contain entries using invalid characters, as an example using '\\' as a path separator is not allowed in Firefox. Try to recreate your add-on package (ZIP) and make sure all entries are using '/' as the path separator.":[ "A csomag érvénytelen. Érvénytelen karaktereket használó bejegyzéseket tartalmazhat, mert például a Firefoxban nem engedélyezett a „\\” útvonal-elválasztó használata. Próbálja meg újra létrehozni a kiegészítőcsomagot (ZIP), és győződjön meg arról, hogy az összes bejegyzés „/” jelet használ útvonal-elválasztóként." ],
      "We were unable to decompress the zip file.":[ "Nem sikerült a zip fájl kibontása." ],
      "manifest.json was not found":[ "a manifest.json nem található" ],
      "No manifest.json was found at the root of the extension. The package file must be a ZIP of the extension's files themselves, not of the containing directory. See: https://mzl.la/2r2McKv for more on packaging.":[ "Nem található manifest.json a kiegészítő gyökerében. A csomagfájlnak magának a kiegészítő fájljaiból készült ZIP-fájlnak kell lennie, nem az azokat tartalmazó könyvtárat kell becsomagolni. A csomagolás további információért lásd: https://mzl.la/2r2McKv." ],
      "File is too large to parse.":[ "A fájl túl nagy a feldolgozáshoz" ],
      "This file is not binary and is too large to parse. Files larger than %(maxFileSizeToParseMB)sMB will not be parsed. Consider moving large lists of data out of JavaScript files and into JSON files, or splitting very large files into smaller ones.":[ "Ez a fájl nem bináris, és túl nagy a feldolgozáshoz. A %(maxFileSizeToParseMB)s MB-nál nagyobb fájlok nem lesznek feldolgozva. Fontolja meg a nagy adatlisták JavaScript-fájlokból JSON-fájlokba történő áthelyezését, vagy a nagyon nagy fájlok kisebbekre bontását." ],
      "Hidden file flagged":[ "Rejtett fájl megjelölve" ],
      "Hidden files complicate the review process and can contain sensitive information about the system that generated the add-on. Please modify the packaging process so that these files aren't included.":[ "A rejtett fájlok nehezítik az ellenőrzési folyamatot, és érzékeny információkat tartalmazhatnak a kiegészítőt előállító rendszerről. Módosítsa a csomagolási folyamatot, hogy ezek a fájlok ne legyenek benne." ],
      "Package contains duplicate entries":[ "A csomag ismétlődő bejegyzéseket tartalmaz" ],
      "Flagged filename found":[ "Megjelölt fájlnév találva" ],
      "Files were found that are either unnecessary or have been included unintentionally. They should be removed.":[ "Olyan fájlok találhatók, amelyek vagy feleslegesek, vagy véletlenül kerültek bele. Ezeket el kell távolítani." ],
      "The package contains multiple entries with the same name. This practice has been banned. Try unzipping and re-zipping your add-on package and try again.":[ "A csomag több azonos nevű bejegyzést tartalmaz. Ez a gyakorlat tiltott. Próbálja ki a kiegészítőcsomag újracsomagolását, majd próbálja újra." ],
      "Flagged file extensions found":[ "Megjelölt fájlkiterjesztés találva" ],
      "Flagged file type found":[ "Megjelölt fájltípus találva" ],
      "Files whose names end with flagged extensions have been found in the add-on. The extension of these files are flagged because they usually identify binary components. Please see https://bit.ly/review-policy for more information on the binary content review process.":[ "Megjelölt kiterjesztéssel végződő fájlok találhatók a kiegészítőben. Ezen fájlok kiterjesztései azért lettek megjelölve, mert ezek általában bináris komponenseket azonosítanak. Keresse fel a https://bit.ly/review-policy oldalt a bináris tartalmak értékelési folyamatának további információiért." ],
      "Package already signed":[ "A csomag már aláírt" ],
      "Add-ons which are already signed will be re-signed when published on AMO. This will replace any existing signatures on the add-on.":[ "A már aláírt kiegészítők újra aláírásra kerülnek az AMO-n történő közzétételükkor. Ez lecseréli a kiegészítőn lévő aláírásokat." ],
      "Firefox add-ons are not allowed to run coin miners.":[ "A Firefox kiegészítők nem futtathatnak kriptovaluta bányász programokat." ],
      "We do not allow coinminer scripts to be run inside WebExtensions. See https://github.com/mozilla/addons-linter/issues/1643 for more details.":[ "Nem engedélyezzük a kriptopénzbányász parancsfájlok WebExtensionökön belül történő futtatását. További részletekért lásd: https://github.com/mozilla/addons-linter/issues/1643." ],
      "String name is reserved for a predefined message":[ "A karakterlánc neve egy előre definiált üzenetnek van fenntartva" ],
      "String names starting with @@ get translated to built-in constants (https://mzl.la/2BL9ZjE).":[ "A @@ karakterlánccal kezdődő karakterláncnevek beépített konstansokká alakulnak (https://mzl.la/2BL9ZjE)." ],
      "String name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2Eztyi5).":[ "A karakterláncnév csak alfanumerikus karaktereket, _ és @ karaktereket tartalmazhat (https://mzl.la/2Eztyi5)." ],
      "Placeholder for message is missing":[ "Az üzenet helykitöltője hiányzik" ],
      "A placeholder used in the message is not defined.":[ "Az üzenetben használt helykitöltő nincs definiálva." ],
      "Placeholder name contains invalid characters":[ "A helykitöltő neve érvénytelen karaktereket tartalmaz" ],
      "Placeholder name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2ExbYez).":[ "A helykitöltő neve csak alfanumerikus karaktereket, _ és @ karaktereket tartalmazhat (https://mzl.la/2ExbYez)." ],
      "Placeholder is missing the content property":[ "A helykitöltőből hiányzik a content tulajdonság" ],
      "A placeholder needs a content property defining the replacement of it (https://mzl.la/2DT1MQd)":[ "Egy helykitöltőhöz szükséges a tartalom tulajdonság, amely meghatározza, hogy mire lesz cserélve (https://mzl.la/2DT1MQd)" ],
      "Translation string is missing the message property":[ "A fordítási karakterláncból hiányzik a message tulajdonság" ],
      "No \"message\" message property is set for a string (https://mzl.la/2DSBTjA).":[ "Nincs „message” üzenettulajdonság beállítva a karakterlánchoz (https://mzl.la/2DSBTjA)." ],
      "The field is required.":[ "A mező kötelező." ],
      "See https://mzl.la/1ZOhoEN (MDN Docs) for more information.":[ "További információkért lásd: https://mzl.la/1ZOhoEN (MDN dokumentáció)." ],
      "The permission type is unsupported.":[ "Az engedélytípus nem támogatott." ],
      "See https://mzl.la/1R1n1t0 (MDN Docs) for more information.":[ "További információkért lásd: https://mzl.la/1R1n1t0 (MDN dokumentáció)." ],
      "See https://mzl.la/2Qn0fWC (MDN Docs) for more information.":[ "További információkért lásd: https://mzl.la/2Qn0fWC (MDN dokumentáció)." ],
      "See https://mzl.la/3Woeqv4 (MDN Docs) for more information.":[ "További információkért lásd: https://mzl.la/3Woeqv4 (MDN dokumentáció)." ],
      "Unknown permission.":[ "Ismeretlen engedély." ],
      "%(instancePath)s: the following privileged permissions are only allowed in privileged extensions: %(privilegedPermissions)s.":[ "%(instancePath)s: a következő privilegizált engedélyek csak privilegizált kiegészítőkben engedélyezettek: %(privilegedPermissions)s." ],
      "Invalid host permission.":[ "Érvénytelen gazdagép-engedély." ],
      "Invalid install origin.":[ "Érvénytelen telepítési eredet." ],
      "Invalid install origin. A valid origin has - only - a scheme, hostname and optional port. See https://mzl.la/3TEbqbE (MDN Docs) for more information.":[ "Érvénytelen telepítési eredet. Egy érvényes eredetnek – csak – egy sémája, gazdagépneve és nem kötelező portja van. További információkért lásd: https://mzl.la/3TEbqbE (MDN dokumentáció)." ],
      "The field is invalid.":[ "A mező érvénytelen." ],
      "\"manifest_version\" in the manifest.json is not a valid value":[ "A manifest.json fájlban szereplő „manifest_version” értéke érvénytelen" ],
      "See https://mzl.la/20PenXl (MDN Docs) for more information.":[ "További információkért lásd: https://mzl.la/20PenXl (MDN dokumentáció)." ],
      "\"%(property)s\" allows remote code execution in manifest.json":[ "A(z) „%(property)s” lehetővé teszi távoli kódfuttatást a manifest.json fájlban" ],
      "A custom \"%(property)s\" needs additional review.":[ "Egy egyéni „%(property)s” további ellenőrzésre szorul." ],
      "\"%(property)s\" allows 'eval', which has strong security and performance implications.":[ "A(z) „%(property)s” engedélyezi az „eval” függvényt, amelynek komoly biztonsági és teljesítménybeli vonatkozásai vannak." ],
      "In most cases the same result can be achieved differently, therefore it is generally prohibited":[ "A legtöbb esetben ugyanaz az eredmény máshogy is elérhető, ezért általában tilos" ],
      "The \"name\" property must be a string with no leading/trailing whitespaces.":[ "A „name” tulajdonságnak kezdő/záró üres karakterek nélküli karakterláncnak kell lennie." ],
      "See http://mzl.la/1STmr48 (MDN Docs) for more information.":[ "További információkért lásd: http://mzl.la/1STmr48 (MDN dokumentáció)." ],
      "\"update_url\" is not allowed.":[ "Az „update_url” nem engedélyezett." ],
      "\"applications.gecko.update_url\" or \"browser_specific_settings.gecko.update_url\" are not allowed for Mozilla-hosted add-ons.":[ "Az „applications.gecko.update_url” vagy a „browser_specific_settings.gecko.update_url” nem engedélyezett a Mozilla által kiszolgált kiegészítőknél." ],
      "The \"update_url\" property is not used by Firefox.":[ "Az „update_url” tulajdonságot nem használja a Firefox." ],
      "The \"update_url\" is not used by Firefox in the root of a manifest; your add-on will be updated via the Add-ons site and not your \"update_url\". See: https://mzl.la/25zqk4O":[ "A leírófájl gyökerében lévő „update_url” paramétert nem használja a Firefox; a kiegészítője a Kiegészítők oldalon keresztül fog frissülni, és nem az „update_url” címen keresztül. Lásd: https://mzl.la/25zqk4O." ],
      "\"strict_max_version\" not required.":[ "A „strict_max_version” nem kötelező." ],
      "\"strict_max_version\" shouldn't be used unless the add-on is expected not to work with future versions of Firefox.":[ "A „strict_max_version” paramétert nem szabad használni, kivéve, ha a kiegészítő várhatóan nem fog működni a Firefox jövőbeli verzióival." ],
      "Manifest Version 3 is not fully supported on Firefox for Android.":[ "A 3-as verziójú jegyzékfájl még nem teljes körűen támogatott a Firefox for Androidban." ],
      "No \"%(property)s\" property found in manifest.json":[ "Nem található „%(property)s” tulajdonság a manifest.json fájlban" ],
      "\"%(property)s\" is required":[ "A(z) „%(property)s” kötelező" ],
      "An icon defined in the manifest could not be found in the package.":[ "A leíróban definiált ikon nem található a csomagban." ],
      "Icon could not be found at \"%(path)s\".":[ "A(z) „%(path)s” helyen lévő ikon nem található." ],
      "A background script defined in the manifest could not be found.":[ "A leíróban definiált háttérparancsfájl nem található." ],
      "A background page defined in the manifest could not be found.":[ "A leíróban definiált háttéroldal nem található." ],
      "Background script could not be found at \"%(path)s\".":[ "A háttérparancsfájl nem található itt: „%(path)s”." ],
      "Background page could not be found at \"%(path)s\".":[ "A háttéroldal nem található itt: „%(path)s”." ],
      "A content script defined in the manifest could not be found.":[ "A leíróban definiált tartalomparancsfájl nem található." ],
      "A content script css file defined in the manifest could not be found.":[ "A leíróban definiált css fájl nem található." ],
      "Content script defined in the manifest could not be found at \"%(path)s\".":[ "A leíróban definiált tartalomparancsfájl nem található itt: „%(path)s”." ],
      "Content script css file defined in the manifest could not be found at \"%(path)s\".":[ "A leíróban definiált tartalom CSS fájl nem található itt: „%(path)s”. " ],
      "A dictionary file defined in the manifest could not be found.":[ "A leíróban definiált szótárfájl nem található." ],
      "Dictionary file defined in the manifest could not be found at \"%(path)s\".":[ "A leíróban definiált szótárfájl nem található itt: „%(path)s”." ],
      "The manifest contains multiple dictionaries.":[ "A leíró több szótárat is tartalmaz." ],
      "Multiple dictionaries were defined in the manifest, which is unsupported.":[ "Több szótárat is definiált a leíróban, amely nem támogatott." ],
      "The manifest contains a dictionaries object, but it is empty.":[ "A leíró szótárobjektumot tartalmaz, de az üres." ],
      "A dictionaries object was defined in the manifest, but it was empty.":[ "A leíróban egy szótár lett definiálva, de az üres volt." ],
      "The manifest contains a dictionary but no id property.":[ "A leírófájl szótárat tartalmaz, de nincs id tulajdonsága." ],
      "A dictionary was found in the manifest, but there was no id set.":[ "Egy szótár található a leíróban, de nincs id tulajdonság megadva." ],
      "Please refer to https://github.com/mozilla-extensions/xpi-manifest to learn more about privileged extensions and signing.":[ "Keresse fel a https://github.com/mozilla-extensions/xpi-manifest oldalt, hogy többet tudjon meg a privilegizált kiegészítőkről és az aláírásról." ],
      "Forbidden content found in add-on.":[ "Tiltott tartalom található a kiegészítőben." ],
      "This add-on contains forbidden content.":[ "Ez a kiegészítő tiltott tartalmat tartalmaz." ],
      "Icons must be square.":[ "Az ikonoknak négyzetesnek kell lennie." ],
      "Icon at \"%(path)s\" must be square.":[ "A(z) „%(path)s” helyen lévő ikonnak négyzetesnek kell lennie." ],
      "The size of the icon does not match the manifest.":[ "Az ikon mérete nem egyezik a leíróval." ],
      "Expected icon at \"%(path)s\" to be %(expected)d pixels wide but was %(actual)d.":[ "A(z) „%(path)s” ikon %(expected)d képpont szélességűként várt, de valójában ilyen széles volt: %(actual)d." ],
      "\"%(fieldName)s\" is ignored for non-privileged add-ons.":[ "A(z) „%(fieldName)s” figyelmen kívül van hagyva a nem privilegizált kiegészítőknél." ],
      "Corrupt image file":[ "Sérült képfájl" ],
      "Expected icon file at \"%(path)s\" is corrupted":[ "A(z) „%(path)s” helyen várt ikon sérült" ],
      "This property has been deprecated.":[ "Ez a tulajdonság elavult." ],
      "This theme LWT alias has been removed in Firefox 70.":[ "A téma LWT álneve eltávolításra került a Firefox 70-ben." ],
      "See https://mzl.la/2T11Lkc (MDN Docs) for more information.":[ "További információkért lásd: https://mzl.la/2T11Lkc (MDN dokumentáció)." ],
      "Theme image for \"%(type)s\" could not be found at \"%(path)s\"":[ "A(z) „%(type)s” típushoz tartozó témakép nem található itt: „%(path)s”" ],
      "\"%(fieldName)s\" manifest field is only used for privileged and temporarily installed extensions.":[ "A(z) „%(fieldName)s” jegyzékmező csak privilegizált és ideiglenesen telepített kiegészítőknél használatos." ],
      "Corrupted theme image file":[ "Sérült témaképfájl" ],
      "Theme image file at \"%(path)s\" is corrupted":[ "A(z) „%(path)s” helyen lévő témaképfájl sérült" ],
      "Theme image file has an unsupported file extension":[ "A téma képfájlja nem támogatott fájlkiterjesztéssel rendelkezik" ],
      "Theme image file at \"%(path)s\" has an unsupported file extension":[ "A(z) „%(path)s” helyen lévő témafájl kiterjesztése nem támogatott" ],
      "Theme image file has an unsupported mime type":[ "A téma képfájlja nem támogatott MIME-típussal rendelkezik" ],
      "Theme image file at \"%(path)s\" has the unsupported mime type \"%(mime)s\"":[ "A(z) „%(path)s” helyen lévő képfájl nem támogatott MIME-típussal rendelkezik" ],
      "Theme image file mime type does not match its file extension":[ "A téma képfájljának MIME-típusa nem egyezik a fájlkiterjesztésével" ],
      "Theme image file extension at \"%(path)s\" does not match its actual mime type \"%(mime)s\"":[ "A(z) „%(path)s” helyen lévő képfájl kiterjesztése nem egyezik a tényleges „%(mime)s” MIME-típusával." ],
      "The \"default_locale\" is missing localizations.":[ "A „default_locale” területi beállításhoz hiányoznak a fordítások." ],
      "The \"default_locale\" value is specified in the manifest, but no matching \"messages.json\" in the \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "A „default_locale” érték meg van adva a jegyzékfájlban, de nem létezik megfelelő „messages.json” a „_locales” könyvtárban. Lásd: https://mzl.la/2hjcaEE." ],
      "The \"default_locale\" is missing but \"_locales\" exist.":[ "A „default_locale” hiányzik, de a „_locales” létezik." ],
      "The \"default_locale\" value is not specifed in the manifest, but a \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "A „default_locale” érték nincs megadva a jegyzékfájlban, de létezik „_locales” könyvtár. Lásd: https://mzl.la/2hjcaEE." ],
      "Unsupported image extension":[ "Nem támogatott képkiterjesztés" ],
      "Icons should be one of JPG/JPEG, WebP, GIF, PNG or SVG.":[ "Az ikonoknak JPG/JPEG, WebP, GIF, PNG vagy SVG fájloknak kell lenniük." ],
      "\"applications\" property overridden by \"browser_specific_settings\" property":[ "Az „applications” tulajdonságot felülírja a „browser_specific_settings” tulajdonság" ],
      "The \"applications\" property is being ignored because it is superseded by the \"browser_specific_settings\" property which is also defined in your manifest. Consider removing applications.":[ "Az „applications” tulajdonság figyelmen kívül van hagyva, mert felváltja a „browser_specific_settings” tulajdonság, amely ugyancsak a jegyzékfájlban adható meg. Fontolja meg az „applications” eltávolítását." ],
      "Empty language directory":[ "Üres nyelvi könyvtár" ],
      "messages.json file missing in \"%(path)s\"":[ "A messages.json fájl hiányzik innen: „%(path)s”" ],
      "Manifest key not supported by the specified minimum Firefox version":[ "A leírókulcs nem támogatott a megadott minimális Firefox verzióban" ],
      "\"strict_min_version\" requires Firefox %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "A(z) „strict_min_version” szerint Firefox %(minVersion)s szükséges, amely korábbi, mint a(z) %(versionAdded)s verzió, amely bevezette a(z) „%(key)s” támogatását." ],
      "\"%(fieldName)s\" is not supported in manifest versions %(versionRange)s.":[ "A(z) „%(fieldName)s” nem támogatott a következő jegyzékfájlverziókban: %(versionRange)s." ],
      "Permission not supported by the specified minimum Firefox version":[ "Az engedély nem támogatott a megadott minimális Firefox verzióban" ],
      "\"%(fieldName)s\" is not supported.":[ "A(z) „%(fieldName)s” nem támogatott." ],
      "Manifest key not supported by the specified minimum Firefox for Android version":[ "A leírókulcs nem támogatott a megadott minimális Firefox for Android verzióban" ],
      "\"strict_min_version\" requires Firefox for Android %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "A(z) „strict_min_version” szerint Firefox for Android %(minVersion)s szükséges, amely korábbi, mint a(z) %(versionAdded)s verzió, amely bevezette a(z) „%(key)s” támogatását." ],
      "Permission not supported by the specified minimum Firefox for Android version":[ "Az engedély nem támogatott a megadott minimális Firefox for Android verzióban" ],
      "Linking to \"addons.mozilla.org\" is not allowed":[ "Az „addons.mozilla.org” webhelyre való hivatkozás nem engedélyezett" ],
      "Links directing to \"addons.mozilla.org\" are not allowed to be used for homepage":[ "Az „addons.mozilla.org” oldalra mutató hivatkozások nem használhatók honlapként" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above":[ "A(z) „%(permission)s” engedélyhez szükséges, hogy a „strict_min_version” legalább a következőre legyen beállítva: „%(minFirefoxVersion)s”." ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above. Please update your manifest.json version to specify a minimum Firefox version.":[ "A(z) „%(permission)s” engedélyhez szükséges, hogy a „strict_min_version” legalább a következőre legyen beállítva: „%(minFirefoxVersion)s”. Frissítse a manifest.json verzióját, hogy megadja a Firefox minimális verzióját." ],
      "The extension ID is required in Manifest Version 3 and above.":[ "A bővítményazonosító kötelező a Manifest 3-as és újabb verzióiban." ],
      "See https://mzl.la/3PLZYdo for more information.":[ "További információkért lásd: https://mzl.la/3PLZYdo." ],
      "%(instancePath)s: Privileged extensions should declare privileged permissions.":[ "%(instancePath)s: A privilegizált kiegészítőknek privilegizált engedélyeket kell deklarálniuk." ],
      "This extension does not declare any privileged permission. It does not need to be signed with the privileged certificate. Please upload it directly to https://addons.mozilla.org/.":[ "Ez a kiegészítő nem deklarál privilegizált engedélyt. Nem kell a privilegizált tanúsítvánnyal aláírni. Töltse fel közvetlenül a https://addons.mozilla.org/ oldalra." ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for privileged extensions.":[ "%(instancePath)s: A „mozillaAddons” engedély szükséges a privilegizált kiegészítőkhöz." ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for extensions that include privileged manifest fields.":[ "%(instancePath)s: A „mozillaAddons” engedély szükséges az olyan kiegészítőkhöz, amelyek privilegizált jegyzékmezőket tartalmaznak." ],
      "%(instancePath)s: privileged manifest fields are only allowed in privileged extensions.":[ "%(instancePath)s: a privilegizált jegyzékmezők csak privilegizált kiegészítőkben engedélyezettek." ],
      "This extension does not include the \"mozillaAddons\" permission, which is required for privileged extensions.":[ "Ez a kiegészítő nem tartalmazza a „mozillaAddons” engedélyt, amely a privilegizált kiegészítőkhöz szükséges." ],
      "Cannot use actions in hidden add-ons.":[ "A műveletek nem használhatók rejtett kiegészítőkben." ],
      "The hidden and browser_action/page_action (or action in Manifest Version 3 and above) properties are mutually exclusive.":[ "A „hidden” és a „browser_action/page_action” (vagy „action” a 3-as és újabb jegyzékverziókban) tulajdonságok kölcsönösen kizárják egymást." ],
      "Use \"browser_specific_settings\" instead of \"applications\".":[ "Használja a „browser_specific_settings” értéket az „applications” helyett." ],
      "The \"applications\" property in the manifest is deprecated and will no longer be accepted in Manifest Version 3 and above.":[ "A jegyzékfájl „applications” tulajdonsága elavult, és már nem lesz elfogadva a 3-as és újabb jegyzékverziókban." ],
      "\"applications\" is no longer allowed in Manifest Version 3 and above.":[ "Az „applications” már nem engedélyezett a 3-as és újabb jegyzékverziókban." ],
      "The \"applications\" property in the manifest is no longer allowed in Manifest Version 3 and above. Use \"browser_specific_settings\" instead.":[ "A jegyzékfájlban szereplő „applications” tulajdonság már nem engedélyezett a 3-as és újabb jegyzékverziókban. Használja helyette a „browser_specific_settings” paramétert." ],
      "The version string should be simplified because it won't be compatible with Manifest Version 3 and above.":[ "A verzió karakterláncot egyszerűsíteni kell, mert nem lesz kompatibilis a 3-as és újabb jegyzékfájlverziókkal." ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros will no longer be allowed. Letters will no longer be allowed either. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "A verzió legyen egy 1 és 4 közti számú számjegycsoportból álló, pontokkal elválasztott karakterlánc. Minden szám legfeljebb 9 számjegyből állhat, és a kezdő nullák többé nem engedélyezettek. A betűk sem lesznek többé engedélyezettek. További információkért lásd: https://mzl.la/3h3mCRu (MDN dokumentáció)." ],
      "The version string should be simplified.":[ "A verzió karakterláncot egyszerűsíteni kell." ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros are not allowed. Letters are no longer allowed. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "A verzió legyen egy 1 és 4 közti számú számjegycsoportból álló, pontokkal elválasztott karakterlánc. Minden szám legfeljebb 9 számjegyből állhat, és a kezdő nullák nem engedélyezettek. Már a betűk sem engedélyezettek. További információkért lásd: https://mzl.la/3h3mCRu (MDN dokumentáció)." ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported in manifest versions %(versionRange)s.":[ "/%(fieldName)s: A(z) „%(permissionName)s” nem támogatott a következő jegyzékfájlverziókban: %(versionRange)s." ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported.":[ "/%(fieldName)s: A(z) „%(permissionName)s” nem támogatott." ] } } }