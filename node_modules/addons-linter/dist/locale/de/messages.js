module.exports = { domain:"messages",
  locale_data:{ messages:{ "":{ domain:"messages",
        plural_forms:"nplurals=2; plural=(n != 1);",
        lang:"de" },
      "Validation Summary:":[ "Validierungs-Zusammenfassung:" ],
      Code:[ "Quelltext" ],
      Message:[ "Nachricht" ],
      Description:[ "Beschreibung" ],
      File:[ "Datei" ],
      Line:[ "Zeile" ],
      Column:[ "Spalte" ],
      "Invalid manifest version range requested: --min-manifest-version (currently set to %(minManifestVersion)s) should not be greater than --max-manifest-version (currently set to %(maxManifestVersion)s).":[ "Ungültiger Manifest-Versionsbereich angefordert: --min-manifest-version (derzeit auf %(minManifestVersion)s gesetzt) sollte nicht größer sein als --max-manifest-version (derzeit auf %(maxManifestVersion)s gesetzt)." ],
      "Your FTL is not valid.":[ "Ihre FTL ist ungültig." ],
      "Your FTL file could not be parsed.":[ "Ihre FTL-Datei konnte nicht geparst werden." ],
      "Remote scripts are not allowed as per the Add-on Policies.":[ "Fremd-Skripte sind laut Add-on-Regeln nicht erlaubt." ],
      "Please include all scripts in the add-on. For more information, refer to https://mzl.la/2uEOkYp.":[ "Bitte fügen Sie alle Skripte im Add-on hinzu. Weitere Informationen finden Sie unter https://mzl.la/2uEOkYp." ],
      "Inline scripts blocked by default":[ "Inline-Skripte werden standardmäßig blockiert" ],
      "Default CSP rules prevent inline JavaScript from running (https://mzl.la/2pn32nd).":[ "Standard-CSP-Regeln verhindern die Ausführung von Inline-JavaScript (https://mzl.la/2pn32nd)." ],
      "Unadvised 3rd-party JS library":[ "Unbekannte externe JS-Bibliothek" ],
      "Your add-on uses a JavaScript library we do not recommend. Read more: https://bit.ly/1TRIyZY":[ "Ihr Add-on verwendet eine JavaScript-Bibliothek, die wir nicht empfehlen. Weitere Informationen: https://bit.ly/1TRIyZY" ],
      "Known JS library detected":[ "Bekannte JS-Bibliothek erkannt" ],
      "JavaScript libraries are discouraged for simple add-ons, but are generally accepted.":[ "Bei einfachen Add-ons wird von JavaScript-Bibliotheken abgeraten, aber sie werden im Allgemeinen akzeptiert." ],
      "Due to both security and performance concerns, this may not be set using dynamic values which have not been adequately sanitized. This can lead to security issues or fairly serious performance degradation.":[ "Sowohl aus Sicherheits- als auch aus Leistungsgründen kann dies nicht mit nicht ausreichend bereinigten dynamische Werten gesetzt werden. Dies kann zu Sicherheitsproblemen oder ziemlich ernsthaften Leistungseinbußen führen." ],
      "{{api}} is not supported":[ "{{api}} wird nicht unterstützt" ],
      "This API has not been implemented by Firefox.":[ "Diese API wurde von Firefox nicht implementiert." ],
      "\"{{api}}\" has been removed in Manifest Version 3 (`manifest_version` property)":[ "„{{api}}“ wurde in Manifest Version 3 entfernt (`manifest_version`-Eigenschaft)" ],
      "{{api}} is deprecated":[ "{{api}} wird nicht mehr unterstützt" ],
      "This API has been deprecated by Firefox.":[ "Diese API wird von Firefox nicht mehr unterstützt." ],
      "Content script file could not be found.":[ "Inhalts-Skript-Datei wurde nicht gefunden." ],
      "\"{{api}}\" is deprecated or unimplemented":[ "„{{api}}“ wird nicht mehr unterstützt oder ist nicht implementiert" ],
      "Content script file could not be found":[ "Inhalts-Skript-Datei wurde nicht gefunden" ],
      "\"%(api)s\" can cause issues when loaded temporarily":[ "„%(api)s“ kann Probleme verursachen, wenn es temporär geladen wird" ],
      "This API can cause issues when loaded temporarily using about:debugging in Firefox unless you specify \"browser_specific_settings.gecko.id\" in the manifest. Please see: https://mzl.la/2hizK4a for more.":[ "Diese API kann Probleme verursachen, wenn sie temporär mit about:debugging in Firefox geladen wird, es sei denn, Sie geben „browser_specific_settings.gecko.id“ im Manifest an. Weitere Informationen finden Sie unter: https://mzl.la/2hizK4a." ],
      "{{api}} is not supported in Firefox version {{minVersion}}":[ "{{api}} wird in Firefox-Version {{minVersion}} nicht unterstützt" ],
      "This API is not implemented by the given minimum Firefox version":[ "Diese API ist in der angegebenen Mindestversion von Firefox nicht implementiert" ],
      "{{api}} is not supported in Firefox for Android version {{minVersion}}":[ "{{api}} wird in Firefox-Version für Android {{minVersion}} nicht unterstützt" ],
      "This API is not implemented by the given minimum Firefox for Android version":[ "Diese API ist in der angegebenen Mindestversion von Firefox für Android nicht implementiert" ],
      "Content script file name should not be empty.":[ "Inhalts-Skript-Datei sollte nicht leer sein." ],
      "Content script file name should not be empty":[ "Inhalts-Skript-Datei sollte nicht leer sein" ],
      "\"%(method)s\" called with a non-literal uri":[ "\"%(method)s\" mit einem Nicht-Literal-uri aufgerufen" ],
      "Calling \"%(method)s\" with variable parameters can result in potential security vulnerabilities if the variable contains a remote URI. Consider using 'window.open' with the 'chrome=no' flag.":[ "Der Aufruf von „%(method)s“ mit variablen Parametern kann zu möglichen Sicherheitslücken führen, wenn die Variable eine externe URI enthält. Ziehen Sie die Verwendung von 'window.open' mit dem Flag 'chrome=no' in Erwägung." ],
      "\"%(method)s\" called with non-local URI":[ "„%(method)s“ mit nicht-lokaler URI aufgerufen" ],
      "Calling \"%(method)s\" with a non-local URI will result in the dialog being opened with chrome privileges.":[ "Der Aufruf von „%(method)s“ mit einer nicht-lokalen URI führt dazu, dass der Dialog mit Chrome-Berechtigungen geöffnet wird." ],
      "JavaScript syntax error":[ "JavaScript-Syntaxfehler" ],
      "There is a JavaScript syntax error in your code, which might be related to some experimental JavaScript features that aren't an official part of the language specification and therefore not supported yet. The validation cannot continue on this file.":[ "Es gibt einen JavaScript-Syntaxfehler in Ihrem Quelltext, was mit einigen experimentellen JavaScript-Funktionen zusammenhängen könnte, die kein offizieller Teil der Sprachspezifikation sind und daher noch nicht unterstützt werden. Die Überprüfung kann für diese Datei nicht fortgesetzt werden." ],
      "Evaluation of strings as code can lead to security vulnerabilities and performance issues, even in the most innocuous of circumstances. Please avoid using `eval` and the `Function` constructor when at all possible.":[ "Die Auswertung von Strings als Quelltext kann selbst unter harmlosesten Umständen zu Sicherheitslücken und Leistungsproblemen führen. Bitte vermeiden Sie die Verwendung 'eval' und den 'Function'-Konstruktor, wenn möglich." ],
      "setTimeout, setInterval and execScript functions should be called only with function expressions as their first argument":[ "setTimeout-, setInterval- und execScript-Funktionen sollten nur mit Funktionsausdrücken als erstem Argument aufgerufen werden." ],
      "Unexpected global passed as an argument":[ "Argument wurde unerwartet als global weitergegeben" ],
      "Passing a global as an argument is not recommended. Please make this a var instead.":[ "Die Weitergabe eines global als Argument wird nicht empfohlen. Bitte machen Sie dies stattdessen zu einer var." ],
      "Use of document.write strongly discouraged.":[ "Von der Verwendung von document.write wird dringend abgeraten." ],
      "document.write will fail in many circumstances when used in extensions, and has potentially severe security repercussions when used improperly. Therefore, it should not be used.":[ "document.write wird in vielen Fällen fehlschlagen, wenn es in Erweiterungen verwendet wird, und hat potenziell schwerwiegende Auswirkungen auf die Sicherheit, wenn es nicht richtig verwendet wird. Daher sollte er nicht verwendet werden." ],
      "Banned 3rd-party JS library":[ "Gesperrte externe JS-Bibliothek" ],
      "Your add-on uses a JavaScript library we consider unsafe. Read more: https://bit.ly/1TRIyZY":[ "Ihr Add-on verwendet eine JavaScript-Bibliothek, die wir als nicht sicher ansehen. Weitere Informationen: https://bit.ly/1TRIyZY" ],
      "Your JSON contains block comments.":[ "Ihre JSON enthält Block-Kommentare." ],
      "Only line comments (comments beginning with \"//\") are allowed in JSON files. Please remove block comments (comments beginning with \"/*\")":[ "In JSON-Dateien sind nur Zeilenkommentare (Kommentare, die mit \"//\" beginnen) erlaubt. Bitte entfernen Sie blockweise Kommentare (Kommentare, die mit „/*“ beginnen“)" ],
      "Duplicate keys are not allowed in JSON files.":[ "Doppelte Schlüssel sind in JSON-Dateien nicht erlaubt." ],
      "Duplicate key found in JSON file.":[ "Doppelter Schlüssel in JSON-Datei gefunden." ],
      "Your JSON is not valid.":[ "Ihre JSON ist ungültig." ],
      "Your JSON file could not be parsed.":[ "Ihre JSON-Datei konnte nicht geparst werden." ],
      "Reserved filename found.":[ "Reservierter Dateiname gefunden." ],
      "Files whose names are reserved have been found in the add-on. Please refrain from using them and rename your files.":[ "Im Add-on wurden Dateien gefunden, deren Namen reserviert sind. Bitte verwenden Sie diese nicht und benennen Sie Ihre Dateien um." ],
      "The package is invalid. It may contain entries using invalid characters, as an example using '\\' as a path separator is not allowed in Firefox. Try to recreate your add-on package (ZIP) and make sure all entries are using '/' as the path separator.":[ "Das Paket ist ungültig. Es kann Einträge mit ungültigen Zeichen enthalten, beispielsweise ist die Verwendung von '\\' als Pfadtrennzeichen in Firefox nicht erlaubt. Versuchen Sie, Ihr Add-on-Paket (ZIP) erneut zu erstellen, und stellen Sie sicher, dass alle Einträge '/' als Pfadtrennzeichen verwenden." ],
      "We were unable to decompress the zip file.":[ "Wir konnten die ZIP-Datei nicht entpacken." ],
      "manifest.json was not found":[ "manifest.json wurde nicht gefunden" ],
      "No manifest.json was found at the root of the extension. The package file must be a ZIP of the extension's files themselves, not of the containing directory. See: https://mzl.la/2r2McKv for more on packaging.":[ "Es wurde keine manifest.json im Wurzelverzeichnis der Erweiterung gefunden. Die Paketdatei muss eine ZIP-Datei der Dateien der Erweiterung selbst sein, nicht des Ordners, den die Erweiterung enthält. Weitere Informationen zum Verpacken finden Sie unter: https://mzl.la/2r2McKv." ],
      "File is too large to parse.":[ "Datei ist zu groß, um geparst zu werden." ],
      "This file is not binary and is too large to parse. Files larger than %(maxFileSizeToParseMB)sMB will not be parsed. Consider moving large lists of data out of JavaScript files and into JSON files, or splitting very large files into smaller ones.":[ "Diese Datei ist nicht binär und zu groß, um geparst zu werden. Dateien, die größer als %(maxFileSizeToParseMB)s MB sind, werden nicht geparst. Ziehen Sie in Erwägung, große Listen mit Daten aus JavaScript-Dateien in JSON-Dateien zu verschieben, oder sehr große Dateien in kleinere aufzuteilen." ],
      "Hidden file flagged":[ "Versteckte Datei gemeldet" ],
      "Hidden files complicate the review process and can contain sensitive information about the system that generated the add-on. Please modify the packaging process so that these files aren't included.":[ "Versteckte Dateien erschweren den Überprüfungsprozess und können sensible Informationen über das System enthalten, das das Add-on generiert hat. Bitte ändern Sie den Paketierungsprozess, damit diese Dateien nicht enthalten sind." ],
      "Package contains duplicate entries":[ "Paket enthält doppelte Einträge" ],
      "Flagged filename found":[ "Gemeldeter Dateiname gefunden" ],
      "Files were found that are either unnecessary or have been included unintentionally. They should be removed.":[ "Es wurden Dateien gefunden, die entweder unnötig sind oder unbeabsichtigt eingefügt wurden. Sie sollten entfernt werden." ],
      "The package contains multiple entries with the same name. This practice has been banned. Try unzipping and re-zipping your add-on package and try again.":[ "Das Paket enthält mehrere Einträge mit demselben Namen. Diese Praxis wurde verboten. Versuchen Sie, Ihr Add-on-Paket zu entpacken und erneut zu komprimieren, und versuchen Sie es erneut." ],
      "Flagged file extensions found":[ "Gemeldete Dateiendungen gefunden" ],
      "Flagged file type found":[ "Gemeldeter Dateityp gefunden" ],
      "Files whose names end with flagged extensions have been found in the add-on. The extension of these files are flagged because they usually identify binary components. Please see https://bit.ly/review-policy for more information on the binary content review process.":[ "Dateien, deren Namen auf gekennzeichnete Dateiendungen enden, wurden im Add-on gefunden. Die Endungen dieser Dateien sind markiert, da sie normalerweise binäre Komponenten identifizieren. Weitere Informationen zum Prozess der Überprüfung binärer Inhalte finden Sie unter https://bit.ly/review-policy." ],
      "Package already signed":[ "Paket bereit signiert" ],
      "Add-ons which are already signed will be re-signed when published on AMO. This will replace any existing signatures on the add-on.":[ "Bereits signierte Add-ons werden bei der Veröffentlichung auf AMO erneut signiert. Dies wird alle bestehenden Signaturen auf dem Add-on ersetzen." ],
      "Firefox add-ons are not allowed to run coin miners.":[ "Firefox-Add-ons dürfen keine Coin-Miner einsetzen." ],
      "We do not allow coinminer scripts to be run inside WebExtensions. See https://github.com/mozilla/addons-linter/issues/1643 for more details.":[ "Wir erlauben nicht, dass Coinminer-Skripte in WebExtensions ausgeführt werden. Weitere Details finden Sie unter https://github.com/mozilla/addons-linter/issues-1643." ],
      "String name is reserved for a predefined message":[ "Stringname ist für eine vordefinierte Nachricht reserviert" ],
      "String names starting with @@ get translated to built-in constants (https://mzl.la/2BL9ZjE).":[ "String-Namen, die mit @@ beginnen, werden in eingebaute Konstanten übersetzt (https://mzl.la/2BL9ZjE)." ],
      "String name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2Eztyi5).":[ "Der String-Name sollte nur alphanumerische Zeichen, _ und @ enthalten (https://mzl.la/2Eztyi5)." ],
      "Placeholder for message is missing":[ "Platzhalter für Nachricht fehlt" ],
      "A placeholder used in the message is not defined.":[ "Ein in der Nachricht verwendeter Platzhalter wurde nicht definiert." ],
      "Placeholder name contains invalid characters":[ "Platzhaltername enthält ungültige Zeichen" ],
      "Placeholder name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2ExbYez).":[ "Platzhaltername sollte nur alphanumerische Zeichen, _ und @ enthalten (https://mzl.la/2ExbYez)." ],
      "Placeholder is missing the content property":[ "Platzhalter hat keine Inhaltseigenschaft" ],
      "A placeholder needs a content property defining the replacement of it (https://mzl.la/2DT1MQd)":[ "Ein Platzhalter benötigt eine Inhaltseigenschaft, die seinen Ersatz definiert (https://mzl.la/2DT1MQd)" ],
      "Translation string is missing the message property":[ "Übersetzungsstring hat keine Nachrichteneigenschaft" ],
      "No \"message\" message property is set for a string (https://mzl.la/2DSBTjA).":[ "Für einen String wurde keine Nachrichteneigenschaft „message“ gesetzt (https://mzl.la/2DSBTjA)." ],
      "The field is required.":[ "Dieses Feld ist erforderlich." ],
      "See https://mzl.la/1ZOhoEN (MDN Docs) for more information.":[ "Weitere Informationen finden Sie unter https://mzl.la/1ZOhoEN (MDN Docs)." ],
      "The permission type is unsupported.":[ "Der Erlaubnistyp wird nicht unterstützt." ],
      "See https://mzl.la/1R1n1t0 (MDN Docs) for more information.":[ "Weitere Informationen finden Sie unter https://mzl.la/1R1n1t0 (MDN Docs)." ],
      "See https://mzl.la/2Qn0fWC (MDN Docs) for more information.":[ "Weitere Informationen finden Sie unter https://mzl.la/2Qn0fWC (MDN Docs)." ],
      "See https://mzl.la/3Woeqv4 (MDN Docs) for more information.":[ "Weitere Informationen finden Sie unter https://mzl.la/3Woeqv4 (MDN Docs)." ],
      "Unknown permission.":[ "Unbekannte Erlaubnis." ],
      "%(instancePath)s: the following privileged permissions are only allowed in privileged extensions: %(privilegedPermissions)s.":[ "%(instancePath)s: Die folgenden privilegierten Berechtigungen sind nur in privilegierten Erweiterungen erlaubt: %(privilegedPermissions)s." ],
      "Invalid host permission.":[ "Ungültige Hostberechtigung." ],
      "Invalid install origin.":[ "Ungültiger Installationsursprung." ],
      "Invalid install origin. A valid origin has - only - a scheme, hostname and optional port. See https://mzl.la/3TEbqbE (MDN Docs) for more information.":[ "Ungültiger Installationsursprung. Ein gültiger Ursprung hat – nur – ein Schema, einen Hostnamen und einen optionalen Port. Weitere Informationen finden Sie unter https://mzl.la/3TEbqbE (MDN Docs)." ],
      "The field is invalid.":[ "Das Feld ist ungültig." ],
      "\"manifest_version\" in the manifest.json is not a valid value":[ "„manifest_version“ in der manifest.json ist kein gültiger Wert" ],
      "See https://mzl.la/20PenXl (MDN Docs) for more information.":[ "Weitere Informationen finden Sie unter https://mzl.la/20PenXl (MDN Docs)." ],
      "\"%(property)s\" allows remote code execution in manifest.json":[ "„%(property)s“ ermöglicht die Ausführung von externem Quelltext in manifest.json" ],
      "A custom \"%(property)s\" needs additional review.":[ "Ein benutzerdefiniertes „%(property)s“ bedarf einer zusätzlichen Überprüfung." ],
      "\"%(property)s\" allows 'eval', which has strong security and performance implications.":[ "\"%(property)s\" erlaubt 'eval', was starke Auswirkungen auf Sicherheit und Leistung hat." ],
      "In most cases the same result can be achieved differently, therefore it is generally prohibited":[ "In den meisten Fällen kann das gleiche Ergebnis auf andere Weise erreicht werden, daher ist es grundsätzlich untersagt" ],
      "The \"name\" property must be a string with no leading/trailing whitespaces.":[ "Die Eigenschaft „name“ muss ein String ohne führende/folgende Leerzeichen sein." ],
      "See http://mzl.la/1STmr48 (MDN Docs) for more information.":[ "Weitere Informationen finden Sie unter http://mzl.la/1STmr48 (MDN Docs)." ],
      "\"update_url\" is not allowed.":[ "„update_url“ ist nicht zulässig." ],
      "\"applications.gecko.update_url\" or \"browser_specific_settings.gecko.update_url\" are not allowed for Mozilla-hosted add-ons.":[ "„applications.gecko.update_url“ oder „browser_specific_settings.gecko.update_url“ sind für von Mozilla gehostete Add-ons nicht erlaubt." ],
      "The \"update_url\" property is not used by Firefox.":[ "Die Eigenschaft „update_url“ wird von Firefox nicht verwendet." ],
      "The \"update_url\" is not used by Firefox in the root of a manifest; your add-on will be updated via the Add-ons site and not your \"update_url\". See: https://mzl.la/25zqk4O":[ "Die „update_url“ wird von Firefox nicht im Wurzelverzeichnis eines Manifests verwendet. Ihr Add-on wird über die Add-ons-Website aktualisiert und nicht über Ihre „update_url“. Siehe: https://mzl.la/25zqk4O" ],
      "\"strict_max_version\" not required.":[ "„strict_max_version“ nicht erforderlich." ],
      "\"strict_max_version\" shouldn't be used unless the add-on is expected not to work with future versions of Firefox.":[ "„strict_max_version“ sollte nicht verwendet werden, es sei denn, das Add-on wird nicht mit zukünftigen Firefox-Versionen funktionieren." ],
      "Manifest Version 3 is not fully supported on Firefox for Android.":[ "Manifest-Version 3 wird von Firefox für Android nicht vollständig unterstützt." ],
      "No \"%(property)s\" property found in manifest.json":[ "Keine „%(property)s“-Eigenschaft in manifest.json gefunden" ],
      "\"%(property)s\" is required":[ "„%(property)s“ ist erforderlich" ],
      "An icon defined in the manifest could not be found in the package.":[ "Ein im Manifest definiertes Symbol konnte im Paket nicht gefunden werden." ],
      "Icon could not be found at \"%(path)s\".":[ "Symbol konnte nicht unter „%(path)s“ gefunden werden." ],
      "A background script defined in the manifest could not be found.":[ "Ein im Manifest definiertes Hintergrundskript wurde nicht gefunden." ],
      "A background page defined in the manifest could not be found.":[ "Eine im Manifest definierte Hintergrundseite wurde nicht gefunden." ],
      "Background script could not be found at \"%(path)s\".":[ "Hintergrundskript konnte nicht unter „%(path)s“ gefunden werden." ],
      "Background page could not be found at \"%(path)s\".":[ "Hintergrundseite konnte nicht unter „%(path)s“ gefunden werden." ],
      "A content script defined in the manifest could not be found.":[ "Ein im Manifest definiertes Inhaltsskript wurde nicht gefunden." ],
      "A content script css file defined in the manifest could not be found.":[ "Eine im Manifest definierte Inhaltsskript-CSS-Datei wurde nicht gefunden." ],
      "Content script defined in the manifest could not be found at \"%(path)s\".":[ "Ein im Manifest definiertes Inhaltsskript konnte unter „%(path)s“ nicht gefunden werden." ],
      "Content script css file defined in the manifest could not be found at \"%(path)s\".":[ "Eine im Manifest definierte CSS-Datei konnte unter „%(path)s“ nicht gefunden werden." ],
      "A dictionary file defined in the manifest could not be found.":[ "Eine im Manifest definierte Wörterbuchdatei wurde nicht gefunden." ],
      "Dictionary file defined in the manifest could not be found at \"%(path)s\".":[ "Eine im Manifest definierte Wörterbuchdatei wurde unter „%(path)s“ nicht gefunden." ],
      "The manifest contains multiple dictionaries.":[ "Das Manifest enthält mehrere Wörterbücher." ],
      "Multiple dictionaries were defined in the manifest, which is unsupported.":[ "Im Manifest wurden mehrere Wörterbücher definiert, was nicht unterstützt wird." ],
      "The manifest contains a dictionaries object, but it is empty.":[ "Das Manifest enthält ein Wörterbuch-Objekt, es ist jedoch leer." ],
      "A dictionaries object was defined in the manifest, but it was empty.":[ "Im Manifest wurde ein Wörterbuch-Objekt definiert, das jedoch leer war." ],
      "The manifest contains a dictionary but no id property.":[ "Das Manifest enthält ein Wörterbuch, aber keine ID-Eigenschaft." ],
      "A dictionary was found in the manifest, but there was no id set.":[ "Es wurde ein Wörterbuch im Manifest gefunden, aber es wurde keine ID festgelegt." ],
      "Please refer to https://github.com/mozilla-extensions/xpi-manifest to learn more about privileged extensions and signing.":[ "Bitte besuchen Sie https://github.com/mozilla-extensions/xpi-manifest, um mehr über privilegierte Erweiterungen und Signieren zu erfahren." ],
      "Forbidden content found in add-on.":[ "Verbotene Inhalte im Add-on gefunden." ],
      "This add-on contains forbidden content.":[ "Dieses Add-on enthält verbotene Inhalte." ],
      "Icons must be square.":[ "Symbole müssen quadratisch sein." ],
      "Icon at \"%(path)s\" must be square.":[ "Symbol unter „%(path)s“ muss quadratisch sein." ],
      "The size of the icon does not match the manifest.":[ "Die Größe des Symbols passt nicht zum Manifest." ],
      "Expected icon at \"%(path)s\" to be %(expected)d pixels wide but was %(actual)d.":[ "Symbol an „%(path)s“ mit Breite von %(expected)d Pixel erwartet, ist aber %(actual)d breit." ],
      "\"%(fieldName)s\" is ignored for non-privileged add-ons.":[ "„%(fieldName)s“ wird bei nicht-privilegierten Add-ons ignoriert." ],
      "Corrupt image file":[ "Grafikdatei beschädigt" ],
      "Expected icon file at \"%(path)s\" is corrupted":[ "Erwartete Symboldatei unter „%(path)s“ ist beschädigt" ],
      "This property has been deprecated.":[ "Diese Eigenschaft wird nicht mehr unterstützt." ],
      "This theme LWT alias has been removed in Firefox 70.":[ "Dieser Theme-LWT-Alias wurde in Firefox 70 entfernt." ],
      "See https://mzl.la/2T11Lkc (MDN Docs) for more information.":[ "Weitere Informationen finden Sie unter https://mzl.la/2T11Lkc (MDN Docs)." ],
      "Theme image for \"%(type)s\" could not be found at \"%(path)s\"":[ "Theme-Grafik für „%(type)s“ konnte nicht unter „%(path)s“ gefunden werden" ],
      "\"%(fieldName)s\" manifest field is only used for privileged and temporarily installed extensions.":[ "„%(fieldName)s“-Manifest-Feld wird nur für privilegierte und temporär installierte Erweiterungen verwendet." ],
      "Corrupted theme image file":[ "Beschädigte Theme-Grafikdatei" ],
      "Theme image file at \"%(path)s\" is corrupted":[ "Die Theme-Grafikdatei unter „%(path)s“ ist beschädigt" ],
      "Theme image file has an unsupported file extension":[ "Die Theme-Grafikdatei hat eine nicht unterstützte Dateierweiterung" ],
      "Theme image file at \"%(path)s\" has an unsupported file extension":[ "Die Theme-Grafikdatei unter „%(path)s“ hat eine nicht unterstützte Dateierweiterung" ],
      "Theme image file has an unsupported mime type":[ "Die Theme-Grafikdatei hat einen nicht unterstützten MIME-Typ" ],
      "Theme image file at \"%(path)s\" has the unsupported mime type \"%(mime)s\"":[ "Die Theme-Grafikdatei unter „%(path)s“ hat einen nicht unterstützten MIME-Typ „%(mime)s“" ],
      "Theme image file mime type does not match its file extension":[ "Der MIME-Typ der Grafikdatei stimmt nicht mit der Dateierweiterung überein" ],
      "Theme image file extension at \"%(path)s\" does not match its actual mime type \"%(mime)s\"":[ "Die Dateierweiterung der Theme-Grafikdatei unter „%(path)s“ stimmt nicht mit dem tatsächlichen MIME-Typ „%(mime)s“ überein" ],
      "The \"default_locale\" is missing localizations.":[ "In der „default_locale“ fehlen Lokalisierungen." ],
      "The \"default_locale\" value is specified in the manifest, but no matching \"messages.json\" in the \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "Der Wert „default_locale“ ist im Manifest angegeben, aber es existiert keine übereinstimmende „messages.json“ im Verzeichnis „_locales“. Siehe: https://mzl.la/2hjcaIE" ],
      "The \"default_locale\" is missing but \"_locales\" exist.":[ "Die „default_locale“ fehlt, aber „_locales“ existiert." ],
      "The \"default_locale\" value is not specifed in the manifest, but a \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "Der Wert für „default_locale“ wurde im Manifest nicht angegeben, aber ein „_locales“-Ordner existiert. Siehe: https://mzl.la/2hjcaIE" ],
      "Unsupported image extension":[ "Nicht unterstützte Endung einer Grafikdatei" ],
      "Icons should be one of JPG/JPEG, WebP, GIF, PNG or SVG.":[ "Symbole sollten JPG/JPEG, WebP, GIF, PNG oder SVG sein." ],
      "\"applications\" property overridden by \"browser_specific_settings\" property":[ "„applications“-Eigenschaft wird überschrieben durch „browser_specific_settings“-Eigenschaft" ],
      "The \"applications\" property is being ignored because it is superseded by the \"browser_specific_settings\" property which is also defined in your manifest. Consider removing applications.":[ "Die „applications“-Eigenschaft wird ignoriert, weil sie durch die Eigenschaft „browser_specific_settings“ ersetzt wird, die auch in Ihrem Manifest definiert ist. Ziehen Sie das Entfernen von Anwendungen in Erwägung." ],
      "Empty language directory":[ "Leeres Sprachverzeichnis" ],
      "messages.json file missing in \"%(path)s\"":[ "Datei „messages.json“ fehlt in „%(path)s“" ],
      "Manifest key not supported by the specified minimum Firefox version":[ "Manifest-Schlüssel wird von der angegebenen Mindestversion von Firefox nicht unterstützt" ],
      "\"strict_min_version\" requires Firefox %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "„strict_min_version“ benötigt Firefox %(minVersion)s, der veröffentlicht wurde, bevor Version %(versionAdded)s die Unterstützung für „%(key)s“ einführte." ],
      "\"%(fieldName)s\" is not supported in manifest versions %(versionRange)s.":[ "„%(fieldName)s“ wird in Manifest-Versionen %(versionRange)s nicht unterstützt." ],
      "Permission not supported by the specified minimum Firefox version":[ "Die Berechtigung wird von der angegebenen Mindestversion von Firefox nicht unterstützt" ],
      "\"%(fieldName)s\" is not supported.":[ "„%(fieldName)s“ wird nicht unterstützt." ],
      "Manifest key not supported by the specified minimum Firefox for Android version":[ "Manifest-Schlüssel wird von der angegebenen Mindestversion von Firefox für Android nicht unterstützt" ],
      "\"strict_min_version\" requires Firefox for Android %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "„strict_min_version“ benötigt Firefox für Android %(minVersion)s, der veröffentlicht wurde, bevor Version %(versionAdded)s die Unterstützung für „%(key)s“ einführte." ],
      "Permission not supported by the specified minimum Firefox for Android version":[ "Die Berechtigung wird von der angegebenen Mindestversion von Firefox für Android nicht unterstützt" ],
      "Linking to \"addons.mozilla.org\" is not allowed":[ "Das Verlinken auf „addons.mozilla.org“ ist nicht erlaubt" ],
      "Links directing to \"addons.mozilla.org\" are not allowed to be used for homepage":[ "Links, die auf „addons.mozilla.org“ verweisen, dürfen nicht für die Homepage verwendet werden" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above":[ "Die „%(permission)s“-Berechtigung erfordert, dass „strict_min_version“ auf „%(minFirefoxVersion)s“ oder höher gesetzt ist" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above. Please update your manifest.json version to specify a minimum Firefox version.":[ "Die „%(permission)s“-Berechtigung erfordert, dass „strict_min_version“ auf „%(minFirefoxVersion)s“ oder höher gesetzt ist. Bitte aktualisieren Sie Ihre manifest.json-Version, um eine Mindestversion von Firefox anzugeben." ],
      "The extension ID is required in Manifest Version 3 and above.":[ "Die Erweiterungs-ID ist in Manifest-Version 3 und höher erforderlich." ],
      "See https://mzl.la/3PLZYdo for more information.":[ "Weitere Informationen finden Sie unter https://mzl.la/3PLZYdo" ],
      "%(instancePath)s: Privileged extensions should declare privileged permissions.":[ "%(instancePath)s: Erweiterungen mit zusätzlichen Rechten sollten für privilegierte Berechtigungen deklarieren." ],
      "This extension does not declare any privileged permission. It does not need to be signed with the privileged certificate. Please upload it directly to https://addons.mozilla.org/.":[ "Diese Erweiterung deklariert keine privilegierten Berechtigungen. Es muss nicht mit dem privilegierten Zertifikat signiert werden. Bitte laden Sie es direkt auf https://addons.mozilla.org/ hoch." ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for privileged extensions.":[ "%(instancePath)s: Die „mozillaAddons“-Berechtigung ist für privilegierte Erweiterungen erforderlich." ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for extensions that include privileged manifest fields.":[ "%(instancePath)s: Die „mozillaAddons“-Berechtigung ist für Erweiterungen erforderlich, die privilegierte Manifest-Felder enthalten." ],
      "%(instancePath)s: privileged manifest fields are only allowed in privileged extensions.":[ "%(instancePath)s: privilegierte Manifest-Felder sind nur in privilegierten Erweiterungen erlaubt." ],
      "This extension does not include the \"mozillaAddons\" permission, which is required for privileged extensions.":[ "Diese Erweiterung enthält nicht die „mozillaAddons“-Berechtigung, die für privilegierte Erweiterungen erforderlich ist." ],
      "Cannot use actions in hidden add-ons.":[ "In versteckten Add-ons können keine Aktionen verwendet werden." ],
      "The hidden and browser_action/page_action (or action in Manifest Version 3 and above) properties are mutually exclusive.":[ "Die Eigenschaften „hidden“ und „browser_action/page_action“ (oder „action“ in Manifest-Version 3 und höher) schließen sich gegenseitig aus." ],
      "Use \"browser_specific_settings\" instead of \"applications\".":[ "Verwenden Sie „browser_specific_settings“ statt „applications“." ],
      "The \"applications\" property in the manifest is deprecated and will no longer be accepted in Manifest Version 3 and above.":[ "Die Eigenschaft „applications“ im Manifest sollte nicht mehr verwendet werden und wird in Manifest-Version 3 und höher nicht mehr akzeptiert." ],
      "\"applications\" is no longer allowed in Manifest Version 3 and above.":[ "„applications“ ist in Manifest-Version 3 und höher nicht mehr erlaubt." ],
      "The \"applications\" property in the manifest is no longer allowed in Manifest Version 3 and above. Use \"browser_specific_settings\" instead.":[ "Die Eigenschaft „applications“ im Manifest ist in Manifest-Version 3 und höher nicht mehr erlaubt. Verwenden Sie stattdessen „browser_specific_settings“." ],
      "The version string should be simplified because it won't be compatible with Manifest Version 3 and above.":[ "Der Versions-String sollte vereinfacht werden, da er mit Manifest-Version 3 und höher nicht kompatibel ist." ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros will no longer be allowed. Letters will no longer be allowed either. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "Die Version sollte ein String mit 1 bis 4 Zahlen sein, die durch Punkte getrennt sind. Jede Zahl sollte aus bis zu 9 Zeichen bestehen und führende Nullen sind nicht mehr erlaubt. Buchstaben sind auch nicht mehr erlaubt. Weitere Informationen finden Sie unter https://mzl.la/3h3mCRu (MDN Docs)." ],
      "The version string should be simplified.":[ "Der Versions-String sollte vereinfacht werden." ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros are not allowed. Letters are no longer allowed. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "Die Version sollte ein String mit 1 bis 4 Zahlen sein, die durch Punkte getrennt sind. Jede Zahl sollte aus bis zu 9 Zeichen bestehen und führende Nullen sind nicht erlaubt. Buchstaben sind nicht mehr erlaubt. Weitere Informationen finden Sie unter https://mzl.la/3h3mCRu (MDN Docs)." ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported in manifest versions %(versionRange)s.":[ "/%(fieldName)s: \"%(permissionName)s\" wird in Manifest-Versionen %(versionRange)s nicht unterstützt." ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported.":[ "/%(fieldName)s: \"%(permissionName)s\" wird nicht unterstützt." ] } } }