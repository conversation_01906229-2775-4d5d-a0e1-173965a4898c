module.exports = { domain:"messages",
  locale_data:{ messages:{ "":{ domain:"messages",
        plural_forms:"nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);",
        lang:"ru" },
      "Validation Summary:":[ "Сводка валидации:" ],
      Code:[ "Код" ],
      Message:[ "Сообщение" ],
      Description:[ "Описание" ],
      File:[ "Файл" ],
      Line:[ "Строка" ],
      Column:[ "Столбец" ],
      "Invalid manifest version range requested: --min-manifest-version (currently set to %(minManifestVersion)s) should not be greater than --max-manifest-version (currently set to %(maxManifestVersion)s).":[ "Запрошен недопустимый диапазон версий манифеста: --min-manifest-version (в настоящее время установлено значение %(minManifestVersion)s) не должен превышать --max-manifest-version (в настоящее время установлено значение %(maxManifestVersion)s)." ],
      "Your FTL is not valid.":[ "Ваш FTL некорректен." ],
      "Your FTL file could not be parsed.":[ "Не удалось распарсить ваш FTL файл." ],
      "Remote scripts are not allowed as per the Add-on Policies.":[ "Внешние скрипты не разрешены Политикой дополнений." ],
      "Please include all scripts in the add-on. For more information, refer to https://mzl.la/2uEOkYp.":[ "Пожалуйста, включите все скрипты в дополнение. Для получения дополнительной информации прочитайте https://mzl.la/2uEOkYp." ],
      "Inline scripts blocked by default":[ "Встроенные скрипты блокируются по умолчанию" ],
      "Default CSP rules prevent inline JavaScript from running (https://mzl.la/2pn32nd).":[ "Правила CSP по умолчанию запрещают запуск встроенного JavaScript (https://mzl.la/2pn32nd)." ],
      "Unadvised 3rd-party JS library":[ "Нерекомендуемая сторонняя JS библиотека" ],
      "Your add-on uses a JavaScript library we do not recommend. Read more: https://bit.ly/1TRIyZY":[ "В вашем дополнении используется библиотека JavaScript, которую мы не рекомендуем. Подробнее: https://bit.ly/1TRIyZY" ],
      "Known JS library detected":[ "Обнаружена известная JS библиотека" ],
      "JavaScript libraries are discouraged for simple add-ons, but are generally accepted.":[ "Библиотеки JavaScript не рекомендуются для простых дополнений, но обычно допустимы." ],
      "Due to both security and performance concerns, this may not be set using dynamic values which have not been adequately sanitized. This can lead to security issues or fairly serious performance degradation.":[ "Из соображений безопасности и производительности это не может быть установлено с использованием динамических значений, которые не были должным образом очищены. Это может привести к проблемам с безопасностью или серьезному снижению производительности." ],
      "{{api}} is not supported":[ "{{api}} не поддерживается" ],
      "This API has not been implemented by Firefox.":[ "Это API не реализовано в Firefox." ],
      "\"{{api}}\" has been removed in Manifest Version 3 (`manifest_version` property)":[ "«{{api}}» было удалено из Манифеста версии 3 (свойство `manifest_version`)" ],
      "{{api}} is deprecated":[ "{{api}} устарело" ],
      "This API has been deprecated by Firefox.":[ "Это API объявлено Firefox устаревшим." ],
      "Content script file could not be found.":[ "Не удалось найти файл скрипта содержимого." ],
      "\"{{api}}\" is deprecated or unimplemented":[ "«{{api}}» устарело или не реализовано" ],
      "Content script file could not be found":[ "Не удалось найти файл скрипта содержимого" ],
      "\"%(api)s\" can cause issues when loaded temporarily":[ "«%(api)s» может вызывать проблемы при временной загрузке" ],
      "This API can cause issues when loaded temporarily using about:debugging in Firefox unless you specify \"browser_specific_settings.gecko.id\" in the manifest. Please see: https://mzl.la/2hizK4a for more.":[ "Этот API может вызывать проблемы при временной загрузке с использованием about:debugging в Firefox, если вы не укажете «browser_specific_settings.gecko.id» в манифесте. Пожалуйста, прочитайте: https://mzl.la/2hizK4a для получения дополнительной информации." ],
      "{{api}} is not supported in Firefox version {{minVersion}}":[ "{{api}} не поддерживается в Firefox {{minVersion}}" ],
      "This API is not implemented by the given minimum Firefox version":[ "Это API не реализовано в указанной минимальной версии Firefox" ],
      "{{api}} is not supported in Firefox for Android version {{minVersion}}":[ "{{api}} не поддерживается в Firefox для Android {{minVersion}}" ],
      "This API is not implemented by the given minimum Firefox for Android version":[ "Это API не реализовано в указанной минимальной версии Firefox для Android" ],
      "Content script file name should not be empty.":[ "Имя файла скрипта содержимого не должно быть пустым." ],
      "Content script file name should not be empty":[ "Имя файла скрипта содержимого не должно быть пустым" ],
      "\"%(method)s\" called with a non-literal uri":[ "«%(method)s» вызывается с небуквенным uri" ],
      "Calling \"%(method)s\" with variable parameters can result in potential security vulnerabilities if the variable contains a remote URI. Consider using 'window.open' with the 'chrome=no' flag.":[ "Вызов «%(method)s» с переменными параметрами может привести к потенциальной уязвимости системы безопасности, если переменная содержит удалённый URI. Рассмотрите возможность использования 'window.open' с флагом 'chrome=no'." ],
      "\"%(method)s\" called with non-local URI":[ "«%(method)s» вызывается с нелокальным URI" ],
      "Calling \"%(method)s\" with a non-local URI will result in the dialog being opened with chrome privileges.":[ "Вызов «%(method)s» с нелокальным URI приведет к открытию диалогового окна с привилегиями chrome." ],
      "JavaScript syntax error":[ "Синтаксическая ошибка JavaScript" ],
      "There is a JavaScript syntax error in your code, which might be related to some experimental JavaScript features that aren't an official part of the language specification and therefore not supported yet. The validation cannot continue on this file.":[ "В вашем коде есть синтаксическая ошибка JavaScript, которая может быть связана с некоторыми экспериментальными функциями JavaScript, которые не являются официальной частью спецификации языка и поэтому ещё не поддерживаются. Проверка этого файла не может быть продолжена." ],
      "Evaluation of strings as code can lead to security vulnerabilities and performance issues, even in the most innocuous of circumstances. Please avoid using `eval` and the `Function` constructor when at all possible.":[ "Оценка строк как кода может привести к уязвимостям безопасности и проблемам с производительностью даже в самых безобидных обстоятельствах. Пожалуйста, по возможности избегайте использования `eval` и конструктора `Function`." ],
      "setTimeout, setInterval and execScript functions should be called only with function expressions as their first argument":[ "Функции setTimeout, setInterval и execScript следует вызывать только с функциональными выражениями в качестве первого аргумента" ],
      "Unexpected global passed as an argument":[ "Неожиданное использование глобальной переменной в качестве аргумента" ],
      "Passing a global as an argument is not recommended. Please make this a var instead.":[ "Нежелательно передавать общее значение в качестве аргумента. Пожалуйста, сделайте это переменной." ],
      "Use of document.write strongly discouraged.":[ "Использование document.write настоятельно не рекомендуется." ],
      "document.write will fail in many circumstances when used in extensions, and has potentially severe security repercussions when used improperly. Therefore, it should not be used.":[ "document.write во многих случаях не будет работать при использовании в расширениях и может иметь серьезные последствия для безопасности при неправильном использовании. Поэтому его не следует использовать." ],
      "Banned 3rd-party JS library":[ "Запрещённая сторонняя JS библиотека" ],
      "Your add-on uses a JavaScript library we consider unsafe. Read more: https://bit.ly/1TRIyZY":[ "В вашем дополнении используется библиотека JavaScript, которую мы считаем небезопасной. Подробнее: https://bit.ly/1TRIyZY" ],
      "Your JSON contains block comments.":[ "Ваш JSON содержит многострочные комментарии." ],
      "Only line comments (comments beginning with \"//\") are allowed in JSON files. Please remove block comments (comments beginning with \"/*\")":[ "В файлах JSON разрешены только строчные комментарии (комментарии, начинающиеся с «//»). Пожалуйста, удалите блочные комментарии (комментарии, начинающиеся с «/*»)" ],
      "Duplicate keys are not allowed in JSON files.":[ "В JSON-файлах не разрешены дублирующиеся ключи." ],
      "Duplicate key found in JSON file.":[ "В файле JSON обнаружен дубликат ключа." ],
      "Your JSON is not valid.":[ "Ваш JSON некорректен." ],
      "Your JSON file could not be parsed.":[ "Не удалось распарсить ваш JSON файл." ],
      "Reserved filename found.":[ "Используется зарезервированное имя файла." ],
      "Files whose names are reserved have been found in the add-on. Please refrain from using them and rename your files.":[ "В дополнении были обнаружены файлы, имена которых зарезервированы. Пожалуйста, воздержитесь от их использования и переименуйте файлы." ],
      "The package is invalid. It may contain entries using invalid characters, as an example using '\\' as a path separator is not allowed in Firefox. Try to recreate your add-on package (ZIP) and make sure all entries are using '/' as the path separator.":[ "Пакет недействителен. Он может содержать записи с использованием недопустимых символов, например, использование «\\» в качестве разделителя пути не разрешено в Firefox. Попробуйте пересоздать пакет дополнения (ZIP) и убедитесь, что все записи используют «/» в качестве разделителя пути." ],
      "We were unable to decompress the zip file.":[ "Нам не удалось разархивировать zip файл." ],
      "manifest.json was not found":[ "не найден manifest.json" ],
      "No manifest.json was found at the root of the extension. The package file must be a ZIP of the extension's files themselves, not of the containing directory. See: https://mzl.la/2r2McKv for more on packaging.":[ "В корне расширения не найден файл manifest.json. Файл пакета должен быть ZIP-архивом самих файлов расширения, а не содержащего его каталога. Подробнее об упаковке читайте здесь: https://mzl.la/2r2McKv." ],
      "File is too large to parse.":[ "Файл слишком большой для парсинга." ],
      "This file is not binary and is too large to parse. Files larger than %(maxFileSizeToParseMB)sMB will not be parsed. Consider moving large lists of data out of JavaScript files and into JSON files, or splitting very large files into smaller ones.":[ "Этот файл не является двоичным и слишком велик для анализа. Файлы размером более %(maxFileSizeToParseMB)sМБ анализироваться не будут. Рассмотрите возможность переноса больших списков данных из файлов JavaScript в файлы JSON или разделения очень больших файлов на более мелкие." ],
      "Hidden file flagged":[ "Найден скрытый файл" ],
      "Hidden files complicate the review process and can contain sensitive information about the system that generated the add-on. Please modify the packaging process so that these files aren't included.":[ "Скрытые файлы усложняют процесс проверки и могут содержать конфиденциальную информацию о системе, сгенерировавшей дополнение. Пожалуйста, измените процесс упаковки, чтобы эти файлы не были включены." ],
      "Package contains duplicate entries":[ "Пакет содержит дублирующиеся записи" ],
      "Flagged filename found":[ "Найдено отмеченное имя файла" ],
      "Files were found that are either unnecessary or have been included unintentionally. They should be removed.":[ "Были обнаружены файлы, которые либо не нужны, либо были включены непреднамеренно. Их следует удалить." ],
      "The package contains multiple entries with the same name. This practice has been banned. Try unzipping and re-zipping your add-on package and try again.":[ "Пакет содержит несколько записей с одинаковым именем. Эта практика была запрещена. Попробуйте разархивировать и повторно заархивировать пакет дополнения и повторите попытку." ],
      "Flagged file extensions found":[ "Найдено отмеченное расширение файла" ],
      "Flagged file type found":[ "Найден отмеченный тип файла" ],
      "Files whose names end with flagged extensions have been found in the add-on. The extension of these files are flagged because they usually identify binary components. Please see https://bit.ly/review-policy for more information on the binary content review process.":[ "В дополнении были обнаружены файлы, имена которых заканчиваются отмеченными расширениями. Расширения этих файлов отмечены, поскольку они обычно идентифицируют бинарные компоненты. Пожалуйста, посетите https://bit.ly/review-policy для получения дополнительной информации о процессе проверки бинарного контента." ],
      "Package already signed":[ "Пакет уже подписан" ],
      "Add-ons which are already signed will be re-signed when published on AMO. This will replace any existing signatures on the add-on.":[ "Дополнения, которые уже подписаны, будут повторно подписаны при публикации в AMO. Это заменит любые существующие подписи в дополнении." ],
      "Firefox add-ons are not allowed to run coin miners.":[ "Дополнениям Firefox не разрешено запускать майнинг." ],
      "We do not allow coinminer scripts to be run inside WebExtensions. See https://github.com/mozilla/addons-linter/issues/1643 for more details.":[ "Мы не разрешаем запускать сценарии coinminer внутри WebExtensions. Подробнее см. https://github.com/mozilla/addons-linter/issues/1643." ],
      "String name is reserved for a predefined message":[ "Имя строки зарезервировано для предустановленного сообщения" ],
      "String names starting with @@ get translated to built-in constants (https://mzl.la/2BL9ZjE).":[ "Имена строк, начинающиеся с @@, преобразуются во встроенные константы (https://mzl.la/2BL9ZjE)." ],
      "String name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2Eztyi5).":[ "Имя строки должно содержать только буквенно-цифровые символы, _ и @ (https://mzl.la/2Eztyi5)." ],
      "Placeholder for message is missing":[ "Замещающий текст для сообщения не задан" ],
      "A placeholder used in the message is not defined.":[ "Замещающий текст, используемый в сообщении, не определён." ],
      "Placeholder name contains invalid characters":[ "Имя замещающего текста содержит неправильные символы" ],
      "Placeholder name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2ExbYez).":[ "Имя замещающего текста должно содержать только буквенно-цифровые символы, _ и @ (https://mzl.la/2ExbYez)." ],
      "Placeholder is missing the content property":[ "В замещающем тексте отсутствует свойство «content»" ],
      "A placeholder needs a content property defining the replacement of it (https://mzl.la/2DT1MQd)":[ "Для замещающего текста требуется свойство «content», определяющее его замену (https://mzl.la/2DT1MQd)" ],
      "Translation string is missing the message property":[ "В переведённой строке отсутствует свойство «message»" ],
      "No \"message\" message property is set for a string (https://mzl.la/2DSBTjA).":[ "Свойство сообщения «message» для строки не задано (https://mzl.la/2DSBTjA)." ],
      "The field is required.":[ "Это обязательное поле." ],
      "See https://mzl.la/1ZOhoEN (MDN Docs) for more information.":[ "Смотрите https://mzl.la/1ZOhoEN (Документацию на MDN) для получения дополнительной информации." ],
      "The permission type is unsupported.":[ "Неподдерживаемый тип разрешения." ],
      "See https://mzl.la/1R1n1t0 (MDN Docs) for more information.":[ "Смотрите https://mzl.la/1R1n1t0 (Документацию на MDN) для получения дополнительной информации." ],
      "See https://mzl.la/2Qn0fWC (MDN Docs) for more information.":[ "Смотрите https://mzl.la/2Qn0fWC (Документацию на MDN) для получения дополнительной информации." ],
      "See https://mzl.la/3Woeqv4 (MDN Docs) for more information.":[ "Смотрите https://mzl.la/3Woeqv4 (Документацию на MDN) для получения дополнительной информации." ],
      "Unknown permission.":[ "Неизвестное разрешение." ],
      "%(instancePath)s: the following privileged permissions are only allowed in privileged extensions: %(privilegedPermissions)s.":[ "%(instancePath)s: следующие привилегированные разрешения разрешены только в привилегированных расширениях: %(privilegedPermissions)s." ],
      "Invalid host permission.":[ "Недействительное разрешение хоста." ],
      "Invalid install origin.":[ "Недопустимый источник установки." ],
      "Invalid install origin. A valid origin has - only - a scheme, hostname and optional port. See https://mzl.la/3TEbqbE (MDN Docs) for more information.":[ "Недопустимый источник установки. Действительный источник имеет только схему, имя хоста и необязательный порт. См. https://mzl.la/3TEbqbE (MDN Docs) для получения дополнительной информации." ],
      "The field is invalid.":[ "Недопустимое поле." ],
      "\"manifest_version\" in the manifest.json is not a valid value":[ "«manifest_version» в файле manifest.json имеет некорректное значение" ],
      "See https://mzl.la/20PenXl (MDN Docs) for more information.":[ "Смотрите https://mzl.la/20PenXl (Документацию на MDN) для получения дополнительной информации." ],
      "\"%(property)s\" allows remote code execution in manifest.json":[ "«%(property)s» разрешает удалённое выполнение кода в manifest.json" ],
      "A custom \"%(property)s\" needs additional review.":[ "Пользовательский «%(property)s» требует дополнительной проверки." ],
      "\"%(property)s\" allows 'eval', which has strong security and performance implications.":[ "«%(property)s» разрешает 'eval', что имеет серьезные последствия для безопасности и производительности." ],
      "In most cases the same result can be achieved differently, therefore it is generally prohibited":[ "В большинстве случаев один и тот же результат может быть достигнут по-разному, поэтому это обычно запрещено" ],
      "The \"name\" property must be a string with no leading/trailing whitespaces.":[ "Свойство «name» должно быть строкой без начальных/конечных пробелов." ],
      "See http://mzl.la/1STmr48 (MDN Docs) for more information.":[ "Смотрите http://mzl.la/1STmr48 (Документацию на MDN) для получения дополнительной информации." ],
      "\"update_url\" is not allowed.":[ "«update_url» не разрешено." ],
      "\"applications.gecko.update_url\" or \"browser_specific_settings.gecko.update_url\" are not allowed for Mozilla-hosted add-ons.":[ "«applications.gecko.update_url» или «browser_specific_settings.gecko.update_url» не разрешены для дополнений, размещенных у Mozilla." ],
      "The \"update_url\" property is not used by Firefox.":[ "Свойство «update_url» не используется Firefox." ],
      "The \"update_url\" is not used by Firefox in the root of a manifest; your add-on will be updated via the Add-ons site and not your \"update_url\". See: https://mzl.la/25zqk4O":[ "«update_url» не используется Firefox в корне манифеста; ваше дополнение будет обновлено через сайт дополнений, а не через ваш «update_url». См.: https://mzl.la/25zqk4O" ],
      "\"strict_max_version\" not required.":[ "«strict_max_version» не требуется." ],
      "\"strict_max_version\" shouldn't be used unless the add-on is expected not to work with future versions of Firefox.":[ "«strict_max_version» не следует использовать, если не ожидается, что дополнение не будет работать с будущими версиями Firefox." ],
      "Manifest Version 3 is not fully supported on Firefox for Android.":[ "Манифест версии 3 не полностью поддерживается в Firefox для Android." ],
      "No \"%(property)s\" property found in manifest.json":[ "Свойство «%(property)s» не найдено в manifest.json" ],
      "\"%(property)s\" is required":[ "Требуется «%(property)s»" ],
      "An icon defined in the manifest could not be found in the package.":[ "Значок, обозначенный в манифесте, не удалось найти в пакете." ],
      "Icon could not be found at \"%(path)s\".":[ "Не удалось найти значок по пути «%(path)s»." ],
      "A background script defined in the manifest could not be found.":[ "Не удалось найти фоновый скрипт, определённый в манифесте." ],
      "A background page defined in the manifest could not be found.":[ "Не удалось найти фоновую страницу, определённую в манифесте." ],
      "Background script could not be found at \"%(path)s\".":[ "Не удалось найти файл фонового скрипта по пути «%(path)s»." ],
      "Background page could not be found at \"%(path)s\".":[ "Не удалось найти фоновую страницу по пути «%(path)s»." ],
      "A content script defined in the manifest could not be found.":[ "Не удалось найти скрипт содержимого, определённый в манифесте." ],
      "A content script css file defined in the manifest could not be found.":[ "Не удалось найти CSS файл скрипта содержимого, определённый в манифесте." ],
      "Content script defined in the manifest could not be found at \"%(path)s\".":[ "Не удалось найти скрипт содержимого, обозначенный в манифесте, по пути «%(path)s»." ],
      "Content script css file defined in the manifest could not be found at \"%(path)s\".":[ "CSS файл скрипта содержимого, обозначенный в манифесте, не найден по пути «%(path)s»." ],
      "A dictionary file defined in the manifest could not be found.":[ "Не удалось найти файл словаря, определённый в манифесте." ],
      "Dictionary file defined in the manifest could not be found at \"%(path)s\".":[ "Не удалось найти файл словаря, определённый в манифесте, по пути «%(path)s»." ],
      "The manifest contains multiple dictionaries.":[ "Манифест содержит несколько словарей." ],
      "Multiple dictionaries were defined in the manifest, which is unsupported.":[ "В манифесте определены несколько словарей, что не поддерживается." ],
      "The manifest contains a dictionaries object, but it is empty.":[ "Манифест содержит объект словарей, но он пустой." ],
      "A dictionaries object was defined in the manifest, but it was empty.":[ "Объект словарей определен в манифесте, но он пустой." ],
      "The manifest contains a dictionary but no id property.":[ "Манифест содержит словарь, но без свойства «id»." ],
      "A dictionary was found in the manifest, but there was no id set.":[ "В манифесте найден словарь, но «id» не установлен." ],
      "Please refer to https://github.com/mozilla-extensions/xpi-manifest to learn more about privileged extensions and signing.":[ "Пожалуйста, обратитесь к https://github.com/mozilla-extensions/xpi-manifest, чтобы узнать больше о привилегированных расширениях и подписи." ],
      "Forbidden content found in add-on.":[ "В дополнении найдено запрещённое содержимое." ],
      "This add-on contains forbidden content.":[ "Это дополнение содержит запрещённое содержимое." ],
      "Icons must be square.":[ "Значки должны быть квадратными." ],
      "Icon at \"%(path)s\" must be square.":[ "Значок по пути «%(path)s» должен быть квадратным." ],
      "The size of the icon does not match the manifest.":[ "Размер значка не соответствует манифесту." ],
      "Expected icon at \"%(path)s\" to be %(expected)d pixels wide but was %(actual)d.":[ "Ожидаемый значок в «%(path)s» должен иметь ширину %(expected)d пикселей, но был %(actual)d." ],
      "\"%(fieldName)s\" is ignored for non-privileged add-ons.":[ "«%(fieldName)s» игнорируется для непривилегированных дополнений." ],
      "Corrupt image file":[ "Повреждённый файл изображения" ],
      "Expected icon file at \"%(path)s\" is corrupted":[ "Ожидаемый файл значка по пути «%(path)s» повреждён" ],
      "This property has been deprecated.":[ "Это свойство устарело." ],
      "This theme LWT alias has been removed in Firefox 70.":[ "LWT-алиас этой темы был удалён в Firefox 70." ],
      "See https://mzl.la/2T11Lkc (MDN Docs) for more information.":[ "Смотрите https://mzl.la/2T11Lkc (Документацию на MDN) для получения дополнительной информации." ],
      "Theme image for \"%(type)s\" could not be found at \"%(path)s\"":[ "Изображение темы для «%(type)s» не найдено по пути «%(path)s»" ],
      "\"%(fieldName)s\" manifest field is only used for privileged and temporarily installed extensions.":[ "Поле манифеста «%(fieldName)s» используется только для привилегированных и временно установленных расширений." ],
      "Corrupted theme image file":[ "Повреждённый файл изображения темы" ],
      "Theme image file at \"%(path)s\" is corrupted":[ "Файл изображения темы по пути «%(path)s» повреждён" ],
      "Theme image file has an unsupported file extension":[ "Файл изображения темы имеет неподдерживаемое расширение" ],
      "Theme image file at \"%(path)s\" has an unsupported file extension":[ "Файл изображения темы по пути «%(path)s» имеет неподдерживаемое расширение" ],
      "Theme image file has an unsupported mime type":[ "Файл изображения темы имеет неподдерживаемый MIME-тип" ],
      "Theme image file at \"%(path)s\" has the unsupported mime type \"%(mime)s\"":[ "Файл изображения темы по пути «%(path)s» имеет неподдерживаемый MIME-тип «%(mime)s»" ],
      "Theme image file mime type does not match its file extension":[ "MIME-тип файла изображения темы не соответствует его расширению" ],
      "Theme image file extension at \"%(path)s\" does not match its actual mime type \"%(mime)s\"":[ "Расширение файла изображения темы по пути «%(path)s» не соответствует его MIME-типу «%(mime)s»" ],
      "The \"default_locale\" is missing localizations.":[ "Отсутствуют переводы для \"default_locale\"." ],
      "The \"default_locale\" value is specified in the manifest, but no matching \"messages.json\" in the \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "Значение «default_locale» указано в манифесте, но в каталоге «_locales» не существует соответствующего файла messages.json. См.: https://mzl.la/2hjcaEE" ],
      "The \"default_locale\" is missing but \"_locales\" exist.":[ "«default_locale» отсутствует, а «_locales» существует." ],
      "The \"default_locale\" value is not specifed in the manifest, but a \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "Значение «default_locale» не указано в манифесте, но каталог «_locales» существует. См.: https://mzl.la/2hjcaEE" ],
      "Unsupported image extension":[ "Неподдерживаемое расширение изображения" ],
      "Icons should be one of JPG/JPEG, WebP, GIF, PNG or SVG.":[ "Значок должен быть в одном из следующих форматов — JPG/JPEG, WebP, GIF, PNG или SVG." ],
      "\"applications\" property overridden by \"browser_specific_settings\" property":[ "свойство «applications» переопределено свойством «browser_specific_settings»" ],
      "The \"applications\" property is being ignored because it is superseded by the \"browser_specific_settings\" property which is also defined in your manifest. Consider removing applications.":[ "Свойство «applications» игнорируется, поскольку оно заменено свойством «browser_specific_settings», которое также определено в вашем манифесте. Рассмотрите возможность удаления «applications»." ],
      "Empty language directory":[ "Пустой каталог языков" ],
      "messages.json file missing in \"%(path)s\"":[ "Файл messages.json отсутствует по пути «%(path)s»" ],
      "Manifest key not supported by the specified minimum Firefox version":[ "Ключ манифеста не поддерживается указанной минимальной версией Firefox" ],
      "\"strict_min_version\" requires Firefox %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "Для «strict_min_version» требуется Firefox %(minVersion)s, который был выпущен до того, как версия %(versionAdded)s представила поддержку «%(key)s»." ],
      "\"%(fieldName)s\" is not supported in manifest versions %(versionRange)s.":[ "«%(fieldName)s» не поддерживается в версиях манифеста %(versionRange)s." ],
      "Permission not supported by the specified minimum Firefox version":[ "Разрешение не поддерживается указанной минимальной версией Firefox" ],
      "\"%(fieldName)s\" is not supported.":[ "«%(fieldName)s» не поддерживается." ],
      "Manifest key not supported by the specified minimum Firefox for Android version":[ "Ключ манифеста не поддерживается указанной минимальной версией Firefox для Android" ],
      "\"strict_min_version\" requires Firefox for Android %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "Для «strict_min_version» требуется Firefox для Android %(minVersion)s, который был выпущен до того, как версия %(versionAdded)s представила поддержку «%(key)s»." ],
      "Permission not supported by the specified minimum Firefox for Android version":[ "Разрешение не поддерживается указанной минимальной версией Firefox для Android" ],
      "Linking to \"addons.mozilla.org\" is not allowed":[ "Ссылка на \"addons.mozilla.org\" не разрешена" ],
      "Links directing to \"addons.mozilla.org\" are not allowed to be used for homepage":[ "Ссылки, ведущие на \"addons.mozilla.org\", не могут быть использованы на главной странице" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above":[ "Разрешение «%(permission)s» требует, чтобы для параметра «strict_min_version» было установлено значение «%(minFirefoxVersion)s» или выше" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above. Please update your manifest.json version to specify a minimum Firefox version.":[ "Разрешение «%(permission)s» требует, чтобы для «strict_min_version» было установлено значение «%(minFirefoxVersion)s» или выше. Обновите версию manifest.json, указав минимальную версию Firefox." ],
      "The extension ID is required in Manifest Version 3 and above.":[ "Идентификатор расширения обязателен для манифеста версии 3 и выше." ],
      "See https://mzl.la/3PLZYdo for more information.":[ "Посетите https://mzl.la/3PLZYdo для получения дополнительной информации." ],
      "%(instancePath)s: Privileged extensions should declare privileged permissions.":[ "%(instancePath)s: привилегированные расширения должны объявлять привилегированные разрешения." ],
      "This extension does not declare any privileged permission. It does not need to be signed with the privileged certificate. Please upload it directly to https://addons.mozilla.org/.":[ "Это расширение не объявляет никаких привилегированных разрешений. Его не нужно подписывать привилегированным сертификатом. Пожалуйста, загрузите его прямо на https://addons.mozilla.org/." ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for privileged extensions.":[ "%(instancePath)s: для привилегированных расширений требуется разрешение «mozillaAddons»." ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for extensions that include privileged manifest fields.":[ "%(instancePath)s: для расширений, которые включают привилегированные поля манифеста, требуется разрешение «mozillaAddons»." ],
      "%(instancePath)s: privileged manifest fields are only allowed in privileged extensions.":[ "%(instancePath)s: привилегированные поля манифеста разрешены только в привилегированных расширениях." ],
      "This extension does not include the \"mozillaAddons\" permission, which is required for privileged extensions.":[ "Это расширение не включает разрешение «mozillaAddons», которое требуется для привилегированных расширений." ],
      "Cannot use actions in hidden add-ons.":[ "В скрытых дополнениях нельзя использовать действия." ],
      "The hidden and browser_action/page_action (or action in Manifest Version 3 and above) properties are mutually exclusive.":[ "Свойства hidden и browser_action/page_action (или action в версии манифеста 3 и выше) являются взаимоисключающими." ],
      "Use \"browser_specific_settings\" instead of \"applications\".":[ "Используйте «browser_specific_settings» вместо «applications»." ],
      "The \"applications\" property in the manifest is deprecated and will no longer be accepted in Manifest Version 3 and above.":[ "Свойство «applications» в манифесте устарело и больше не будет приниматься в манифесте версии 3 и выше." ],
      "\"applications\" is no longer allowed in Manifest Version 3 and above.":[ "«applications» больше не разрешены в манифесте версии 3 и выше." ],
      "The \"applications\" property in the manifest is no longer allowed in Manifest Version 3 and above. Use \"browser_specific_settings\" instead.":[ "Свойство «applications» в манифесте больше не разрешено в манифесте версии 3 и выше. Вместо этого используйте «browser_specific_settings»." ],
      "The version string should be simplified because it won't be compatible with Manifest Version 3 and above.":[ "Строка версии должна быть упрощена, поскольку она не будет совместима с манифестом версии 3 и выше." ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros will no longer be allowed. Letters will no longer be allowed either. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "Версия должна быть строкой, которая содержит от 1 до 4 чисел, разделенных точками. Каждое число должно содержать до 9 цифр, а начальные нули более не допускаются. Символы также больше не будут разрешены. См. https://mzl.la/3h3mCRu (MDN Docs) для получения дополнительной информации." ],
      "The version string should be simplified.":[ "Строка версии должна быть упрощена." ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros are not allowed. Letters are no longer allowed. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "Версия должна быть строкой, которая содержит от 1 до 4 чисел, разделенных точками. Каждое число должно содержать до 9 цифр, а начальные нули более не допускаются. Символы более не разрешены. См. https://mzl.la/3h3mCRu (MDN Docs) для получения дополнительной информации." ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported in manifest versions %(versionRange)s.":[ "/%(fieldName)s: «%(permissionName)s» не поддерживается в версиях манифеста %(versionRange)s." ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported.":[ "/%(fieldName)s: «%(permissionName)s» не поддерживается." ] } } }