module.exports = { domain:"messages",
  locale_data:{ messages:{ "":{ domain:"messages",
        plural_forms:"nplurals=2; plural=(n > 1);",
        lang:"kab" },
      "Validation Summary:":[ "Agzul n usentem:" ],
      Code:[ "Tangalt" ],
      Message:[ "Izen" ],
      Description:[ "Aglam" ],
      File:[ "Afaylu" ],
      Line:[ "Izirig" ],
      Column:[ "Tigejdit" ],
      "Invalid manifest version range requested: --min-manifest-version (currently set to %(minManifestVersion)s) should not be greater than --max-manifest-version (currently set to %(maxManifestVersion)s).":[ "" ],
      "Your FTL is not valid.":[ "FTL inek mačči d ameγtu." ],
      "Your FTL file could not be parsed.":[ "Ur d-yelli wamek ad yettwasleḍ FTL inek." ],
      "Remote scripts are not allowed as per the Add-on Policies.":[ "Iskripten inmeggagen ur ttwasirgen ara akken i d-yettwalmmel deg tiwtilin icudden ar izegrar." ],
      "Please include all scripts in the add-on. For more information, refer to https://mzl.la/2uEOkYp.":[ "" ],
      "Inline scripts blocked by default":[ "Iskripten inline weḥlen si tazwara" ],
      "Default CSP rules prevent inline JavaScript from running (https://mzl.la/2pn32nd).":[ "" ],
      "Unadvised 3rd-party JS library":[ "Tamkerḍit JS tis kraḍ d iritt i useqdec" ],
      "Your add-on uses a JavaScript library we do not recommend. Read more: https://bit.ly/1TRIyZY":[ "" ],
      "Known JS library detected":[ "Tamkarḍit JS tettwaf" ],
      "JavaScript libraries are discouraged for simple add-ons, but are generally accepted.":[ "" ],
      "Due to both security and performance concerns, this may not be set using dynamic values which have not been adequately sanitized. This can lead to security issues or fairly serious performance degradation.":[ "" ],
      "{{api}} is not supported":[ "{{api}} ur yettwaqbel" ],
      "This API has not been implemented by Firefox.":[ "Asnas-agi API yettwabna di Firefox." ],
      "\"{{api}}\" has been removed in Manifest Version 3 (`manifest_version` property)":[ "" ],
      "{{api}} is deprecated":[ "{{api}} ur yettwaqbel ara" ],
      "This API has been deprecated by Firefox.":[ "Asnas-a API yettuneḥsab d aqbur sɣur Firefox." ],
      "Content script file could not be found.":[ "Afaylu n uskript n ugbur ulac-it." ],
      "\"{{api}}\" is deprecated or unimplemented":[ "" ],
      "Content script file could not be found":[ "Afaylu n uskript n ugbur ulac-it" ],
      "\"%(api)s\" can cause issues when loaded temporarily":[ "" ],
      "This API can cause issues when loaded temporarily using about:debugging in Firefox unless you specify \"browser_specific_settings.gecko.id\" in the manifest. Please see: https://mzl.la/2hizK4a for more.":[ "" ],
      "{{api}} is not supported in Firefox version {{minVersion}}":[ "{{api}} ur yettwasefrak ara deg lqem-a n Firefox {{minVersion}}" ],
      "This API is not implemented by the given minimum Firefox version":[ "Asnas-agi API ur yettwabna ara deg lqem adday n Firefox" ],
      "{{api}} is not supported in Firefox for Android version {{minVersion}}":[ "{{api}} ur yettwasefrak ara deg lqem-a n Firefox i Android {{minVersion}}" ],
      "This API is not implemented by the given minimum Firefox for Android version":[ "Asnas-agi API ur yettwabna ara deg lqem adday n Firefox Android" ],
      "Content script file name should not be empty.":[ "Isem n ufaylu n uskript n ugbur ur yessefk ara ad yili d ilem." ],
      "Content script file name should not be empty":[ "Isem n ufaylu n uskript n ugbur ur yessefk ara ad yili d ilem" ],
      "\"%(method)s\" called with a non-literal uri":[ "" ],
      "Calling \"%(method)s\" with variable parameters can result in potential security vulnerabilities if the variable contains a remote URI. Consider using 'window.open' with the 'chrome=no' flag.":[ "" ],
      "\"%(method)s\" called with non-local URI":[ "" ],
      "Calling \"%(method)s\" with a non-local URI will result in the dialog being opened with chrome privileges.":[ "" ],
      "JavaScript syntax error":[ "Tuccḍa n tseddast JavaScript" ],
      "There is a JavaScript syntax error in your code, which might be related to some experimental JavaScript features that aren't an official part of the language specification and therefore not supported yet. The validation cannot continue on this file.":[ "" ],
      "Evaluation of strings as code can lead to security vulnerabilities and performance issues, even in the most innocuous of circumstances. Please avoid using `eval` and the `Function` constructor when at all possible.":[ "" ],
      "setTimeout, setInterval and execScript functions should be called only with function expressions as their first argument":[ "" ],
      "Unexpected global passed as an argument":[ "Amutti amatu iɛedda am tɣiret" ],
      "Passing a global as an argument is not recommended. Please make this a var instead.":[ "" ],
      "Use of document.write strongly discouraged.":[ "Aseqdec n document.write ur yelhi ara." ],
      "document.write will fail in many circumstances when used in extensions, and has potentially severe security repercussions when used improperly. Therefore, it should not be used.":[ "" ],
      "Banned 3rd-party JS library":[ "Tamkerḍit JS tis kraḍ tegdel i useqdec" ],
      "Your add-on uses a JavaScript library we consider unsafe. Read more: https://bit.ly/1TRIyZY":[ "" ],
      "Your JSON contains block comments.":[ "Tangalt-ik JSON tegber iḥder n yiwenniten." ],
      "Only line comments (comments beginning with \"//\") are allowed in JSON files. Please remove block comments (comments beginning with \"/*\")":[ "" ],
      "Duplicate keys are not allowed in JSON files.":[ "Tiisura tusligin ur ttwasirgent ara deg ifuyla JSON." ],
      "Duplicate key found in JSON file.":[ "" ],
      "Your JSON is not valid.":[ "JSON inek mačči d ameγtu." ],
      "Your JSON file could not be parsed.":[ "Tangalt-ik JSON ur tezmir ara tettwasleḍ." ],
      "Reserved filename found.":[ "Isem n ufauylu ittuḥaṛṛen yettwaf." ],
      "Files whose names are reserved have been found in the add-on. Please refrain from using them and rename your files.":[ "" ],
      "The package is invalid. It may contain entries using invalid characters, as an example using '\\' as a path separator is not allowed in Firefox. Try to recreate your add-on package (ZIP) and make sure all entries are using '/' as the path separator.":[ "" ],
      "We were unable to decompress the zip file.":[ "Ur nezmir ara ad n sefruri afaylu zip." ],
      "manifest.json was not found":[ "Afaylu manifest.json ur yettwaf ara" ],
      "No manifest.json was found at the root of the extension. The package file must be a ZIP of the extension's files themselves, not of the containing directory. See: https://mzl.la/2r2McKv for more on packaging.":[ "" ],
      "File is too large to parse.":[ "Afaylu meqqer aṭas akken ad yettwasleḍ." ],
      "This file is not binary and is too large to parse. Files larger than %(maxFileSizeToParseMB)sMB will not be parsed. Consider moving large lists of data out of JavaScript files and into JSON files, or splitting very large files into smaller ones.":[ "" ],
      "Hidden file flagged":[ "Afaylu uffir yettwacreḍ" ],
      "Hidden files complicate the review process and can contain sensitive information about the system that generated the add-on. Please modify the packaging process so that these files aren't included.":[ "" ],
      "Package contains duplicate entries":[ "Akemmus yegber inekcamen usligen" ],
      "Flagged filename found":[ "Isem n ufaylu yettwacerḍen yettwaf" ],
      "Files were found that are either unnecessary or have been included unintentionally. They should be removed.":[ "" ],
      "The package contains multiple entries with the same name. This practice has been banned. Try unzipping and re-zipping your add-on package and try again.":[ "" ],
      "Flagged file extensions found":[ "Isiɣzaf n ufaylu yettwacerḍen ttwafen" ],
      "Flagged file type found":[ "Anaw n ufaylu yettwacerḍen yettwaf" ],
      "Files whose names end with flagged extensions have been found in the add-on. The extension of these files are flagged because they usually identify binary components. Please see https://bit.ly/review-policy for more information on the binary content review process.":[ "" ],
      "Package already signed":[ "Akemmus yettwasezmel yakan" ],
      "Add-ons which are already signed will be re-signed when published on AMO. This will replace any existing signatures on the add-on.":[ "" ],
      "Firefox add-ons are not allowed to run coin miners.":[ "Izegrar Firefox ur zmiren ara ad selkemen tifextin n tedrimt." ],
      "We do not allow coinminer scripts to be run inside WebExtensions. See https://github.com/mozilla/addons-linter/issues/1643 for more details.":[ "" ],
      "String name is reserved for a predefined message":[ "ISem n uzrir n yisekkilen yettwaḥeṛṛ i yizen yettwasbadun yakan" ],
      "String names starting with @@ get translated to built-in constants (https://mzl.la/2BL9ZjE).":[ "" ],
      "String name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2Eztyi5).":[ "" ],
      "Placeholder for message is missing":[ "Amḍiq yettwaḥerzen i yizen ulac-it" ],
      "A placeholder used in the message is not defined.":[ "Amḍiq yettwaḥerzen yettwasqedcen deg izen ur yettwasbadu ara." ],
      "Placeholder name contains invalid characters":[ "Amḍiq yettwaḥerzen yegber yir isekkilen" ],
      "Placeholder name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2ExbYez).":[ "" ],
      "Placeholder is missing the content property":[ "Amḍiq yettwaḥerzen ulac-it di tmeẓlit n ugbur" ],
      "A placeholder needs a content property defining the replacement of it (https://mzl.la/2DT1MQd)":[ "" ],
      "Translation string is missing the message property":[ "Azrir n tsuqilt ur yegbir ara taɣara n yizen" ],
      "No \"message\" message property is set for a string (https://mzl.la/2DSBTjA).":[ "Ulac taɣara n yizen \"message yettusbadun i uzrir (https://mzl.la/2DSBTjA)." ],
      "The field is required.":[ "Aferdis-agi yettwasra." ],
      "See https://mzl.la/1ZOhoEN (MDN Docs) for more information.":[ "Wali https://mzl.la/1ZOhoEN (MDN Docs) i wugar n telɣut." ],
      "The permission type is unsupported.":[ "Anaw-agi n tsiregt ur yettwasefrak ara." ],
      "See https://mzl.la/1R1n1t0 (MDN Docs) for more information.":[ "Wali https://mzl.la/1R1n1t0 (MDN Docs) i wugar n telɣut." ],
      "See https://mzl.la/2Qn0fWC (MDN Docs) for more information.":[ "Wali https://mzl.la/20PenXl (MDN Docs) i wugar n telɣut." ],
      "See https://mzl.la/3Woeqv4 (MDN Docs) for more information.":[ "" ],
      "Unknown permission.":[ "Tasiragt tarussint." ],
      "%(instancePath)s: the following privileged permissions are only allowed in privileged extensions: %(privilegedPermissions)s.":[ "" ],
      "Invalid host permission.":[ "Tasiregt n usenneftaɣ d tarameɣtut." ],
      "Invalid install origin.":[ "Asebded aɣbalu d arameɣtu." ],
      "Invalid install origin. A valid origin has - only - a scheme, hostname and optional port. See https://mzl.la/3TEbqbE (MDN Docs) for more information.":[ "" ],
      "The field is invalid.":[ "Aferdis arameγtu." ],
      "\"manifest_version\" in the manifest.json is not a valid value":[ "Azal n \"manifest_version\" di manifest.json mačči d ameɣtu" ],
      "See https://mzl.la/20PenXl (MDN Docs) for more information.":[ "Wali https://mzl.la/20PenXl (MDN Docs) i wugar n telɣut." ],
      "\"%(property)s\" allows remote code execution in manifest.json":[ "" ],
      "A custom \"%(property)s\" needs additional review.":[ "" ],
      "\"%(property)s\" allows 'eval', which has strong security and performance implications.":[ "" ],
      "In most cases the same result can be achieved differently, therefore it is generally prohibited":[ "" ],
      "The \"name\" property must be a string with no leading/trailing whitespaces.":[ "" ],
      "See http://mzl.la/1STmr48 (MDN Docs) for more information.":[ "Wali http://mzl.la/1STmr48 (MDN Docs) i wugar n telɣut." ],
      "\"update_url\" is not allowed.":[ "\"update_url\" ur ittwasireg ara." ],
      "\"applications.gecko.update_url\" or \"browser_specific_settings.gecko.update_url\" are not allowed for Mozilla-hosted add-ons.":[ "" ],
      "The \"update_url\" property is not used by Firefox.":[ "Taɣara \"update_url\" ur tt-yesseqdac ara Firefox." ],
      "The \"update_url\" is not used by Firefox in the root of a manifest; your add-on will be updated via the Add-ons site and not your \"update_url\". See: https://mzl.la/25zqk4O":[ "" ],
      "\"strict_max_version\" not required.":[ "\"strict_max_version\" ilaq." ],
      "\"strict_max_version\" shouldn't be used unless the add-on is expected not to work with future versions of Firefox.":[ "" ],
      "Manifest Version 3 is not fully supported on Firefox for Android.":[ "" ],
      "No \"%(property)s\" property found in manifest.json":[ "" ],
      "\"%(property)s\" is required":[ "" ],
      "An icon defined in the manifest could not be found in the package.":[ "Tignit yettwasbadun deg umeskan ulac-itt deg ukemmus." ],
      "Icon could not be found at \"%(path)s\".":[ "Ulac-ot di \"%(path)s\"." ],
      "A background script defined in the manifest could not be found.":[ "Ur izmir ara ad d-yaf askript n ugilal i yettusbadun deg umeskan." ],
      "A background page defined in the manifest could not be found.":[ "Ur izmir ara ad d-yaf tugna n ugilal i yettusbadun deg umeskan." ],
      "Background script could not be found at \"%(path)s\".":[ "Askript n ugilal ulac-it di \"%(path)s\"." ],
      "Background page could not be found at \"%(path)s\".":[ "Asebter n ugilal ulac-it di \"%(path)s\"." ],
      "A content script defined in the manifest could not be found.":[ "Ur izmir ara ad d-yaf askript n ugbur i yettusbadun deg umeskan." ],
      "A content script css file defined in the manifest could not be found.":[ "Ur izmir ara ad d-yaf afaylu css n uskript n ugbur i yettusbadun deg umeskan." ],
      "Content script defined in the manifest could not be found at \"%(path)s\".":[ "Askript n ugbur deg umeskan ulac-it di \"%(path)s\"." ],
      "Content script css file defined in the manifest could not be found at \"%(path)s\".":[ "Askript n ugbur deg ufaylu CSS deg umeskan ulac-it di \"%(path)s\"." ],
      "A dictionary file defined in the manifest could not be found.":[ "Ur izmir ara ad d-yaf afaylu n umawal i yettusbadun deg umeskan." ],
      "Dictionary file defined in the manifest could not be found at \"%(path)s\".":[ "Ur izmir ara ad d-yaf afaylu n umawal i yettusbadun deg umeskan deg \"%(path)s\"." ],
      "The manifest contains multiple dictionaries.":[ "Afaylu ameskan igber ddeqs n yimawalen." ],
      "Multiple dictionaries were defined in the manifest, which is unsupported.":[ "Ddeqs n yimawalen ttusbadun deg umeskan, ayen ur nettusefrak ara." ],
      "The manifest contains a dictionaries object, but it is empty.":[ "Ameskan yegber taɣawsa amawal, maca d ilem." ],
      "A dictionaries object was defined in the manifest, but it was empty.":[ "Taɣawsa amawal tettusbadu deg umeskan, maca d ilem." ],
      "The manifest contains a dictionary but no id property.":[ "Ameskan yegber amawal, maca ulac timeẓlit id." ],
      "A dictionary was found in the manifest, but there was no id set.":[ "Amawal yettwaf deg umeskan, maca ulac asulay id i yettusbadun." ],
      "Please refer to https://github.com/mozilla-extensions/xpi-manifest to learn more about privileged extensions and signing.":[ "" ],
      "Forbidden content found in add-on.":[ "Agbur igedlen yettwaf deg uzegrir." ],
      "This add-on contains forbidden content.":[ "Azegrir-agi yegber iferdisen yettwagedlen." ],
      "Icons must be square.":[ "Yessefk tignitin ad ilint d amkuz." ],
      "Icon at \"%(path)s\" must be square.":[ "Tignit yellan deg wadi \"%(path)s\" yessefk ad tili d amkuz." ],
      "The size of the icon does not match the manifest.":[ "Teɣzi n tignit ur temṣada ara akked umeskan." ],
      "Expected icon at \"%(path)s\" to be %(expected)d pixels wide but was %(actual)d.":[ "" ],
      "\"%(fieldName)s\" is ignored for non-privileged add-ons.":[ "" ],
      "Corrupt image file":[ "Afaylu n tugna irreẓ" ],
      "Expected icon file at \"%(path)s\" is corrupted":[ "Tignit yettwarǧan deg wadig \"%(path)s\" texseṛ" ],
      "This property has been deprecated.":[ "" ],
      "This theme LWT alias has been removed in Firefox 70.":[ "Isem n usentel LWT yettwakkes deg Firefox 70." ],
      "See https://mzl.la/2T11Lkc (MDN Docs) for more information.":[ "Wali https://mzl.la/2T11Lkc (MDN Docs) i wugar n yisallen." ],
      "Theme image for \"%(type)s\" could not be found at \"%(path)s\"":[ "Tugna n usentel i \"%(type)s\" ur tettwaf ara deg \"%(path)s\"" ],
      "\"%(fieldName)s\" manifest field is only used for privileged and temporarily installed extensions.":[ "" ],
      "Corrupted theme image file":[ "Afaylu n tugna n usentel yeṛṛez" ],
      "Theme image file at \"%(path)s\" is corrupted":[ "Afaylu ntugna n usentel deg \"%(path)s\" yexṣer" ],
      "Theme image file has an unsupported file extension":[ "Asiɣzef n ufaylu n tugna n usentel ur yettusefrak ara" ],
      "Theme image file at \"%(path)s\" has an unsupported file extension":[ "Asiɣzef n ufaylu deg \"%(path)s\" ɣer-s asiɣzef ur yettusefraken ara" ],
      "Theme image file has an unsupported mime type":[ "Tugna n usentel ɣur-s anaw mime ur yettisefraken ara" ],
      "Theme image file at \"%(path)s\" has the unsupported mime type \"%(mime)s\"":[ "Tugna n usentel deg \"%(path)s\" ɣur-s anaw mime \"%(mime)s\" ur yettusefraken ara" ],
      "Theme image file mime type does not match its file extension":[ "Anaw mime n ufaylu n tugna n usentel ur yemṣada ara d usiɣzef n ufaylu-is" ],
      "Theme image file extension at \"%(path)s\" does not match its actual mime type \"%(mime)s\"":[ "Asiɣzef n ufaylu n usentel deg \"%(path)s\" ur yemṣada ara akked wanaw-is mime amiran \"%(mime)s\"" ],
      "The \"default_locale\" is missing localizations.":[ "Tisuqilin ulac-iten i \"default_locale\"." ],
      "The \"default_locale\" value is specified in the manifest, but no matching \"messages.json\" in the \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "" ],
      "The \"default_locale\" is missing but \"_locales\" exist.":[ "\"default_locale\" ulac-it acu kan \"_locales\" yella." ],
      "The \"default_locale\" value is not specifed in the manifest, but a \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "" ],
      "Unsupported image extension":[ "Asiɣzef n tugna ur yettusefrak ara" ],
      "Icons should be one of JPG/JPEG, WebP, GIF, PNG or SVG.":[ "Yessefk tignitin ad ilint d yiwet seg  JPG/JPEG, WebP, GIF, PNG neɣ SVG." ],
      "\"applications\" property overridden by \"browser_specific_settings\" property":[ "" ],
      "The \"applications\" property is being ignored because it is superseded by the \"browser_specific_settings\" property which is also defined in your manifest. Consider removing applications.":[ "" ],
      "Empty language directory":[ "Akaram n tutlayin d ilem" ],
      "messages.json file missing in \"%(path)s\"":[ "Afaylu messages.json ulac-it di \"%(path)s\"" ],
      "Manifest key not supported by the specified minimum Firefox version":[ "Tasarut n umeskan ur tettusefrak ara deg lqem Firefox adday i d-ittunefken" ],
      "\"strict_min_version\" requires Firefox %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "" ],
      "\"%(fieldName)s\" is not supported in manifest versions %(versionRange)s.":[ "" ],
      "Permission not supported by the specified minimum Firefox version":[ "Tasiregt ur tettusefrak ara deg lqem Firefox adday i d-ittunefken" ],
      "\"%(fieldName)s\" is not supported.":[ "" ],
      "Manifest key not supported by the specified minimum Firefox for Android version":[ "Tasarut n umeskan ur tettusefrak ara deg lqem Firefox Android adday i d-ittunefken" ],
      "\"strict_min_version\" requires Firefox for Android %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "" ],
      "Permission not supported by the specified minimum Firefox for Android version":[ "Tasiregt ur tettusefrak ara deg lqem Firefox Android adday i d-ittunefken" ],
      "Linking to \"addons.mozilla.org\" is not allowed":[ "Iseɣwan yerran srid ɣer \"addons.mozilla.org\" ur ttusirgen ara" ],
      "Links directing to \"addons.mozilla.org\" are not allowed to be used for homepage":[ "Iseɣwan yerran srid ɣer \"addons.mozilla.org\" ur ttusirgen ara d asebter agejdan" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above":[ "" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above. Please update your manifest.json version to specify a minimum Firefox version.":[ "" ],
      "The extension ID is required in Manifest Version 3 and above.":[ "" ],
      "See https://mzl.la/3PLZYdo for more information.":[ "Wali https://mzl.la/3PLZYdo i wugar n telɣut." ],
      "%(instancePath)s: Privileged extensions should declare privileged permissions.":[ "" ],
      "This extension does not declare any privileged permission. It does not need to be signed with the privileged certificate. Please upload it directly to https://addons.mozilla.org/.":[ "" ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for privileged extensions.":[ "" ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for extensions that include privileged manifest fields.":[ "" ],
      "%(instancePath)s: privileged manifest fields are only allowed in privileged extensions.":[ "" ],
      "This extension does not include the \"mozillaAddons\" permission, which is required for privileged extensions.":[ "" ],
      "Cannot use actions in hidden add-ons.":[ "" ],
      "The hidden and browser_action/page_action (or action in Manifest Version 3 and above) properties are mutually exclusive.":[ "" ],
      "Use \"browser_specific_settings\" instead of \"applications\".":[ "" ],
      "The \"applications\" property in the manifest is deprecated and will no longer be accepted in Manifest Version 3 and above.":[ "" ],
      "\"applications\" is no longer allowed in Manifest Version 3 and above.":[ "" ],
      "The \"applications\" property in the manifest is no longer allowed in Manifest Version 3 and above. Use \"browser_specific_settings\" instead.":[ "" ],
      "The version string should be simplified because it won't be compatible with Manifest Version 3 and above.":[ "" ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros will no longer be allowed. Letters will no longer be allowed either. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "" ],
      "The version string should be simplified.":[ "" ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros are not allowed. Letters are no longer allowed. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "" ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported in manifest versions %(versionRange)s.":[ "" ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported.":[ "" ] } } }