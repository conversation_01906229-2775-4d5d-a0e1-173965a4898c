module.exports = { domain:"messages",
  locale_data:{ messages:{ "":{ domain:"messages",
        plural_forms:"nplurals=3; plural=(n==1 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);",
        lang:"pl" },
      "Validation Summary:":[ "Podsumowanie sprawdzania poprawności:" ],
      Code:[ "Kod" ],
      Message:[ "Komunikat" ],
      Description:[ "Opis" ],
      File:[ "Plik" ],
      Line:[ "Wiersz" ],
      Column:[ "Kolumna" ],
      "Invalid manifest version range requested: --min-manifest-version (currently set to %(minManifestVersion)s) should not be greater than --max-manifest-version (currently set to %(maxManifestVersion)s).":[ "" ],
      "Your FTL is not valid.":[ "Plik FTL jest nieprawidłowy." ],
      "Your FTL file could not be parsed.":[ "Nie można przetworzyć pliku FTL." ],
      "Remote scripts are not allowed as per the Add-on Policies.":[ "Zdalne skrypty są niedozwolone zgodnie z zasadami dodatków." ],
      "Please include all scripts in the add-on. For more information, refer to https://mzl.la/2uEOkYp.":[ "" ],
      "Inline scripts blocked by default":[ "Wstawione skrypty są domyślnie blokowane" ],
      "Default CSP rules prevent inline JavaScript from running (https://mzl.la/2pn32nd).":[ "" ],
      "Unadvised 3rd-party JS library":[ "Niezalecana biblioteka JS od firmy trzeciej" ],
      "Your add-on uses a JavaScript library we do not recommend. Read more: https://bit.ly/1TRIyZY":[ "" ],
      "Known JS library detected":[ "Wykryto znaną bibliotekę JS" ],
      "JavaScript libraries are discouraged for simple add-ons, but are generally accepted.":[ "" ],
      "Due to both security and performance concerns, this may not be set using dynamic values which have not been adequately sanitized. This can lead to security issues or fairly serious performance degradation.":[ "" ],
      "{{api}} is not supported":[ "„{{api}}” jest nieobsługiwane" ],
      "This API has not been implemented by Firefox.":[ "To API nie zostało zaimplementowane w Firefoksie." ],
      "\"{{api}}\" has been removed in Manifest Version 3 (`manifest_version` property)":[ "„{{api}}” zostało usunięte w 3. wersji Manifestu (właściwość „manifest_version”)" ],
      "{{api}} is deprecated":[ "„{{api}}” jest przestarzałe" ],
      "This API has been deprecated by Firefox.":[ "To API zostało oznaczone jako przestarzałe w Firefoksie." ],
      "Content script file could not be found.":[ "Nie można odnaleźć pliku skryptu treści." ],
      "\"{{api}}\" is deprecated or unimplemented":[ "„{{api}}” jest przestarzałe lub niezaimplementowane" ],
      "Content script file could not be found":[ "Nie można odnaleźć pliku skryptu treści" ],
      "\"%(api)s\" can cause issues when loaded temporarily":[ "" ],
      "This API can cause issues when loaded temporarily using about:debugging in Firefox unless you specify \"browser_specific_settings.gecko.id\" in the manifest. Please see: https://mzl.la/2hizK4a for more.":[ "" ],
      "{{api}} is not supported in Firefox version {{minVersion}}":[ "„{{api}}” nie jest obsługiwane w wersji Firefoksa {{minVersion}}" ],
      "This API is not implemented by the given minimum Firefox version":[ "To API nie jest zaimplementowane w podanej minimalnej wersji Firefoksa" ],
      "{{api}} is not supported in Firefox for Android version {{minVersion}}":[ "„{{api}}” nie jest obsługiwane w wersji Firefoksa na Androida {{minVersion}}" ],
      "This API is not implemented by the given minimum Firefox for Android version":[ "To API nie jest zaimplementowane w podanej minimalnej wersji Firefoksa na Androida" ],
      "Content script file name should not be empty.":[ "Nazwa pliku skryptu treści nie może być pusta." ],
      "Content script file name should not be empty":[ "Nazwa pliku skryptu treści nie może być pusta" ],
      "\"%(method)s\" called with a non-literal uri":[ "" ],
      "Calling \"%(method)s\" with variable parameters can result in potential security vulnerabilities if the variable contains a remote URI. Consider using 'window.open' with the 'chrome=no' flag.":[ "" ],
      "\"%(method)s\" called with non-local URI":[ "" ],
      "Calling \"%(method)s\" with a non-local URI will result in the dialog being opened with chrome privileges.":[ "" ],
      "JavaScript syntax error":[ "Błąd składni języka JavaScript" ],
      "There is a JavaScript syntax error in your code, which might be related to some experimental JavaScript features that aren't an official part of the language specification and therefore not supported yet. The validation cannot continue on this file.":[ "" ],
      "Evaluation of strings as code can lead to security vulnerabilities and performance issues, even in the most innocuous of circumstances. Please avoid using `eval` and the `Function` constructor when at all possible.":[ "" ],
      "setTimeout, setInterval and execScript functions should be called only with function expressions as their first argument":[ "" ],
      "Unexpected global passed as an argument":[ "Nieoczekiwane „global” przekazane jako parametr" ],
      "Passing a global as an argument is not recommended. Please make this a var instead.":[ "" ],
      "Use of document.write strongly discouraged.":[ "Użycie document.write jest mocno niezalecane." ],
      "document.write will fail in many circumstances when used in extensions, and has potentially severe security repercussions when used improperly. Therefore, it should not be used.":[ "" ],
      "Banned 3rd-party JS library":[ "Zabroniona biblioteka JS od firmy trzeciej" ],
      "Your add-on uses a JavaScript library we consider unsafe. Read more: https://bit.ly/1TRIyZY":[ "" ],
      "Your JSON contains block comments.":[ "Kod JSON zawiera komentarze blokowe." ],
      "Only line comments (comments beginning with \"//\") are allowed in JSON files. Please remove block comments (comments beginning with \"/*\")":[ "" ],
      "Duplicate keys are not allowed in JSON files.":[ "Podwójne klucze nie są dozwolone w plikach JSON." ],
      "Duplicate key found in JSON file.":[ "" ],
      "Your JSON is not valid.":[ "Plik JSON jest nieprawidłowy." ],
      "Your JSON file could not be parsed.":[ "Nie można przetworzyć pliku JSON." ],
      "Reserved filename found.":[ "Odnaleziono zastrzeżoną nazwę pliku." ],
      "Files whose names are reserved have been found in the add-on. Please refrain from using them and rename your files.":[ "" ],
      "The package is invalid. It may contain entries using invalid characters, as an example using '\\' as a path separator is not allowed in Firefox. Try to recreate your add-on package (ZIP) and make sure all entries are using '/' as the path separator.":[ "" ],
      "We were unable to decompress the zip file.":[ "Nie można zdekompresować pliku zip." ],
      "manifest.json was not found":[ "Nie odnaleziono pliku manifest.json" ],
      "No manifest.json was found at the root of the extension. The package file must be a ZIP of the extension's files themselves, not of the containing directory. See: https://mzl.la/2r2McKv for more on packaging.":[ "" ],
      "File is too large to parse.":[ "Plik jest za duży, aby można go było przetworzyć." ],
      "This file is not binary and is too large to parse. Files larger than %(maxFileSizeToParseMB)sMB will not be parsed. Consider moving large lists of data out of JavaScript files and into JSON files, or splitting very large files into smaller ones.":[ "" ],
      "Hidden file flagged":[ "Oznaczono ukryty plik" ],
      "Hidden files complicate the review process and can contain sensitive information about the system that generated the add-on. Please modify the packaging process so that these files aren't included.":[ "" ],
      "Package contains duplicate entries":[ "Pakiet zawiera podwójne wpisy" ],
      "Flagged filename found":[ "Odnaleziono oznaczoną nazwę pliku" ],
      "Files were found that are either unnecessary or have been included unintentionally. They should be removed.":[ "" ],
      "The package contains multiple entries with the same name. This practice has been banned. Try unzipping and re-zipping your add-on package and try again.":[ "" ],
      "Flagged file extensions found":[ "Odnaleziono oznaczone rozszerzenia plików" ],
      "Flagged file type found":[ "Odnaleziono oznaczony typ pliku" ],
      "Files whose names end with flagged extensions have been found in the add-on. The extension of these files are flagged because they usually identify binary components. Please see https://bit.ly/review-policy for more information on the binary content review process.":[ "" ],
      "Package already signed":[ "Pakiet jest już podpisany" ],
      "Add-ons which are already signed will be re-signed when published on AMO. This will replace any existing signatures on the add-on.":[ "" ],
      "Firefox add-ons are not allowed to run coin miners.":[ "Dodatki do Firefoksa nie mogą zawierać kodu wydobywającego waluty." ],
      "We do not allow coinminer scripts to be run inside WebExtensions. See https://github.com/mozilla/addons-linter/issues/1643 for more details.":[ "" ],
      "String name is reserved for a predefined message":[ "Nazwa ciągu jest zastrzeżona dla wcześniej określonego komunikatu" ],
      "String names starting with @@ get translated to built-in constants (https://mzl.la/2BL9ZjE).":[ "" ],
      "String name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2Eztyi5).":[ "" ],
      "Placeholder for message is missing":[ "Brak elementu zastępczego dla komunikatu" ],
      "A placeholder used in the message is not defined.":[ "Element zastępczy w komunikacie nie jest określony." ],
      "Placeholder name contains invalid characters":[ "Nazwa elementu zastępczego zawiera nieprawidłowe znaki" ],
      "Placeholder name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2ExbYez).":[ "" ],
      "Placeholder is missing the content property":[ "W elemencie zastępczym brak właściwości treści" ],
      "A placeholder needs a content property defining the replacement of it (https://mzl.la/2DT1MQd)":[ "" ],
      "Translation string is missing the message property":[ "W ciągu tłumaczenia brak właściwości komunikatu" ],
      "No \"message\" message property is set for a string (https://mzl.la/2DSBTjA).":[ "Dla ciągu nie jest ustawiona właściwość komunikatu „message” (https://mzl.la/2DSBTjA)." ],
      "The field is required.":[ "Pole jest wymagane." ],
      "See https://mzl.la/1ZOhoEN (MDN Docs) for more information.":[ "https://mzl.la/1ZOhoEN (dokumentacja MDN) zawiera więcej informacji." ],
      "The permission type is unsupported.":[ "Typ uprawnienia jest nieobsługiwany." ],
      "See https://mzl.la/1R1n1t0 (MDN Docs) for more information.":[ "https://mzl.la/1R1n1t0 (dokumentacja MDN) zawiera więcej informacji." ],
      "See https://mzl.la/2Qn0fWC (MDN Docs) for more information.":[ "https://mzl.la/2Qn0fWC (dokumentacja MDN) zawiera więcej informacji." ],
      "See https://mzl.la/3Woeqv4 (MDN Docs) for more information.":[ "" ],
      "Unknown permission.":[ "Nieznane uprawnienie." ],
      "%(instancePath)s: the following privileged permissions are only allowed in privileged extensions: %(privilegedPermissions)s.":[ "" ],
      "Invalid host permission.":[ "Nieprawidłowe uprawnienie hosta." ],
      "Invalid install origin.":[ "Nieprawidłowe źródło instalacji." ],
      "Invalid install origin. A valid origin has - only - a scheme, hostname and optional port. See https://mzl.la/3TEbqbE (MDN Docs) for more information.":[ "" ],
      "The field is invalid.":[ "Nieprawidłowe pole." ],
      "\"manifest_version\" in the manifest.json is not a valid value":[ "„manifest_version” w pliku manifest.json jest nieprawidłową wartością" ],
      "See https://mzl.la/20PenXl (MDN Docs) for more information.":[ "https://mzl.la/20PenXl (dokumentacja MDN) zawiera więcej informacji." ],
      "\"%(property)s\" allows remote code execution in manifest.json":[ "" ],
      "A custom \"%(property)s\" needs additional review.":[ "" ],
      "\"%(property)s\" allows 'eval', which has strong security and performance implications.":[ "" ],
      "In most cases the same result can be achieved differently, therefore it is generally prohibited":[ "" ],
      "The \"name\" property must be a string with no leading/trailing whitespaces.":[ "" ],
      "See http://mzl.la/1STmr48 (MDN Docs) for more information.":[ "http://mzl.la/1STmr48 (dokumentacja MDN) zawiera więcej informacji." ],
      "\"update_url\" is not allowed.":[ "„update_url” jest niedozwolone." ],
      "\"applications.gecko.update_url\" or \"browser_specific_settings.gecko.update_url\" are not allowed for Mozilla-hosted add-ons.":[ "" ],
      "The \"update_url\" property is not used by Firefox.":[ "Właściwość „update_url” nie jest używana w Firefoksie." ],
      "The \"update_url\" is not used by Firefox in the root of a manifest; your add-on will be updated via the Add-ons site and not your \"update_url\". See: https://mzl.la/25zqk4O":[ "" ],
      "\"strict_max_version\" not required.":[ "„strict_max_version” nie jest wymagane." ],
      "\"strict_max_version\" shouldn't be used unless the add-on is expected not to work with future versions of Firefox.":[ "" ],
      "Manifest Version 3 is not fully supported on Firefox for Android.":[ "" ],
      "No \"%(property)s\" property found in manifest.json":[ "" ],
      "\"%(property)s\" is required":[ "" ],
      "An icon defined in the manifest could not be found in the package.":[ "Nie można odnaleźć w pakiecie ikony określonej w manifeście." ],
      "Icon could not be found at \"%(path)s\".":[ "Nie można odnaleźć ikony w „%(path)s”." ],
      "A background script defined in the manifest could not be found.":[ "Nie można odnaleźć skryptu tła określonego w manifeście." ],
      "A background page defined in the manifest could not be found.":[ "Nie można odnaleźć strony tła określonej w manifeście." ],
      "Background script could not be found at \"%(path)s\".":[ "Nie można odnaleźć skryptu tła w „%(path)s”." ],
      "Background page could not be found at \"%(path)s\".":[ "Nie można odnaleźć strony tła w „%(path)s”." ],
      "A content script defined in the manifest could not be found.":[ "Nie można odnaleźć skryptu treści określonego w manifeście." ],
      "A content script css file defined in the manifest could not be found.":[ "Nie można odnaleźć pliku CSS skryptu treści określonego w manifeście." ],
      "Content script defined in the manifest could not be found at \"%(path)s\".":[ "Nie można odnaleźć w „%(path)s” skryptu treści określonego w manifeście." ],
      "Content script css file defined in the manifest could not be found at \"%(path)s\".":[ "Nie można odnaleźć w „%(path)s” pliku CSS skryptu treści określonego w manifeście." ],
      "A dictionary file defined in the manifest could not be found.":[ "Nie można odnaleźć pliku słownika określonego w manifeście." ],
      "Dictionary file defined in the manifest could not be found at \"%(path)s\".":[ "Nie można odnaleźć w „%(path)s” pliku słownika określonego w manifeście." ],
      "The manifest contains multiple dictionaries.":[ "Manifest zawiera wiele słowników." ],
      "Multiple dictionaries were defined in the manifest, which is unsupported.":[ "W manifeście określono wiele słowników, co jest nieobsługiwane." ],
      "The manifest contains a dictionaries object, but it is empty.":[ "Manifest zawiera obiekt słowników, ale jest pusty." ],
      "A dictionaries object was defined in the manifest, but it was empty.":[ "W manifeście określono obiekt słowników, ale był pusty." ],
      "The manifest contains a dictionary but no id property.":[ "Manifest zawiera słownik, ale nie ma właściwości identyfikatora." ],
      "A dictionary was found in the manifest, but there was no id set.":[ "W manifeście odnaleziono słownik, ale nie było ustawionego identyfikatora." ],
      "Please refer to https://github.com/mozilla-extensions/xpi-manifest to learn more about privileged extensions and signing.":[ "" ],
      "Forbidden content found in add-on.":[ "W dodatku odnaleziono zabronione treści." ],
      "This add-on contains forbidden content.":[ "Ten dodatek zawiera zabronione treści." ],
      "Icons must be square.":[ "Ikona musi być kwadratowa." ],
      "Icon at \"%(path)s\" must be square.":[ "Ikona w „%(path)s” musi być kwadratowa." ],
      "The size of the icon does not match the manifest.":[ "Wymiary ikony nie zgadzają się z manifestem." ],
      "Expected icon at \"%(path)s\" to be %(expected)d pixels wide but was %(actual)d.":[ "" ],
      "\"%(fieldName)s\" is ignored for non-privileged add-ons.":[ "" ],
      "Corrupt image file":[ "Uszkodzony plik obrazu" ],
      "Expected icon file at \"%(path)s\" is corrupted":[ "Oczekiwany plik ikony w „%(path)s” jest uszkodzony" ],
      "This property has been deprecated.":[ "Ta właściwość została oznaczona jako przestarzała." ],
      "This theme LWT alias has been removed in Firefox 70.":[ "Ten alias LWT motywu został usunięty w Firefoksie 70." ],
      "See https://mzl.la/2T11Lkc (MDN Docs) for more information.":[ "https://mzl.la/2T11Lkc (dokumentacja MDN) zawiera więcej informacji." ],
      "Theme image for \"%(type)s\" could not be found at \"%(path)s\"":[ "Nie można odnaleźć obrazu motywu dla „%(type)s” w „%(path)s”" ],
      "\"%(fieldName)s\" manifest field is only used for privileged and temporarily installed extensions.":[ "" ],
      "Corrupted theme image file":[ "Uszkodzony plik obrazu motywu" ],
      "Theme image file at \"%(path)s\" is corrupted":[ "Plik obrazu motywu w „%(path)s” jest uszkodzony" ],
      "Theme image file has an unsupported file extension":[ "Plik obrazu motywu ma nieobsługiwane rozszerzenie pliku" ],
      "Theme image file at \"%(path)s\" has an unsupported file extension":[ "Plik obrazu motywu w „%(path)s” ma nieobsługiwane rozszerzenie pliku" ],
      "Theme image file has an unsupported mime type":[ "Plik obrazu motywu ma nieobsługiwany typ MIME" ],
      "Theme image file at \"%(path)s\" has the unsupported mime type \"%(mime)s\"":[ "Plik obrazu motywu w „%(path)s” ma nieobsługiwany typ MIME „%(mime)s”" ],
      "Theme image file mime type does not match its file extension":[ "Typ MIME pliku obrazu motywu nie zgadza się z jego rozszerzeniem" ],
      "Theme image file extension at \"%(path)s\" does not match its actual mime type \"%(mime)s\"":[ "Rozszerzenie pliku obrazu motywu w „%(path)s” nie zgadza się z jego rzeczywistym typem MIME „%(mime)s”" ],
      "The \"default_locale\" is missing localizations.":[ "„default_locale” nie ma lokalizacji." ],
      "The \"default_locale\" value is specified in the manifest, but no matching \"messages.json\" in the \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "" ],
      "The \"default_locale\" is missing but \"_locales\" exist.":[ "Brakuje „default_locale”, ale istnieje „_locales”." ],
      "The \"default_locale\" value is not specifed in the manifest, but a \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "" ],
      "Unsupported image extension":[ "Nieobsługiwane rozszerzenie obrazu" ],
      "Icons should be one of JPG/JPEG, WebP, GIF, PNG or SVG.":[ "Ikony muszą być typu JPG/JPEG, WebP, GIF, PNG lub SVG." ],
      "\"applications\" property overridden by \"browser_specific_settings\" property":[ "Właściwość „applications” zastąpiona właściwością „browser_specific_settings”" ],
      "The \"applications\" property is being ignored because it is superseded by the \"browser_specific_settings\" property which is also defined in your manifest. Consider removing applications.":[ "" ],
      "Empty language directory":[ "Pusty katalog języka" ],
      "messages.json file missing in \"%(path)s\"":[ "W „%(path)s” brakuje pliku messages.json" ],
      "Manifest key not supported by the specified minimum Firefox version":[ "Klucz manifestu nie jest obsługiwany przez podaną minimalną wersję Firefoksa" ],
      "\"strict_min_version\" requires Firefox %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "" ],
      "\"%(fieldName)s\" is not supported in manifest versions %(versionRange)s.":[ "" ],
      "Permission not supported by the specified minimum Firefox version":[ "Uprawnienie nie jest obsługiwane przez podaną minimalną wersję Firefoksa" ],
      "\"%(fieldName)s\" is not supported.":[ "" ],
      "Manifest key not supported by the specified minimum Firefox for Android version":[ "Klucz manifestu nie jest obsługiwany przez podaną minimalną wersję Firefoksa na Androida" ],
      "\"strict_min_version\" requires Firefox for Android %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "" ],
      "Permission not supported by the specified minimum Firefox for Android version":[ "Uprawnienie nie jest obsługiwane przez podaną minimalną wersję Firefoksa na Androida" ],
      "Linking to \"addons.mozilla.org\" is not allowed":[ "Odsyłanie do „addons.mozilla.org” jest niedozwolone" ],
      "Links directing to \"addons.mozilla.org\" are not allowed to be used for homepage":[ "Odnośniki prowadzące do „addons.mozilla.org” nie mogą być używane jako strona domowa" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above":[ "" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above. Please update your manifest.json version to specify a minimum Firefox version.":[ "" ],
      "The extension ID is required in Manifest Version 3 and above.":[ "Identyfikator rozszerzenia jest wymagany w 3. wersji Manifestu i nowszych." ],
      "See https://mzl.la/3PLZYdo for more information.":[ "https://mzl.la/3PLZYdo zawiera więcej informacji." ],
      "%(instancePath)s: Privileged extensions should declare privileged permissions.":[ "" ],
      "This extension does not declare any privileged permission. It does not need to be signed with the privileged certificate. Please upload it directly to https://addons.mozilla.org/.":[ "" ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for privileged extensions.":[ "" ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for extensions that include privileged manifest fields.":[ "" ],
      "%(instancePath)s: privileged manifest fields are only allowed in privileged extensions.":[ "" ],
      "This extension does not include the \"mozillaAddons\" permission, which is required for privileged extensions.":[ "" ],
      "Cannot use actions in hidden add-ons.":[ "" ],
      "The hidden and browser_action/page_action (or action in Manifest Version 3 and above) properties are mutually exclusive.":[ "" ],
      "Use \"browser_specific_settings\" instead of \"applications\".":[ "" ],
      "The \"applications\" property in the manifest is deprecated and will no longer be accepted in Manifest Version 3 and above.":[ "" ],
      "\"applications\" is no longer allowed in Manifest Version 3 and above.":[ "" ],
      "The \"applications\" property in the manifest is no longer allowed in Manifest Version 3 and above. Use \"browser_specific_settings\" instead.":[ "" ],
      "The version string should be simplified because it won't be compatible with Manifest Version 3 and above.":[ "" ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros will no longer be allowed. Letters will no longer be allowed either. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "" ],
      "The version string should be simplified.":[ "" ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros are not allowed. Letters are no longer allowed. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "" ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported in manifest versions %(versionRange)s.":[ "" ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported.":[ "" ] } } }