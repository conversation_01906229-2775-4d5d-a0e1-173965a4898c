module.exports = { domain:"messages",
  locale_data:{ messages:{ "":{ domain:"messages",
        plural_forms:"nplurals=2; plural=(n != 1);",
        lang:"fy_NL" },
      "Validation Summary:":[ "Falidaasjegearfetting:" ],
      Code:[ "Koade" ],
      Message:[ "<PERSON><PERSON><PERSON><PERSON>" ],
      Description:[ "Beskriuwing" ],
      File:[ "Bestân" ],
      Line:[ "Rige" ],
      Column:[ "Kolom" ],
      "Invalid manifest version range requested: --min-manifest-version (currently set to %(minManifestVersion)s) should not be greater than --max-manifest-version (currently set to %(maxManifestVersion)s).":[ "Unjildich manifestferzjeberik oanfrege: --min-manifest-version (op dit stuit ynsteld op %(minManifestVersion)s) mei net grutter wêze as --max-manifest-version (op dit stuit ynsteld op %(maxManifestVersion)s)." ],
      "Your FTL is not valid.":[ "Jo FTL is net jildich." ],
      "Your FTL file could not be parsed.":[ "Jo FTL-bestân koe net ferwurke wurde." ],
      "Remote scripts are not allowed as per the Add-on Policies.":[ "Neefens it add-onbelied binne eksterne scripts net tastien." ],
      "Please include all scripts in the add-on. For more information, refer to https://mzl.la/2uEOkYp.":[ "Foegje alle scripts ta oan de add-on. Foar mear ynformaasje, sjoch https://mzl.la/2uEOkYp." ],
      "Inline scripts blocked by default":[ "Ynline scripts wurde standert blokkearre" ],
      "Default CSP rules prevent inline JavaScript from running (https://mzl.la/2pn32nd).":[ "Standert CSP-regels foarkomme dat inline JavaScript útfierd wurdt (https://mzl.la/2pn32nd)." ],
      "Unadvised 3rd-party JS library":[ "Ofrêden JS-biblioteek fan tredden" ],
      "Your add-on uses a JavaScript library we do not recommend. Read more: https://bit.ly/1TRIyZY":[ "Jo add-on brûkt in JavaScript-biblioteek dy’t wy net oanrekommandearje. Mear ynfo: https://bit.ly/1TRIyZY" ],
      "Known JS library detected":[ "Bekende JS-biblioteek detektearre" ],
      "JavaScript libraries are discouraged for simple add-ons, but are generally accepted.":[ "JavaScript-biblioteken wurde ûntmoedige foar ienfâldige add-ons, mar wurde algemien akseptearre." ],
      "Due to both security and performance concerns, this may not be set using dynamic values which have not been adequately sanitized. This can lead to security issues or fairly serious performance degradation.":[ "Fanwegen sawol befeiligings- as prestaasjessoargen, mei dit net ynsteld wurde mei help fan dynamyske wearden dy’t net genôch opskjinne binne. Dit kin liede ta befeiligingsproblemen of frij serieuze prestaasjesdegradaasje." ],
      "{{api}} is not supported":[ "{{api}} is net stipe" ],
      "This API has not been implemented by Firefox.":[ "Dizze API is net troch Firefox ymplemintearre." ],
      "\"{{api}}\" has been removed in Manifest Version 3 (`manifest_version` property)":[ "‘{{api}}’ is fuortsmiten yn Manifest ferzje 3 (eigenskip ‘manifest_version’)" ],
      "{{api}} is deprecated":[ "{{api}} is ferâldere" ],
      "This API has been deprecated by Firefox.":[ "Dizze API is yn Firefox ferâldere." ],
      "Content script file could not be found.":[ "Ynhâldsscriptbestân koe net fûn wurde." ],
      "\"{{api}}\" is deprecated or unimplemented":[ "‘{{api}}’ is ferâldere of net ymplemintearre" ],
      "Content script file could not be found":[ "Ynhâldsscriptbestân koe net fûn wurde" ],
      "\"%(api)s\" can cause issues when loaded temporarily":[ "‘%(api)s’ kin problemen feroarsaakje as it tydlik laden wurdt" ],
      "This API can cause issues when loaded temporarily using about:debugging in Firefox unless you specify \"browser_specific_settings.gecko.id\" in the manifest. Please see: https://mzl.la/2hizK4a for more.":[ "Dizze API kin problemen feroarsaakje by it tydlik laden mei about:debugging yn Firefox, útsein as jo ‘browser_specific_settings.gecko.id’ opjaan yn it manifest. Sjoch: https://mzl.la/2hizK4a foar mear ynformaasje." ],
      "{{api}} is not supported in Firefox version {{minVersion}}":[ "{{api}} wurdt net stipe yn Firefox ferzje {{minVersion}}" ],
      "This API is not implemented by the given minimum Firefox version":[ "Dizze API is net troch de opjûne minimumferzje fan Firefox ymplemintearre" ],
      "{{api}} is not supported in Firefox for Android version {{minVersion}}":[ "{{api}} wurdt net stipe yn Firefox foar Android ferzje {{minVersion}}" ],
      "This API is not implemented by the given minimum Firefox for Android version":[ "Dizze API is net troch de opjûne minimumferzje fan Firefox foar Android ymplemintearre" ],
      "Content script file name should not be empty.":[ "Namme fan ynhâldsscriptbestân mei net leech wêze." ],
      "Content script file name should not be empty":[ "Namme fan ynhâldsscriptbestân mei net leech wêze" ],
      "\"%(method)s\" called with a non-literal uri":[ "‘%(method)s’ oanroppen mei in net-letterlike URI" ],
      "Calling \"%(method)s\" with variable parameters can result in potential security vulnerabilities if the variable contains a remote URI. Consider using 'window.open' with the 'chrome=no' flag.":[ "It oproppen fan ‘%(method)s’ mei fariabele parameters kin resultearje yn potinsjele befeiligingsproblemen as de fariabele in eksterne URI befettet. Tink oan it brûken fan ‘window.open’ mei de flagge ‘chrome=no’." ],
      "\"%(method)s\" called with non-local URI":[ "‘%(method)s’ oanroppen mei in net-lokale URI" ],
      "Calling \"%(method)s\" with a non-local URI will result in the dialog being opened with chrome privileges.":[ "It oproppen fan ‘%(method)s’ mei in net-lokale URI sil resultearje yn it iepenjen fan it dialoochfinster mei chromeprivileezjes." ],
      "JavaScript syntax error":[ "JavaScript-syntaksisflater" ],
      "There is a JavaScript syntax error in your code, which might be related to some experimental JavaScript features that aren't an official part of the language specification and therefore not supported yet. The validation cannot continue on this file.":[ "Der sit in JavaScript-syntaksisflater yn jo koade, dy’t mooglik relatearre is oan guon eksperimintele JavaScript-funksjes dy’t gjin offisjeel ûnderdiel binne fan de taalspesifikaasje en dus noch net stipe. De falidaasje kin net trochgean op dit bestân." ],
      "Evaluation of strings as code can lead to security vulnerabilities and performance issues, even in the most innocuous of circumstances. Please avoid using `eval` and the `Function` constructor when at all possible.":[ "Evaluaasje fan strings as koade kin liede ta befeiligingsproblemen en prestaasjeproblemen, sels yn de meast ûnskealike omstannichheden. Mij wannear mooglik it gebrûk fan ‘eval’ en de ‘Function’-constructor." ],
      "setTimeout, setInterval and execScript functions should be called only with function expressions as their first argument":[ "De funksjes setTimeout, setInterval en execScript meie allinnich oanroppen wurde mei funksje-ekspresjes as harren earste argumint" ],
      "Unexpected global passed as an argument":[ "Unferwachte globale fariabele trochjûn as argumint" ],
      "Passing a global as an argument is not recommended. Please make this a var instead.":[ "It trochjaan fan in globale wearde as argumint wurdt net oanrekommandearre. Meitsje hjir in var fan." ],
      "Use of document.write strongly discouraged.":[ "Gebrûk fan document.write wurdt strang ôfrêd." ],
      "document.write will fail in many circumstances when used in extensions, and has potentially severe security repercussions when used improperly. Therefore, it should not be used.":[ "document.write sil yn in protte gefallen mislearje by gebrûk yn útwreidingen, en hat mooglik earnstige gefolgen foar de feilichheid by net kret gebrûk. Dêrom mei it net brûkt wurde." ],
      "Banned 3rd-party JS library":[ "Blokkearre JS-biblioteek fan tredden" ],
      "Your add-on uses a JavaScript library we consider unsafe. Read more: https://bit.ly/1TRIyZY":[ "Jo add-on brûkt in JavaScript-biblioteek dy’t wy as ûnfeilich beskôgje. Mear ynfo: https://bit.ly/1TRIyZY" ],
      "Your JSON contains block comments.":[ "Jo JSON befettet blokkommentaren." ],
      "Only line comments (comments beginning with \"//\") are allowed in JSON files. Please remove block comments (comments beginning with \"/*\")":[ "Allinnich regelopmerkingen (opmerkingen dy’t begjinne me ‘//’) binne tastien yn JSON-bestannen. Smyt blokopmerkingen (opmerkingen begjinnend mei ‘/*’) fuort" ],
      "Duplicate keys are not allowed in JSON files.":[ "Dûbele kaaien binne net tastien yn JSON-bestannen." ],
      "Duplicate key found in JSON file.":[ "Dûbele kaai fûn yn JSON-bestân." ],
      "Your JSON is not valid.":[ "Jo JSON is net jildich." ],
      "Your JSON file could not be parsed.":[ "Jo JSON-bestân koe net ferwurke wurde." ],
      "Reserved filename found.":[ "Reservearre bestânsnamme fûn." ],
      "Files whose names are reserved have been found in the add-on. Please refrain from using them and rename your files.":[ "Bestannen wêrfan de namme reservearre binne fûn yn de add-on. Brûk dizze net jou jo bestannen in oare namme." ],
      "The package is invalid. It may contain entries using invalid characters, as an example using '\\' as a path separator is not allowed in Firefox. Try to recreate your add-on package (ZIP) and make sure all entries are using '/' as the path separator.":[ "It pakket is ûnjildich. It kin fermeldingen befetsje mei ûnjildige tekens, bygelyks it brûken fan ‘\\’ as paadskieding is net tastien yn Firefox. Probearje jo add-on-pakket (ZIP) opnij oan te meitsjen en soargje derfoar dat alle entries ‘/’ brûke as de paadskieding." ],
      "We were unable to decompress the zip file.":[ "Wy koene it zip-bestân net útpakke." ],
      "manifest.json was not found":[ "manifest.json is net fûn" ],
      "No manifest.json was found at the root of the extension. The package file must be a ZIP of the extension's files themselves, not of the containing directory. See: https://mzl.la/2r2McKv for more on packaging.":[ "Der is gjin manifest.json fûn yn de haadmap fan de útwreiding. It pakketbestân moat in ZIP wêze fan de bestannen fan de útwreiding sels, net fan de befetsjende map. Sjoch: https://mzl.la/2r2McKv foar mear ynformaasje oer ferpakkingen." ],
      "File is too large to parse.":[ "Bestân is te grut om te parsen." ],
      "This file is not binary and is too large to parse. Files larger than %(maxFileSizeToParseMB)sMB will not be parsed. Consider moving large lists of data out of JavaScript files and into JSON files, or splitting very large files into smaller ones.":[ "Dit bestân is net binêr en te grut om te ûntleden. Bestannen grutter as %(maxFileSizeToParseMB)sMB wurde net ûntleed. Probearje grutte listen mei gegevens út JavaScript-bestannen nei JSON-bestannen te ferpleatsen, of hiel grutte bestannen op te splitsen yn lytsere." ],
      "Hidden file flagged":[ "Ferburgen bestân markearre" ],
      "Hidden files complicate the review process and can contain sensitive information about the system that generated the add-on. Please modify the packaging process so that these files aren't included.":[ "Ferburgen bestannen meitsje it beoardielingsproses swierder en kinne gefoelige gegevens befetsje oer it systeem dat de add-on generearre hat. Wizigje it ferpakkingsproses, sadat dizze bestannen net opnommen wurde." ],
      "Package contains duplicate entries":[ "Pakket befettet dûbele fermeldingen" ],
      "Flagged filename found":[ "Markearre bestânsnamme fûn" ],
      "Files were found that are either unnecessary or have been included unintentionally. They should be removed.":[ "Der binne bestannen fûn dy’t net nedich binne of sûnder bedoeling tafoege binne. Se moatte fuortsmiten wurde." ],
      "The package contains multiple entries with the same name. This practice has been banned. Try unzipping and re-zipping your add-on package and try again.":[ "It pakket befettet meardere entries mei deselde namme. Dit is ferbean. Probearje jo add-onpakket út te pakken en opnij te zippen en probearje it opnij." ],
      "Flagged file extensions found":[ "Markearre bestânsútwreidingen fûn" ],
      "Flagged file type found":[ "Markearre bestânstype fûn" ],
      "Files whose names end with flagged extensions have been found in the add-on. The extension of these files are flagged because they usually identify binary components. Please see https://bit.ly/review-policy for more information on the binary content review process.":[ "Bestannen wêrfan de nammen einigje mei markearre útwreidingen binne fûn yn de add-on. De útwreiding fan dizze bestannen wurde markearre, omdat se gewoanlik binêre komponinten identifisearje. Sjoch https://bit.ly/review-policy foar mear ynformaasje oer it proses foar beoardieling fan binêre ynhâld." ],
      "Package already signed":[ "Pakket al ûndertekene" ],
      "Add-ons which are already signed will be re-signed when published on AMO. This will replace any existing signatures on the add-on.":[ "Add-ons dy’t al ûndertekene binne, wurde opnij ûndertekene as se publisearre wurde op AMO. Dit sil alle besteande hantekeningen op de add-on ferfange." ],
      "Firefox add-ons are not allowed to run coin miners.":[ "Firefox-add-ons meie gjin coinminers útfiere." ],
      "We do not allow coinminer scripts to be run inside WebExtensions. See https://github.com/mozilla/addons-linter/issues/1643 for more details.":[ "Wy stean gjin coinminer-skripts ta om te rinnen yn WebExtensions. Sjoch https://github.com/mozilla/addons-linter/issues/1643 foar mear details." ],
      "String name is reserved for a predefined message":[ "Stringnamme is reservearre foar in yn it foar definiearre berjocht" ],
      "String names starting with @@ get translated to built-in constants (https://mzl.la/2BL9ZjE).":[ "Stringnammen dy’t begjinne mei @@ wurde oerset nei ynboude konstanten (https://mzl.la/2BL9ZjE)." ],
      "String name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2Eztyi5).":[ "Stringnamme mei allinnich alfanumerike tekens, _ en @ befetsje (https://mzl.la/2Eztyi5)." ],
      "Placeholder for message is missing":[ "Placeholder foar berjocht mist" ],
      "A placeholder used in the message is not defined.":[ "In placeholder brûkt yn it berjocht is net definiearre." ],
      "Placeholder name contains invalid characters":[ "Placeholdernamme befettet net jildige karakters" ],
      "Placeholder name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2ExbYez).":[ "Plakhâldernamme mei allinnich alfanumerike tekens, _ en @ befetsje (https://mzl.la/2ExbYez)." ],
      "Placeholder is missing the content property":[ "Placeholder mist de eigenskip ynhâld" ],
      "A placeholder needs a content property defining the replacement of it (https://mzl.la/2DT1MQd)":[ "In plakhâlder hat in ynhâldeigenskip nedich dyt de ferfanging dêrfan definiearret (https://mzl.la/2DT1MQd)" ],
      "Translation string is missing the message property":[ "Oersetrigel mist de eigenskip berjocht" ],
      "No \"message\" message property is set for a string (https://mzl.la/2DSBTjA).":[ "Der is gjin ‘berjocht’ foar de eigenskip berjocht ynsteld foar in rigel (https://mzl.la/2DSBTjA)." ],
      "The field is required.":[ "It fjild is fereaske." ],
      "See https://mzl.la/1ZOhoEN (MDN Docs) for more information.":[ "Sjoch https://mzl.la/1ZOhoEN (MDN Docs) foar mear ynformaasje." ],
      "The permission type is unsupported.":[ "It tastimmingstype wurdt net stipe." ],
      "See https://mzl.la/1R1n1t0 (MDN Docs) for more information.":[ "Sjoch https://mzl.la/1R1n1t0 (MDN Docs) foar mear ynformaasje." ],
      "See https://mzl.la/2Qn0fWC (MDN Docs) for more information.":[ "Sjoch https://mzl.la/2Qn0fWC (MDN Docs) foar mear ynformaasje." ],
      "See https://mzl.la/3Woeqv4 (MDN Docs) for more information.":[ "Sjoch https://mzl.la/3Woeqv4 (MDN Docs) foar mear ynformaasje." ],
      "Unknown permission.":[ "Unbekende tastimming." ],
      "%(instancePath)s: the following privileged permissions are only allowed in privileged extensions: %(privilegedPermissions)s.":[ "%(instancePath)s: de folgjende privilezjiearre tagongsrjochten binne allinnich tastien yn privilezjiearre útwreidingen: %(privilegedPermissions)s." ],
      "Invalid host permission.":[ "Unbekende hosttastimming." ],
      "Invalid install origin.":[ "Unbekende ynstallaasjeoarsprong." ],
      "Invalid install origin. A valid origin has - only - a scheme, hostname and optional port. See https://mzl.la/3TEbqbE (MDN Docs) for more information.":[ "Unjildige ynstallaasjeboaronen. In jildige oarsprong hat – allinnich – in skema, hostnamme en opsjonele poarte. Sjoch https://mzl.la/3TEbqbE (MDN Docs) foar mear ynformaasje." ],
      "The field is invalid.":[ "It fjild is ûnjildich." ],
      "\"manifest_version\" in the manifest.json is not a valid value":[ "‘manifest_version’ yn it bestân manifest.json is gjin jildige wearde" ],
      "See https://mzl.la/20PenXl (MDN Docs) for more information.":[ "Sjoch https://mzl.la/20PenXl (MDN Docs) foar mear ynformaasje." ],
      "\"%(property)s\" allows remote code execution in manifest.json":[ "‘%(property)s’ makket útfiering fan eksterne koade mooglik yn manifest.json" ],
      "A custom \"%(property)s\" needs additional review.":[ "In oanpaste ‘%(property)s’ hat ekstra beoardieling nedich." ],
      "\"%(property)s\" allows 'eval', which has strong security and performance implications.":[ "‘%(property)s’ stiet ‘eval’ ta, wat grutte ymplikaasjes hat foar feilichheid en prestaasjes." ],
      "In most cases the same result can be achieved differently, therefore it is generally prohibited":[ "Yn de measte gefallen kin itselde resultaat oare manier berikt wurde, dêrom is yn it algemien ferbean" ],
      "The \"name\" property must be a string with no leading/trailing whitespaces.":[ "De eigenskip ‘name’ moat in tekenrige wêze sûnder spaasjes oan it begjin of ein." ],
      "See http://mzl.la/1STmr48 (MDN Docs) for more information.":[ "Sjoch http://mzl.la/1STmr48 (MDN Docs) foar mear ynformaasje." ],
      "\"update_url\" is not allowed.":[ "‘update_url’ is net tastien." ],
      "\"applications.gecko.update_url\" or \"browser_specific_settings.gecko.update_url\" are not allowed for Mozilla-hosted add-ons.":[ "‘applications.gecko.update_url’ of ‘browser_specific_settings.gecko.update_url’ binne net tastien foar troch Mozilla hoste add-ons." ],
      "The \"update_url\" property is not used by Firefox.":[ "De eigenskip ‘update_url’ wurdt net troch Firefox brûkt." ],
      "The \"update_url\" is not used by Firefox in the root of a manifest; your add-on will be updated via the Add-ons site and not your \"update_url\". See: https://mzl.la/25zqk4O":[ "De ‘update_url’ wurdt troch Firefox net brûkt yn de haadmap fan in manifest; jo add-on wurdt bywurke fia de Add-ons-website en net fia jo ‘update_url’. Sjoch: https://mzl.la/25zqk4O" ],
      "\"strict_max_version\" not required.":[ "‘strict_max_version’ net fereaske." ],
      "\"strict_max_version\" shouldn't be used unless the add-on is expected not to work with future versions of Firefox.":[ "‘strict_max_version’ mei net brûkt wurde, útsein ferwachte wurdt dat de add-on net wurkje sil mei takomstige ferzjes fan Firefox." ],
      "Manifest Version 3 is not fully supported on Firefox for Android.":[ "Manifest ferzje 3 wurdt net folslein stipe op Firefox foar Android." ],
      "No \"%(property)s\" property found in manifest.json":[ "Gjin eigenskip ‘%(property)s’ fûn yn manifest.json" ],
      "\"%(property)s\" is required":[ "‘%(property)s’ is ferplichte" ],
      "An icon defined in the manifest could not be found in the package.":[ "In yn it manifest definiearre piktogram koe net yn it pakket fûn wurde." ],
      "Icon could not be found at \"%(path)s\".":[ "Piktogram koe net fûn wurde fûn yn ‘%(path)s’." ],
      "A background script defined in the manifest could not be found.":[ "In yn it manifest definiearre eftergrûnscript koe net fûn wurde." ],
      "A background page defined in the manifest could not be found.":[ "In yn it manifest definiearre eftergrûnside koe net fûn wurde." ],
      "Background script could not be found at \"%(path)s\".":[ "Eftergrûnscript koe net fûn wurde yn ‘%(path)s’." ],
      "Background page could not be found at \"%(path)s\".":[ "Eftergrûnside koe net fûn wurde yn ‘%(path)s’." ],
      "A content script defined in the manifest could not be found.":[ "In yn it manifest definiearre ynhâldsscript koe net fûn wurde yn." ],
      "A content script css file defined in the manifest could not be found.":[ "In yn it manifest definiearre CSS-bestân fan ynhâldsscript koe net fûn wurde." ],
      "Content script defined in the manifest could not be found at \"%(path)s\".":[ "Yn it manifest definiearre ynhâldsscript koe net fûn wurde yn ‘%(path)s’." ],
      "Content script css file defined in the manifest could not be found at \"%(path)s\".":[ "Yn it manifest definiearre CSS-bestân fan ynhâldsscript koe net fûn wurde yn ‘%(path)s’." ],
      "A dictionary file defined in the manifest could not be found.":[ "In yn it manifest definiearre biblioteekbestân koe net fûn wurde." ],
      "Dictionary file defined in the manifest could not be found at \"%(path)s\".":[ "In yn it manifest definiearre biblioteekbestân koe net fûn wurde yn ‘%(path)s’." ],
      "The manifest contains multiple dictionaries.":[ "It manifest befettet mear wurdboeken." ],
      "Multiple dictionaries were defined in the manifest, which is unsupported.":[ "Der binne mear wurdboeken definiearre yn it manifest, wat net stipe wurdt." ],
      "The manifest contains a dictionaries object, but it is empty.":[ "It manifest befettet in wurdboek-objekt, mar it is leech." ],
      "A dictionaries object was defined in the manifest, but it was empty.":[ "Der is in wurdboek-objekt definiearre yn it manifest, mar it is leech." ],
      "The manifest contains a dictionary but no id property.":[ "It manifest befettet in wurdboek, mar gjin id-eigenskip." ],
      "A dictionary was found in the manifest, but there was no id set.":[ "Der is in wurdboek fûn yn it manifest, mar der is gjin id ynsteld." ],
      "Please refer to https://github.com/mozilla-extensions/xpi-manifest to learn more about privileged extensions and signing.":[ "Lês https://github.com/mozilla-extensions/xpi-manifest foar mear ynformaasje oer privilezjiearre útwreidingen en ûndertekening." ],
      "Forbidden content found in add-on.":[ "Ferbeane ynhâld fûn yn add-on." ],
      "This add-on contains forbidden content.":[ "Dizze add-on befettet ferbeane ynhâld." ],
      "Icons must be square.":[ "Piktogrammen moatte fjouwerkant wêze." ],
      "Icon at \"%(path)s\" must be square.":[ "Piktogram yn ‘%(path)s’ moat fjouwerkant wêze." ],
      "The size of the icon does not match the manifest.":[ "De grutte fan it piktogram komt net oerien mei it manifest." ],
      "Expected icon at \"%(path)s\" to be %(expected)d pixels wide but was %(actual)d.":[ "Ferwachte piktogram by ‘%(path)s’ is %(expected)d pixels breed, mar wie %(actual)d." ],
      "\"%(fieldName)s\" is ignored for non-privileged add-ons.":[ "‘%(fieldName)s’ wurdt negearre foar net-privilezjiearre add-ons." ],
      "Corrupt image file":[ "Skansearre ôfbyldingsbestân" ],
      "Expected icon file at \"%(path)s\" is corrupted":[ "Ferwacht piktogrambestân yn ‘%(path)s’ is skansearre" ],
      "This property has been deprecated.":[ "Dizze eigenskip is ferâldere." ],
      "This theme LWT alias has been removed in Firefox 70.":[ "It LWT-alias fan dit tema is fuortsmiten yn Firefox 70." ],
      "See https://mzl.la/2T11Lkc (MDN Docs) for more information.":[ "Sjoch https://mzl.la/2T11Lkc (MDN Docs) foar mear ynformaasje." ],
      "Theme image for \"%(type)s\" could not be found at \"%(path)s\"":[ "Temaôfbylding foar ‘%(type)s’ koe net fûn wurde yn ‘%(path)s’" ],
      "\"%(fieldName)s\" manifest field is only used for privileged and temporarily installed extensions.":[ "It manifestfjild ‘%(fieldName)s’ wurdt allinnich brûkt foar privilezjiearre en tydlik ynstallearre útwreidingen." ],
      "Corrupted theme image file":[ "Skansearre temaôfbyldingsbestân" ],
      "Theme image file at \"%(path)s\" is corrupted":[ "Temaôfbyldingsbestân yn ‘%(path)s’ is skansearre" ],
      "Theme image file has an unsupported file extension":[ "Temaôfbyldingsbestân hat in net-stipe bestânsekstinsje" ],
      "Theme image file at \"%(path)s\" has an unsupported file extension":[ "Temaôfbyldingsbestân op ‘%(path)s’ hat in net-stipe bestânsekstinsje" ],
      "Theme image file has an unsupported mime type":[ "Temaôfbyldingsbestân hat in net-stipe mime-type" ],
      "Theme image file at \"%(path)s\" has the unsupported mime type \"%(mime)s\"":[ "Temaôfbyldingsbestân op ‘%(path)s’ hat it net-stipe mime-type ‘%(mime)s’" ],
      "Theme image file mime type does not match its file extension":[ "Mime-type fan it temaôfbyldingsbestân komt net oerien mei de bestandsekstinsje" ],
      "Theme image file extension at \"%(path)s\" does not match its actual mime type \"%(mime)s\"":[ "Temaôfbyldingsbestân op ‘%(path)s’ komt net oerien mei it wurklike mime-type ‘%(mime)s’" ],
      "The \"default_locale\" is missing localizations.":[ "De ‘default_locale’ mist lokalisaasjes." ],
      "The \"default_locale\" value is specified in the manifest, but no matching \"messages.json\" in the \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "De wearde ‘default_locale’ is opjûn yn it manifest, mar der bestiet gjin oerienkommende ‘messages.json’ yn de map ‘_locales’. Sjoch: https://mzl.la/2hjcaEE" ],
      "The \"default_locale\" is missing but \"_locales\" exist.":[ "De ‘default_locale’ ûntbrekt, mar ‘_locales’ bestiet." ],
      "The \"default_locale\" value is not specifed in the manifest, but a \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "De wearde ‘default_locale’ wurdt net opjûn yn it manifest, mar der bestiet in map ‘_locales’. Sjoch: https://mzl.la/2hjcaEE" ],
      "Unsupported image extension":[ "Net-stipe ôfbyldingsútwreding" ],
      "Icons should be one of JPG/JPEG, WebP, GIF, PNG or SVG.":[ "Piktogrammen moatte JPG/JPEG, WebP, GIF, PNG of SVG wêze." ],
      "\"applications\" property overridden by \"browser_specific_settings\" property":[ "Eigenskip ‘applications’ wurdt oerskreaun troch de eigenskip ‘browser_specific_settings’" ],
      "The \"applications\" property is being ignored because it is superseded by the \"browser_specific_settings\" property which is also defined in your manifest. Consider removing applications.":[ "De eigenskip ‘applications’ wurdt negearre, omdat dizze ferfongen wurdt troch de eigenskip ‘browser_specific_settings’ dy’t ek yn jo manifest definiearre is. Tink oan it fuortsmiten fan applikaasjes." ],
      "Empty language directory":[ "Lege taalmap" ],
      "messages.json file missing in \"%(path)s\"":[ "Bestân messages.json ûntbrekt yn ‘%(path)s’" ],
      "Manifest key not supported by the specified minimum Firefox version":[ "Manifestkaai net stipe troch de opjûne minimale Firefox-ferzje" ],
      "\"strict_min_version\" requires Firefox %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "‘strict_min_version’ fereasket Firefox %(minVersion)s, dy’t útbrocht waard eardat ferzje %(versionAdded)s stipe foar ‘%(key)s’ yntrodusearre." ],
      "\"%(fieldName)s\" is not supported in manifest versions %(versionRange)s.":[ "‘%(fieldName)s’ wurdt net stipe yn manifestferzjes %(versionRange)s." ],
      "Permission not supported by the specified minimum Firefox version":[ "Tastimming net stipe troch de opjûne minimale Firefox-ferzje" ],
      "\"%(fieldName)s\" is not supported.":[ "‘%(fieldName)s’ wurdt net stipe." ],
      "Manifest key not supported by the specified minimum Firefox for Android version":[ "Manifestkaai net stipe troch de opjûne minimale Firefox foar Android-ferzje" ],
      "\"strict_min_version\" requires Firefox for Android %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "‘strict_min_version’ fereasket Firefox foar Android %(minVersion)s, dy’t útbrocht waard eardat ferzje %(versionAdded)s stipe foar ‘%(key)s’ yntrodusearre." ],
      "Permission not supported by the specified minimum Firefox for Android version":[ "Tastemming net stipe troch de opjûne minimale Firefox foar Android-ferzje" ],
      "Linking to \"addons.mozilla.org\" is not allowed":[ "Keppeljen nei ‘addons.mozilla.org’ is net tastien" ],
      "Links directing to \"addons.mozilla.org\" are not allowed to be used for homepage":[ "Keppelingen dy’t ferwize nei ‘addons.mozilla.org’ meie net foar de startside brûkt wurde" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above":[ "De machtiging ‘%(permission)s’ fereasket dat ‘strict_min_version’ ynsteld is op ‘%(minFirefoxVersion)s’ of heger" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above. Please update your manifest.json version to specify a minimum Firefox version.":[ "De machtiging ‘%(permission)s’ fereasket dat ‘strict_min_version’ ynsteld is op ‘%(minFirefoxVersion)s’ of heger. Wurkje jo manifest.json-ferzje by om in minimale Firefox-ferzje op te jaan." ],
      "The extension ID is required in Manifest Version 3 and above.":[ "De útwreiding-ID is fereaske yn manifest ferzje 3 en heger." ],
      "See https://mzl.la/3PLZYdo for more information.":[ "Sjoch https://mzl.la/3PLZYdo foar mear ynformaasje." ],
      "%(instancePath)s: Privileged extensions should declare privileged permissions.":[ "%(instancePath)s: privilezjiearre útwreidingen moatte privilezjiearre machtigingen deklarearje." ],
      "This extension does not declare any privileged permission. It does not need to be signed with the privileged certificate. Please upload it directly to https://addons.mozilla.org/.":[ "Dizze útwreiding deklarearret gjin privilezjiearre machtiging. It hoecht net ûndertekene te wurden mei it privilezjiearre sertifikaat. Laad it streekrjocht op nei https://addons.mozilla.org/." ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for privileged extensions.":[ "%(instancePath)s: de machtiging ‘mozillaAddons’ is fereaske foar privilezjiearre útwreidingen." ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for extensions that include privileged manifest fields.":[ "%(instancePath)s: de machtiging ‘mozillaAddons’ is fereaske foar útwreidingen dy’t privilezjiearre manifestfjilden befetsje." ],
      "%(instancePath)s: privileged manifest fields are only allowed in privileged extensions.":[ "%(instancePath)s: privilezjiearre manifestfjilden binne allinnich tastien yn privilezjiearre útwreidingen." ],
      "This extension does not include the \"mozillaAddons\" permission, which is required for privileged extensions.":[ "Dizze útwreiding befettet net de machtiging ‘mozillaAddons’, dy’t fereaske is foar privilezjiearre útwreidingen." ],
      "Cannot use actions in hidden add-ons.":[ "Aksjes yn ferstoppe add-ons brûke is net mooglik." ],
      "The hidden and browser_action/page_action (or action in Manifest Version 3 and above) properties are mutually exclusive.":[ "De eigenskippen hidden en browser_action/page_action (of action yn Manifest Version 3 en heger) slute inoar fan beide kanten út." ],
      "Use \"browser_specific_settings\" instead of \"applications\".":[ "Brûk ‘browser_specific_settings’ yn stee fan ‘applications’." ],
      "The \"applications\" property in the manifest is deprecated and will no longer be accepted in Manifest Version 3 and above.":[ "De eigenskip ‘applications’ yn it manifest is ferâldere en wurdt net langer akseptearre yn manifestferzje 3 en heger." ],
      "\"applications\" is no longer allowed in Manifest Version 3 and above.":[ "‘applications’ is net langer tastien yn manifestferzje 3 en heger." ],
      "The \"applications\" property in the manifest is no longer allowed in Manifest Version 3 and above. Use \"browser_specific_settings\" instead.":[ "De eigenskip ‘applications’ yn it manifest is net langer tastien yn manifestferzje 3 en heger. Brûk yn stee dêrfan ‘browser_specific_settings’." ],
      "The version string should be simplified because it won't be compatible with Manifest Version 3 and above.":[ "De ferzjestring moat ferienfâldige wurde, omdat dizze net kompatibel is mei manifestferzje 3 en heger." ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros will no longer be allowed. Letters will no longer be allowed either. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "De ferzje moat in string wêze mei 1 oant 4 getallen, skieden troch punten. Elk getal moat út maksimaal 9 sifers bestean en foarrinnullen binne net langer tastien. Letters binne ek net mear tastien. Sjoch https://mzl.la/3h3mCRu (MDN Docs) foar mear ynformaasje." ],
      "The version string should be simplified.":[ "De ferzjestring moat ferienfâldige wurde." ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros are not allowed. Letters are no longer allowed. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "De ferzje moat in string wêze mei 1 oant 4 getallen, skieden troch punten. Elk getal moat út maksimaal 9 sifers bestean en foarrinnullen binne net tastien. Letters binne ek net mear tastien. Sjoch https://mzl.la/3h3mCRu (MDN Docs) foar mear ynformaasje." ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported in manifest versions %(versionRange)s.":[ "/%(fieldName)s: ‘%(permissionName)s’ wurdt net stipe yn manifestferzjes %(versionRange)s." ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported.":[ "/%(fieldName)s: ‘%(permissionName)s’ wurdt net stipe." ] } } }