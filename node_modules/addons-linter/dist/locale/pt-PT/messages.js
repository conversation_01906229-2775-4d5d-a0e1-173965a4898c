module.exports = { domain:"messages",
  locale_data:{ messages:{ "":{ domain:"messages",
        plural_forms:"nplurals=2; plural=(n != 1);",
        lang:"pt_PT" },
      "Validation Summary:":[ "Sumário de validação:" ],
      Code:[ "Código" ],
      Message:[ "Mensagem" ],
      Description:[ "Descrição" ],
      File:[ "Ficheiro" ],
      Line:[ "Linha" ],
      Column:[ "Coluna" ],
      "Invalid manifest version range requested: --min-manifest-version (currently set to %(minManifestVersion)s) should not be greater than --max-manifest-version (currently set to %(maxManifestVersion)s).":[ "Pedido de intervalo da versão de manifesto inválido: --min-manifest-version (atualmente definido como %(minManifestVersion)s) não deveria ser maior do que --max-manifest-version (atualmente definido como %(maxManifestVersion)s)." ],
      "Your FTL is not valid.":[ "O seu FTL não é válido." ],
      "Your FTL file could not be parsed.":[ "O seu ficheiro FTL não pôde ser analisado." ],
      "Remote scripts are not allowed as per the Add-on Policies.":[ "Scripts remotos não são permitidos conforme as políticas de extras." ],
      "Please include all scripts in the add-on. For more information, refer to https://mzl.la/2uEOkYp.":[ "Por favor, inclua todos os scripts no extra. Para mais informação, consulte https://mzl.la/2uEOkYp." ],
      "Inline scripts blocked by default":[ "Scripts em linha bloqueados por predefinição" ],
      "Default CSP rules prevent inline JavaScript from running (https://mzl.la/2pn32nd).":[ "As regras CSP predefinidas impedem a execução do JavaScript em linha (https://mzl.la/2pn32nd)." ],
      "Unadvised 3rd-party JS library":[ "Biblioteca JS de terceiros desaconselhada" ],
      "Your add-on uses a JavaScript library we do not recommend. Read more: https://bit.ly/1TRIyZY":[ "O seu extra utiliza uma biblioteca de JavaScript que nós não recomendamos. Saber mais: https://bit.ly/1TRIyZY" ],
      "Known JS library detected":[ "Biblioteca JS conhecida detetada" ],
      "JavaScript libraries are discouraged for simple add-ons, but are generally accepted.":[ "As bibliotecas de JavaScript são desencorajadas para extras simples, mas geralmente são aceites." ],
      "Due to both security and performance concerns, this may not be set using dynamic values which have not been adequately sanitized. This can lead to security issues or fairly serious performance degradation.":[ "Devido a questões de segurança e de desempenho, isto não pode ser definido utilizando valores dinâmicos que não foram adequadamente sanitizados. Isto pode conduzir a problemas de segurança ou de uma degradação bastante séria do desempenho." ],
      "{{api}} is not supported":[ "{{api}} não é suportado" ],
      "This API has not been implemented by Firefox.":[ "Esta API ainda não foi implementada pelo Firefox." ],
      "\"{{api}}\" has been removed in Manifest Version 3 (`manifest_version` property)":[ "\"{{api}}\" foi removido da versão 3 do manifesto (propriedade `manifest_version`)" ],
      "{{api}} is deprecated":[ "{{api}} está obsoleta" ],
      "This API has been deprecated by Firefox.":[ "Esta API foi descontinuada pelo Firefox." ],
      "Content script file could not be found.":[ "O ficheiro de script de conteúdo não pôde ser encontrado." ],
      "\"{{api}}\" is deprecated or unimplemented":[ "\"{{api}}\" está obsoleto ou não está implementado" ],
      "Content script file could not be found":[ "O ficheiro de script de conteúdo não pôde ser encontrado" ],
      "\"%(api)s\" can cause issues when loaded temporarily":[ "\"%(api)s\" pode causar problemas quando carregado temporariamente" ],
      "This API can cause issues when loaded temporarily using about:debugging in Firefox unless you specify \"browser_specific_settings.gecko.id\" in the manifest. Please see: https://mzl.la/2hizK4a for more.":[ "Esta API pode causar problemas quando carregada temporariamente utilizando about:debugging no Firefox, a menos que especifique \"browser_specific_settings.gecko.id\" no manifesto. Por favor, consulte: https://mzl.la/2hizK4a para mais." ],
      "{{api}} is not supported in Firefox version {{minVersion}}":[ "{{api}} não é suportado na versão {{minVersion}} do Firefox" ],
      "This API is not implemented by the given minimum Firefox version":[ "Esta API não está implementada pela versão mínima indicada do Firefox" ],
      "{{api}} is not supported in Firefox for Android version {{minVersion}}":[ "{{api}} não é suportado na versão {{minVersion}} do Firefox para Android" ],
      "This API is not implemented by the given minimum Firefox for Android version":[ "Esta API não está implementada pela versão mínima indicada do Firefox para Android" ],
      "Content script file name should not be empty.":[ "O nome do ficheiro de script de conteúdo não deve estar vazio." ],
      "Content script file name should not be empty":[ "O nome do ficheiro de script de conteúdo não deve estar vazio" ],
      "\"%(method)s\" called with a non-literal uri":[ "" ],
      "Calling \"%(method)s\" with variable parameters can result in potential security vulnerabilities if the variable contains a remote URI. Consider using 'window.open' with the 'chrome=no' flag.":[ "" ],
      "\"%(method)s\" called with non-local URI":[ "" ],
      "Calling \"%(method)s\" with a non-local URI will result in the dialog being opened with chrome privileges.":[ "" ],
      "JavaScript syntax error":[ "Erro de sintaxe JavaScript" ],
      "There is a JavaScript syntax error in your code, which might be related to some experimental JavaScript features that aren't an official part of the language specification and therefore not supported yet. The validation cannot continue on this file.":[ "" ],
      "Evaluation of strings as code can lead to security vulnerabilities and performance issues, even in the most innocuous of circumstances. Please avoid using `eval` and the `Function` constructor when at all possible.":[ "" ],
      "setTimeout, setInterval and execScript functions should be called only with function expressions as their first argument":[ "" ],
      "Unexpected global passed as an argument":[ "Global inesperado passado como um argumento" ],
      "Passing a global as an argument is not recommended. Please make this a var instead.":[ "" ],
      "Use of document.write strongly discouraged.":[ "Utilização de document.write fortemente desencorajada." ],
      "document.write will fail in many circumstances when used in extensions, and has potentially severe security repercussions when used improperly. Therefore, it should not be used.":[ "" ],
      "Banned 3rd-party JS library":[ "Biblioteca JS de terceiros banida" ],
      "Your add-on uses a JavaScript library we consider unsafe. Read more: https://bit.ly/1TRIyZY":[ "" ],
      "Your JSON contains block comments.":[ "O seu JSON contém comentários em bloco." ],
      "Only line comments (comments beginning with \"//\") are allowed in JSON files. Please remove block comments (comments beginning with \"/*\")":[ "" ],
      "Duplicate keys are not allowed in JSON files.":[ "Chaves duplicadas não são permitidas em ficheiros JSON." ],
      "Duplicate key found in JSON file.":[ "" ],
      "Your JSON is not valid.":[ "O seu JSON não é válido." ],
      "Your JSON file could not be parsed.":[ "O seu ficheiro JSON não pôde ser analisado." ],
      "Reserved filename found.":[ "Foi encontrado um nome de ficheiro reservado." ],
      "Files whose names are reserved have been found in the add-on. Please refrain from using them and rename your files.":[ "" ],
      "The package is invalid. It may contain entries using invalid characters, as an example using '\\' as a path separator is not allowed in Firefox. Try to recreate your add-on package (ZIP) and make sure all entries are using '/' as the path separator.":[ "" ],
      "We were unable to decompress the zip file.":[ "Não conseguimos descomprimir o ficheiro zip." ],
      "manifest.json was not found":[ "manifest.json não foi encontrado" ],
      "No manifest.json was found at the root of the extension. The package file must be a ZIP of the extension's files themselves, not of the containing directory. See: https://mzl.la/2r2McKv for more on packaging.":[ "" ],
      "File is too large to parse.":[ "Ficheiro é muito grande para analisar." ],
      "This file is not binary and is too large to parse. Files larger than %(maxFileSizeToParseMB)sMB will not be parsed. Consider moving large lists of data out of JavaScript files and into JSON files, or splitting very large files into smaller ones.":[ "" ],
      "Hidden file flagged":[ "Ficheiro oculto marcado" ],
      "Hidden files complicate the review process and can contain sensitive information about the system that generated the add-on. Please modify the packaging process so that these files aren't included.":[ "" ],
      "Package contains duplicate entries":[ "Pacote contém entradas duplicadas" ],
      "Flagged filename found":[ "Nome de ficheiro marcado encontrado" ],
      "Files were found that are either unnecessary or have been included unintentionally. They should be removed.":[ "" ],
      "The package contains multiple entries with the same name. This practice has been banned. Try unzipping and re-zipping your add-on package and try again.":[ "" ],
      "Flagged file extensions found":[ "Extensões de ficheiro marcadas encontradas" ],
      "Flagged file type found":[ "Tipo de ficheiro marcado encontrado" ],
      "Files whose names end with flagged extensions have been found in the add-on. The extension of these files are flagged because they usually identify binary components. Please see https://bit.ly/review-policy for more information on the binary content review process.":[ "" ],
      "Package already signed":[ "Pacote já assinado" ],
      "Add-ons which are already signed will be re-signed when published on AMO. This will replace any existing signatures on the add-on.":[ "" ],
      "Firefox add-ons are not allowed to run coin miners.":[ "Extras do Firefox não têm permissão para executar \"coin miners\"." ],
      "We do not allow coinminer scripts to be run inside WebExtensions. See https://github.com/mozilla/addons-linter/issues/1643 for more details.":[ "" ],
      "String name is reserved for a predefined message":[ "Nome de string está reservado para uma mensagem predefinida" ],
      "String names starting with @@ get translated to built-in constants (https://mzl.la/2BL9ZjE).":[ "" ],
      "String name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2Eztyi5).":[ "" ],
      "Placeholder for message is missing":[ "Marcador de posição para a mensagem está em falta" ],
      "A placeholder used in the message is not defined.":[ "Um marcador de posição utilizado na mensagem não está definido." ],
      "Placeholder name contains invalid characters":[ "Nome de marcador de posição contém caracteres inválidos" ],
      "Placeholder name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2ExbYez).":[ "" ],
      "Placeholder is missing the content property":[ "Marcador de posição tem em falta a propriedade do conteúdo" ],
      "A placeholder needs a content property defining the replacement of it (https://mzl.la/2DT1MQd)":[ "" ],
      "Translation string is missing the message property":[ "String de tradução está em falta na propriedade de mensagem" ],
      "No \"message\" message property is set for a string (https://mzl.la/2DSBTjA).":[ "Nenhuma propriedade de mensagem \"message\" está definida para uma string (https://mzl.la/2DSBTjA)." ],
      "The field is required.":[ "O campo é requerido." ],
      "See https://mzl.la/1ZOhoEN (MDN Docs) for more information.":[ "Veja https://mzl.la/1ZOhoEN (MDN Docs) para mais informação." ],
      "The permission type is unsupported.":[ "O tipo de permissão não é suportado." ],
      "See https://mzl.la/1R1n1t0 (MDN Docs) for more information.":[ "Veja https://mzl.la/1R1n1t0 (MDN Docs) para mais informação." ],
      "See https://mzl.la/2Qn0fWC (MDN Docs) for more information.":[ "Consulte https://mzl.la/2Qn0fWC (MDN Docs) para mais informação." ],
      "See https://mzl.la/3Woeqv4 (MDN Docs) for more information.":[ "" ],
      "Unknown permission.":[ "Permissão desconhecida." ],
      "%(instancePath)s: the following privileged permissions are only allowed in privileged extensions: %(privilegedPermissions)s.":[ "" ],
      "Invalid host permission.":[ "Permissão de hospedeiro desconhecida." ],
      "Invalid install origin.":[ "Origem de instalação inválida." ],
      "Invalid install origin. A valid origin has - only - a scheme, hostname and optional port. See https://mzl.la/3TEbqbE (MDN Docs) for more information.":[ "" ],
      "The field is invalid.":[ "O campo é inválido." ],
      "\"manifest_version\" in the manifest.json is not a valid value":[ "\"manifest_version\" no manifest.json não é um valor válido" ],
      "See https://mzl.la/20PenXl (MDN Docs) for more information.":[ "Veja https://mzl.la/20PenXl (MDN Docs) para mais informação." ],
      "\"%(property)s\" allows remote code execution in manifest.json":[ "" ],
      "A custom \"%(property)s\" needs additional review.":[ "" ],
      "\"%(property)s\" allows 'eval', which has strong security and performance implications.":[ "" ],
      "In most cases the same result can be achieved differently, therefore it is generally prohibited":[ "" ],
      "The \"name\" property must be a string with no leading/trailing whitespaces.":[ "" ],
      "See http://mzl.la/1STmr48 (MDN Docs) for more information.":[ "Veja http://mzl.la/1STmr48 (MDN Docs) para mais informação." ],
      "\"update_url\" is not allowed.":[ "\"update_url\" não é permitido." ],
      "\"applications.gecko.update_url\" or \"browser_specific_settings.gecko.update_url\" are not allowed for Mozilla-hosted add-ons.":[ "" ],
      "The \"update_url\" property is not used by Firefox.":[ "A propriedade \"update_url\" não é utilizada pelo Firefox." ],
      "The \"update_url\" is not used by Firefox in the root of a manifest; your add-on will be updated via the Add-ons site and not your \"update_url\". See: https://mzl.la/25zqk4O":[ "" ],
      "\"strict_max_version\" not required.":[ "\"strict_max_version\" não é requerido." ],
      "\"strict_max_version\" shouldn't be used unless the add-on is expected not to work with future versions of Firefox.":[ "" ],
      "Manifest Version 3 is not fully supported on Firefox for Android.":[ "" ],
      "No \"%(property)s\" property found in manifest.json":[ "" ],
      "\"%(property)s\" is required":[ "" ],
      "An icon defined in the manifest could not be found in the package.":[ "Um ícone definido no manifesto não pôde ser encontrado no pacote." ],
      "Icon could not be found at \"%(path)s\".":[ "Ícone não pôde ser encontrado em \"%(path)s\"." ],
      "A background script defined in the manifest could not be found.":[ "Não foi possível encontrar um script de segundo plano definido no manifesto." ],
      "A background page defined in the manifest could not be found.":[ "Não foi possível encontrar uma página de segundo plano definida no manifesto." ],
      "Background script could not be found at \"%(path)s\".":[ "Script de fundo não pôde ser encontrado em \"%(path)s\"." ],
      "Background page could not be found at \"%(path)s\".":[ "Página de fundo não pôde ser encontrada em \"%(path)s\"." ],
      "A content script defined in the manifest could not be found.":[ "Não foi possível encontrar um script de conteúdo definido no manifesto." ],
      "A content script css file defined in the manifest could not be found.":[ "Não foi possível encontrar um ficheiro de conteúdo css definido no manifesto." ],
      "Content script defined in the manifest could not be found at \"%(path)s\".":[ "Não foi possível encontrar em \"%(path)s\" um script de conteúdo definido no manifesto." ],
      "Content script css file defined in the manifest could not be found at \"%(path)s\".":[ "Ficheiro de css do script de conteúdo definido no manifesto não pôde ser encontrado em \"%(path)s\"." ],
      "A dictionary file defined in the manifest could not be found.":[ "Não foi possível encontrar um ficheiro de dicionário definido no manifesto." ],
      "Dictionary file defined in the manifest could not be found at \"%(path)s\".":[ "Não foi possível encontrar em \"%(path)s\" um ficheiro de dicionário definido no manifesto." ],
      "The manifest contains multiple dictionaries.":[ "O manifesto contém múltiplos dicionários." ],
      "Multiple dictionaries were defined in the manifest, which is unsupported.":[ "Foram definidos múltiplos dicionários no manifesto, o que não é suportado." ],
      "The manifest contains a dictionaries object, but it is empty.":[ "O manifesto contém um objeto de dicionários, mas está vazio." ],
      "A dictionaries object was defined in the manifest, but it was empty.":[ "Foi definido um objeto de dicionários no manifesto, mas este estava vazio." ],
      "The manifest contains a dictionary but no id property.":[ "O manifesto contém um dicionário, mas nenhuma propriedade de identificação." ],
      "A dictionary was found in the manifest, but there was no id set.":[ "Foi encontrado um dicionário no manifesto, mas não havia nenhuma identificação definida." ],
      "Please refer to https://github.com/mozilla-extensions/xpi-manifest to learn more about privileged extensions and signing.":[ "" ],
      "Forbidden content found in add-on.":[ "Conteúdo proibido encontrado no extra." ],
      "This add-on contains forbidden content.":[ "Este extra contém conteúdo proibido." ],
      "Icons must be square.":[ "Ícones têm de ser quadrados." ],
      "Icon at \"%(path)s\" must be square.":[ "Ícone em \"%(path)s\" tem de ser quadrado." ],
      "The size of the icon does not match the manifest.":[ "O tamanho do ícone não corresponde ao manifesto." ],
      "Expected icon at \"%(path)s\" to be %(expected)d pixels wide but was %(actual)d.":[ "" ],
      "\"%(fieldName)s\" is ignored for non-privileged add-ons.":[ "" ],
      "Corrupt image file":[ "Ficheiro de imagem corrupto" ],
      "Expected icon file at \"%(path)s\" is corrupted":[ "Ficheiro de ícone esperado em \"%(path)s\" está corrompido" ],
      "This property has been deprecated.":[ "Esta propriedade foi descontinuada." ],
      "This theme LWT alias has been removed in Firefox 70.":[ "Este nome alternativo do tema LWT foi removido no Firefox 70." ],
      "See https://mzl.la/2T11Lkc (MDN Docs) for more information.":[ "Consulte https://mzl.la/2T11Lkc (Documentação da MDN) para mais informação." ],
      "Theme image for \"%(type)s\" could not be found at \"%(path)s\"":[ "Não foi possível encontrar em \"%(path)s\" a imagem do tema para \"%(type)s\"" ],
      "\"%(fieldName)s\" manifest field is only used for privileged and temporarily installed extensions.":[ "" ],
      "Corrupted theme image file":[ "Ficheiro de imagem de tema corrompido" ],
      "Theme image file at \"%(path)s\" is corrupted":[ "O ficheiro de imagem do tema em \"%(path)s\" está corrompido" ],
      "Theme image file has an unsupported file extension":[ "O ficheiro de imagem do tema tem uma extensão de ficheiro não suportada" ],
      "Theme image file at \"%(path)s\" has an unsupported file extension":[ "O ficheiro de imagem do tema em \"%(path)s\" tem uma extensão de ficheiro não suportada" ],
      "Theme image file has an unsupported mime type":[ "O ficheiro de imagem do tema tem um tipo de MIME não suportado" ],
      "Theme image file at \"%(path)s\" has the unsupported mime type \"%(mime)s\"":[ "O ficheiro de imagem do tema em \"%(path)s\" tem um tipo de MIME \"%(mime)s\" não suportado" ],
      "Theme image file mime type does not match its file extension":[ "O tipo de MIME do ficheiro de imagem do tema não corresponde com a sua extensão de ficheiro" ],
      "Theme image file extension at \"%(path)s\" does not match its actual mime type \"%(mime)s\"":[ "O ficheiro de imagem do tema em \"%(path)s\" não corresponde com o seu tipo de MIME \"%(mime)s\" atual" ],
      "The \"default_locale\" is missing localizations.":[ "O \"default_locale\" tem falta de idiomas." ],
      "The \"default_locale\" value is specified in the manifest, but no matching \"messages.json\" in the \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "" ],
      "The \"default_locale\" is missing but \"_locales\" exist.":[ "O \"default_locale\" está em falta mas \"_locales\" existe." ],
      "The \"default_locale\" value is not specifed in the manifest, but a \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "" ],
      "Unsupported image extension":[ "Extensão de imagem não suportada" ],
      "Icons should be one of JPG/JPEG, WebP, GIF, PNG or SVG.":[ "Íones devem ser um de JPG/JPEG, WebP, GIF, PNG ou SVG." ],
      "\"applications\" property overridden by \"browser_specific_settings\" property":[ "Propriedade \"applications\" foi substituída pela propriedade \"browser_specific_settings\"" ],
      "The \"applications\" property is being ignored because it is superseded by the \"browser_specific_settings\" property which is also defined in your manifest. Consider removing applications.":[ "" ],
      "Empty language directory":[ "Diretório de idiomas vazio" ],
      "messages.json file missing in \"%(path)s\"":[ "Ficheiro messages.json em falta em \"%(path)s\"" ],
      "Manifest key not supported by the specified minimum Firefox version":[ "Chave de manifesto não suportada pela versão mínima especificada do Firefox" ],
      "\"strict_min_version\" requires Firefox %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "" ],
      "\"%(fieldName)s\" is not supported in manifest versions %(versionRange)s.":[ "" ],
      "Permission not supported by the specified minimum Firefox version":[ "Permissão não suportada pela versão mínima do Firefox especificada" ],
      "\"%(fieldName)s\" is not supported.":[ "" ],
      "Manifest key not supported by the specified minimum Firefox for Android version":[ "Permissão não suportada pela versão mínima do Firefox para Android especificada" ],
      "\"strict_min_version\" requires Firefox for Android %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "" ],
      "Permission not supported by the specified minimum Firefox for Android version":[ "Permissão não suportada versão mínima do Firefox para Android especificada" ],
      "Linking to \"addons.mozilla.org\" is not allowed":[ "A ligação a \"addons.mozilla.org\" não é permitida" ],
      "Links directing to \"addons.mozilla.org\" are not allowed to be used for homepage":[ "Não podem ser utilizadas ligações na página inicial que direcionem para \"addons.mozilla.org\"" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above":[ "" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above. Please update your manifest.json version to specify a minimum Firefox version.":[ "" ],
      "The extension ID is required in Manifest Version 3 and above.":[ "O ID da extensão é necessário no Manifesto Versão 3 e superior." ],
      "See https://mzl.la/3PLZYdo for more information.":[ "Veja https://mzl.la/3PLZYdo para mais informações." ],
      "%(instancePath)s: Privileged extensions should declare privileged permissions.":[ "" ],
      "This extension does not declare any privileged permission. It does not need to be signed with the privileged certificate. Please upload it directly to https://addons.mozilla.org/.":[ "" ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for privileged extensions.":[ "" ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for extensions that include privileged manifest fields.":[ "" ],
      "%(instancePath)s: privileged manifest fields are only allowed in privileged extensions.":[ "" ],
      "This extension does not include the \"mozillaAddons\" permission, which is required for privileged extensions.":[ "" ],
      "Cannot use actions in hidden add-ons.":[ "" ],
      "The hidden and browser_action/page_action (or action in Manifest Version 3 and above) properties are mutually exclusive.":[ "" ],
      "Use \"browser_specific_settings\" instead of \"applications\".":[ "" ],
      "The \"applications\" property in the manifest is deprecated and will no longer be accepted in Manifest Version 3 and above.":[ "" ],
      "\"applications\" is no longer allowed in Manifest Version 3 and above.":[ "" ],
      "The \"applications\" property in the manifest is no longer allowed in Manifest Version 3 and above. Use \"browser_specific_settings\" instead.":[ "" ],
      "The version string should be simplified because it won't be compatible with Manifest Version 3 and above.":[ "" ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros will no longer be allowed. Letters will no longer be allowed either. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "" ],
      "The version string should be simplified.":[ "" ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros are not allowed. Letters are no longer allowed. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "" ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported in manifest versions %(versionRange)s.":[ "" ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported.":[ "" ] } } }