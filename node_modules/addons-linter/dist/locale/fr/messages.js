module.exports = { domain:"messages",
  locale_data:{ messages:{ "":{ domain:"messages",
        plural_forms:"nplurals=2; plural=(n > 1);",
        lang:"fr" },
      "Validation Summary:":[ "Résumé des validations :" ],
      Code:[ "Code" ],
      Message:[ "Message" ],
      Description:[ "Description" ],
      File:[ "Fichier" ],
      Line:[ "Ligne" ],
      Column:[ "Colonne" ],
      "Invalid manifest version range requested: --min-manifest-version (currently set to %(minManifestVersion)s) should not be greater than --max-manifest-version (currently set to %(maxManifestVersion)s).":[ "L’intervalle de version de manifeste demandé n’est pas valide : --min-manifest-version (la valeur actuelle est %(minManifestVersion)s) ne doit pas être supérieur à --max-manifest-version (la valeur actuelle est %(maxManifestVersion)s)." ],
      "Your FTL is not valid.":[ "Votre fichier FTL n’est pas valide." ],
      "Your FTL file could not be parsed.":[ "Votre fichier FTL n’a pas pu être analysé." ],
      "Remote scripts are not allowed as per the Add-on Policies.":[ "Les scripts externes ne sont pas autorisés, comme indiqué dans les termes relatifs aux modules." ],
      "Please include all scripts in the add-on. For more information, refer to https://mzl.la/2uEOkYp.":[ "Merci d’inclure tous les scripts dans le module complémentaire. Pour plus d’informations, consultez https://mzl.la/2uEOkYp." ],
      "Inline scripts blocked by default":[ "Les scripts inline sont bloqués par défaut" ],
      "Default CSP rules prevent inline JavaScript from running (https://mzl.la/2pn32nd).":[ "Les règles CSP par défaut empêchent l’exécution de JavaScript en ligne (https://mzl.la/2pn32nd)." ],
      "Unadvised 3rd-party JS library":[ "Bibliothèque JS tierce déconseillée" ],
      "Your add-on uses a JavaScript library we do not recommend. Read more: https://bit.ly/1TRIyZY":[ "Votre module complémentaire utilise une bibliothèque JavaScript que nous ne recommandons pas. Pour plus d’informations, consultez : https://bit.ly/1TRIyZY" ],
      "Known JS library detected":[ "Bibliothèque JS connue détectée" ],
      "JavaScript libraries are discouraged for simple add-ons, but are generally accepted.":[ "Les bibliothèques JavaScript sont déconseillées pour les modules complémentaires simples, mais sont généralement acceptées." ],
      "Due to both security and performance concerns, this may not be set using dynamic values which have not been adequately sanitized. This can lead to security issues or fairly serious performance degradation.":[ "Pour des raisons de sécurité et de performances, cette valeur ne peut pas être définie en utilisant des valeurs dynamiques qui n’ont pas été correctement nettoyées. Cela peut entraîner des problèmes de sécurité ou une dégradation assez importante des performances." ],
      "{{api}} is not supported":[ "{{api}} n’est pas pris en charge" ],
      "This API has not been implemented by Firefox.":[ "Cette API n’a pas été implémentée dans Firefox." ],
      "\"{{api}}\" has been removed in Manifest Version 3 (`manifest_version` property)":[ "« {{api}} » a été supprimé dans Manifest Version 3 (propriété `manifest_version`)" ],
      "{{api}} is deprecated":[ "{{api}} n’est pas pris en charge" ],
      "This API has been deprecated by Firefox.":[ "Firefox considère désormais cette API obsolète." ],
      "Content script file could not be found.":[ "Impossible de trouver le fichier de script." ],
      "\"{{api}}\" is deprecated or unimplemented":[ "« {{api}} » est obsolète ou non implémenté" ],
      "Content script file could not be found":[ "Impossible de trouver le fichier de script de contenu" ],
      "\"%(api)s\" can cause issues when loaded temporarily":[ "« %(api)s » peut causer des problèmes lorsqu’elle est chargée temporairement" ],
      "This API can cause issues when loaded temporarily using about:debugging in Firefox unless you specify \"browser_specific_settings.gecko.id\" in the manifest. Please see: https://mzl.la/2hizK4a for more.":[ "Cette API peut causer des problèmes lorsqu’elle est chargée temporairement en utilisant about:debugging dans Firefox, à moins que vous ne spécifiiez « browser_specific_settings.gecko.id » dans le manifeste. Pour en savoir plus, rendez-vous sur https://mzl.la/2hizK4a." ],
      "{{api}} is not supported in Firefox version {{minVersion}}":[ "{{api}} n’est pas pris en charge par la version {{minVersion}} de Firefox" ],
      "This API is not implemented by the given minimum Firefox version":[ "Cette API n'est pas implémentée par la version minimale de Firefox indiquée" ],
      "{{api}} is not supported in Firefox for Android version {{minVersion}}":[ "{{api}} n’est pas pris en charge par la version {{minVersion}} de Firefox pour Android" ],
      "This API is not implemented by the given minimum Firefox for Android version":[ "Cette API n'est pas implémentée par la version minimale donnée de Firefox pour Android" ],
      "Content script file name should not be empty.":[ "Le fichier du script de contenu doit avoir un nom." ],
      "Content script file name should not be empty":[ "Le fichier du script de contenu doit avoir un nom" ],
      "\"%(method)s\" called with a non-literal uri":[ "« %(method)s » appelée avec une URI non littérale" ],
      "Calling \"%(method)s\" with variable parameters can result in potential security vulnerabilities if the variable contains a remote URI. Consider using 'window.open' with the 'chrome=no' flag.":[ "L’appel à « %(method)s » avec des paramètres variables peut entraîner des failles de sécurité potentielles si la variable contient un URI distant. Pensez à utiliser « window.open » avec l’option « chrome=no »." ],
      "\"%(method)s\" called with non-local URI":[ "« %(method)s » appelée avec un URI non local" ],
      "Calling \"%(method)s\" with a non-local URI will result in the dialog being opened with chrome privileges.":[ "L’appel à « %(method)s » avec un URI non local ouvrira la boîte de dialogue avec les privilèges chrome." ],
      "JavaScript syntax error":[ "Erreur de syntaxe JavaScript" ],
      "There is a JavaScript syntax error in your code, which might be related to some experimental JavaScript features that aren't an official part of the language specification and therefore not supported yet. The validation cannot continue on this file.":[ "Il y a une erreur de syntaxe JavaScript dans votre code, qui peut être liée à certaines fonctionnalités JavaScript expérimentales qui ne font pas partie des spécifications du langage et ne sont donc pas encore prises en charge. La validation ne peut pas continuer pour ce fichier." ],
      "Evaluation of strings as code can lead to security vulnerabilities and performance issues, even in the most innocuous of circumstances. Please avoid using `eval` and the `Function` constructor when at all possible.":[ "L’évaluation de chaînes de caractères en tant que code peut entraîner des failles de sécurité et des problèmes de performances, même dans les circonstances les plus inoffensives. Veuillez éviter d’utiliser `eval` et le constructeur `Function` autant que possible." ],
      "setTimeout, setInterval and execScript functions should be called only with function expressions as their first argument":[ "Les fonctions setTimeout, setInterval et execScript doivent être appelées uniquement avec des expressions de fonction comme premier argument" ],
      "Unexpected global passed as an argument":[ "Variable globale inattendue passée comme un argument" ],
      "Passing a global as an argument is not recommended. Please make this a var instead.":[ "Il n’est pas recommandé de passer une variable globale comme argument. Veuillez plutôt utiliser « var »." ],
      "Use of document.write strongly discouraged.":[ "L’utilisation de document.write est fortement déconseillée." ],
      "document.write will fail in many circumstances when used in extensions, and has potentially severe security repercussions when used improperly. Therefore, it should not be used.":[ "document.write échouera dans de nombreuses circonstances lorsque cette fonction est utilisée dans des extensions, et a de graves répercussions sur la sécurité si elle est utilisée incorrectement. Par conséquent, elle ne doit pas être utilisée." ],
      "Banned 3rd-party JS library":[ "Bibliothèque JS tierce interdite d’utilisation" ],
      "Your add-on uses a JavaScript library we consider unsafe. Read more: https://bit.ly/1TRIyZY":[ "Votre module complémentaire utilise une bibliothèque JavaScript que nous considérons comme non sécurisée. Pour plus d’informations, consultez : https://bit.ly/1TRIyZY" ],
      "Your JSON contains block comments.":[ "Le code JSON contient des blocs de commentaires." ],
      "Only line comments (comments beginning with \"//\") are allowed in JSON files. Please remove block comments (comments beginning with \"/*\")":[ "Seuls les commentaires de ligne (commentaires commençant par « // ») sont autorisés dans les fichiers JSON. Veuillez retirer les blocs de commentaires (commentaires commençant par « /* »)" ],
      "Duplicate keys are not allowed in JSON files.":[ "Les clés dupliquées ne sont pas autorisées dans les fichiers JSON." ],
      "Duplicate key found in JSON file.":[ "Doublon de clé détecté dans un fichier JSON." ],
      "Your JSON is not valid.":[ "Votre JSON n’est pas valide." ],
      "Your JSON file could not be parsed.":[ "Votre fichier JSON n’a pas pu être analysé." ],
      "Reserved filename found.":[ "Nom de fichier réservé détecté." ],
      "Files whose names are reserved have been found in the add-on. Please refrain from using them and rename your files.":[ "Des fichiers dont le nom est réservé ont été trouvés dans le module. Veuillez ne pas les utiliser et renommer vos fichiers." ],
      "The package is invalid. It may contain entries using invalid characters, as an example using '\\' as a path separator is not allowed in Firefox. Try to recreate your add-on package (ZIP) and make sure all entries are using '/' as the path separator.":[ "Le paquet est invalide. Il contient peut-être des entrées utilisant des caractères invalides. Par exemple, l’utilisation de « \\ » comme séparateur de chemin n’est pas autorisée dans Firefox. Essayez de recréer le paquet (ZIP) de votre module et assurez-vous que toutes les entrées utilisent « / » comme séparateur de chemin." ],
      "We were unable to decompress the zip file.":[ "Il est impossible de décompresser le fichier zip." ],
      "manifest.json was not found":[ "Le fichier manifest.json est introuvable" ],
      "No manifest.json was found at the root of the extension. The package file must be a ZIP of the extension's files themselves, not of the containing directory. See: https://mzl.la/2r2McKv for more on packaging.":[ "Aucun fichier manifest.json trouvé à la racine de cette extension. Le fichier du paquet doit être un fichier ZIP des fichiers de l’extension directement, et non du répertoire qui les contient. Pour plus d’informations sur la création de paquets, consultez : https://mzl.la/2r2McKv" ],
      "File is too large to parse.":[ "Le fichier est trop volumineux pour être analysé." ],
      "This file is not binary and is too large to parse. Files larger than %(maxFileSizeToParseMB)sMB will not be parsed. Consider moving large lists of data out of JavaScript files and into JSON files, or splitting very large files into smaller ones.":[ "Ce fichier n’est pas binaire et est trop volumineux pour être analysé. Les fichiers dont la taille est supérieure à %(maxFileSizeToParseMB)s Mo ne seront pas analysés. Envisagez de déplacer de grandes listes de données depuis des fichiers JavaScript vers des fichiers JSON, ou de diviser les fichiers très volumineux en fichiers plus petits." ],
      "Hidden file flagged":[ "Un fichier caché a été détecté" ],
      "Hidden files complicate the review process and can contain sensitive information about the system that generated the add-on. Please modify the packaging process so that these files aren't included.":[ "Les fichiers cachés compliquent le processus de revue et peuvent contenir des informations sensibles sur le système qui a généré le module. Merci de modifier le processus de création du paquet afin que ces fichiers ne soient plus inclus." ],
      "Package contains duplicate entries":[ "Le paquet contient des entrées dupliquées" ],
      "Flagged filename found":[ "Nom de fichier signalé détecté" ],
      "Files were found that are either unnecessary or have been included unintentionally. They should be removed.":[ "Des fichiers inutiles ou ajoutés par inadvertance ont été trouvés. Ils devraient être supprimés." ],
      "The package contains multiple entries with the same name. This practice has been banned. Try unzipping and re-zipping your add-on package and try again.":[ "Le paquet contient plusieurs entrées avec le même nom. Cette pratique a été interdite. Essayez de décompresser puis de re-compresser le paquet de votre module puis essayez à nouveau." ],
      "Flagged file extensions found":[ "Extensions de fichiers signalées détectées" ],
      "Flagged file type found":[ "Type de fichier signalé détecté" ],
      "Files whose names end with flagged extensions have been found in the add-on. The extension of these files are flagged because they usually identify binary components. Please see https://bit.ly/review-policy for more information on the binary content review process.":[ "Des fichiers dont le nom se termine par une extension signalée ont été trouvés dans le module. L’extension de ces fichiers est signalée car ils identifient généralement des composants binaires. Veuillez consulter https://bit.ly/review-policy pour plus d’informations sur le processus de revue de contenu binaire." ],
      "Package already signed":[ "Le paquet est déjà signé" ],
      "Add-ons which are already signed will be re-signed when published on AMO. This will replace any existing signatures on the add-on.":[ "Les modules déjà signés le seront à nouveau lors de leur publication sur AMO. Ce processus remplacera toutes les signatures existantes sur le module." ],
      "Firefox add-ons are not allowed to run coin miners.":[ "Les modules Firefox ne peuvent pas miner de cryptomonnaies." ],
      "We do not allow coinminer scripts to be run inside WebExtensions. See https://github.com/mozilla/addons-linter/issues/1643 for more details.":[ "Nous n’autorisons pas l’exécution de scripts de minage de cryptomonnaies dans les WebExtensions. Consultez https://github.com/mozilla/addons-linter/issues/1643 pour plus de détails." ],
      "String name is reserved for a predefined message":[ "Le nom de la chaîne est déjà réservé à un message prédéfini" ],
      "String names starting with @@ get translated to built-in constants (https://mzl.la/2BL9ZjE).":[ "Les chaînes dont le nom commence par @@ sont converties en constantes intégrées (https://mzl.la/2BL9ZjE)." ],
      "String name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2Eztyi5).":[ "Le nom de la chaîne ne doit contenir que des caractères alphanumériques, _ et @ (https://mzl.la/2Eztyi5)." ],
      "Placeholder for message is missing":[ "L’emplacement pour le message est manquant" ],
      "A placeholder used in the message is not defined.":[ "L’emplacement utilisé dans le message n’est pas défini." ],
      "Placeholder name contains invalid characters":[ "Le nom de l’emplacement contient des caractères invalides" ],
      "Placeholder name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2ExbYez).":[ "Le nom de l’emplacement ne doit contenir que des caractères alphanumériques, _ et @ (https://mzl.la/2ExbYez)." ],
      "Placeholder is missing the content property":[ "L’emplacement ne possède pas de propriété de contenu" ],
      "A placeholder needs a content property defining the replacement of it (https://mzl.la/2DT1MQd)":[ "Un nom d’emplacement a besoin d’une propriété de contenu définissant son remplacement (https://mzl.la/2DT1MQd)" ],
      "Translation string is missing the message property":[ "La chaîne de traduction ne contient pas la propriété « message »" ],
      "No \"message\" message property is set for a string (https://mzl.la/2DSBTjA).":[ "Aucune propriété de « message » n’est attribuée à une chaîne (https://mzl.la/2DSBTjA)." ],
      "The field is required.":[ "Ce champ est requis." ],
      "See https://mzl.la/1ZOhoEN (MDN Docs) for more information.":[ "Voir https://mzl.la/1ZOhoEN (MDN Docs) pour plus d’informations." ],
      "The permission type is unsupported.":[ "Ce type de permission n’est pas pris en charge." ],
      "See https://mzl.la/1R1n1t0 (MDN Docs) for more information.":[ "Voir https://mzl.la/1R1n1t0 (MDN Docs) pour plus d’informations." ],
      "See https://mzl.la/2Qn0fWC (MDN Docs) for more information.":[ "Voir https://mzl.la/2Qn0fWC (MDN Docs) pour plus d’informations." ],
      "See https://mzl.la/3Woeqv4 (MDN Docs) for more information.":[ "Voir https://mzl.la/3Woeqv4 (MDN Docs) pour plus d’informations." ],
      "Unknown permission.":[ "Permission inconnue." ],
      "%(instancePath)s: the following privileged permissions are only allowed in privileged extensions: %(privilegedPermissions)s.":[ "%(instancePath)s : les permissions privilégiées suivantes ne sont autorisées que dans les extensions privilégiées : %(privilegedPermissions)s." ],
      "Invalid host permission.":[ "Autorisation de l’hôte invalide." ],
      "Invalid install origin.":[ "Origine d’installation invalide." ],
      "Invalid install origin. A valid origin has - only - a scheme, hostname and optional port. See https://mzl.la/3TEbqbE (MDN Docs) for more information.":[ "Origine d’installation invalide. Une origine valide a uniquement un schéma, un nom d’hôte et un port facultatif. Voir https://mzl.la/3TEbqbE (MDN Docs) pour plus d’informations." ],
      "The field is invalid.":[ "Le champ est invalide." ],
      "\"manifest_version\" in the manifest.json is not a valid value":[ "La valeur « manifest_version » dans le fichier manifest.json est invalide" ],
      "See https://mzl.la/20PenXl (MDN Docs) for more information.":[ "Voir https://mzl.la/20PenXl (MDN Docs) pour plus d’informations." ],
      "\"%(property)s\" allows remote code execution in manifest.json":[ "« %(property)s » permet l’exécution de code à distance dans le fichier manifest.json" ],
      "A custom \"%(property)s\" needs additional review.":[ "Une propriété personnalisée « %(property)s » nécessite une revue supplémentaire." ],
      "\"%(property)s\" allows 'eval', which has strong security and performance implications.":[ "« %(property)s » permet l’utilisation de « eval », ce qui a de fortes conséquences sur les performances et la sécurité." ],
      "In most cases the same result can be achieved differently, therefore it is generally prohibited":[ "Dans la plupart des cas, le même résultat peut être obtenu différemment, c’est pourquoi c’est généralement interdit" ],
      "The \"name\" property must be a string with no leading/trailing whitespaces.":[ "La propriété « name » est une chaîne qui ne doit ni commencer ni finir par une espace." ],
      "See http://mzl.la/1STmr48 (MDN Docs) for more information.":[ "Voir https://mzl.la/1STmr48 (MDN Docs) pour plus d’informations." ],
      "\"update_url\" is not allowed.":[ "« update_url » n’est pas autorisé." ],
      "\"applications.gecko.update_url\" or \"browser_specific_settings.gecko.update_url\" are not allowed for Mozilla-hosted add-ons.":[ "« applications.gecko.update_url » et « browser_specific_settings.gecko.update_url » ne sont pas autorisés pour les modules hébergés par Mozilla." ],
      "The \"update_url\" property is not used by Firefox.":[ "La propriété « update_url » n’est pas utilisée par Firefox." ],
      "The \"update_url\" is not used by Firefox in the root of a manifest; your add-on will be updated via the Add-ons site and not your \"update_url\". See: https://mzl.la/25zqk4O":[ "Le champ « update_url » n’est pas utilisé par Firefox à la racine d’un manifeste : votre module complémentaire se mettra à jour depuis le site des modules complémentaires et non depuis votre adresse « update_url ». Voir : https://mzl.la/25zqk4O" ],
      "\"strict_max_version\" not required.":[ "« strict_max_version » n’est pas nécessaire." ],
      "\"strict_max_version\" shouldn't be used unless the add-on is expected not to work with future versions of Firefox.":[ "« strict_max_version » ne doit pas être utilisé à moins qu’il soit prévu que le module ne fonctionne pas avec les futures versions de Firefox." ],
      "Manifest Version 3 is not fully supported on Firefox for Android.":[ "La version 3 de Manifest n’est pas totalement prise en charge par Firefox pour Android." ],
      "No \"%(property)s\" property found in manifest.json":[ "Aucune propriété « %(property)s » trouvée dans le fichier manifest.json" ],
      "\"%(property)s\" is required":[ "La propriété « %(property)s » est requise" ],
      "An icon defined in the manifest could not be found in the package.":[ "Une icône définie dans le manifeste est introuvable dans le paquet." ],
      "Icon could not be found at \"%(path)s\".":[ "Icône introuvable à l’emplacement « %(path)s »." ],
      "A background script defined in the manifest could not be found.":[ "Impossible de trouver un script d’arrière-plan défini dans le manifeste." ],
      "A background page defined in the manifest could not be found.":[ "Impossible de trouver une page d’arrière-plan définie dans le manifeste." ],
      "Background script could not be found at \"%(path)s\".":[ "Le script d’arrière-plan n’a pu être trouvé à l’emplacement « %(path)s »." ],
      "Background page could not be found at \"%(path)s\".":[ "La page d’arrière-plan n’a pu être trouvée à l’emplacement « %(path)s »." ],
      "A content script defined in the manifest could not be found.":[ "Impossible de trouver un script de contenu défini dans le manifeste." ],
      "A content script css file defined in the manifest could not be found.":[ "Impossible de trouver un fichier de script de contenu CSS défini dans le manifeste." ],
      "Content script defined in the manifest could not be found at \"%(path)s\".":[ "Impossible de trouver dans « %(path)s » le script de contenu défini dans le manifeste." ],
      "Content script css file defined in the manifest could not be found at \"%(path)s\".":[ "Le script de contenu CSS défini dans le manifeste est introuvable à l’emplacement « %(path)s »." ],
      "A dictionary file defined in the manifest could not be found.":[ "Impossible de trouver un fichier de dictionnaire défini dans le manifeste." ],
      "Dictionary file defined in the manifest could not be found at \"%(path)s\".":[ "Impossible de trouver un fichier de dictionnaire défini dans le manifeste à l’emplacement « %(path)s »." ],
      "The manifest contains multiple dictionaries.":[ "Le fichier manifest contient plusieurs dictionnaires." ],
      "Multiple dictionaries were defined in the manifest, which is unsupported.":[ "Plusieurs dictionnaires sont définis dans le fichier manifest, ce qui n’est pas pris en charge." ],
      "The manifest contains a dictionaries object, but it is empty.":[ "Le manifeste contient un objet dictionnaire, mais il est vide." ],
      "A dictionaries object was defined in the manifest, but it was empty.":[ "Un objet dictionnaire a été défini dans le manifeste, mais il est vide." ],
      "The manifest contains a dictionary but no id property.":[ "Le manifeste contient un dictionnaire, mais pas la propriété id." ],
      "A dictionary was found in the manifest, but there was no id set.":[ "Un dictionnaire a été trouvé dans le manifeste, mais aucun id n’a été défini." ],
      "Please refer to https://github.com/mozilla-extensions/xpi-manifest to learn more about privileged extensions and signing.":[ "Pour en savoir plus sur les extensions privilégiées et la signature, veuillez consulter le site https://github.com/mozilla-extensions/xpi-manifest." ],
      "Forbidden content found in add-on.":[ "Contenu interdit détecté dans le module." ],
      "This add-on contains forbidden content.":[ "Ce module contient des éléments interdits." ],
      "Icons must be square.":[ "Les icônes doivent être carrées." ],
      "Icon at \"%(path)s\" must be square.":[ "L’icône située à l’emplacement « %(path)s » doit être carrée." ],
      "The size of the icon does not match the manifest.":[ "La taille de l’icône ne correspond pas au manifeste." ],
      "Expected icon at \"%(path)s\" to be %(expected)d pixels wide but was %(actual)d.":[ "La largeur de l’icône à l’adresse « %(path)s » devrait mesurer %(expected)d pixels mais était de %(actual)d." ],
      "\"%(fieldName)s\" is ignored for non-privileged add-ons.":[ "Le champs « %(fieldName)s » est ignoré par les modules complémentaires non privilégiés." ],
      "Corrupt image file":[ "Fichier image corrompu" ],
      "Expected icon file at \"%(path)s\" is corrupted":[ "L’icône attendue, située à l’emplacement « %(path)s » est corrompue" ],
      "This property has been deprecated.":[ "Cette propriété est désormais obsolète." ],
      "This theme LWT alias has been removed in Firefox 70.":[ "L’alias de ce thème LWT a été supprimé dans Firefox 70." ],
      "See https://mzl.la/2T11Lkc (MDN Docs) for more information.":[ "Voir la documentation MDN à l’adresse https://mzl.la/2T11Lkc pour plus d’informations." ],
      "Theme image for \"%(type)s\" could not be found at \"%(path)s\"":[ "L’image du thème pour « %(type)s » n’a pu être trouvée à l’emplacement « %(path)s »" ],
      "\"%(fieldName)s\" manifest field is only used for privileged and temporarily installed extensions.":[ "Dans le fichier manifest, le champs « %(fieldName)s » n’est utilisé que pour les extensions privilégiées et installées temporairement." ],
      "Corrupted theme image file":[ "Fichier image du thème corrompu" ],
      "Theme image file at \"%(path)s\" is corrupted":[ "L’image du thème à l’emplacement « %(path)s » est corrompue" ],
      "Theme image file has an unsupported file extension":[ "L’extension du fichier de l'image du thème n'est pas prise en charge" ],
      "Theme image file at \"%(path)s\" has an unsupported file extension":[ "L’extension du fichier de l’image du thème à l’emplacement « %(path)s » n'est pas prise en charge" ],
      "Theme image file has an unsupported mime type":[ "Le type MIME du fichier de l’image du thème n’est pas pris en charge" ],
      "Theme image file at \"%(path)s\" has the unsupported mime type \"%(mime)s\"":[ "Le type MIME « %(mime)s » du fichier de l’image du thème à l’emplacement « %(path)s » n’est pas pris en charge" ],
      "Theme image file mime type does not match its file extension":[ "Le type MIME du fichier de l’image du thème ne correspond pas à l’extension de son fichier" ],
      "Theme image file extension at \"%(path)s\" does not match its actual mime type \"%(mime)s\"":[ "L’extension du fichier de l’image du thème à l’emplacement « %(path)s » ne correspond pas au type MIME « %(mime)s » contenu" ],
      "The \"default_locale\" is missing localizations.":[ "Des traductions sont manquantes pour « default_locale »." ],
      "The \"default_locale\" value is specified in the manifest, but no matching \"messages.json\" in the \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "La valeur « default_locale » est spécifiée dans le manifeste, mais il n’existe pas de « messages.json » correspondant dans le répertoire « _locales ». Voir : https://mzl.la/2hjcaEE" ],
      "The \"default_locale\" is missing but \"_locales\" exist.":[ "« default_locale » est manquant, mais « _locales » existe." ],
      "The \"default_locale\" value is not specifed in the manifest, but a \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "La valeur « default_locale » n’est pas spécifiée dans le manifeste, mais un répertoire « _locales » existe. Voir : https://mzl.la/2hjcaEE" ],
      "Unsupported image extension":[ "Extension d’image non prise en charge" ],
      "Icons should be one of JPG/JPEG, WebP, GIF, PNG or SVG.":[ "Les icônes doivent être au format JPG/JPEG, WebP, GIF, PNG ou SVG." ],
      "\"applications\" property overridden by \"browser_specific_settings\" property":[ "Propriété « applications » remplacée par la propriété « browser_specific_settings »" ],
      "The \"applications\" property is being ignored because it is superseded by the \"browser_specific_settings\" property which is also defined in your manifest. Consider removing applications.":[ "La propriété « applications » est ignorée car elle est remplacée par la propriété « browser_specific_settings » qui est également définie dans votre manifeste. Envisagez de supprimer « applications »." ],
      "Empty language directory":[ "Répertoire de langues vide" ],
      "messages.json file missing in \"%(path)s\"":[ "Fichier messages.json manquant dans « %(path)s »" ],
      "Manifest key not supported by the specified minimum Firefox version":[ "La clé du manifeste n’est pas prise en charge par la version minimale de Firefox spécifiée" ],
      "\"strict_min_version\" requires Firefox %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "« strict_min_version » a besoin de la version %(minVersion)s de Firefox, publiée avant la version %(versionAdded)s qui a introduit la prise en charge de « %(key)s »." ],
      "\"%(fieldName)s\" is not supported in manifest versions %(versionRange)s.":[ "« %(fieldName)s » n’est pas pris en charge dans les versions %(versionRange)s du manifeste." ],
      "Permission not supported by the specified minimum Firefox version":[ "La permission n’est pas prise en charge par la version minimale de Firefox spécifiée" ],
      "\"%(fieldName)s\" is not supported.":[ "« %(fieldName)s » n’est pas pris en charge." ],
      "Manifest key not supported by the specified minimum Firefox for Android version":[ "La clé du manifeste n’est pas prise en charge par la version minimale de Firefox pour Android spécifiée" ],
      "\"strict_min_version\" requires Firefox for Android %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "« strict_min_version » a besoin de la version %(minVersion)s de Firefox pour Android, publiée avant la version %(versionAdded)s qui a introduit la prise en charge de « %(key)s »." ],
      "Permission not supported by the specified minimum Firefox for Android version":[ "La permission n’est pas prise en charge par la version minimale de Firefox pour Android spécifiée" ],
      "Linking to \"addons.mozilla.org\" is not allowed":[ "Les liens vers « addons.mozilla.org » ne sont pas autorisés" ],
      "Links directing to \"addons.mozilla.org\" are not allowed to be used for homepage":[ "Les liens vers « addons.mozilla.org » ne sont pas autorisés comme page d’accueil" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above":[ "La permission « %(permission)s » a besoin que « strict_min_version » soit défini avec la valeur « %(minFirefoxVersion)s » ou supérieure" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above. Please update your manifest.json version to specify a minimum Firefox version.":[ "La permission « %(permission)s » a besoin que « strict_min_version » soit défini avec la valeur « %(minFirefoxVersion)s » ou supérieure. Veuillez mettre à jour la version de votre fichier manifest.json pour spécifier une version minimale de Firefox." ],
      "The extension ID is required in Manifest Version 3 and above.":[ "L’ID de l’extension est requis à partir de la version 3 de Manifest." ],
      "See https://mzl.la/3PLZYdo for more information.":[ "Consultez https://mzl.la/3PLZYdo pour plus d’informations." ],
      "%(instancePath)s: Privileged extensions should declare privileged permissions.":[ "%(instancePath)s : les extensions privilégiées devraient déclarer les permissions privilégiées." ],
      "This extension does not declare any privileged permission. It does not need to be signed with the privileged certificate. Please upload it directly to https://addons.mozilla.org/.":[ "Cette extension ne déclare aucune permission privilégiée. Elle n’a pas besoin d’être signée avec le certificat privilégié. Veuillez l’envoyer directement sur https://addons.mozilla.org/." ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for privileged extensions.":[ "%(instancePath)s : la permission « mozillaAddons » est requise pour les extensions privilégiées." ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for extensions that include privileged manifest fields.":[ "%(instancePath)s : la permission « mozillaAddons » est requise pour les extensions qui incluent des champs manifeste privilégiés." ],
      "%(instancePath)s: privileged manifest fields are only allowed in privileged extensions.":[ "%(instancePath)s : les champs de manifeste privilégiés ne sont autorisés que dans les extensions privilégiées." ],
      "This extension does not include the \"mozillaAddons\" permission, which is required for privileged extensions.":[ "Cette extension ne possède pas la permission « mozillaAddons », qui est nécessaire pour les extensions privilégiées." ],
      "Cannot use actions in hidden add-ons.":[ "Impossible d’utiliser des actions dans des modules complémentaires masqués." ],
      "The hidden and browser_action/page_action (or action in Manifest Version 3 and above) properties are mutually exclusive.":[ "Les propriétés « hidden » et « browser_action/page_action » (ou « action » dans les versions 3 et supérieures de Manifest) s’excluent mutuellement." ],
      "Use \"browser_specific_settings\" instead of \"applications\".":[ "Utilisez « browser_specific_settings » au lieu de « applications »." ],
      "The \"applications\" property in the manifest is deprecated and will no longer be accepted in Manifest Version 3 and above.":[ "La propriété « applications » dans le manifeste est obsolète et ne sera plus acceptée dans les versions 3 et supérieures de Manifest." ],
      "\"applications\" is no longer allowed in Manifest Version 3 and above.":[ "La propriété « applications » n’est plus autorisée dans les versions 3 et supérieures de Manifest." ],
      "The \"applications\" property in the manifest is no longer allowed in Manifest Version 3 and above. Use \"browser_specific_settings\" instead.":[ "La propriété « applications » dans le manifeste n’est plus autorisée dans les versions 3 et supérieures de Manifest. Utilisez plutôt « browser_specific_settings »." ],
      "The version string should be simplified because it won't be compatible with Manifest Version 3 and above.":[ "La chaîne de version devrait être simplifiée car elle ne sera pas compatible avec les versions 3 et supérieures de Manifest." ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros will no longer be allowed. Letters will no longer be allowed either. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "La version doit être une chaîne de caractères de 1 à 4 chiffres séparés par des points. Chaque numéro doit comporter jusqu’à 9 chiffres. Les zéros au début ne seront plus autorisés. Les lettres ne seront plus autorisées non plus. Consultez https://mzl.la/3h3mCRu (MDN Docs) pour plus d’informations." ],
      "The version string should be simplified.":[ "La chaîne de caractères de la version devrait être simplifiée." ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros are not allowed. Letters are no longer allowed. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "La version doit être une chaîne de caractères de 1 à 4 chiffres séparés par des points. Chaque numéro doit comporter jusqu’à 9 chiffres. Les zéros au début ne sont pas autorisés. Les lettres ne sont plus autorisées. Consultez https://mzl.la/3h3mCRu (MDN Docs) pour plus d’informations." ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported in manifest versions %(versionRange)s.":[ "/%(fieldName)s : « %(permissionName)s » n’est pas pris en charge dans les versions %(versionRange)s du manifeste." ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported.":[ "/%(fieldName)s : « %(permissionName)s » n’est pas pris en charge." ] } } }