module.exports = { domain:"messages",
  locale_data:{ messages:{ "":{ domain:"messages",
        plural_forms:"nplurals=2; plural=(n != 1);",
        lang:"nl" },
      "Validation Summary:":[ "Validatiesamenvatting:" ],
      Code:[ "Code" ],
      Message:[ "Bericht" ],
      Description:[ "Beschrijving" ],
      File:[ "Bestand" ],
      Line:[ "Regel" ],
      Column:[ "Kolom" ],
      "Invalid manifest version range requested: --min-manifest-version (currently set to %(minManifestVersion)s) should not be greater than --max-manifest-version (currently set to %(maxManifestVersion)s).":[ "Ongeldig manifestversiebereik aangevraagd: --min-manifest-version (momenteel ingesteld op %(minManifestVersion)s) mag niet groter zijn dan --max-manifest-version (momenteel ingesteld op %(maxManifestVersion)s)." ],
      "Your FTL is not valid.":[ "Uw FTL is niet geldig." ],
      "Your FTL file could not be parsed.":[ "Uw FTL-bestand kon niet worden geparset." ],
      "Remote scripts are not allowed as per the Add-on Policies.":[ "Externe scripts zijn volgens het add-onbeleid niet toegestaan." ],
      "Please include all scripts in the add-on. For more information, refer to https://mzl.la/2uEOkYp.":[ "Voeg alle scripts toe aan de add-on. Voor meer informatie, zie https://mzl.la/2uEOkYp." ],
      "Inline scripts blocked by default":[ "Inline scripts zijn standaard geblokkeerd" ],
      "Default CSP rules prevent inline JavaScript from running (https://mzl.la/2pn32nd).":[ "Standaard CSP-regels voorkomen dat inline JavaScript wordt uitgevoerd (https://mzl.la/2pn32nd)." ],
      "Unadvised 3rd-party JS library":[ "Niet-geadviseerde JS-bibliotheek van derden" ],
      "Your add-on uses a JavaScript library we do not recommend. Read more: https://bit.ly/1TRIyZY":[ "Uw add-on gebruikt een JavaScript-bibliotheek die we niet aanbevelen. Meer info: https://bit.ly/1TRIyZY" ],
      "Known JS library detected":[ "Bekende JS-bibliotheek gedetecteerd" ],
      "JavaScript libraries are discouraged for simple add-ons, but are generally accepted.":[ "JavaScript-bibliotheken worden afgeraden voor eenvoudige add-ons, maar worden algemeen geaccepteerd." ],
      "Due to both security and performance concerns, this may not be set using dynamic values which have not been adequately sanitized. This can lead to security issues or fairly serious performance degradation.":[ "Vanwege zowel beveiligings- als prestatiezorgen, mag dit niet worden ingesteld met behulp van dynamische waarden die niet voldoende zijn opgeschoond. Dit kan leiden tot beveiligingsproblemen of een ernstige verslechtering van de prestaties." ],
      "{{api}} is not supported":[ "{{api}} wordt niet ondersteund" ],
      "This API has not been implemented by Firefox.":[ "Deze API is niet door Firefox geïmplementeerd." ],
      "\"{{api}}\" has been removed in Manifest Version 3 (`manifest_version` property)":[ "‘{{api}}’ is verwijderd in Manifest versie 3 (eigenschap ‘manifest_version’)" ],
      "{{api}} is deprecated":[ "{{api}} is verouderd" ],
      "This API has been deprecated by Firefox.":[ "Deze API is in Firefox verouderd." ],
      "Content script file could not be found.":[ "Inhoudsscriptbestand kon niet worden gevonden." ],
      "\"{{api}}\" is deprecated or unimplemented":[ "‘{{api}}’ is verouderd of niet geïmplementeerd" ],
      "Content script file could not be found":[ "Inhoudsscriptbestand kon niet worden gevonden" ],
      "\"%(api)s\" can cause issues when loaded temporarily":[ "‘%(api)s’ kan problemen veroorzaken als het tijdelijk wordt geladen" ],
      "This API can cause issues when loaded temporarily using about:debugging in Firefox unless you specify \"browser_specific_settings.gecko.id\" in the manifest. Please see: https://mzl.la/2hizK4a for more.":[ "Deze API kan problemen veroorzaken wanneer deze tijdelijk wordt geladen met about:debugging in Firefox, tenzij u ‘browser_specific_settings.gecko.id’ opgeeft in het manifest. Zie: https://mzl.la/2hizK4a voor meer informatie." ],
      "{{api}} is not supported in Firefox version {{minVersion}}":[ "{{api}} wordt niet ondersteund in Firefox versie {{minVersion}}" ],
      "This API is not implemented by the given minimum Firefox version":[ "Deze API is niet door de opgegeven minimumversie van Firefox geïmplementeerd" ],
      "{{api}} is not supported in Firefox for Android version {{minVersion}}":[ "{{api}} wordt niet ondersteund in Firefox voor Android versie {{minVersion}}" ],
      "This API is not implemented by the given minimum Firefox for Android version":[ "Deze API is niet door de opgegeven minimumversie van Firefox voor Android geïmplementeerd" ],
      "Content script file name should not be empty.":[ "Naam van inhoudsscriptbestand mag niet leeg zijn." ],
      "Content script file name should not be empty":[ "Naam van inhoudsscriptbestand mag niet leeg zijn" ],
      "\"%(method)s\" called with a non-literal uri":[ "‘%(method)s’ aangeroepen met een niet-letterlijke uri" ],
      "Calling \"%(method)s\" with variable parameters can result in potential security vulnerabilities if the variable contains a remote URI. Consider using 'window.open' with the 'chrome=no' flag.":[ "Het aanroepen van ‘%(method)s’ met variabele parameters kan resulteren in potentiële beveiligingsproblemen als de variabele een externe URI bevat. Overweeg om ‘window.open’ te gebruiken met de vlag ‘chrome=no’." ],
      "\"%(method)s\" called with non-local URI":[ "‘%(method)s’ aangeroepen met niet-lokale URI" ],
      "Calling \"%(method)s\" with a non-local URI will result in the dialog being opened with chrome privileges.":[ "Het aanroepen van ‘%(method)s’ met een niet-lokale URI zal resulteren in het openen van het dialoogvenster met chromeprivileges." ],
      "JavaScript syntax error":[ "JavaScript-syntaxisfout" ],
      "There is a JavaScript syntax error in your code, which might be related to some experimental JavaScript features that aren't an official part of the language specification and therefore not supported yet. The validation cannot continue on this file.":[ "Er zit een JavaScript-syntaxisfout in uw code, die mogelijk verband houdt met enkele experimentele JavaScript-functies die geen officieel onderdeel zijn van de taalspecificatie en daarom nog niet worden ondersteund. De validatie kan niet doorgaan op dit bestand." ],
      "Evaluation of strings as code can lead to security vulnerabilities and performance issues, even in the most innocuous of circumstances. Please avoid using `eval` and the `Function` constructor when at all possible.":[ "Evaluatie van strings als code kan leiden tot beveiligingsproblemen en prestatieproblemen, zelfs in de meest onschadelijke omstandigheden. Vermijd indien mogelijk het gebruik van ‘eval’ en de ‘Function’-constructor." ],
      "setTimeout, setInterval and execScript functions should be called only with function expressions as their first argument":[ "De functies setTimeout, setInterval en execScript mogen alleen worden aangeroepen met functie-expressies als hun eerste argument" ],
      "Unexpected global passed as an argument":[ "Onverwachte globale variabele doorgegeven als argument" ],
      "Passing a global as an argument is not recommended. Please make this a var instead.":[ "Het doorgeven van een globale waarde als argument wordt niet aanbevolen. Maak hier een var van." ],
      "Use of document.write strongly discouraged.":[ "Gebruik van document.write wordt sterk afgeraden." ],
      "document.write will fail in many circumstances when used in extensions, and has potentially severe security repercussions when used improperly. Therefore, it should not be used.":[ "document.write zal in veel gevallen mislukken bij gebruik in extensies, en heeft mogelijk ernstige gevolgen voor de veiligheid bij onjuist gebruik. Daarom mag het niet worden gebruikt." ],
      "Banned 3rd-party JS library":[ "Geblokkeerde JS-bibliotheek van derden" ],
      "Your add-on uses a JavaScript library we consider unsafe. Read more: https://bit.ly/1TRIyZY":[ "Uw add-on gebruikt een JavaScript-bibliotheek die wij als onveilig beschouwen. Meer info: https://bit.ly/1TRIyZY" ],
      "Your JSON contains block comments.":[ "Uw JSON bevat blokcommentaren." ],
      "Only line comments (comments beginning with \"//\") are allowed in JSON files. Please remove block comments (comments beginning with \"/*\")":[ "Alleen regelopmerkingen (opmerkingen die beginnen met ‘//’) zijn toegestaan in JSON-bestanden. Verwijder blokopmerkingen (opmerkingen beginnend met ‘/*’)" ],
      "Duplicate keys are not allowed in JSON files.":[ "Dubbele sleutels zijn niet toegestaan in JSON-bestanden." ],
      "Duplicate key found in JSON file.":[ "Dubbele sleutel gevonden in JSON-bestand." ],
      "Your JSON is not valid.":[ "Uw JSON is niet geldig." ],
      "Your JSON file could not be parsed.":[ "Uw JSON-bestand kon niet worden geparset." ],
      "Reserved filename found.":[ "Gereserveerde bestandsnaam gevonden." ],
      "Files whose names are reserved have been found in the add-on. Please refrain from using them and rename your files.":[ "Bestanden waarvan de naam is gereserveerd zijn gevonden in de add-on. Gebruik deze niet en geef uw bestanden een andere naam." ],
      "The package is invalid. It may contain entries using invalid characters, as an example using '\\' as a path separator is not allowed in Firefox. Try to recreate your add-on package (ZIP) and make sure all entries are using '/' as the path separator.":[ "Het pakket is ongeldig. Het kan vermeldingen bevatten met ongeldige tekens, bijvoorbeeld het gebruik van  ‘\\’ als padscheidingsteken is niet toegestaan in Firefox. Probeer uw add-on-pakket (ZIP) opnieuw te maken en zorg ervoor dat alle entries ‘/’ gebruiken als padscheidingsteken." ],
      "We were unable to decompress the zip file.":[ "We konden het zip-bestand niet uitpakken." ],
      "manifest.json was not found":[ "manifest.json is niet gevonden" ],
      "No manifest.json was found at the root of the extension. The package file must be a ZIP of the extension's files themselves, not of the containing directory. See: https://mzl.la/2r2McKv for more on packaging.":[ "Er is geen manifest.json gevonden in de hoofdmap van de extensie. Het pakketbestand moet een ZIP zijn van de bestanden van de extensie zelf, niet van de bevattende map. Zie: https://mzl.la/2r2McKv voor meer informatie over verpakkingen." ],
      "File is too large to parse.":[ "Bestand is te groot om te parsen." ],
      "This file is not binary and is too large to parse. Files larger than %(maxFileSizeToParseMB)sMB will not be parsed. Consider moving large lists of data out of JavaScript files and into JSON files, or splitting very large files into smaller ones.":[ "Dit bestand is niet binair en te groot om te ontleden. Bestanden groter dan %(maxFileSizeToParseMB)sMB worden niet ontleed. Overweeg grote lijsten met gegevens uit JavaScript-bestanden naar JSON-bestanden te verplaatsen, of zeer grote bestanden op te splitsen in kleinere." ],
      "Hidden file flagged":[ "Verborgen bestand gemarkeerd" ],
      "Hidden files complicate the review process and can contain sensitive information about the system that generated the add-on. Please modify the packaging process so that these files aren't included.":[ "Verborgen bestanden bemoeilijken het beoordelingsproces en kunnen gevoelige gegevens bevatten over het systeem dat de add-on heeft gegenereerd. Wijzig het verpakkingsproces, zodat deze bestanden niet worden opgenomen." ],
      "Package contains duplicate entries":[ "Pakket bevat dubbele vermeldingen" ],
      "Flagged filename found":[ "Gemarkeerde bestandsnaam gevonden" ],
      "Files were found that are either unnecessary or have been included unintentionally. They should be removed.":[ "Er zijn bestanden gevonden die overbodig zijn of onbedoeld zijn toegevoegd. Ze moeten worden verwijderd." ],
      "The package contains multiple entries with the same name. This practice has been banned. Try unzipping and re-zipping your add-on package and try again.":[ "Het pakket bevat meerdere entries met dezelfde naam. Dit is verboden. Probeer uw add-onpakket uit te pakken en opnieuw te zippen en probeer het opnieuw." ],
      "Flagged file extensions found":[ "Gemarkeerde bestandsextensies gevonden" ],
      "Flagged file type found":[ "Gemarkeerd bestandstype gevonden" ],
      "Files whose names end with flagged extensions have been found in the add-on. The extension of these files are flagged because they usually identify binary components. Please see https://bit.ly/review-policy for more information on the binary content review process.":[ "Bestanden waarvan de naam eindigt op gemarkeerde extensies zijn gevonden in de add-on. De extensie van deze bestanden is gemarkeerd, omdat ze meestal binaire componenten identificeren. Zie https://bit.ly/review-policy voor meer informatie over het beoordelingsproces van binaire inhoud." ],
      "Package already signed":[ "Pakket al ondertekend" ],
      "Add-ons which are already signed will be re-signed when published on AMO. This will replace any existing signatures on the add-on.":[ "Add-ons die al zijn ondertekend, worden opnieuw ondertekend wanneer ze op AMO worden gepubliceerd. Dit zal alle bestaande handtekeningen op de add-on vervangen." ],
      "Firefox add-ons are not allowed to run coin miners.":[ "Firefox-add-ons mogen geen coinminers uitvoeren." ],
      "We do not allow coinminer scripts to be run inside WebExtensions. See https://github.com/mozilla/addons-linter/issues/1643 for more details.":[ "We staan niet toe dat coinminer-scripts worden uitgevoerd binnen WebExtensions. Zie https://github.com/mozilla/addons-linter/issues/1643 voor meer details." ],
      "String name is reserved for a predefined message":[ "Naam van tekenreeks is voor een vooraf gedefinieerd bericht gereserveerd" ],
      "String names starting with @@ get translated to built-in constants (https://mzl.la/2BL9ZjE).":[ "Stringnamen die beginnen met @@ worden vertaald naar ingebouwde constanten (https://mzl.la/2BL9ZjE)." ],
      "String name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2Eztyi5).":[ "Stringnaam mag alleen alfanumerieke tekens, _ en @ bevatten (https://mzl.la/2Eztyi5)." ],
      "Placeholder for message is missing":[ "Plaatshouder voor bericht ontbreekt" ],
      "A placeholder used in the message is not defined.":[ "Een in het bericht gebruikte plaatshouder is niet gedefinieerd." ],
      "Placeholder name contains invalid characters":[ "Naam van plaatshouder bevat ongeldige tekens" ],
      "Placeholder name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2ExbYez).":[ "Plaatshoudernaam mag alleen alfanumerieke tekens, _ en @ bevatten (https://mzl.la/2ExbYez)." ],
      "Placeholder is missing the content property":[ "Plaatshouder bevat geen inhoudseigenschap" ],
      "A placeholder needs a content property defining the replacement of it (https://mzl.la/2DT1MQd)":[ "Een plaatshouder heeft een inhoudseigenschap nodig die de vervanging ervan definieert (https://mzl.la/2DT1MQd)" ],
      "Translation string is missing the message property":[ "Te vertalen tekenreeks bevat geen berichteigenschap" ],
      "No \"message\" message property is set for a string (https://mzl.la/2DSBTjA).":[ "Geen berichteigenschap ‘message’ voor een tekenreeks ingesteld (https://mzl.la/2DSBTjA)." ],
      "The field is required.":[ "Het veld is vereist." ],
      "See https://mzl.la/1ZOhoEN (MDN Docs) for more information.":[ "Zie https://mzl.la/1ZOhoEN (MDN Docs) voor meer informatie." ],
      "The permission type is unsupported.":[ "Het toestemmingstype wordt niet ondersteund." ],
      "See https://mzl.la/1R1n1t0 (MDN Docs) for more information.":[ "Zie https://mzl.la/1R1n1t0 (MDN Docs) voor meer informatie." ],
      "See https://mzl.la/2Qn0fWC (MDN Docs) for more information.":[ "Zie https://mzl.la/2Qn0fWC (MDN Docs) voor meer informatie." ],
      "See https://mzl.la/3Woeqv4 (MDN Docs) for more information.":[ "Zie https://mzl.la/3Woeqv4 (MDN Docs) voor meer informatie." ],
      "Unknown permission.":[ "Onbekende toestemming." ],
      "%(instancePath)s: the following privileged permissions are only allowed in privileged extensions: %(privilegedPermissions)s.":[ "%(instancePath)s: de volgende geprivilegieerde machtigingen zijn alleen toegestaan in geprivilegieerde extensies: %(privilegedPermissions)s." ],
      "Invalid host permission.":[ "Onbekende hosttoestemming." ],
      "Invalid install origin.":[ "Onbekende installatieoorsprong." ],
      "Invalid install origin. A valid origin has - only - a scheme, hostname and optional port. See https://mzl.la/3TEbqbE (MDN Docs) for more information.":[ "Ongeldige installatiebron. Een geldige bron heeft – alleen – een schema, hostnaam en optionele poort. Zie https://mzl.la/3TEbqbE (MDN Docs) voor meer informatie." ],
      "The field is invalid.":[ "Het veld is ongeldig." ],
      "\"manifest_version\" in the manifest.json is not a valid value":[ "‘manifest_version’ in het bestand manifest.json is geen geldige waarde" ],
      "See https://mzl.la/20PenXl (MDN Docs) for more information.":[ "Zie https://mzl.la/20PenXl (MDN Docs) voor meer informatie." ],
      "\"%(property)s\" allows remote code execution in manifest.json":[ "‘%(property)s’ maakt uitvoering van externe code mogelijk in manifest.json" ],
      "A custom \"%(property)s\" needs additional review.":[ "Een aangepaste ‘%(property)s’ heeft aanvullende beoordeling nodig." ],
      "\"%(property)s\" allows 'eval', which has strong security and performance implications.":[ "‘%(property)s’ staat ‘eval’ toe, wat grote implicaties heeft voor veiligheid en prestaties." ],
      "In most cases the same result can be achieved differently, therefore it is generally prohibited":[ "In de meeste gevallen kan hetzelfde resultaat op een andere manier worden bereikt, daarom is het over het algemeen verboden" ],
      "The \"name\" property must be a string with no leading/trailing whitespaces.":[ "De eigenschap ‘name’ moet een tekenreeks zijn zonder spaties aan het begin/einde." ],
      "See http://mzl.la/1STmr48 (MDN Docs) for more information.":[ "Zie http://mzl.la/1STmr48 (MDN Docs) voor meer informatie." ],
      "\"update_url\" is not allowed.":[ "‘update_url’ is niet toegestaan." ],
      "\"applications.gecko.update_url\" or \"browser_specific_settings.gecko.update_url\" are not allowed for Mozilla-hosted add-ons.":[ "‘applications.gecko.update_url’ of ‘browser_specific_settings.gecko.update_url’ zijn niet toegestaan voor door Mozilla gehoste add-ons." ],
      "The \"update_url\" property is not used by Firefox.":[ "De eigenschap ‘update_url’ wordt niet door Firefox gebruikt." ],
      "The \"update_url\" is not used by Firefox in the root of a manifest; your add-on will be updated via the Add-ons site and not your \"update_url\". See: https://mzl.la/25zqk4O":[ "De ‘update_url’ wordt door Firefox niet gebruikt in de hoofdmap van een manifest; uw add-on wordt bijgewerkt via de Add-ons-website en niet via uw ‘update_url’. Zie: https://mzl.la/25zqk4O" ],
      "\"strict_max_version\" not required.":[ "‘strict_max_version’ niet vereist." ],
      "\"strict_max_version\" shouldn't be used unless the add-on is expected not to work with future versions of Firefox.":[ "‘strict_max_version’ mag niet worden gebruikt, tenzij wordt verwacht dat de add-on niet zal werken met toekomstige versies van Firefox." ],
      "Manifest Version 3 is not fully supported on Firefox for Android.":[ "Manifest versie 3 wordt niet volledig ondersteund in Firefox voor Android." ],
      "No \"%(property)s\" property found in manifest.json":[ "Geen eigenschap ‘%(property)s’ gevonden in manifest.json" ],
      "\"%(property)s\" is required":[ "‘%(property)s’ is verplicht" ],
      "An icon defined in the manifest could not be found in the package.":[ "Een in het manifest gedefinieerd pictogram kon niet in het pakket worden gevonden." ],
      "Icon could not be found at \"%(path)s\".":[ "Pictogram kon niet worden gevonden in ‘%(path)s’." ],
      "A background script defined in the manifest could not be found.":[ "Een in het manifest gedefinieerd achtergrondscript kon niet worden gevonden." ],
      "A background page defined in the manifest could not be found.":[ "Een in het manifest gedefinieerde achtergrondpagina kon niet worden gevonden." ],
      "Background script could not be found at \"%(path)s\".":[ "Achtergrondscript kon niet worden gevonden in ‘%(path)s’." ],
      "Background page could not be found at \"%(path)s\".":[ "Achtergrondpagina kon niet worden gevonden in ‘%(path)s’." ],
      "A content script defined in the manifest could not be found.":[ "Een in het manifest gedefinieerd inhoudsscript kon niet worden gevonden." ],
      "A content script css file defined in the manifest could not be found.":[ "Een in het manifest gedefinieerd CSS-bestand van inhoudsscript kon niet worden gevonden." ],
      "Content script defined in the manifest could not be found at \"%(path)s\".":[ "In het manifest gedefinieerd inhoudsscript kon niet worden gevonden in ‘%(path)s’." ],
      "Content script css file defined in the manifest could not be found at \"%(path)s\".":[ "In het manifest gedefinieerd CSS-bestand van inhoudsscript kon niet worden gevonden in ‘%(path)s’." ],
      "A dictionary file defined in the manifest could not be found.":[ "Een in het manifest gedefinieerd bibliotheekbestand kon niet worden gevonden." ],
      "Dictionary file defined in the manifest could not be found at \"%(path)s\".":[ "In het manifest gedefinieerd bibliotheekbestand kon niet worden gevonden in ‘%(path)s’." ],
      "The manifest contains multiple dictionaries.":[ "Het manifest bevat meerdere woordenboeken." ],
      "Multiple dictionaries were defined in the manifest, which is unsupported.":[ "Er zijn meerdere woordenboeken gedefinieerd in het manifest, wat niet wordt ondersteund." ],
      "The manifest contains a dictionaries object, but it is empty.":[ "Het manifest bevat een woordenboek-object, maar het is leeg." ],
      "A dictionaries object was defined in the manifest, but it was empty.":[ "Er is een woordenboek-object gedefinieerd in het manifest, maar het is leeg." ],
      "The manifest contains a dictionary but no id property.":[ "Het manifest bevat een woordenboek maar geen id-eigenschap." ],
      "A dictionary was found in the manifest, but there was no id set.":[ "Er is een woordenboek gevonden in het manifest, maar er is geen id ingesteld." ],
      "Please refer to https://github.com/mozilla-extensions/xpi-manifest to learn more about privileged extensions and signing.":[ "Raadpleeg https://github.com/mozilla-extensions/xpi-manifest voor meer informatie over geprivilegieerde extensies en ondertekening." ],
      "Forbidden content found in add-on.":[ "Verboden inhoud gevonden in add-on." ],
      "This add-on contains forbidden content.":[ "Deze add-on bevat verboden inhoud." ],
      "Icons must be square.":[ "Pictogrammen moeten vierkant zijn." ],
      "Icon at \"%(path)s\" must be square.":[ "Pictogram in ‘%(path)s’ moet vierkant zijn." ],
      "The size of the icon does not match the manifest.":[ "De grootte van het pictogram komt niet overeen met het manifest." ],
      "Expected icon at \"%(path)s\" to be %(expected)d pixels wide but was %(actual)d.":[ "Verwacht pictogram bij ‘%(path)s’ is %(expected)d pixels breed, maar was %(actual)d." ],
      "\"%(fieldName)s\" is ignored for non-privileged add-ons.":[ "‘%(fieldName)s’ wordt genegeerd voor niet-geprivilegieerde add-ons." ],
      "Corrupt image file":[ "Beschadigd afbeeldingsbestand" ],
      "Expected icon file at \"%(path)s\" is corrupted":[ "Verwacht pictogrambestand in ‘%(path)s’ is beschadigd" ],
      "This property has been deprecated.":[ "Deze eigenschap is verouderd." ],
      "This theme LWT alias has been removed in Firefox 70.":[ "Het LWT-alias van dit thema is verwijderd in Firefox 70." ],
      "See https://mzl.la/2T11Lkc (MDN Docs) for more information.":[ "Zie https://mzl.la/2T11Lkc (MDN Docs) voor meer informatie." ],
      "Theme image for \"%(type)s\" could not be found at \"%(path)s\"":[ "Thema-afbeelding voor ‘%(type)s’ kon niet worden gevonden in ‘%(path)s’" ],
      "\"%(fieldName)s\" manifest field is only used for privileged and temporarily installed extensions.":[ "Het manifestveld ‘%(fieldName)s’ wordt alleen gebruikt voor geprivilegieerde en tijdelijk geïnstalleerde extensies." ],
      "Corrupted theme image file":[ "Beschadigd thema-afbeeldingsbestand" ],
      "Theme image file at \"%(path)s\" is corrupted":[ "Thema-afbeeldingsbestand in ‘%(path)s’ is beschadigd" ],
      "Theme image file has an unsupported file extension":[ "Thema-afbeeldingsbestand heeft een niet-ondersteunde bestandsextensie" ],
      "Theme image file at \"%(path)s\" has an unsupported file extension":[ "Thema-afbeeldingsbestand op ‘%(path)s’ heeft een niet-ondersteunde bestandsextensie" ],
      "Theme image file has an unsupported mime type":[ "Thema-afbeeldingsbestand heeft een niet-ondersteund mime-type" ],
      "Theme image file at \"%(path)s\" has the unsupported mime type \"%(mime)s\"":[ "Thema-afbeeldingsbestand op ‘%(path)s’ heeft het niet-ondersteunde mime-type ‘%(mime)s’" ],
      "Theme image file mime type does not match its file extension":[ "Mime-type van het thema-afbeeldingsbestand komt niet overeen met de bestandsextensie" ],
      "Theme image file extension at \"%(path)s\" does not match its actual mime type \"%(mime)s\"":[ "Thema-afbeeldingsbestand op ‘%(path)s’ komt niet overeen met het werkelijke mime-type ‘%(mime)s’" ],
      "The \"default_locale\" is missing localizations.":[ "De ‘default_locale’ mist lokalisaties." ],
      "The \"default_locale\" value is specified in the manifest, but no matching \"messages.json\" in the \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "De waarde ‘default_locale’ is opgegeven in het manifest, maar er bestaat geen overeenkomende ‘messages.json’ in de map ‘_locales’. Zie: https://mzl.la/2hjcaEE" ],
      "The \"default_locale\" is missing but \"_locales\" exist.":[ "De ‘default_locale’ ontbreekt, maar ‘_locales’ bestaat." ],
      "The \"default_locale\" value is not specifed in the manifest, but a \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "De waarde ‘default_locale’ wordt niet opgegeven in het manifest, maar er bestaat een map ‘_locales’. Zie: https://mzl.la/2hjcaEE" ],
      "Unsupported image extension":[ "Niet-ondersteunde afbeeldingsextensie" ],
      "Icons should be one of JPG/JPEG, WebP, GIF, PNG or SVG.":[ "Pictogrammen dienen JPG/JPEG, WebP, GIF, PNG of SVG te zijn." ],
      "\"applications\" property overridden by \"browser_specific_settings\" property":[ "Eigenschap ‘applications’ wordt overschreven door de eigenschap ‘browser_specific_settings’" ],
      "The \"applications\" property is being ignored because it is superseded by the \"browser_specific_settings\" property which is also defined in your manifest. Consider removing applications.":[ "De eigenschap ‘applications’ wordt genegeerd, omdat deze wordt vervangen door de eigenschap ‘browser_specific_settings’ die ook in uw manifest is gedefinieerd. Overweeg om toepassingen te verwijderen." ],
      "Empty language directory":[ "Lege taalmap" ],
      "messages.json file missing in \"%(path)s\"":[ "Bestand messages.json ontbreekt in ‘%(path)s’" ],
      "Manifest key not supported by the specified minimum Firefox version":[ "Manifestsleutel niet ondersteund door de opgegeven minimale Firefox-versie" ],
      "\"strict_min_version\" requires Firefox %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "‘strict_min_version’ vereist Firefox %(minVersion)s, dat werd uitgebracht voordat versie %(versionAdded)s ondersteuning voor ‘%(key)s’ introduceerde." ],
      "\"%(fieldName)s\" is not supported in manifest versions %(versionRange)s.":[ "‘%(fieldName)s’ wordt niet ondersteund in manifestversies %(versionRange)s." ],
      "Permission not supported by the specified minimum Firefox version":[ "Toestemming niet ondersteund door de opgegeven minimale Firefox-versie" ],
      "\"%(fieldName)s\" is not supported.":[ "‘%(fieldName)s’ wordt niet ondersteund." ],
      "Manifest key not supported by the specified minimum Firefox for Android version":[ "Manifestsleutel niet ondersteund door de opgegeven minimale Firefox voor Android-versie" ],
      "\"strict_min_version\" requires Firefox for Android %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "‘strict_min_version’ vereist Firefox voor Android %(minVersion)s, dat werd uitgebracht voordat versie %(versionAdded)s ondersteuning voor ‘%(key)s’ introduceerde." ],
      "Permission not supported by the specified minimum Firefox for Android version":[ "Toestemming niet ondersteund door de opgegeven minimale Firefox voor Android-versie" ],
      "Linking to \"addons.mozilla.org\" is not allowed":[ "Koppelen naar ‘addons.mozilla.org’ is niet toegestaan" ],
      "Links directing to \"addons.mozilla.org\" are not allowed to be used for homepage":[ "Koppelingen die verwijzen naar ‘addons.mozilla.org’ mogen niet voor de startpagina worden gebruikt" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above":[ "De machtiging ‘%(permission)s’ vereist dat ‘strict_min_version’ is ingesteld op ‘%(minFirefoxVersion)s’ of hoger" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above. Please update your manifest.json version to specify a minimum Firefox version.":[ "De machtiging ‘%(permission)s’ vereist dat ‘strict_min_version’ is ingesteld op ‘%(minFirefoxVersion)s’ of hoger. Werk uw manifest.json-versie bij om een minimale Firefox-versie op te geven." ],
      "The extension ID is required in Manifest Version 3 and above.":[ "De extensie-ID is vereist in manifest versie 3 en hoger." ],
      "See https://mzl.la/3PLZYdo for more information.":[ "Zie https://mzl.la/3PLZYdo voor meer informatie." ],
      "%(instancePath)s: Privileged extensions should declare privileged permissions.":[ "%(instancePath)s: geprivilegieerde extensies moeten geprivilegieerde machtigingen declareren." ],
      "This extension does not declare any privileged permission. It does not need to be signed with the privileged certificate. Please upload it directly to https://addons.mozilla.org/.":[ "Deze extensie declareert geen geprivilegieerde machtiging. Het hoeft niet te worden ondertekend met het geprivilegieerde certificaat. Upload het rechtstreeks naar https://addons.mozilla.org/." ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for privileged extensions.":[ "%(instancePath)s: de machtiging ‘mozillaAddons’ is vereist voor geprivilegieerde extensies." ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for extensions that include privileged manifest fields.":[ "%(instancePath)s: de machtiging ‘mozillaAddons’ is vereist voor extensies die geprivilegieerde manifestvelden bevatten." ],
      "%(instancePath)s: privileged manifest fields are only allowed in privileged extensions.":[ "%(instancePath)s: geprivilegieerde manifestvelden zijn alleen toegestaan in geprivilegieerde extensies." ],
      "This extension does not include the \"mozillaAddons\" permission, which is required for privileged extensions.":[ "Deze extensie bevat niet de machtiging ‘mozillaAddons’, die vereist is voor geprivilegieerde extensies." ],
      "Cannot use actions in hidden add-ons.":[ "Acties in verborgen add-ons gebruiken is niet mogelijk." ],
      "The hidden and browser_action/page_action (or action in Manifest Version 3 and above) properties are mutually exclusive.":[ "De eigenschappen hidden en browser_action/page_action (of action in Manifest Version 3 en hoger) sluiten elkaar wederzijds uit." ],
      "Use \"browser_specific_settings\" instead of \"applications\".":[ "Gebruik ‘browser_specific_settings’ in plaats van ‘applications’." ],
      "The \"applications\" property in the manifest is deprecated and will no longer be accepted in Manifest Version 3 and above.":[ "De eigenschap ‘applications’ in het manifest is verouderd en wordt niet langer geaccepteerd in manifestversie 3 en hoger." ],
      "\"applications\" is no longer allowed in Manifest Version 3 and above.":[ "‘applications’ is niet langer toegestaan in manifestversie 3 en hoger." ],
      "The \"applications\" property in the manifest is no longer allowed in Manifest Version 3 and above. Use \"browser_specific_settings\" instead.":[ "De eigenschap ‘applications’ in het manifest is niet langer toegestaan in manifestversie 3 en hoger. Gebruik in plaats daarvan ‘browser_specific_settings’." ],
      "The version string should be simplified because it won't be compatible with Manifest Version 3 and above.":[ "De versiestring moet worden vereenvoudigd, omdat deze niet compatibel is met manifestversie 3 en hoger." ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros will no longer be allowed. Letters will no longer be allowed either. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "De versie moet een string zijn met 1 tot 4 getallen, gescheiden door punten. Elk getal moet uit maximaal 9 cijfers bestaan en voorloopnullen zijn niet langer toegestaan. Letters zijn ook niet meer toegestaan. Zie https://mzl.la/3h3mCRu (MDN Docs) voor meer informatie." ],
      "The version string should be simplified.":[ "De versiestring moet worden vereenvoudigd." ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros are not allowed. Letters are no longer allowed. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "De versie moet een string zijn met 1 tot 4 getallen, gescheiden door punten. Elk getal moet uit maximaal 9 cijfers bestaan en voorloopnullen zijn niet toegestaan. Letters zijn niet meer toegestaan. Zie https://mzl.la/3h3mCRu (MDN Docs) voor meer informatie." ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported in manifest versions %(versionRange)s.":[ "/%(fieldName)s: ‘%(permissionName)s’ wordt niet ondersteund in manifestversies %(versionRange)s." ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported.":[ "/%(fieldName)s: ‘%(permissionName)s’ wordt niet ondersteund." ] } } }