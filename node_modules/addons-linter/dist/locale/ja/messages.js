module.exports = { domain:"messages",
  locale_data:{ messages:{ "":{ domain:"messages",
        plural_forms:"nplurals=1; plural=0;",
        lang:"ja" },
      "Validation Summary:":[ "検証結果:" ],
      Code:[ "コード" ],
      Message:[ "メッセージ" ],
      Description:[ "説明" ],
      File:[ "ファイル" ],
      Line:[ "行" ],
      Column:[ "列" ],
      "Invalid manifest version range requested: --min-manifest-version (currently set to %(minManifestVersion)s) should not be greater than --max-manifest-version (currently set to %(maxManifestVersion)s).":[ "" ],
      "Your FTL is not valid.":[ "FTL が正しくありません。" ],
      "Your FTL file could not be parsed.":[ "FTL ファイルを解析できませんでした。" ],
      "Remote scripts are not allowed as per the Add-on Policies.":[ "アドオンポリシーに従いリモートスクリプトは許可されていません。" ],
      "Please include all scripts in the add-on. For more information, refer to https://mzl.la/2uEOkYp.":[ "" ],
      "Inline scripts blocked by default":[ "インラインスクリプトは既定でブロックされます" ],
      "Default CSP rules prevent inline JavaScript from running (https://mzl.la/2pn32nd).":[ "" ],
      "Unadvised 3rd-party JS library":[ "不明なサードパーティ JS ライブラリ" ],
      "Your add-on uses a JavaScript library we do not recommend. Read more: https://bit.ly/1TRIyZY":[ "" ],
      "Known JS library detected":[ "既知の JS ライブラリーが検出されました" ],
      "JavaScript libraries are discouraged for simple add-ons, but are generally accepted.":[ "" ],
      "Due to both security and performance concerns, this may not be set using dynamic values which have not been adequately sanitized. This can lead to security issues or fairly serious performance degradation.":[ "" ],
      "{{api}} is not supported":[ "{{api}} はサポートされていません" ],
      "This API has not been implemented by Firefox.":[ "この API は Firefox に実装されていません。" ],
      "\"{{api}}\" has been removed in Manifest Version 3 (`manifest_version` property)":[ "\"{{api}}\" はマニフェストバージョン 3 (`manifest_version` プロパティ) で削除されました" ],
      "{{api}} is deprecated":[ "{{api}} は非推奨です" ],
      "This API has been deprecated by Firefox.":[ "この API は Firefox で推奨されていません。" ],
      "Content script file could not be found.":[ "コンテンツスクリプトファイルが見つかりませんでした。" ],
      "\"{{api}}\" is deprecated or unimplemented":[ "\"{{api}}\" は非推奨または未実装です" ],
      "Content script file could not be found":[ "コンテンツスクリプトファイルが見つかりませんでした" ],
      "\"%(api)s\" can cause issues when loaded temporarily":[ "" ],
      "This API can cause issues when loaded temporarily using about:debugging in Firefox unless you specify \"browser_specific_settings.gecko.id\" in the manifest. Please see: https://mzl.la/2hizK4a for more.":[ "" ],
      "{{api}} is not supported in Firefox version {{minVersion}}":[ "{{api}} は Firefox バージョン {{minVersion}} でサポートされていません" ],
      "This API is not implemented by the given minimum Firefox version":[ "この API は指定された最低バージョンの Firefox に実装されていません" ],
      "{{api}} is not supported in Firefox for Android version {{minVersion}}":[ "{{api}} は Android 版 Firefox バージョン {{minVersion}} でサポートされていません" ],
      "This API is not implemented by the given minimum Firefox for Android version":[ "この API は指定された最低バージョンの Android 版 Firefox に実装されていません" ],
      "Content script file name should not be empty.":[ "コンテンツスクリプトファイル名は空欄にできません。" ],
      "Content script file name should not be empty":[ "コンテンツスクリプトファイル名は空欄にできません" ],
      "\"%(method)s\" called with a non-literal uri":[ "" ],
      "Calling \"%(method)s\" with variable parameters can result in potential security vulnerabilities if the variable contains a remote URI. Consider using 'window.open' with the 'chrome=no' flag.":[ "" ],
      "\"%(method)s\" called with non-local URI":[ "" ],
      "Calling \"%(method)s\" with a non-local URI will result in the dialog being opened with chrome privileges.":[ "" ],
      "JavaScript syntax error":[ "JavaScript 構文エラー" ],
      "There is a JavaScript syntax error in your code, which might be related to some experimental JavaScript features that aren't an official part of the language specification and therefore not supported yet. The validation cannot continue on this file.":[ "" ],
      "Evaluation of strings as code can lead to security vulnerabilities and performance issues, even in the most innocuous of circumstances. Please avoid using `eval` and the `Function` constructor when at all possible.":[ "" ],
      "setTimeout, setInterval and execScript functions should be called only with function expressions as their first argument":[ "" ],
      "Unexpected global passed as an argument":[ "予期せぬグローバル変数が引数として渡されています" ],
      "Passing a global as an argument is not recommended. Please make this a var instead.":[ "" ],
      "Use of document.write strongly discouraged.":[ "document.write の使用は避けてください。" ],
      "document.write will fail in many circumstances when used in extensions, and has potentially severe security repercussions when used improperly. Therefore, it should not be used.":[ "" ],
      "Banned 3rd-party JS library":[ "禁止されているサードパーティ JS ライブラリー" ],
      "Your add-on uses a JavaScript library we consider unsafe. Read more: https://bit.ly/1TRIyZY":[ "" ],
      "Your JSON contains block comments.":[ "JSON にブロックコメントが含まれています。" ],
      "Only line comments (comments beginning with \"//\") are allowed in JSON files. Please remove block comments (comments beginning with \"/*\")":[ "" ],
      "Duplicate keys are not allowed in JSON files.":[ "JSON ファイルには重複するキーを含められません。" ],
      "Duplicate key found in JSON file.":[ "" ],
      "Your JSON is not valid.":[ "JSON の形式が正しくありません。" ],
      "Your JSON file could not be parsed.":[ "JSON ファイルを解析できませんでした。" ],
      "Reserved filename found.":[ "予約済みのファイル名が見つかりました。" ],
      "Files whose names are reserved have been found in the add-on. Please refrain from using them and rename your files.":[ "" ],
      "The package is invalid. It may contain entries using invalid characters, as an example using '\\' as a path separator is not allowed in Firefox. Try to recreate your add-on package (ZIP) and make sure all entries are using '/' as the path separator.":[ "" ],
      "We were unable to decompress the zip file.":[ "zip ファイルを展開できませんでした。" ],
      "manifest.json was not found":[ "manifest.json が見つかりませんでした" ],
      "No manifest.json was found at the root of the extension. The package file must be a ZIP of the extension's files themselves, not of the containing directory. See: https://mzl.la/2r2McKv for more on packaging.":[ "" ],
      "File is too large to parse.":[ "ファイルサイズが大きすぎるため解析できません。" ],
      "This file is not binary and is too large to parse. Files larger than %(maxFileSizeToParseMB)sMB will not be parsed. Consider moving large lists of data out of JavaScript files and into JSON files, or splitting very large files into smaller ones.":[ "" ],
      "Hidden file flagged":[ "隠しファイルが特定されました" ],
      "Hidden files complicate the review process and can contain sensitive information about the system that generated the add-on. Please modify the packaging process so that these files aren't included.":[ "" ],
      "Package contains duplicate entries":[ "パッケージに重複したエントリーが含まれています" ],
      "Flagged filename found":[ "特定のファイル名が確認されました" ],
      "Files were found that are either unnecessary or have been included unintentionally. They should be removed.":[ "" ],
      "The package contains multiple entries with the same name. This practice has been banned. Try unzipping and re-zipping your add-on package and try again.":[ "" ],
      "Flagged file extensions found":[ "特定のファイル拡張子が確認されました" ],
      "Flagged file type found":[ "特定のファイル形式が確認されました" ],
      "Files whose names end with flagged extensions have been found in the add-on. The extension of these files are flagged because they usually identify binary components. Please see https://bit.ly/review-policy for more information on the binary content review process.":[ "" ],
      "Package already signed":[ "パッケージはすでに署名されています" ],
      "Add-ons which are already signed will be re-signed when published on AMO. This will replace any existing signatures on the add-on.":[ "" ],
      "Firefox add-ons are not allowed to run coin miners.":[ "Firefox アドオンで仮想通貨のマイニングを行うことは許可されていません。" ],
      "We do not allow coinminer scripts to be run inside WebExtensions. See https://github.com/mozilla/addons-linter/issues/1643 for more details.":[ "" ],
      "String name is reserved for a predefined message":[ "文字列名が定義済みメッセージ用に予約されています" ],
      "String names starting with @@ get translated to built-in constants (https://mzl.la/2BL9ZjE).":[ "" ],
      "String name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2Eztyi5).":[ "" ],
      "Placeholder for message is missing":[ "メッセージのプレースホルダーが見当たりません" ],
      "A placeholder used in the message is not defined.":[ "メッセージ内で使用されているプレースホルダーが定義されていません。" ],
      "Placeholder name contains invalid characters":[ "プレースホルダー名に不正な文字が含まれています" ],
      "Placeholder name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2ExbYez).":[ "" ],
      "Placeholder is missing the content property":[ "プレースホルダーにコンテンツプロパティが含まれていません" ],
      "A placeholder needs a content property defining the replacement of it (https://mzl.la/2DT1MQd)":[ "" ],
      "Translation string is missing the message property":[ "翻訳メッセージにメッセージプロパティが含まれていません" ],
      "No \"message\" message property is set for a string (https://mzl.la/2DSBTjA).":[ "文字列に「message」メッセージプロパティが設定されていません。(https://mzl.la/2DSBTjA)" ],
      "The field is required.":[ "この項目は必須です。" ],
      "See https://mzl.la/1ZOhoEN (MDN Docs) for more information.":[ "詳しい情報は、https://mzl.la/1ZOhoEN (MDN Docs) をご覧ください。" ],
      "The permission type is unsupported.":[ "この許可タイプには対応していません。" ],
      "See https://mzl.la/1R1n1t0 (MDN Docs) for more information.":[ "詳しくは https://mzl.la/1R1n1t0 (MDN Docs) をご覧ください。" ],
      "See https://mzl.la/2Qn0fWC (MDN Docs) for more information.":[ "詳しくは https://mzl.la/2Qn0fWC (MDN Docs) をご覧ください。" ],
      "See https://mzl.la/3Woeqv4 (MDN Docs) for more information.":[ "" ],
      "Unknown permission.":[ "不明な許可設定です。" ],
      "%(instancePath)s: the following privileged permissions are only allowed in privileged extensions: %(privilegedPermissions)s.":[ "" ],
      "Invalid host permission.":[ "無効なホスト権限です。" ],
      "Invalid install origin.":[ "無効なインストール元です。" ],
      "Invalid install origin. A valid origin has - only - a scheme, hostname and optional port. See https://mzl.la/3TEbqbE (MDN Docs) for more information.":[ "" ],
      "The field is invalid.":[ "項目が無効です。" ],
      "\"manifest_version\" in the manifest.json is not a valid value":[ "manifest.json 内の「manifest_version」の値が不正です" ],
      "See https://mzl.la/20PenXl (MDN Docs) for more information.":[ "詳しくは https://mzl.la/20PenXl (MDN Docs) をご覧ください。" ],
      "\"%(property)s\" allows remote code execution in manifest.json":[ "" ],
      "A custom \"%(property)s\" needs additional review.":[ "" ],
      "\"%(property)s\" allows 'eval', which has strong security and performance implications.":[ "" ],
      "In most cases the same result can be achieved differently, therefore it is generally prohibited":[ "" ],
      "The \"name\" property must be a string with no leading/trailing whitespaces.":[ "" ],
      "See http://mzl.la/1STmr48 (MDN Docs) for more information.":[ "詳しくは http://mzl.la/1STmr48 (MDN Docs) をご覧ください。" ],
      "\"update_url\" is not allowed.":[ "「update_url」は使用できません。" ],
      "\"applications.gecko.update_url\" or \"browser_specific_settings.gecko.update_url\" are not allowed for Mozilla-hosted add-ons.":[ "" ],
      "The \"update_url\" property is not used by Firefox.":[ "Firefox では「update_url」プロパティは使われていません。" ],
      "The \"update_url\" is not used by Firefox in the root of a manifest; your add-on will be updated via the Add-ons site and not your \"update_url\". See: https://mzl.la/25zqk4O":[ "" ],
      "\"strict_max_version\" not required.":[ "\"strict_max_version\" は必須ではありません。" ],
      "\"strict_max_version\" shouldn't be used unless the add-on is expected not to work with future versions of Firefox.":[ "" ],
      "Manifest Version 3 is not fully supported on Firefox for Android.":[ "" ],
      "No \"%(property)s\" property found in manifest.json":[ "" ],
      "\"%(property)s\" is required":[ "" ],
      "An icon defined in the manifest could not be found in the package.":[ "manifest で定義されたアイコンがパッケージ内に見つかりませんでした。" ],
      "Icon could not be found at \"%(path)s\".":[ "アイコンが \"%(path)s\" に見つかりませんでした。" ],
      "A background script defined in the manifest could not be found.":[ "manifest で定義されたバックグラウンドスクリプトが見つかりませんでした。" ],
      "A background page defined in the manifest could not be found.":[ "manifest で定義されたバックグラウンドページが見つかりませんでした。" ],
      "Background script could not be found at \"%(path)s\".":[ "バックグラウンドスクリプトが \"%(path)s\" に見つかりませんでした。" ],
      "Background page could not be found at \"%(path)s\".":[ "バックグラウンドページが \"%(path)s\" に見つかりませんでした。" ],
      "A content script defined in the manifest could not be found.":[ "manifest で定義されたコンテンツスクリプトが見つかりませんでした。" ],
      "A content script css file defined in the manifest could not be found.":[ "manifest で定義されたコンテンツスクリプト CSS ファイルが見つかりませんでした。" ],
      "Content script defined in the manifest could not be found at \"%(path)s\".":[ "マニフェストで定義されているコンテンツスクリプトが「%(path)s」に見つかりませんでした。" ],
      "Content script css file defined in the manifest could not be found at \"%(path)s\".":[ "マニフェストで定義されているコンテンツスクリプト CSS ファイルが「%(path)s」に見つかりませんでした。" ],
      "A dictionary file defined in the manifest could not be found.":[ "manifest で定義された辞書ファイルが見つかりませんでした。" ],
      "Dictionary file defined in the manifest could not be found at \"%(path)s\".":[ "manifest で定義された辞書ファイルが「%(path)s」に見つかりませんでした。" ],
      "The manifest contains multiple dictionaries.":[ "manifest に複数の辞書が含まれています。" ],
      "Multiple dictionaries were defined in the manifest, which is unsupported.":[ "manifest で複数の辞書が定義されていましたが、これはサポートされていません。" ],
      "The manifest contains a dictionaries object, but it is empty.":[ "manifest に辞書オブジェクトが含まれていますが、中身が空です。" ],
      "A dictionaries object was defined in the manifest, but it was empty.":[ "manifest で辞書オブジェクトが定義されていましたが、中身が空でした。" ],
      "The manifest contains a dictionary but no id property.":[ "manifest に辞書が含まれていますが、id プロパティがありません。" ],
      "A dictionary was found in the manifest, but there was no id set.":[ "manifest に辞書が見つかりましたが、id が設定されていませんでした。" ],
      "Please refer to https://github.com/mozilla-extensions/xpi-manifest to learn more about privileged extensions and signing.":[ "" ],
      "Forbidden content found in add-on.":[ "アドオン内に禁止されているコンテンツが見つかりました。" ],
      "This add-on contains forbidden content.":[ "このアドオンには禁止されているコンテンツが含まれています。" ],
      "Icons must be square.":[ "アイコンは正方形でなければなりません。" ],
      "Icon at \"%(path)s\" must be square.":[ "「%(path)s」にあるアイコンは正方形でなければなりません。" ],
      "The size of the icon does not match the manifest.":[ "アイコンのサイズがマニフェストと一致しません。" ],
      "Expected icon at \"%(path)s\" to be %(expected)d pixels wide but was %(actual)d.":[ "" ],
      "\"%(fieldName)s\" is ignored for non-privileged add-ons.":[ "" ],
      "Corrupt image file":[ "壊れた画像ファイル" ],
      "Expected icon file at \"%(path)s\" is corrupted":[ "「%(path)s」にあると期待されるアイコンファイルが壊れています" ],
      "This property has been deprecated.":[ "このプロパティは推奨されていません。" ],
      "This theme LWT alias has been removed in Firefox 70.":[ "このテーマ LWT エイリアスは Firefox 70 で削除されました。" ],
      "See https://mzl.la/2T11Lkc (MDN Docs) for more information.":[ "詳しくは https://mzl.la/2T11Lkc (MDN Docs) をご覧ください。" ],
      "Theme image for \"%(type)s\" could not be found at \"%(path)s\"":[ "「%(type)s」のテーマ画像が「%(path)s」に見つかりませんでした" ],
      "\"%(fieldName)s\" manifest field is only used for privileged and temporarily installed extensions.":[ "" ],
      "Corrupted theme image file":[ "壊れたテーマ画像ファイル" ],
      "Theme image file at \"%(path)s\" is corrupted":[ "「%(path)s」にあるテーマ画像ファイルが壊れています" ],
      "Theme image file has an unsupported file extension":[ "テーマ画像ファイルのファイル拡張子はサポートされていません" ],
      "Theme image file at \"%(path)s\" has an unsupported file extension":[ "「%(path)s」のテーマ画像ファイルのファイル拡張子はサポートされていません" ],
      "Theme image file has an unsupported mime type":[ "テーマ画像ファイルの MIME タイプはサポートされていません" ],
      "Theme image file at \"%(path)s\" has the unsupported mime type \"%(mime)s\"":[ "「%(path)s」のテーマ画像ファイルの MIME タイプ「%(mime)s」はサポートされていません" ],
      "Theme image file mime type does not match its file extension":[ "テーマ画像ファイルの MIME タイプがファイル拡張子と一致しません" ],
      "Theme image file extension at \"%(path)s\" does not match its actual mime type \"%(mime)s\"":[ "「%(path)s」のテーマ画像ファイルの拡張子が実際の MIME タイプ「%(mime)s」と一致しません" ],
      "The \"default_locale\" is missing localizations.":[ "「default_locale」でロケールが定義されていません。" ],
      "The \"default_locale\" value is specified in the manifest, but no matching \"messages.json\" in the \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "" ],
      "The \"default_locale\" is missing but \"_locales\" exist.":[ "「default_locale」が見当たらない一方で「_locales」は存在します" ],
      "The \"default_locale\" value is not specifed in the manifest, but a \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "" ],
      "Unsupported image extension":[ "非対応画像拡張子" ],
      "Icons should be one of JPG/JPEG, WebP, GIF, PNG or SVG.":[ "アイコンは、JPG/JPEG、WebP、GIF、PNG あるいは SVG のいずれかでなければなりません。" ],
      "\"applications\" property overridden by \"browser_specific_settings\" property":[ "\"applications\" プロパティは \"browser_specific_settings\" プロパティによって上書きされます" ],
      "The \"applications\" property is being ignored because it is superseded by the \"browser_specific_settings\" property which is also defined in your manifest. Consider removing applications.":[ "" ],
      "Empty language directory":[ "空の言語ディレクトリー" ],
      "messages.json file missing in \"%(path)s\"":[ "messages.json が「%(path)s」に存在しません" ],
      "Manifest key not supported by the specified minimum Firefox version":[ "manifest キーは指定された Firefox の最低バージョンでサポートされていません" ],
      "\"strict_min_version\" requires Firefox %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "" ],
      "\"%(fieldName)s\" is not supported in manifest versions %(versionRange)s.":[ "" ],
      "Permission not supported by the specified minimum Firefox version":[ "許可設定は指定された Firefox の最低バージョンでサポートされていません" ],
      "\"%(fieldName)s\" is not supported.":[ "" ],
      "Manifest key not supported by the specified minimum Firefox for Android version":[ "manifest キーは指定された Android 版 Firefox の最低バージョンでサポートされていません" ],
      "\"strict_min_version\" requires Firefox for Android %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "" ],
      "Permission not supported by the specified minimum Firefox for Android version":[ "許可設定は指定された Android 版 Firefox の最低バージョンでサポートされていません" ],
      "Linking to \"addons.mozilla.org\" is not allowed":[ "\"addons.mozilla.org\" へのリンクは許可されていません" ],
      "Links directing to \"addons.mozilla.org\" are not allowed to be used for homepage":[ "\"addons.mozilla.org\" へのリンクをホームページに使用することは許可されていません" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above":[ "" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above. Please update your manifest.json version to specify a minimum Firefox version.":[ "" ],
      "The extension ID is required in Manifest Version 3 and above.":[ "Manifest バージョン 3 以降では、拡張機能 ID が必要です。" ],
      "See https://mzl.la/3PLZYdo for more information.":[ "詳しくは https://mzl.la/3PLZYdo をご覧ください。" ],
      "%(instancePath)s: Privileged extensions should declare privileged permissions.":[ "" ],
      "This extension does not declare any privileged permission. It does not need to be signed with the privileged certificate. Please upload it directly to https://addons.mozilla.org/.":[ "" ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for privileged extensions.":[ "" ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for extensions that include privileged manifest fields.":[ "" ],
      "%(instancePath)s: privileged manifest fields are only allowed in privileged extensions.":[ "" ],
      "This extension does not include the \"mozillaAddons\" permission, which is required for privileged extensions.":[ "" ],
      "Cannot use actions in hidden add-ons.":[ "" ],
      "The hidden and browser_action/page_action (or action in Manifest Version 3 and above) properties are mutually exclusive.":[ "" ],
      "Use \"browser_specific_settings\" instead of \"applications\".":[ "" ],
      "The \"applications\" property in the manifest is deprecated and will no longer be accepted in Manifest Version 3 and above.":[ "" ],
      "\"applications\" is no longer allowed in Manifest Version 3 and above.":[ "" ],
      "The \"applications\" property in the manifest is no longer allowed in Manifest Version 3 and above. Use \"browser_specific_settings\" instead.":[ "" ],
      "The version string should be simplified because it won't be compatible with Manifest Version 3 and above.":[ "" ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros will no longer be allowed. Letters will no longer be allowed either. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "" ],
      "The version string should be simplified.":[ "" ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros are not allowed. Letters are no longer allowed. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "" ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported in manifest versions %(versionRange)s.":[ "" ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported.":[ "" ] } } }