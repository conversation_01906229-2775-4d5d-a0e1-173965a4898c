module.exports = { domain:"messages",
  locale_data:{ messages:{ "":{ domain:"messages",
        plural_forms:"nplurals=2; plural=(n != 1);",
        lang:"te" },
      "Validation Summary:":[ "ధ్రువీకరణ సారాంశం:" ],
      Code:[ "కోడ్" ],
      Message:[ "సందేశం" ],
      Description:[ "వివరణ" ],
      File:[ "ఫైలు" ],
      Line:[ "వరుస" ],
      Column:[ "నిలువు వరుస" ],
      "Invalid manifest version range requested: --min-manifest-version (currently set to %(minManifestVersion)s) should not be greater than --max-manifest-version (currently set to %(maxManifestVersion)s).":[ "" ],
      "Your FTL is not valid.":[ "మీ FTL చెల్లదు." ],
      "Your FTL file could not be parsed.":[ "" ],
      "Remote scripts are not allowed as per the Add-on Policies.":[ "పొడగింతల విధానాల ప్రకారం రిమోట్ స్క్రిప్టులు అనుమతించబడవు." ],
      "Please include all scripts in the add-on. For more information, refer to https://mzl.la/2uEOkYp.":[ "" ],
      "Inline scripts blocked by default":[ "inlinescripts అప్రమేయంగా నిరోధించబడతాయి" ],
      "Default CSP rules prevent inline JavaScript from running (https://mzl.la/2pn32nd).":[ "" ],
      "Unadvised 3rd-party JS library":[ "సూచించబడని 3వ-పక్ష JS లైబ్రరీ" ],
      "Your add-on uses a JavaScript library we do not recommend. Read more: https://bit.ly/1TRIyZY":[ "" ],
      "Known JS library detected":[ "తెలిసిన JS లైబ్రరీ కనుగొనబడింది" ],
      "JavaScript libraries are discouraged for simple add-ons, but are generally accepted.":[ "" ],
      "Due to both security and performance concerns, this may not be set using dynamic values which have not been adequately sanitized. This can lead to security issues or fairly serious performance degradation.":[ "" ],
      "{{api}} is not supported":[ "{{api}} కు మద్దతు లేదు" ],
      "This API has not been implemented by Firefox.":[ "ఈ API Firefox ద్వారా అమలు చేయబడలేదు." ],
      "\"{{api}}\" has been removed in Manifest Version 3 (`manifest_version` property)":[ "" ],
      "Content script file could not be found.":[ "కంటెంట్ స్క్రిప్ట్ ఫైలు కనుగొనబడలేదు." ],
      "\"{{api}}\" is deprecated or unimplemented":[ "" ],
      "Content script file could not be found":[ "కంటెంట్ స్క్రిప్ట్ ఫైల్ కనుగొనబడలేదు" ],
      "\"%(api)s\" can cause issues when loaded temporarily":[ "" ],
      "This API can cause issues when loaded temporarily using about:debugging in Firefox unless you specify \"browser_specific_settings.gecko.id\" in the manifest. Please see: https://mzl.la/2hizK4a for more.":[ "" ],
      "{{api}} is not supported in Firefox version {{minVersion}}":[ "" ],
      "This API is not implemented by the given minimum Firefox version":[ "" ],
      "{{api}} is not supported in Firefox for Android version {{minVersion}}":[ "" ],
      "This API is not implemented by the given minimum Firefox for Android version":[ "పేర్కొన్న ఆండ్రాయిడ్ కొరకు Firefox కనిష్ఠ వెర్షనులో ఈ API అమలుచేయబడలేదు" ],
      "Content script file name should not be empty.":[ "కంటెంట్ స్క్రిప్ట్ ఫైలు పేరు ఖాళీగా ఉండకూడదు." ],
      "Content script file name should not be empty":[ "కంటెంట్ స్క్రిప్ట్ ఫైలు పేరు ఖాళీగా ఉండకూడదు" ],
      "\"%(method)s\" called with a non-literal uri":[ "" ],
      "Calling \"%(method)s\" with variable parameters can result in potential security vulnerabilities if the variable contains a remote URI. Consider using 'window.open' with the 'chrome=no' flag.":[ "" ],
      "\"%(method)s\" called with non-local URI":[ "" ],
      "Calling \"%(method)s\" with a non-local URI will result in the dialog being opened with chrome privileges.":[ "" ],
      "JavaScript syntax error":[ "JavaScript సింటాక్స్ లోపం" ],
      "There is a JavaScript syntax error in your code, which might be related to some experimental JavaScript features that aren't an official part of the language specification and therefore not supported yet. The validation cannot continue on this file.":[ "" ],
      "Evaluation of strings as code can lead to security vulnerabilities and performance issues, even in the most innocuous of circumstances. Please avoid using `eval` and the `Function` constructor when at all possible.":[ "" ],
      "setTimeout, setInterval and execScript functions should be called only with function expressions as their first argument":[ "" ],
      "Unexpected global passed as an argument":[ "" ],
      "Passing a global as an argument is not recommended. Please make this a var instead.":[ "" ],
      "Use of document.write strongly discouraged.":[ "document.write వాడకం వారించబడింది." ],
      "document.write will fail in many circumstances when used in extensions, and has potentially severe security repercussions when used improperly. Therefore, it should not be used.":[ "" ],
      "Banned 3rd-party JS library":[ "నిషేధించిన 3వ-పక్ష JS లైబ్రరీ" ],
      "Your add-on uses a JavaScript library we consider unsafe. Read more: https://bit.ly/1TRIyZY":[ "" ],
      "Your JSON contains block comments.":[ "మీ JSONలో బ్లాక్ వ్యాఖ్యలు ఉన్నాయి." ],
      "Only line comments (comments beginning with \"//\") are allowed in JSON files. Please remove block comments (comments beginning with \"/*\")":[ "" ],
      "Duplicate keys are not allowed in JSON files.":[ "JSON ఫైళ్ళలో నకిలీ కీలు అనుమతించబడవు." ],
      "Duplicate key found in JSON file.":[ "" ],
      "Your JSON is not valid.":[ "మీ JSON చెల్లదు." ],
      "Your JSON file could not be parsed.":[ "మీ JSON ఫైలు పార్స్ చెయ్యబడలేదు." ],
      "Reserved filename found.":[ "రిజర్వుచేయబడిన ఫైలుపేరు కనబడింది." ],
      "Files whose names are reserved have been found in the add-on. Please refrain from using them and rename your files.":[ "" ],
      "The package is invalid. It may contain entries using invalid characters, as an example using '\\' as a path separator is not allowed in Firefox. Try to recreate your add-on package (ZIP) and make sure all entries are using '/' as the path separator.":[ "" ],
      "We were unable to decompress the zip file.":[ "మేము జిప్ ఫైలుని డీకంప్రెస్ చెయ్యలేకపోయాం." ],
      "manifest.json was not found":[ "manifest.json దొరకలేదు" ],
      "No manifest.json was found at the root of the extension. The package file must be a ZIP of the extension's files themselves, not of the containing directory. See: https://mzl.la/2r2McKv for more on packaging.":[ "" ],
      "File is too large to parse.":[ "ఫైలు చాలా పెద్దగా ఉన్నందున పార్స్ చెయ్యలేము." ],
      "This file is not binary and is too large to parse. Files larger than %(maxFileSizeToParseMB)sMB will not be parsed. Consider moving large lists of data out of JavaScript files and into JSON files, or splitting very large files into smaller ones.":[ "" ],
      "Hidden file flagged":[ "దాచిన ఫైల్ ఫ్లాగ్ చేయబడింది" ],
      "Hidden files complicate the review process and can contain sensitive information about the system that generated the add-on. Please modify the packaging process so that these files aren't included.":[ "" ],
      "Package contains duplicate entries":[ "ప్యాకేజిలో ద్వంద్వ పద్దులు ఉన్నాయి" ],
      "Flagged filename found":[ "ఫ్లాగ్ చేయబడిన ఫైల్ పేరు కనుగొనబడింది" ],
      "Files were found that are either unnecessary or have been included unintentionally. They should be removed.":[ "" ],
      "The package contains multiple entries with the same name. This practice has been banned. Try unzipping and re-zipping your add-on package and try again.":[ "" ],
      "Flagged file extensions found":[ "ఫ్లాగ్ చేయబడిన ఫైల్ పొడిగింతలు కనుగొనబడ్డాయి" ],
      "Flagged file type found":[ "ఫ్లాగ్ చేయబడిన ఫైల్ రకం కనుగొనబడింది" ],
      "Files whose names end with flagged extensions have been found in the add-on. The extension of these files are flagged because they usually identify binary components. Please see https://bit.ly/review-policy for more information on the binary content review process.":[ "" ],
      "Package already signed":[ "ప్యాకేజీ ఇప్పటికే సంతకం చేయబడింది" ],
      "Add-ons which are already signed will be re-signed when published on AMO. This will replace any existing signatures on the add-on.":[ "" ],
      "Firefox add-ons are not allowed to run coin miners.":[ "కాయిన్ మైనర్లను నపడడానికి Firefox పొడగింతలు అనుమతించబడవు." ],
      "We do not allow coinminer scripts to be run inside WebExtensions. See https://github.com/mozilla/addons-linter/issues/1643 for more details.":[ "" ],
      "String name is reserved for a predefined message":[ "ముందే నిర్వచించబడిన సందేశానికి స్ట్రింగ్ పేరు రిజర్వ్ చేయబడింది" ],
      "String names starting with @@ get translated to built-in constants (https://mzl.la/2BL9ZjE).":[ "" ],
      "String name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2Eztyi5).":[ "" ],
      "Placeholder for message is missing":[ "సందేశానికి హోల్డర్ లేదు" ],
      "A placeholder used in the message is not defined.":[ "సందేశంలో వాడిన ప్లేస్‌హోల్డర్ నిర్వచించబడలేదు." ],
      "Placeholder name contains invalid characters":[ "ప్లేస్‌హోల్డర్ పేరులో చెల్లని అక్షరాలు ఉన్నాయి" ],
      "Placeholder name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2ExbYez).":[ "" ],
      "Placeholder is missing the content property":[ "ప్లేస్‌హోల్డర్‌కు కంటెంట్ లక్షణం లేదు" ],
      "A placeholder needs a content property defining the replacement of it (https://mzl.la/2DT1MQd)":[ "" ],
      "Translation string is missing the message property":[ "అనువాద పదబంధానికి సందేశపు లక్షణం లేదు" ],
      "No \"message\" message property is set for a string (https://mzl.la/2DSBTjA).":[ "" ],
      "The field is required.":[ "ఫీల్డ్ అవసరం." ],
      "See https://mzl.la/1ZOhoEN (MDN Docs) for more information.":[ "మరింత సమాచారం కోసం https://mzl.la/1ZOhoEN (MDN డాక్స్) చూడండి." ],
      "The permission type is unsupported.":[ "ఆ అనుమతి రకానికి తోడ్పాటు లేదు." ],
      "See https://mzl.la/1R1n1t0 (MDN Docs) for more information.":[ "మరింత సమాచారం కోసం https://mzl.la/1R1n1t0 (MDN డాక్స్) చూడండి." ],
      "See https://mzl.la/3Woeqv4 (MDN Docs) for more information.":[ "" ],
      "Unknown permission.":[ "తెలియని అనుమతి." ],
      "%(instancePath)s: the following privileged permissions are only allowed in privileged extensions: %(privilegedPermissions)s.":[ "" ],
      "Invalid install origin.":[ "చెల్లని స్థాపన మూలం." ],
      "Invalid install origin. A valid origin has - only - a scheme, hostname and optional port. See https://mzl.la/3TEbqbE (MDN Docs) for more information.":[ "" ],
      "The field is invalid.":[ "ఆ ఖాళీ చెల్లదు." ],
      "\"manifest_version\" in the manifest.json is not a valid value":[ "manifest.json లోని \"manifest_version\" చెల్లుబాటు కాదు" ],
      "See https://mzl.la/20PenXl (MDN Docs) for more information.":[ "మరింత సమాచారం కోసం https://mzl.la/20PenXl (MDN డాక్స్) చూడండి." ],
      "\"%(property)s\" allows remote code execution in manifest.json":[ "" ],
      "A custom \"%(property)s\" needs additional review.":[ "" ],
      "\"%(property)s\" allows 'eval', which has strong security and performance implications.":[ "" ],
      "In most cases the same result can be achieved differently, therefore it is generally prohibited":[ "" ],
      "The \"name\" property must be a string with no leading/trailing whitespaces.":[ "" ],
      "See http://mzl.la/1STmr48 (MDN Docs) for more information.":[ "మరింత సమాచారం కోసం http://mzl.la/1STmr48 (MDN డాక్స్) చూడండి." ],
      "\"update_url\" is not allowed.":[ "\"update_url\" అనుమతించబడదు." ],
      "\"applications.gecko.update_url\" or \"browser_specific_settings.gecko.update_url\" are not allowed for Mozilla-hosted add-ons.":[ "" ],
      "The \"update_url\" property is not used by Firefox.":[ "\"update_url\" లక్షణాన్ని Firefox వాడుకోదు." ],
      "The \"update_url\" is not used by Firefox in the root of a manifest; your add-on will be updated via the Add-ons site and not your \"update_url\". See: https://mzl.la/25zqk4O":[ "" ],
      "\"strict_max_version\" not required.":[ "\"strict_max_version\" అవసరం లేదు." ],
      "\"strict_max_version\" shouldn't be used unless the add-on is expected not to work with future versions of Firefox.":[ "" ],
      "Manifest Version 3 is not fully supported on Firefox for Android.":[ "" ],
      "No \"%(property)s\" property found in manifest.json":[ "" ],
      "\"%(property)s\" is required":[ "" ],
      "An icon defined in the manifest could not be found in the package.":[ "మానిఫెస్ట్‌లో నిర్వచించిన ప్రతీకం ప్యాకేజీలో కనబడలేదు." ],
      "Icon could not be found at \"%(path)s\".":[ "\"%(path)s\" వద్ద ప్రతీకం కనబడలేదు." ],
      "A background script defined in the manifest could not be found.":[ "" ],
      "A background page defined in the manifest could not be found.":[ "" ],
      "Background script could not be found at \"%(path)s\".":[ "వెనుతలపు స్క్రిప్టు \"%(path)s\"లో కనబడలేదు." ],
      "Background page could not be found at \"%(path)s\".":[ "వెనుతలపు పేజీ \"%(path)s\"లో కనబడలేదు." ],
      "A content script defined in the manifest could not be found.":[ "" ],
      "A content script css file defined in the manifest could not be found.":[ "" ],
      "Content script defined in the manifest could not be found at \"%(path)s\".":[ "" ],
      "Content script css file defined in the manifest could not be found at \"%(path)s\".":[ "" ],
      "A dictionary file defined in the manifest could not be found.":[ "" ],
      "Dictionary file defined in the manifest could not be found at \"%(path)s\".":[ "" ],
      "The manifest contains multiple dictionaries.":[ "మానిఫెస్ట్ బహుళ నిఘంటువులను కలిగి ఉంది." ],
      "Multiple dictionaries were defined in the manifest, which is unsupported.":[ "మానిఫెస్ట్‌లో బహుళ నిఘంటువులు నిర్వచించబడ్డాయి, దీనికి తోడ్పాటు లేదు." ],
      "The manifest contains a dictionaries object, but it is empty.":[ "మానిఫెస్ట్‌లో నిఘంటువు వస్తువు ఉంది, కానీ అది ఖాళీగా ఉంది." ],
      "A dictionaries object was defined in the manifest, but it was empty.":[ "మానిఫెస్ట్‌లో నిఘంటువు వస్తువు నిర్వచించబడింది, కానీ అది ఖాళీగా ఉంది." ],
      "The manifest contains a dictionary but no id property.":[ "మానిఫెస్ట్‌లో నిఘంటువు ఉంది కాని id లక్షణం లేదు." ],
      "A dictionary was found in the manifest, but there was no id set.":[ "మానిఫెస్ట్‌లో ఒక నిఘంటువు కనుగొనబడింది, కాని id అమర్చి లేదు." ],
      "Please refer to https://github.com/mozilla-extensions/xpi-manifest to learn more about privileged extensions and signing.":[ "" ],
      "Forbidden content found in add-on.":[ "పొడగింతలో నిషేధిత కంటెంట్ కనబడింది." ],
      "This add-on contains forbidden content.":[ "ఈ పొడిగింత నిషిద్ధ కంటెంట్ను కలిగి ఉంది." ],
      "Icons must be square.":[ "చిహ్నాలు చదరపు ఆకారంలో ఉండాలి." ],
      "Icon at \"%(path)s\" must be square.":[ "\"%(path)s\" లో ప్రతీకాలు చదరంగా ఉండాలి." ],
      "The size of the icon does not match the manifest.":[ "ప్రతీకపు పరిమాణం మానిఫెస్ట్‌తో సరిపోలలేదు." ],
      "Expected icon at \"%(path)s\" to be %(expected)d pixels wide but was %(actual)d.":[ "" ],
      "\"%(fieldName)s\" is ignored for non-privileged add-ons.":[ "" ],
      "Corrupt image file":[ "చెడిపోయిన చిత్రం ఫైలు" ],
      "Expected icon file at \"%(path)s\" is corrupted":[ "" ],
      "This property has been deprecated.":[ "" ],
      "This theme LWT alias has been removed in Firefox 70.":[ "ఈ అలంకారపు LWT మారుపేరు Firefox 70లో తొలగించబడింది." ],
      "See https://mzl.la/2T11Lkc (MDN Docs) for more information.":[ "మరింత సమాచారం కోసం https://mzl.la/2T11Lkc (MDN పత్రావళి) చూడండి." ],
      "Theme image for \"%(type)s\" could not be found at \"%(path)s\"":[ "%(type)s కొరకు అలంకారపు బొమ్మ \"%(path)s\" వద్ద కనబడలేదు" ],
      "\"%(fieldName)s\" manifest field is only used for privileged and temporarily installed extensions.":[ "" ],
      "Corrupted theme image file":[ "పాడైన అలంకారపు బొమ్మ ఫైలు" ],
      "Theme image file at \"%(path)s\" is corrupted":[ "" ],
      "Theme image file has an unsupported file extension":[ "అలంకారపు బొమ్మ ఫైలు తోడ్పాటులేని ఫైలు ఎక్స్‌టెన్షన్ కలిగివుంది" ],
      "Theme image file at \"%(path)s\" has an unsupported file extension":[ "" ],
      "Theme image file has an unsupported mime type":[ "" ],
      "Theme image file at \"%(path)s\" has the unsupported mime type \"%(mime)s\"":[ "" ],
      "Theme image file mime type does not match its file extension":[ "అలంకారపు బొమ్మ ఫైలు యొక్క మైమ్ రకం దాని ఎక్స్‌టెన్షనుతో సరిపోలలేదు" ],
      "Theme image file extension at \"%(path)s\" does not match its actual mime type \"%(mime)s\"":[ "" ],
      "The \"default_locale\" is missing localizations.":[ "ఈ \"default_locale\" లో స్థానికీకరణలు లేవు." ],
      "The \"default_locale\" value is specified in the manifest, but no matching \"messages.json\" in the \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "" ],
      "The \"default_locale\" is missing but \"_locales\" exist.":[ "\"default_locale\" లేదు కానీ \"_locales\" మాత్రం ఉన్నాయి." ],
      "The \"default_locale\" value is not specifed in the manifest, but a \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "" ],
      "Unsupported image extension":[ "తోడ్పాటు లేని బొమ్మ ఎక్స్‌టెన్షన్" ],
      "Icons should be one of JPG/JPEG, WebP, GIF, PNG or SVG.":[ "ప్రతీకాలు JPG/JPEG, WebP, GIF, PNG లేదా SVGలలో ఒకటయి ఉండాలి." ],
      "\"applications\" property overridden by \"browser_specific_settings\" property":[ "" ],
      "The \"applications\" property is being ignored because it is superseded by the \"browser_specific_settings\" property which is also defined in your manifest. Consider removing applications.":[ "" ],
      "Empty language directory":[ "" ],
      "messages.json file missing in \"%(path)s\"":[ "\"%(path)s\" లలో messages.json ఫైలు లేదు" ],
      "Manifest key not supported by the specified minimum Firefox version":[ "పేర్కొన్న కనిష్ట Firefox వెర్షనులో మానిఫెస్ట్ కీ తోడ్పాటు లేదు" ],
      "\"strict_min_version\" requires Firefox %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "" ],
      "\"%(fieldName)s\" is not supported in manifest versions %(versionRange)s.":[ "" ],
      "Permission not supported by the specified minimum Firefox version":[ "పేర్కొన్న కనీస Firefox వెర్షనులో అనుమతికి తోడ్పాటు లేదు" ],
      "\"%(fieldName)s\" is not supported.":[ "" ],
      "Manifest key not supported by the specified minimum Firefox for Android version":[ "పేర్కొన్న ఆండ్రాయిడ్ కొరకు Firefox కనిష్ట వెర్షనులో మానిఫెస్ట్ కీ తోడ్పాటు లేదు" ],
      "\"strict_min_version\" requires Firefox for Android %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "" ],
      "Permission not supported by the specified minimum Firefox for Android version":[ "" ],
      "Linking to \"addons.mozilla.org\" is not allowed":[ "" ],
      "Links directing to \"addons.mozilla.org\" are not allowed to be used for homepage":[ "" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above":[ "" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above. Please update your manifest.json version to specify a minimum Firefox version.":[ "" ],
      "The extension ID is required in Manifest Version 3 and above.":[ "" ],
      "See https://mzl.la/3PLZYdo for more information.":[ "" ],
      "%(instancePath)s: Privileged extensions should declare privileged permissions.":[ "" ],
      "This extension does not declare any privileged permission. It does not need to be signed with the privileged certificate. Please upload it directly to https://addons.mozilla.org/.":[ "" ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for privileged extensions.":[ "" ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for extensions that include privileged manifest fields.":[ "" ],
      "%(instancePath)s: privileged manifest fields are only allowed in privileged extensions.":[ "" ],
      "This extension does not include the \"mozillaAddons\" permission, which is required for privileged extensions.":[ "" ],
      "Cannot use actions in hidden add-ons.":[ "" ],
      "The hidden and browser_action/page_action (or action in Manifest Version 3 and above) properties are mutually exclusive.":[ "" ],
      "Use \"browser_specific_settings\" instead of \"applications\".":[ "" ],
      "The \"applications\" property in the manifest is deprecated and will no longer be accepted in Manifest Version 3 and above.":[ "" ],
      "\"applications\" is no longer allowed in Manifest Version 3 and above.":[ "" ],
      "The \"applications\" property in the manifest is no longer allowed in Manifest Version 3 and above. Use \"browser_specific_settings\" instead.":[ "" ],
      "The version string should be simplified because it won't be compatible with Manifest Version 3 and above.":[ "" ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros will no longer be allowed. Letters will no longer be allowed either. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "" ],
      "The version string should be simplified.":[ "" ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros are not allowed. Letters are no longer allowed. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "" ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported in manifest versions %(versionRange)s.":[ "" ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported.":[ "" ] } } }