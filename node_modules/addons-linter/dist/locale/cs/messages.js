module.exports = { domain:"messages",
  locale_data:{ messages:{ "":{ domain:"messages",
        plural_forms:"nplurals=3; plural=(n==1) ? 0 : (n>=2 && n<=4) ? 1 : 2;",
        lang:"cs" },
      "Validation Summary:":[ "Souhrn validace:" ],
      Code:[ "Kód" ],
      Message:[ "Zpráva" ],
      Description:[ "Popisek" ],
      File:[ "Soubor" ],
      Line:[ "Řádek" ],
      Column:[ "Sloupec" ],
      "Invalid manifest version range requested: --min-manifest-version (currently set to %(minManifestVersion)s) should not be greater than --max-manifest-version (currently set to %(maxManifestVersion)s).":[ "Požadovaný neplatný rozsah verzí manifestu: --min-manifest-version (aktuálně nastaveno na %(minManifestVersion)s) by nem<PERSON><PERSON> být větší než --max-manifest-version (aktuálně nastaveno na %(maxManifestVersion)s)." ],
      "Your FTL is not valid.":[ "Váš FTL není validní." ],
      "Your FTL file could not be parsed.":[ "Váš FTL nemohl být parsován." ],
      "Remote scripts are not allowed as per the Add-on Policies.":[ "Vzdálené skripty nejsou povoleny bezpečnostní politikou doplňků." ],
      "Please include all scripts in the add-on. For more information, refer to https://mzl.la/2uEOkYp.":[ "Zahrňte prosím všechny skripty do doplňku. Více informací naleznete na https://mzl.la/2uEOkYp." ],
      "Inline scripts blocked by default":[ "Vložené skripty jsou ve výchozím nastavení zablokovány" ],
      "Default CSP rules prevent inline JavaScript from running (https://mzl.la/2pn32nd).":[ "Výchozí pravidla CSP zabraňují spuštění inline JavaScriptu (https://mzl.la/2pn32nd)." ],
      "Unadvised 3rd-party JS library":[ "Nedoporučené externí JS knihovny" ],
      "Your add-on uses a JavaScript library we do not recommend. Read more: https://bit.ly/1TRIyZY":[ "Váš doplněk používá knihovnu JavaScriptu, kterou nedoporučujeme. Čtěte si více na: https://bit.ly/1TRIyZY" ],
      "Known JS library detected":[ "Rozpoznána známá JS knihovna" ],
      "JavaScript libraries are discouraged for simple add-ons, but are generally accepted.":[ "Knihovny JavaScriptu se nedoporučuje pro jednoduché doplňky, ale jsou obecně akceptovány." ],
      "Due to both security and performance concerns, this may not be set using dynamic values which have not been adequately sanitized. This can lead to security issues or fairly serious performance degradation.":[ "Kvůli bezpečnosti i výkonu nemusí být toto nastavení nastaveno pomocí dynamických hodnot, které nebyly adekvátně ošetřeny. To může vést k bezpečnostním problémům nebo poměrně vážnému snížení výkonu." ],
      "{{api}} is not supported":[ "{{api}} není podporováno" ],
      "This API has not been implemented by Firefox.":[ "Tato API nemohla být přidána do Firefoxu." ],
      "\"{{api}}\" has been removed in Manifest Version 3 (`manifest_version` property)":[ "\"{{api}}\" bylo v Manifestu verze 3 (`manifest_version`) odstraněno" ],
      "{{api}} is deprecated":[ "{{api}} je zastaralé" ],
      "This API has been deprecated by Firefox.":[ "Toto API je ve Firefoxu označeno jako zastaralé." ],
      "Content script file could not be found.":[ "Obsah souboru se skriptem nebyl nalezen." ],
      "\"{{api}}\" is deprecated or unimplemented":[ "\"{{api}}\" je zastaralé, nebo není vůbec implementované" ],
      "Content script file could not be found":[ "Obsah skriptu nemohl být nalezen" ],
      "\"%(api)s\" can cause issues when loaded temporarily":[ "API \"%(api)s\" může způsobit problémy při dočasném načítání" ],
      "This API can cause issues when loaded temporarily using about:debugging in Firefox unless you specify \"browser_specific_settings.gecko.id\" in the manifest. Please see: https://mzl.la/2hizK4a for more.":[ "Toto API může způsobit problémy při dočasném načítání pomocí about:debugging ve Firefoxu, pokud v manifestu nespecifikujete \"browser_specific_settings.gecko.id\". Další informace na https://mzl.la/2hizK4a." ],
      "{{api}} is not supported in Firefox version {{minVersion}}":[ "{{api}} není ve verzi Firefoxu {{minVersion}} podporováno" ],
      "This API is not implemented by the given minimum Firefox version":[ "Tato API nejsou uvedenou minimální verzí Firefoxu implementována" ],
      "{{api}} is not supported in Firefox for Android version {{minVersion}}":[ "{{api}} není ve verzi Firefoxu pro Android {{minVersion}} podporováno" ],
      "This API is not implemented by the given minimum Firefox for Android version":[ "Tato API nejsou uvedenou minimální verzí Firefoxu pro Android implementována" ],
      "Content script file name should not be empty.":[ "Název skriptu by neměl být prázdný." ],
      "Content script file name should not be empty":[ "Název skriptu by neměl být prázdný" ],
      "\"%(method)s\" called with a non-literal uri":[ "Metoda \"%(method)s\" volána s URI, který není literálem" ],
      "Calling \"%(method)s\" with variable parameters can result in potential security vulnerabilities if the variable contains a remote URI. Consider using 'window.open' with the 'chrome=no' flag.":[ "Volání metody \"%(method)s\" s parametry proměnných může vést k potenciálním bezpečnostním zranitelnostem, pokud proměnná obsahuje vzdálené URI. Zvažte použití příkazu 'window.open' s příznakem 'chrome=no'." ],
      "\"%(method)s\" called with non-local URI":[ "Metoda \"%(method)s\" volána s nelokálním URI" ],
      "Calling \"%(method)s\" with a non-local URI will result in the dialog being opened with chrome privileges.":[ "Volání \"%(method)s\" s jiným než místním URI bude mít za následek otevření dialogového okna s oprávněními prohlížeče." ],
      "JavaScript syntax error":[ "JavaScript syntax error" ],
      "There is a JavaScript syntax error in your code, which might be related to some experimental JavaScript features that aren't an official part of the language specification and therefore not supported yet. The validation cannot continue on this file.":[ "Ve vašem kódu se vyskytla syntaktická chyba jazyka JavaScript, která může souviset s některými experimentálními funkcemi jazyka JavaScript, které nejsou oficiální součástí specifikace jazyka, a proto ještě nejsou podporovány. V ověřování tohoto souboru nelze pokračovat." ],
      "Evaluation of strings as code can lead to security vulnerabilities and performance issues, even in the most innocuous of circumstances. Please avoid using `eval` and the `Function` constructor when at all possible.":[ "Vyhodnocování řetězců jako kódu může vést ke zranitelnostem zabezpečení a problémům s výkonem, a to i za těch nejneškodnějších okolností. Pokud je to možné, vyhněte se používání konstruktů `eval` a `Function`." ],
      "setTimeout, setInterval and execScript functions should be called only with function expressions as their first argument":[ "Funkce setTimeout, setInterval a execScript by se měly volat pouze s funkčními výrazy jako prvním argumentem." ],
      "Unexpected global passed as an argument":[ "Neočekávaná globální proměnná byla předaná jako parametr" ],
      "Passing a global as an argument is not recommended. Please make this a var instead.":[ "Předávání globálního prvku jako argumentu se nedoporučuje. Místo toho použijte proměnnou." ],
      "Use of document.write strongly discouraged.":[ "Používání document.write není zcela doporučováno." ],
      "document.write will fail in many circumstances when used in extensions, and has potentially severe security repercussions when used improperly. Therefore, it should not be used.":[ "document.write za mnoha okolností při použití v rozšířeních selže a při nesprávném použití může mít závažné bezpečnostní důsledky. Proto by se neměl používat." ],
      "Banned 3rd-party JS library":[ "Zakázané externí JS knihovny" ],
      "Your add-on uses a JavaScript library we consider unsafe. Read more: https://bit.ly/1TRIyZY":[ "Váš doplněk používá JavaScriptovou knihovnu, kterou nepovažujeme za bezpečnou. Podrobnosti na https://bit.ly/1TRIyZY" ],
      "Your JSON contains block comments.":[ "Váš JSON obsahuje blokové komentáře." ],
      "Only line comments (comments beginning with \"//\") are allowed in JSON files. Please remove block comments (comments beginning with \"/*\")":[ "V souborech JSON jsou povoleny pouze řádkové komentáře (komentáře začínající \"//\"). Odstraňte prosím blokové komentáře (komentáře začínající \"/*\")." ],
      "Duplicate keys are not allowed in JSON files.":[ "V JSON souborech nejsou duplicitní klíče povoleny." ],
      "Duplicate key found in JSON file.":[ "V souboru JSON se našel duplicitní klíč." ],
      "Your JSON is not valid.":[ "Váš JSON není platný." ],
      "Your JSON file could not be parsed.":[ "Váš JSON nemohl být parsován." ],
      "Reserved filename found.":[ "Nalezen soubor s rezervovaným názvem." ],
      "Files whose names are reserved have been found in the add-on. Please refrain from using them and rename your files.":[ "V doplňku byly nalezeny soubory, jejichž názvy jsou rezervovány. Vyhněte se jejich používání a přejmenujte své soubory." ],
      "The package is invalid. It may contain entries using invalid characters, as an example using '\\' as a path separator is not allowed in Firefox. Try to recreate your add-on package (ZIP) and make sure all entries are using '/' as the path separator.":[ "Balíček je neplatný. Může obsahovat položky používající neplatné znaky, například použití '\\' jako oddělovače cesty není ve Firefoxu povoleno. Zkuste znovu vytvořit svůj balíček s doplňkem (ZIP) a ujistěte se, že všechny položky používají jako oddělovač cesty '/'." ],
      "We were unable to decompress the zip file.":[ "Nepodařilo se nám rozbalit zip archiv." ],
      "manifest.json was not found":[ "manifest.json nebyl nalezen" ],
      "No manifest.json was found at the root of the extension. The package file must be a ZIP of the extension's files themselves, not of the containing directory. See: https://mzl.la/2r2McKv for more on packaging.":[ "V kořenovém adresáři rozšíření se nenašel soubor manifest.json. Soubor balíčku musí být ZIP se soubory rozšíření, ne s adresářem, který je obsahuje. Více o balení souborů naleznete na https://mzl.la/2r2McKv." ],
      "File is too large to parse.":[ "Soubor je příliš velký pro parsování." ],
      "This file is not binary and is too large to parse. Files larger than %(maxFileSizeToParseMB)sMB will not be parsed. Consider moving large lists of data out of JavaScript files and into JSON files, or splitting very large files into smaller ones.":[ "Tento soubor není binární a je příliš velký na analýzu. Soubory větší než %(maxFileSizeToParseMB)s MB nebudou analyzovány. Zvažte přesun velkých seznamů údajů z JavaScriptových souborů do souborů JSON nebo rozdělení velmi velkých souborů na menší." ],
      "Hidden file flagged":[ "Skrytý soubor označen" ],
      "Hidden files complicate the review process and can contain sensitive information about the system that generated the add-on. Please modify the packaging process so that these files aren't included.":[ "Skryté soubory komplikují proces kontroly a mohou obsahovat citlivé informace o systému, které doplněk vygeneroval. Upravte proces balení tak, aby tyto soubory nebyly zahrnuty." ],
      "Package contains duplicate entries":[ "Balíček obsahuje duplicitní položky" ],
      "Flagged filename found":[ "Označený soubor nalezen" ],
      "Files were found that are either unnecessary or have been included unintentionally. They should be removed.":[ "Byly nalezeny soubory, které jsou buď zbytečné, nebo byly zahrnuty neúmyslně. Měly by být odstraněny." ],
      "The package contains multiple entries with the same name. This practice has been banned. Try unzipping and re-zipping your add-on package and try again.":[ "Balíček obsahuje více položek se stejným názvem. Tato praxe byla zakázána. Zkuste rozbalit a znovu zazipovat svůj balíček s doplňkem a zkuste to znovu." ],
      "Flagged file extensions found":[ "Označený soubor rozšíření nalezen" ],
      "Flagged file type found":[ "Typ označených souborů nalezen" ],
      "Files whose names end with flagged extensions have been found in the add-on. The extension of these files are flagged because they usually identify binary components. Please see https://bit.ly/review-policy for more information on the binary content review process.":[ "V doplňku byly nalezeny soubory, jejichž názvy končí označenými příponami. Přípony těchto souborů jsou označeny, protože obvykle identifikují binární komponenty. Další informace o procesu kontroly binárního obsahu naleznete na adrese https://bit.ly/review-policy." ],
      "Package already signed":[ "Balíček již byl podepsán" ],
      "Add-ons which are already signed will be re-signed when published on AMO. This will replace any existing signatures on the add-on.":[ "Doplňky, které jsou už podepsané, budou opětovně podepsané při zveřejnění na AMO. Tým se nahradí všechny existující podpisy doplňku." ],
      "Firefox add-ons are not allowed to run coin miners.":[ "Doplňky Firefoxu nesmí těžit kryptoměny." ],
      "We do not allow coinminer scripts to be run inside WebExtensions. See https://github.com/mozilla/addons-linter/issues/1643 for more details.":[ "Spouštění skriptů coinminer uvnitř rozšíření WebExtensions není povoleno. Další podrobnosti naleznete na adrese https://github.com/mozilla/addons-linter/issues/1643." ],
      "String name is reserved for a predefined message":[ "Jméno řetězce je rezervováno pro předdefinovanou zprávu" ],
      "String names starting with @@ get translated to built-in constants (https://mzl.la/2BL9ZjE).":[ "Názvy řetězců začínající @@ se překládají na vestavěné konstanty (https://mzl.la/2BL9ZjE)." ],
      "String name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2Eztyi5).":[ "Název řetězce by měl obsahovat pouze alfanumerické znaky, _ a @ (https://mzl.la/2Eztyi5)." ],
      "Placeholder for message is missing":[ "Placeholder pro zprávu chybí" ],
      "A placeholder used in the message is not defined.":[ "Placeholder použitý ve zprávě není definován." ],
      "Placeholder name contains invalid characters":[ "Placeholder obsahuje neplatné znaky" ],
      "Placeholder name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2ExbYez).":[ "Název zástupného textu by měl obsahovat pouze alfanumerické znaky, _ a @ (https://mzl.la/2ExbYez)." ],
      "Placeholder is missing the content property":[ "Zástupnému textu chybí vlastnost content" ],
      "A placeholder needs a content property defining the replacement of it (https://mzl.la/2DT1MQd)":[ "Placeholder musí mít přiřazený atribut content, který definuje jeho nahrazení (https://mzl.la/2DT1MQd)" ],
      "Translation string is missing the message property":[ "Řetězci překladu chybí parametr message" ],
      "No \"message\" message property is set for a string (https://mzl.la/2DSBTjA).":[ "Pro řetězec není nastavena zpráva ve vlastnosti „message“ (https://mzl.la/2DSBTjA)." ],
      "The field is required.":[ "Toto pole je vyžadováno." ],
      "See https://mzl.la/1ZOhoEN (MDN Docs) for more information.":[ "Podívejte se na https://mzl.la/1ZOhoEN (MDN dokumentace) pro více informací." ],
      "The permission type is unsupported.":[ "Tento typ oprávnění není podporován." ],
      "See https://mzl.la/1R1n1t0 (MDN Docs) for more information.":[ "Podívejte se na https://mzl.la/1R1n1t0 (MDN dokumentace) pro více informací." ],
      "See https://mzl.la/2Qn0fWC (MDN Docs) for more information.":[ "Podrobnosti najdete v MDN Docs na adrese https://mzl.la/2Qn0fWC." ],
      "See https://mzl.la/3Woeqv4 (MDN Docs) for more information.":[ "Podívejte se na https://mzl.la/3Woeqv4 (MDN dokumentace) pro více informací." ],
      "Unknown permission.":[ "Neznámé oprávnění." ],
      "%(instancePath)s: the following privileged permissions are only allowed in privileged extensions: %(privilegedPermissions)s.":[ "%(instancePath)s: následující privilegovaná oprávnění jsou povolena pouze v privilegovaných rozšířeních: %(privilegedPermissions)s." ],
      "Invalid host permission.":[ "Neplatné oprávnění pro server." ],
      "Invalid install origin.":[ "Neplatný origin instalace." ],
      "Invalid install origin. A valid origin has - only - a scheme, hostname and optional port. See https://mzl.la/3TEbqbE (MDN Docs) for more information.":[ "Neplatný původ instalace. Platný původ má - pouze - schéma, název hostitele a volitelný port. Další informace naleznete na adrese https://mzl.la/3TEbqbE (MDN Docs)." ],
      "The field is invalid.":[ "Pole není platné." ],
      "\"manifest_version\" in the manifest.json is not a valid value":[ "\"manifest_version\" není v souboru manifest.json platná hodnota." ],
      "See https://mzl.la/20PenXl (MDN Docs) for more information.":[ "Podívejte se na https://mzl.la/20PenXl (MDN dokumentace) pro více informací." ],
      "\"%(property)s\" allows remote code execution in manifest.json":[ "Vlastnost \"%(property)s\" umožňuje vzdálené spouštění kódu v souboru manifest.json" ],
      "A custom \"%(property)s\" needs additional review.":[ "Vlastní „%(property)s“ vyžaduje další kontrolu." ],
      "\"%(property)s\" allows 'eval', which has strong security and performance implications.":[ "\"%(property)s\" umožňuje 'eval', což má silné dopady na bezpečnost a výkon." ],
      "In most cases the same result can be achieved differently, therefore it is generally prohibited":[ "Ve většině případů lze stejného výsledku dosáhnout odlišně, proto je obecně zakázáno" ],
      "The \"name\" property must be a string with no leading/trailing whitespaces.":[ "Vlastnost \"name\" musí být řetězec bez mezer na začátku/na konci." ],
      "See http://mzl.la/1STmr48 (MDN Docs) for more information.":[ "Podívejte se na http://mzl.la/1STmr48 (MDN Dokumenty) pro více informací." ],
      "\"update_url\" is not allowed.":[ "\"update_url\" není povoleno." ],
      "\"applications.gecko.update_url\" or \"browser_specific_settings.gecko.update_url\" are not allowed for Mozilla-hosted add-ons.":[ "\"applications.gecko.update_url\" nebo \"browser_specific_settings.gecko.update_url\" nejsou povoleny pro doplňky hostované Mozillou." ],
      "The \"update_url\" property is not used by Firefox.":[ "Obsah \"update_url\" parametru není používán Firefoxem." ],
      "The \"update_url\" is not used by Firefox in the root of a manifest; your add-on will be updated via the Add-ons site and not your \"update_url\". See: https://mzl.la/25zqk4O":[ "Firefox nepoužívá \"update_url\" v kořenu manifestu; váš doplněk bude aktualizován prostřednictvím webu doplňků, nikoli prostřednictvím vaší \"update_url\". Viz: https://mzl.la/25zqk4O" ],
      "\"strict_max_version\" not required.":[ "\"strict_max_version\" není vyžadováno." ],
      "\"strict_max_version\" shouldn't be used unless the add-on is expected not to work with future versions of Firefox.":[ "\"strict_max_version\" by se neměla používat, pokud se neočekává, že doplněk nebude fungovat s budoucími verzemi Firefoxu." ],
      "Manifest Version 3 is not fully supported on Firefox for Android.":[ "Manifest verze 3 není ve Firefoxu pro Android plně podporován." ],
      "No \"%(property)s\" property found in manifest.json":[ "V souboru manifest.json se nenašla žádná vlastnost \"%(property)s\"." ],
      "\"%(property)s\" is required":[ "Vlastnost \"%(property)s\" je povinná" ],
      "An icon defined in the manifest could not be found in the package.":[ "Ikona definovaná v manifestu nebyla v balíčku nalezena." ],
      "Icon could not be found at \"%(path)s\".":[ "V umístění \"%(path)s\" nebyla nalezena ikona." ],
      "A background script defined in the manifest could not be found.":[ "Skript běžící na pozadí definovaný v manifestu nebyl nalezen." ],
      "A background page defined in the manifest could not be found.":[ "Stránka běžící na pozadí definovaná v manifestu nebyla nalezena." ],
      "Background script could not be found at \"%(path)s\".":[ "Background skript nebyl na cestě „%(path)s“ nalezen." ],
      "Background page could not be found at \"%(path)s\".":[ "Background stránka nebyla na cestě „%(path)s“ nalezena." ],
      "A content script defined in the manifest could not be found.":[ "Obsahový skript definovaný v manifestu nebyl nalezen." ],
      "A content script css file defined in the manifest could not be found.":[ "Styly obsahového skriptu definované v manifestu nebyly nalezeny." ],
      "Content script defined in the manifest could not be found at \"%(path)s\".":[ "Obsahový skript definovaný v manifestu nebyl na cestě „%(path)s“ nalezen." ],
      "Content script css file defined in the manifest could not be found at \"%(path)s\".":[ "Styly obsahového skriptu definované v manifestu nebyly na cestě „%(path)s“ nalezeny." ],
      "A dictionary file defined in the manifest could not be found.":[ "Slovníkový soubor definovaný v manifestu nebyl nalezen." ],
      "Dictionary file defined in the manifest could not be found at \"%(path)s\".":[ "Slovníkový soubor definovaný v manifestu nebyl na cestě „%(path)s“ nalezen." ],
      "The manifest contains multiple dictionaries.":[ "Manifest obsahuje vícero slovníků." ],
      "Multiple dictionaries were defined in the manifest, which is unsupported.":[ "V manifestu bylo definováno vícero slovníků, což není podporováno." ],
      "The manifest contains a dictionaries object, but it is empty.":[ "Manifest obsahuje prázdný objekt slovníků." ],
      "A dictionaries object was defined in the manifest, but it was empty.":[ "V manifestu byl definován objekt slovníků, ale je prázdný." ],
      "The manifest contains a dictionary but no id property.":[ "Manifest obsahuje slovník bez id." ],
      "A dictionary was found in the manifest, but there was no id set.":[ "V manifestu byl nalezen slovník, ale nemá nastaven identifikátor." ],
      "Please refer to https://github.com/mozilla-extensions/xpi-manifest to learn more about privileged extensions and signing.":[ "Další informace o privilegovaných rozšířeních a podepisování naleznete na adrese https://github.com/mozilla-extensions/xpi-manifest." ],
      "Forbidden content found in add-on.":[ "V doplňku byl nalezen zakázaný obsah." ],
      "This add-on contains forbidden content.":[ "Tento doplněk obsahuje zakázaný obsah." ],
      "Icons must be square.":[ "Ikony musí být čtvercové." ],
      "Icon at \"%(path)s\" must be square.":[ "Ikona na cestě „%(path)s“ musí být čtverec." ],
      "The size of the icon does not match the manifest.":[ "Velikost ikony neodpovídá manifestu." ],
      "Expected icon at \"%(path)s\" to be %(expected)d pixels wide but was %(actual)d.":[ "Očekávaná ikona na „%(path)s“ bude %(expected)d pixelů široká, ale byla %(actual)d." ],
      "\"%(fieldName)s\" is ignored for non-privileged add-ons.":[ "\"%(fieldName)s\" se pro neprivilegované doplňky ignoruje." ],
      "Corrupt image file":[ "Poškozený soubor obrázku" ],
      "Expected icon file at \"%(path)s\" is corrupted":[ "Očekávaný soubor ikony na cestě „%(path)s“ je poškozený" ],
      "This property has been deprecated.":[ "Tato vlastnost je zastaralá." ],
      "This theme LWT alias has been removed in Firefox 70.":[ "Tento LWT alias vzhledu je od verze Firefoxu 70 odstraněn." ],
      "See https://mzl.la/2T11Lkc (MDN Docs) for more information.":[ "Více informací najdete v dokumentaci MDN na adrese https://mzl.la/2T11Lkc ." ],
      "Theme image for \"%(type)s\" could not be found at \"%(path)s\"":[ "Obrázek vzhledu s typem „%(type)s“ nebyl na cestě „%(path)s“ nalezen." ],
      "\"%(fieldName)s\" manifest field is only used for privileged and temporarily installed extensions.":[ "Pole manifestu \"%(fieldName)s\" se používá pouze pro privilegovaná a dočasně nainstalovaná rozšíření." ],
      "Corrupted theme image file":[ "Poškozený obrázku vzhledu" ],
      "Theme image file at \"%(path)s\" is corrupted":[ "Obrázek vzhledu na cestě „%(path)s“ je poškozený" ],
      "Theme image file has an unsupported file extension":[ "Obrázek vzhledu má nepodporovanou koncovku" ],
      "Theme image file at \"%(path)s\" has an unsupported file extension":[ "Soubor s obrázkem \"%(path)s\" má nepodporovanou koncovku názvu" ],
      "Theme image file has an unsupported mime type":[ "Obrázek vzhledu je nepodporovaného typu" ],
      "Theme image file at \"%(path)s\" has the unsupported mime type \"%(mime)s\"":[ "Soubor s obrázkem \"%(path)s\" je typu \"%(mime)s\", který není podporován" ],
      "Theme image file mime type does not match its file extension":[ "Typ a koncovka názvu obrázku nesouhlasí" ],
      "Theme image file extension at \"%(path)s\" does not match its actual mime type \"%(mime)s\"":[ "Koncovka souboru s obrázkem \"%(path)s\" neodpovídá jeho skutečnému typu \"%(mime)s\"" ],
      "The \"default_locale\" is missing localizations.":[ "Jazyku „default_locale“ chybí překlady." ],
      "The \"default_locale\" value is specified in the manifest, but no matching \"messages.json\" in the \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "Hodnota „default_locale“ je uvedena v manifestu, ale v adresáři „_locales“ neexistuje žádný odpovídající soubor „messages.json“. Viz: https://mzl.la/2hjcaEE" ],
      "The \"default_locale\" is missing but \"_locales\" exist.":[ "Jazyk „default_locale“ chybí, ale „_locales“ existuje." ],
      "The \"default_locale\" value is not specifed in the manifest, but a \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "Hodnota „default_locale“ není v manifestu uvedena, ale existuje adresář „_locales“. Vizte https://mzl.la/2hjcaEE" ],
      "Unsupported image extension":[ "Nepodporovaný formát obrázku" ],
      "Icons should be one of JPG/JPEG, WebP, GIF, PNG or SVG.":[ "Ikona by měla mít formát JPG, JPEG, WebP, GIFF, PNG nebo SVG." ],
      "\"applications\" property overridden by \"browser_specific_settings\" property":[ "Vlastnost \"applications\" je nahrazena za \"browser_specific_settings\"" ],
      "The \"applications\" property is being ignored because it is superseded by the \"browser_specific_settings\" property which is also defined in your manifest. Consider removing applications.":[ "Vlastnost \"applications\" se ignoruje, protože je nahrazená vlastností \"browser_specific_settings\", která je též definovaná ve vašem manifestu. Zvažte odstranění vlastnosti \"applications\"." ],
      "Empty language directory":[ "Prázdný adresář jazyků" ],
      "messages.json file missing in \"%(path)s\"":[ "messages.json na cestě \"%(path)s\" chybí" ],
      "Manifest key not supported by the specified minimum Firefox version":[ "Klíč v manifestu není uvedenou minimální verzí Firefoxu podporován" ],
      "\"strict_min_version\" requires Firefox %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "\"strict_min_version\" vyžaduje Firefox %(minVersion)s, který byl vydán předtím, než verze %(versionAdded)s zavedla podporu pro \"%(key)s\"." ],
      "\"%(fieldName)s\" is not supported in manifest versions %(versionRange)s.":[ "\"%(fieldName)s\" není podporováno v manifestu ve verzích %(versionRange)s." ],
      "Permission not supported by the specified minimum Firefox version":[ "Oprávnění nepodporované uvedenou minimální verzí Firefoxu" ],
      "\"%(fieldName)s\" is not supported.":[ "\"%(fieldName)s\" není podporováno." ],
      "Manifest key not supported by the specified minimum Firefox for Android version":[ "Klíč v manifestu není uvedenou minimální verzí Firefoxu pro Android podporován" ],
      "\"strict_min_version\" requires Firefox for Android %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "\"strict_min_version\" vyžaduje Firefox pro Android %(minVersion)s, který byl vydán předtím, než verze %(versionAdded)s zavedla podporu pro \"%(key)s\"." ],
      "Permission not supported by the specified minimum Firefox for Android version":[ "Oprávnění nepodporované uvedenou minimální verzí Firefoxu pro Android" ],
      "Linking to \"addons.mozilla.org\" is not allowed":[ "Odkazování ní \"addons.mozilla.org\" není dovoleno" ],
      "Links directing to \"addons.mozilla.org\" are not allowed to be used for homepage":[ "Odkazy směřující na \"addons.mozilla.org\" není možné nastavit jako domovskou stránku" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above":[ "Oprávnění \"%(permission)s\" vyžaduje, aby \"strict_min_version\" bylo nastaveno na \"%(minFirefoxVersion)s\" nebo vyšší" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above. Please update your manifest.json version to specify a minimum Firefox version.":[ "Oprávnění \"%(permission)s\" vyžaduje, aby \"strict_min_version\" bylo nastaveno na \"%(minFirefoxVersion)s\" nebo vyšší. Aktualizujte prosím verzi manifest.json tak, abyste zadali minimální verzi Firefoxu." ],
      "The extension ID is required in Manifest Version 3 and above.":[ "Pro manifest verze 3 a novější je vyžadováno ID rozšíření." ],
      "See https://mzl.la/3PLZYdo for more information.":[ "Pro více informací se podívejte na https://mzl.la/3PLZYdo ." ],
      "%(instancePath)s: Privileged extensions should declare privileged permissions.":[ "%(instancePath)s: Privilegovaná rozšíření by měla deklarovat privilegovaná oprávnění." ],
      "This extension does not declare any privileged permission. It does not need to be signed with the privileged certificate. Please upload it directly to https://addons.mozilla.org/.":[ "Toto rozšíření nedeklaruje žádné privilegované oprávnění. Nemusí být podepsáno privilegovaným certifikátem. Nahrajte jej prosím přímo na adresu https://addons.mozilla.org/." ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for privileged extensions.":[ "%(instancePath)s: Oprávnění \"mozillaAddons\" je vyžadováno pro privilegovaná rozšíření." ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for extensions that include privileged manifest fields.":[ "%(instancePath)s: Oprávnění \"mozillaAddons\" je vyžadováno pro rozšíření, která obsahují privilegovaná pole manifestu." ],
      "%(instancePath)s: privileged manifest fields are only allowed in privileged extensions.":[ "%(instancePath)s: privilegovaná pole manifestu jsou povolena pouze v privilegovaných rozšířeních." ],
      "This extension does not include the \"mozillaAddons\" permission, which is required for privileged extensions.":[ "Toto rozšíření neobsahuje povolení \"mozillaAddons\", které je vyžadováno pro privilegovaná rozšíření." ],
      "Cannot use actions in hidden add-ons.":[ "Nelze použít akce ve skrytých doplňcích." ],
      "The hidden and browser_action/page_action (or action in Manifest Version 3 and above) properties are mutually exclusive.":[ "Vlastnosti hidden a browser_action/page_action (nebo action v manifestu verze 3 či vyšší) se navzájem vylučují." ],
      "Use \"browser_specific_settings\" instead of \"applications\".":[ "Použijte \"browser_specific_settings\" na místo \"applications\"." ],
      "The \"applications\" property in the manifest is deprecated and will no longer be accepted in Manifest Version 3 and above.":[ "Vlastnost \"applications\" v manifestu je zastaralá a už nebude akceptovaná v manifestu verze 3 a vyšší." ],
      "\"applications\" is no longer allowed in Manifest Version 3 and above.":[ "„applications“ již nejsou povoleny v manifetu verze 3 a vyšší." ],
      "The \"applications\" property in the manifest is no longer allowed in Manifest Version 3 and above. Use \"browser_specific_settings\" instead.":[ "Vlastnost \"applications\" v manifestu již není povolena v manifestu verze 3 a vyšší. Místo toho použijte \"browser_specific_settings\"." ],
      "The version string should be simplified because it won't be compatible with Manifest Version 3 and above.":[ "Řetězec verze by měl být zjednodušen, protože nebude kompatibilní s manifestem verze 3 a vyšší." ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros will no longer be allowed. Letters will no longer be allowed either. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "Verze by měl být řetězec s 1 až 4 čísly oddělenými tečkou. Každé číslo může mít až 9 číslic, ale úvodní nuly už nebudou povoleny. Povoleny nebudou ani písmena. Další informace naleznete na stránce https://mzl.la/3h3mCRu (Dokumentace MDN)." ],
      "The version string should be simplified.":[ "Řetězec s informací o verzi by měl být zjednodušen." ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros are not allowed. Letters are no longer allowed. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "Verze by měla být řetězec s 1 až 4 čísly oddělenými tečkou. Každé číslo může mít maximálně 9 číslic, ale úvodní nuly nejsou povoleny. Povoleny nejsou ani písmena. Další informace naleznete na stránce https://mzl.la/3h3mCRu (Dokumentace MDN)." ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported in manifest versions %(versionRange)s.":[ "/%(fieldName)s: \"%(permissionName)s\" není podporováno v manifestu ve verzích %(versionRange)s." ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported.":[ "/%(fieldName)s: \"%(permissionName)s\" není podporováno." ] } } }