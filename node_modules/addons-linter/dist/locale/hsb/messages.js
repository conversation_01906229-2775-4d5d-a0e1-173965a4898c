module.exports = { domain:"messages",
  locale_data:{ messages:{ "":{ domain:"messages",
        plural_forms:"nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);",
        lang:"hsb" },
      "Validation Summary:":[ "Přepruwowanske zhornjenje:" ],
      Code:[ "Kod" ],
      Message:[ "Powěsć" ],
      Description:[ "Wopisanje" ],
      File:[ "Dataja" ],
      Line:[ "Linka" ],
      Column:[ "Špalta" ],
      "Invalid manifest version range requested: --min-manifest-version (currently set to %(minManifestVersion)s) should not be greater than --max-manifest-version (currently set to %(maxManifestVersion)s).":[ "" ],
      "Your FTL is not valid.":[ "Waš FTL płaćiwy njeje." ],
      "Your FTL file could not be parsed.":[ "Waša FTL-dataja njeda so parsować." ],
      "Remote scripts are not allowed as per the Add-on Policies.":[ "Zdalene skripty po prawidłach přidatkow dowolene njejsu." ],
      "Please include all scripts in the add-on. For more information, refer to https://mzl.la/2uEOkYp.":[ "Prošu zapřijmiće wšě skripty do přidatka. Za dalše informacije hlejće https://mzl.la/2uEOkYp." ],
      "Inline scripts blocked by default":[ "Skripty inline so po standardźe blokuja" ],
      "Default CSP rules prevent inline JavaScript from running (https://mzl.la/2pn32nd).":[ "" ],
      "Unadvised 3rd-party JS library":[ "Njeznata JS-biblioteka třećeho poskićowarja" ],
      "Your add-on uses a JavaScript library we do not recommend. Read more: https://bit.ly/1TRIyZY":[ "Waš přidatk biblioteku JavaScript wužiwa, kotruž njeporučamy. Hlejće wjace: https://bit.ly/1TRIyZY" ],
      "Known JS library detected":[ "Znata JS-biblioteka namakana" ],
      "JavaScript libraries are discouraged for simple add-ons, but are generally accepted.":[ "" ],
      "Due to both security and performance concerns, this may not be set using dynamic values which have not been adequately sanitized. This can lead to security issues or fairly serious performance degradation.":[ "" ],
      "{{api}} is not supported":[ "{{api}} so njepodpěruje" ],
      "This API has not been implemented by Firefox.":[ "Tutón API njeje so přez Firefox implementował." ],
      "\"{{api}}\" has been removed in Manifest Version 3 (`manifest_version` property)":[ "„{{api}}“ je so we wersiji 3 manifest wotstronił (kajkosć `manifest_version`)" ],
      "{{api}} is deprecated":[ "{{api}} je zestarjeny" ],
      "This API has been deprecated by Firefox.":[ "Tutón API so přez Firefox hižo njepodpěruje." ],
      "Content script file could not be found.":[ "Wobsahowa skriptowa dataja njeda so namakać." ],
      "\"{{api}}\" is deprecated or unimplemented":[ "„{{api}}“ so hižo njepodpěruje abo je njeimplementowany" ],
      "Content script file could not be found":[ "Wobsahowa skriptowa dataja njeda so namakać" ],
      "\"%(api)s\" can cause issues when loaded temporarily":[ "„%(api)s“ móže problemy zawinować, hdyž so nachwilu začita" ],
      "This API can cause issues when loaded temporarily using about:debugging in Firefox unless you specify \"browser_specific_settings.gecko.id\" in the manifest. Please see: https://mzl.la/2hizK4a for more.":[ "" ],
      "{{api}} is not supported in Firefox version {{minVersion}}":[ "{{api}} so we wersiji {{minVersion}} Firefox njepodpěruje" ],
      "This API is not implemented by the given minimum Firefox version":[ "Tutón API njeje so přez podatu minimalnu wersiju Firefox implementował" ],
      "{{api}} is not supported in Firefox for Android version {{minVersion}}":[ "{{api}} so we wersiji {{minVersion}} Firefox za Android njepodpěruje" ],
      "This API is not implemented by the given minimum Firefox for Android version":[ "Tutón API njeje so přez podatu minimalnu wersiju Firefox za Android implementował" ],
      "Content script file name should not be empty.":[ "Mjeno wobsahoweje skriptoweje dataje njeměło prózdne być." ],
      "Content script file name should not be empty":[ "Mjeno wobsahoweje skriptoweje dataje njeměło prózdne być" ],
      "\"%(method)s\" called with a non-literal uri":[ "" ],
      "Calling \"%(method)s\" with variable parameters can result in potential security vulnerabilities if the variable contains a remote URI. Consider using 'window.open' with the 'chrome=no' flag.":[ "" ],
      "\"%(method)s\" called with non-local URI":[ "" ],
      "Calling \"%(method)s\" with a non-local URI will result in the dialog being opened with chrome privileges.":[ "" ],
      "JavaScript syntax error":[ "Syntaksowy zmylk JavaScript" ],
      "There is a JavaScript syntax error in your code, which might be related to some experimental JavaScript features that aren't an official part of the language specification and therefore not supported yet. The validation cannot continue on this file.":[ "" ],
      "Evaluation of strings as code can lead to security vulnerabilities and performance issues, even in the most innocuous of circumstances. Please avoid using `eval` and the `Function` constructor when at all possible.":[ "" ],
      "setTimeout, setInterval and execScript functions should be called only with function expressions as their first argument":[ "" ],
      "Unexpected global passed as an argument":[ "Njewočakowana globala je so jako argument přepodała" ],
      "Passing a global as an argument is not recommended. Please make this a var instead.":[ "" ],
      "Use of document.write strongly discouraged.":[ "Wot wužiwanja document.write so wuraznje wotradźuje." ],
      "document.write will fail in many circumstances when used in extensions, and has potentially severe security repercussions when used improperly. Therefore, it should not be used.":[ "" ],
      "Banned 3rd-party JS library":[ "JS-biblioteka třećeho poskićowarja zakazana" ],
      "Your add-on uses a JavaScript library we consider unsafe. Read more: https://bit.ly/1TRIyZY":[ "Waš přidatk biblioteku JavaScript wužiwa, kotruž za njewěstu mamy. Hlejće wjace: https://bit.ly/1TRIyZY" ],
      "Your JSON contains block comments.":[ "Waš JSON blokowe komentary wobsahuje." ],
      "Only line comments (comments beginning with \"//\") are allowed in JSON files. Please remove block comments (comments beginning with \"/*\")":[ "" ],
      "Duplicate keys are not allowed in JSON files.":[ "Dwójne kluče w JSON-datajach dowolene njejsu." ],
      "Duplicate key found in JSON file.":[ "Dwójny kluč je so w dataji JSON namakał." ],
      "Your JSON is not valid.":[ "Waš JSON płaćiwy njeje." ],
      "Your JSON file could not be parsed.":[ "Waša JSON-dataja njeda so parsować." ],
      "Reserved filename found.":[ "Rezerwowane datajowe mjeno namakane." ],
      "Files whose names are reserved have been found in the add-on. Please refrain from using them and rename your files.":[ "" ],
      "The package is invalid. It may contain entries using invalid characters, as an example using '\\' as a path separator is not allowed in Firefox. Try to recreate your add-on package (ZIP) and make sure all entries are using '/' as the path separator.":[ "" ],
      "We were unable to decompress the zip file.":[ "Njemóžachmy zip-dataju rozpakować." ],
      "manifest.json was not found":[ "manifest.json njeje so namakał" ],
      "No manifest.json was found at the root of the extension. The package file must be a ZIP of the extension's files themselves, not of the containing directory. See: https://mzl.la/2r2McKv for more on packaging.":[ "" ],
      "File is too large to parse.":[ "Dataja je přewulka za parsowanje." ],
      "This file is not binary and is too large to parse. Files larger than %(maxFileSizeToParseMB)sMB will not be parsed. Consider moving large lists of data out of JavaScript files and into JSON files, or splitting very large files into smaller ones.":[ "" ],
      "Hidden file flagged":[ "Schowana dataja woznamjenjena" ],
      "Hidden files complicate the review process and can contain sensitive information about the system that generated the add-on. Please modify the packaging process so that these files aren't included.":[ "" ],
      "Package contains duplicate entries":[ "Pakćik dwójne zapiski wobsahuje" ],
      "Flagged filename found":[ "Woznamjenjene datajowe mjeno namakane" ],
      "Files were found that are either unnecessary or have been included unintentionally. They should be removed.":[ "" ],
      "The package contains multiple entries with the same name. This practice has been banned. Try unzipping and re-zipping your add-on package and try again.":[ "" ],
      "Flagged file extensions found":[ "Woznamjenjene datajowe kóncowki namakane" ],
      "Flagged file type found":[ "Woznamjenjeny datajowy typ namakany" ],
      "Files whose names end with flagged extensions have been found in the add-on. The extension of these files are flagged because they usually identify binary components. Please see https://bit.ly/review-policy for more information on the binary content review process.":[ "" ],
      "Package already signed":[ "Pakćik je hižo signowany" ],
      "Add-ons which are already signed will be re-signed when published on AMO. This will replace any existing signatures on the add-on.":[ "" ],
      "Firefox add-ons are not allowed to run coin miners.":[ "Přidatki Firefox njesmědźa pjenjezykopaki wuwjesć." ],
      "We do not allow coinminer scripts to be run inside WebExtensions. See https://github.com/mozilla/addons-linter/issues/1643 for more details.":[ "" ],
      "String name is reserved for a predefined message":[ "Mjeno znamješkoweho rjećazka je wuměnjene za předdefinowanu zdźělenku" ],
      "String names starting with @@ get translated to built-in constants (https://mzl.la/2BL9ZjE).":[ "" ],
      "String name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2Eztyi5).":[ "" ],
      "Placeholder for message is missing":[ "Zastupowacy symbol za zdźělenku faluje" ],
      "A placeholder used in the message is not defined.":[ "Zastupowacy symbol, kotryž so w zdźělence wužiwa, njeje definowany." ],
      "Placeholder name contains invalid characters":[ "Mjeno zastupowaceho symbola njepłaćiwe znamješka wobsahuje" ],
      "Placeholder name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2ExbYez).":[ "" ],
      "Placeholder is missing the content property":[ "Zastupowacemu symbolej kajkosć content faluje" ],
      "A placeholder needs a content property defining the replacement of it (https://mzl.la/2DT1MQd)":[ "" ],
      "Translation string is missing the message property":[ "Přełožowanskemu znamješkowemu rjećazkej kajkosć message faluje" ],
      "No \"message\" message property is set for a string (https://mzl.la/2DSBTjA).":[ "Zdźělenska kajkosć \"message\" njeje za znamješkowy rjećazk nastajena (https://mzl.la/2DSBTjA)." ],
      "The field is required.":[ "Polo je trěbne." ],
      "See https://mzl.la/1ZOhoEN (MDN Docs) for more information.":[ "Hlejće https://mzl.la/1ZOhoEN (MDN Docs) za dalše informacije." ],
      "The permission type is unsupported.":[ "Typ dowolnosće so njepodpěruje." ],
      "See https://mzl.la/1R1n1t0 (MDN Docs) for more information.":[ "Hlejće https://mzl.la/1R1n1t0 (MDN Docs) za dalše informacije." ],
      "See https://mzl.la/2Qn0fWC (MDN Docs) for more information.":[ "Hlejće https://mzl.la/2Qn0fWC (MDN Docs) za dalše informacije." ],
      "See https://mzl.la/3Woeqv4 (MDN Docs) for more information.":[ "Hlejće https://mzl.la/3Woeqv4 (MDN Docs) za dalše informacije." ],
      "Unknown permission.":[ "Njeznata dowolnosć." ],
      "%(instancePath)s: the following privileged permissions are only allowed in privileged extensions: %(privilegedPermissions)s.":[ "" ],
      "Invalid host permission.":[ "Njepłaćiwe hostowe prawo" ],
      "Invalid install origin.":[ "Njepłaćiwy instalowanski pochad." ],
      "Invalid install origin. A valid origin has - only - a scheme, hostname and optional port. See https://mzl.la/3TEbqbE (MDN Docs) for more information.":[ "" ],
      "The field is invalid.":[ "Polo je njepłaćiwe." ],
      "\"manifest_version\" in the manifest.json is not a valid value":[ "\"manifest_version\" w manifest.json płaćiwa hódnota njeje" ],
      "See https://mzl.la/20PenXl (MDN Docs) for more information.":[ "Hlejće https://mzl.la/20PenXl (MDN Docs) za dalše informacije." ],
      "\"%(property)s\" allows remote code execution in manifest.json":[ "„%(property)s“ wuwjedźenje zdaleneho koda w manifest.json dowoluje" ],
      "A custom \"%(property)s\" needs additional review.":[ "Swójska kajkosć „%(property)s“ sej přidatne přepruwowanje wužaduje." ],
      "\"%(property)s\" allows 'eval', which has strong security and performance implications.":[ "" ],
      "In most cases the same result can be achieved differently, therefore it is generally prohibited":[ "" ],
      "The \"name\" property must be a string with no leading/trailing whitespaces.":[ "" ],
      "See http://mzl.la/1STmr48 (MDN Docs) for more information.":[ "Hlejće http://mzl.la/1STmr48 (MDN Docs) za dalše informacije." ],
      "\"update_url\" is not allowed.":[ "\"update_url\" dowoleny njeje." ],
      "\"applications.gecko.update_url\" or \"browser_specific_settings.gecko.update_url\" are not allowed for Mozilla-hosted add-ons.":[ "" ],
      "The \"update_url\" property is not used by Firefox.":[ "Kajkosć \"update_url\" so wot Firefox njewužiwa." ],
      "The \"update_url\" is not used by Firefox in the root of a manifest; your add-on will be updated via the Add-ons site and not your \"update_url\". See: https://mzl.la/25zqk4O":[ "" ],
      "\"strict_max_version\" not required.":[ "\"strict_max_version\" trěbny njeje." ],
      "\"strict_max_version\" shouldn't be used unless the add-on is expected not to work with future versions of Firefox.":[ "" ],
      "Manifest Version 3 is not fully supported on Firefox for Android.":[ "" ],
      "No \"%(property)s\" property found in manifest.json":[ "Kajkosć „%(property)s“ njeje so w manifest.json namakała" ],
      "\"%(property)s\" is required":[ "Kajkosć „%(property)s“ je trěbna" ],
      "An icon defined in the manifest could not be found in the package.":[ "Symbol, kotryž je so w manifesće definował, njeda so w pakćiku namakać." ],
      "Icon could not be found at \"%(path)s\".":[ "Symbol njeda so na \"%(path)s\" namakać." ],
      "A background script defined in the manifest could not be found.":[ "Pozadkowy skript, kotryž je so w manifesće definował, njeda so namakać." ],
      "A background page defined in the manifest could not be found.":[ "Pozadkowa strona, kotraž je so w manifesće definowała, njeda so namakać." ],
      "Background script could not be found at \"%(path)s\".":[ "Pozadkowy skript njeda so na \"%(path)s\" namakać." ],
      "Background page could not be found at \"%(path)s\".":[ "Pozadkowa strona njeda so na \"%(path)s\" namakać." ],
      "A content script defined in the manifest could not be found.":[ "Wobsahowy skript, kotryž je so w manifesće definował, njeda so namakać." ],
      "A content script css file defined in the manifest could not be found.":[ "CSS-dataja wobsahoweho skripta, kotraž je so w manifesće definowała, njeda so namakać." ],
      "Content script defined in the manifest could not be found at \"%(path)s\".":[ "Wobsahowy skript, kotryž je so w manifesće definował, njeda so na \"%(path)s\" namakać." ],
      "Content script css file defined in the manifest could not be found at \"%(path)s\".":[ "CSS-dataja wobsahoweho skripta, kotraž je so w manifesće definowała, njeda so na \"%(path)s\" namakać." ],
      "A dictionary file defined in the manifest could not be found.":[ "Słownikowa dataja, kotraž je so w manifesće definowała, njeda so namakać." ],
      "Dictionary file defined in the manifest could not be found at \"%(path)s\".":[ "Słownikowa dataja, kotraž je so w manifesće definowała, njeda so na \"%(path)s\" namakać." ],
      "The manifest contains multiple dictionaries.":[ "Manifest wjacore słowniki wobsahuje." ],
      "Multiple dictionaries were defined in the manifest, which is unsupported.":[ "Wjacore słowniki su so w manifesće definowali, štož so njepodpěruje." ],
      "The manifest contains a dictionaries object, but it is empty.":[ "Manifest słownikowy objekt wobsahuje, ale je prózdny." ],
      "A dictionaries object was defined in the manifest, but it was empty.":[ "Słownikowy objekt je so w manifesće definował, ale je prózdny był." ],
      "The manifest contains a dictionary but no id property.":[ "Manifest słownik wobsahuje, ale nic kajkosć ID." ],
      "A dictionary was found in the manifest, but there was no id set.":[ "Słownik je so w manifesće namakał, ale njeje so žadyn ID postajił." ],
      "Please refer to https://github.com/mozilla-extensions/xpi-manifest to learn more about privileged extensions and signing.":[ "" ],
      "Forbidden content found in add-on.":[ "Zakazany wobsah w přidatku namakany." ],
      "This add-on contains forbidden content.":[ "Tutón přidatk zakazany wobsah wobsahuje." ],
      "Icons must be square.":[ "Symbole dyrbja kwadratiske być." ],
      "Icon at \"%(path)s\" must be square.":[ "Symbol na \"%(path)s\" dyrbi kwadratiski być." ],
      "The size of the icon does not match the manifest.":[ "Wulkosć symbola manifestej njewotpowěduje." ],
      "Expected icon at \"%(path)s\" to be %(expected)d pixels wide but was %(actual)d.":[ "" ],
      "\"%(fieldName)s\" is ignored for non-privileged add-ons.":[ "„%(fieldName)s“ so za njepriwilegowane přidatki ignoruje." ],
      "Corrupt image file":[ "Wobškodźena wobrazowa dataja" ],
      "Expected icon file at \"%(path)s\" is corrupted":[ "Wočakowana symbolowa dataja na \"%(path)s\" je wobškodźena" ],
      "This property has been deprecated.":[ "Tuta kajkosć so hižo njepodpěruje." ],
      "This theme LWT alias has been removed in Firefox 70.":[ "Alias LWT tuteje drasty je so w Firefox 70 wotstronił." ],
      "See https://mzl.la/2T11Lkc (MDN Docs) for more information.":[ "Hlejće https://mzl.la/2T11Lkc (MDN Docs) za dalše informacije." ],
      "Theme image for \"%(type)s\" could not be found at \"%(path)s\"":[ "Drastowy wobraz za \"%(type)s\" njeda so na \"%(path)s\" namakać" ],
      "\"%(fieldName)s\" manifest field is only used for privileged and temporarily installed extensions.":[ "" ],
      "Corrupted theme image file":[ "Wobškodźena wobrazowa dataja drasty" ],
      "Theme image file at \"%(path)s\" is corrupted":[ "Wobrazowa dataja drasty na \"%(path)s\" je wobškodźena" ],
      "Theme image file has an unsupported file extension":[ "Wobrazowa dataja drasty ma datajowu kóncowku, kotraž so njepodpěruje" ],
      "Theme image file at \"%(path)s\" has an unsupported file extension":[ "Wobrazowa dataja drasty na \"%(path)s\" ma datajowu kóncowku, kotraž so njepodpěruje" ],
      "Theme image file has an unsupported mime type":[ "Wobrazowa dataja drasty ma typ MIME, kotryž so njepodpěruje" ],
      "Theme image file at \"%(path)s\" has the unsupported mime type \"%(mime)s\"":[ "Wobrazowa dataja drasty na \"%(path)s\" ma typ MIME \"%(mime)s\", kotryž so njepodpěruje" ],
      "Theme image file mime type does not match its file extension":[ "Typ MIME wobrazoweje dataje drasty jeje datajowej kóncowce njewotpowěduje" ],
      "Theme image file extension at \"%(path)s\" does not match its actual mime type \"%(mime)s\"":[ "Kóncowka wobrazoweje dataje drasty na \"%(path)s\" jeje woprawdźitemu typej MIME \"%(mime)s\" njewotpowěduje" ],
      "The \"default_locale\" is missing localizations.":[ "W \"default_locale\" lokalizacije faluja." ],
      "The \"default_locale\" value is specified in the manifest, but no matching \"messages.json\" in the \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "" ],
      "The \"default_locale\" is missing but \"_locales\" exist.":[ "\"default_locale\" faluje, ale \"_locales\" eksistuje." ],
      "The \"default_locale\" value is not specifed in the manifest, but a \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "" ],
      "Unsupported image extension":[ "Kóncowka wobrazoweje dataje so njepodpěruje" ],
      "Icons should be one of JPG/JPEG, WebP, GIF, PNG or SVG.":[ "Symbole měli dataje typa JPG/JPEG, WebP, GIF, PNG abo SVG być." ],
      "\"applications\" property overridden by \"browser_specific_settings\" property":[ "Kajkosć „applications“ je so přez kajkosć „browser_specific_settings“ přepisała" ],
      "The \"applications\" property is being ignored because it is superseded by the \"browser_specific_settings\" property which is also defined in your manifest. Consider removing applications.":[ "" ],
      "Empty language directory":[ "Pródzny zapis rěčow" ],
      "messages.json file missing in \"%(path)s\"":[ "Dataja messages.json w \"%(path)s\" faluje" ],
      "Manifest key not supported by the specified minimum Firefox version":[ "Manifestowy kluč so přez podatu minimalnu wersiju Firefox njepodpěruje" ],
      "\"strict_min_version\" requires Firefox %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "" ],
      "\"%(fieldName)s\" is not supported in manifest versions %(versionRange)s.":[ "" ],
      "Permission not supported by the specified minimum Firefox version":[ "Prawo so přez podatu minimalnu wersiju Firefox njepodpěruje" ],
      "\"%(fieldName)s\" is not supported.":[ "„%(fieldName)s“ so njepodpěruje." ],
      "Manifest key not supported by the specified minimum Firefox for Android version":[ "Manifestowy kluč so přez podatu minimalnu wersiju Firefox za Android njepodpěruje" ],
      "\"strict_min_version\" requires Firefox for Android %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "" ],
      "Permission not supported by the specified minimum Firefox for Android version":[ "Prawo so přez podatu minimalnu wersiju Firefox za Android njepodpěruje" ],
      "Linking to \"addons.mozilla.org\" is not allowed":[ "Wotkazowanje na „addons.mozilla.org“ dowolene njeje" ],
      "Links directing to \"addons.mozilla.org\" are not allowed to be used for homepage":[ "Wotkazy, kotrež na „addons.mozilla.org“ pokazuja, njesmědźa so za startowu stronu wužiwać." ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above":[ "" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above. Please update your manifest.json version to specify a minimum Firefox version.":[ "" ],
      "The extension ID is required in Manifest Version 3 and above.":[ "ID rozšěrjenja je w manifesće wersije 3 a w nowšich trěbny." ],
      "See https://mzl.la/3PLZYdo for more information.":[ "Hlejće https://mzl.la/3PLZYdo za dalše informacije." ],
      "%(instancePath)s: Privileged extensions should declare privileged permissions.":[ "" ],
      "This extension does not declare any privileged permission. It does not need to be signed with the privileged certificate. Please upload it directly to https://addons.mozilla.org/.":[ "" ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for privileged extensions.":[ "" ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for extensions that include privileged manifest fields.":[ "" ],
      "%(instancePath)s: privileged manifest fields are only allowed in privileged extensions.":[ "" ],
      "This extension does not include the \"mozillaAddons\" permission, which is required for privileged extensions.":[ "" ],
      "Cannot use actions in hidden add-ons.":[ "Akcije njedadźa so w schowanych přidatkach wužiwać." ],
      "The hidden and browser_action/page_action (or action in Manifest Version 3 and above) properties are mutually exclusive.":[ "" ],
      "Use \"browser_specific_settings\" instead of \"applications\".":[ "" ],
      "The \"applications\" property in the manifest is deprecated and will no longer be accepted in Manifest Version 3 and above.":[ "" ],
      "\"applications\" is no longer allowed in Manifest Version 3 and above.":[ "" ],
      "The \"applications\" property in the manifest is no longer allowed in Manifest Version 3 and above. Use \"browser_specific_settings\" instead.":[ "" ],
      "The version string should be simplified because it won't be compatible with Manifest Version 3 and above.":[ "" ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros will no longer be allowed. Letters will no longer be allowed either. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "" ],
      "The version string should be simplified.":[ "Wersijowy znamješkowy rjećazk měł so zjednorić." ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros are not allowed. Letters are no longer allowed. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "" ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported in manifest versions %(versionRange)s.":[ "/%(fieldName)s: \"%(permissionName)s\" so w manifestowych wersijach %(versionRange)s njepodpěruje." ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported.":[ "/%(fieldName)s: \"%(permissionName)s\" so njepodpěruje." ] } } }