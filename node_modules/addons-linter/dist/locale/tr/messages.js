module.exports = { domain:"messages",
  locale_data:{ messages:{ "":{ domain:"messages",
        plural_forms:"nplurals=2; plural=(n != 1);",
        lang:"tr" },
      "Validation Summary:":[ "Doğrulama özeti:" ],
      Code:[ "Kod" ],
      Message:[ "Mesaj" ],
      Description:[ "Açıklama" ],
      File:[ "Dosya" ],
      Line:[ "Satır" ],
      Column:[ "Sütun" ],
      "Invalid manifest version range requested: --min-manifest-version (currently set to %(minManifestVersion)s) should not be greater than --max-manifest-version (currently set to %(maxManifestVersion)s).":[ "" ],
      "Your FTL is not valid.":[ "FTL geçerli değil." ],
      "Your FTL file could not be parsed.":[ "FTL dosyanız işlenemedi." ],
      "Remote scripts are not allowed as per the Add-on Policies.":[ "Eklenti Politikası gereğince uzak betikler kabul edilmemektedir." ],
      "Please include all scripts in the add-on. For more information, refer to https://mzl.la/2uEOkYp.":[ "Lütfen eklentideki tüm betik dosyalarını ekleyin. Daha fazla bilgi için https://mzl.la/2uEOkYp adresine bakabilirsiniz." ],
      "Inline scripts blocked by default":[ "Satır içi betikler varsayılan olarak engellenmiştir" ],
      "Default CSP rules prevent inline JavaScript from running (https://mzl.la/2pn32nd).":[ "Varsayılan CSP kuralları, satır içi JavaScript’in çalışmasını engeller (https://mzl.la/2pn32nd)." ],
      "Unadvised 3rd-party JS library":[ "Önerilmeyen üçüncü taraf JS kütüphanesi" ],
      "Your add-on uses a JavaScript library we do not recommend. Read more: https://bit.ly/1TRIyZY":[ "Eklentiniz önermediğimiz bir JavaScript kitaplığı kullanıyor. Daha fazla bilgi için: https://bit.ly/1TRIyZY" ],
      "Known JS library detected":[ "Bilinen JS kütüphanesi algılandı" ],
      "JavaScript libraries are discouraged for simple add-ons, but are generally accepted.":[ "JavaScript kitaplıkları basit eklentiler için önerilmez fakat genellikle kabul edilir." ],
      "Due to both security and performance concerns, this may not be set using dynamic values which have not been adequately sanitized. This can lead to security issues or fairly serious performance degradation.":[ "" ],
      "{{api}} is not supported":[ "{{api}} desteklenmiyor" ],
      "This API has not been implemented by Firefox.":[ "Bu API Firefox'ta kullanılamamaktadır." ],
      "\"{{api}}\" has been removed in Manifest Version 3 (`manifest_version` property)":[ "\"{{api}}\" Manifest Sürüm 3'te kaldırılmıştır (`manifest_version` özelliği)" ],
      "{{api}} is deprecated":[ "{{api}} kullanımdan kaldırıldı" ],
      "This API has been deprecated by Firefox.":[ "Bu API, Firefox tarafından kullanımdan kaldırıldı." ],
      "Content script file could not be found.":[ "İçerik betik dosyası bulunamadı." ],
      "\"{{api}}\" is deprecated or unimplemented":[ "\"{{api}}\" kullanımdan kaldırıldı veya mevcut değil" ],
      "Content script file could not be found":[ "İçerik betik dosyası bulunamadı" ],
      "\"%(api)s\" can cause issues when loaded temporarily":[ "\"%(api)s\" geçici olarak yüklendiğinde sorunlara neden olabilir" ],
      "This API can cause issues when loaded temporarily using about:debugging in Firefox unless you specify \"browser_specific_settings.gecko.id\" in the manifest. Please see: https://mzl.la/2hizK4a for more.":[ "" ],
      "{{api}} is not supported in Firefox version {{minVersion}}":[ "{{api}} Firefox'un {{minVersion}}. sürümünde desteklenmiyor" ],
      "This API is not implemented by the given minimum Firefox version":[ "Bu API, belirtilen minimum Firefox sürümünde kullanılamamaktadır" ],
      "{{api}} is not supported in Firefox for Android version {{minVersion}}":[ "{{api}} Android için Firefox'un {{minVersion}}. sürümünde desteklenmiyor" ],
      "This API is not implemented by the given minimum Firefox for Android version":[ "Bu API, belirtilen minimum Android için Firefox sürümünde kullanılamamaktadır" ],
      "Content script file name should not be empty.":[ "İçerik betiği adı boş bırakılmamalıdır." ],
      "Content script file name should not be empty":[ "İçerik betiği adı boş bırakılmamalıdır" ],
      "\"%(method)s\" called with a non-literal uri":[ "\"%(method)s\" non-literal uri ile çağrıldı" ],
      "Calling \"%(method)s\" with variable parameters can result in potential security vulnerabilities if the variable contains a remote URI. Consider using 'window.open' with the 'chrome=no' flag.":[ "" ],
      "\"%(method)s\" called with non-local URI":[ "\"%(method)s\" yerel olmayan URI ile çağrıldı" ],
      "Calling \"%(method)s\" with a non-local URI will result in the dialog being opened with chrome privileges.":[ "" ],
      "JavaScript syntax error":[ "JavaScript söz dizimi hatası" ],
      "There is a JavaScript syntax error in your code, which might be related to some experimental JavaScript features that aren't an official part of the language specification and therefore not supported yet. The validation cannot continue on this file.":[ "Kodunuzda, dil şartnamesinin resmen parçası olmayan ve bu nedenle henüz desteklenmeyen bazı deneysel JavaScript özellikleriyle ilgili olabilecek bir JavaScript söz dizimi hatası var. Bu dosyada doğrulama devam edemez." ],
      "Evaluation of strings as code can lead to security vulnerabilities and performance issues, even in the most innocuous of circumstances. Please avoid using `eval` and the `Function` constructor when at all possible.":[ "Dizgilerin kod olarak değerlendirilmesi, en zararsız koşullarda bile güvenlik açıklarına ve performans sorunlarına yol açabilir. Lütfen mümkün olduğunca `eval` ve `Function` constructor'ını kullanmaktan kaçının." ],
      "setTimeout, setInterval and execScript functions should be called only with function expressions as their first argument":[ "setTimeout, setInterval ve execScript fonksiyonları yalnızca ilk argümanları olarak fonksiyon ifadeleriyle çağrılmalıdır" ],
      "Unexpected global passed as an argument":[ "Argüman olarak beklenmeyen global aktarıldı" ],
      "Passing a global as an argument is not recommended. Please make this a var instead.":[ "Global bir değişkeni argüman olarak iletmek önerilmez. Lütfen global yerine var tanımlaması kullanın." ],
      "Use of document.write strongly discouraged.":[ "document.write kullanımı kesinlikle önerilmez." ],
      "document.write will fail in many circumstances when used in extensions, and has potentially severe security repercussions when used improperly. Therefore, it should not be used.":[ "" ],
      "Banned 3rd-party JS library":[ "Yasaklı üçüncü taraf JS kütüphanesi" ],
      "Your add-on uses a JavaScript library we consider unsafe. Read more: https://bit.ly/1TRIyZY":[ "Eklentiniz güvenli olmadığını düşündüğümüz bir JavaScript kitaplığı kullanıyor. Daha fazla bilgi için: https://bit.ly/1TRIyZY" ],
      "Your JSON contains block comments.":[ "JSON dosyanız blok yorumlar çeriyor." ],
      "Only line comments (comments beginning with \"//\") are allowed in JSON files. Please remove block comments (comments beginning with \"/*\")":[ "JSON dosyalarında yalnızca tek satırlık yorumlara (\"//\" ile başlayan yorumlar) izin verilir. Lütfen blok yorumları (\"/*\" ile başlayan yorumlar) kaldırın" ],
      "Duplicate keys are not allowed in JSON files.":[ "JSON dosyalarında yinelenen anahtarlara izin verilmemektedir." ],
      "Duplicate key found in JSON file.":[ "JSON dosyasında yinelenen anahtar bulundu." ],
      "Your JSON is not valid.":[ "JSON geçerli değil." ],
      "Your JSON file could not be parsed.":[ "JSON dosyanız ayrıştırılamadı." ],
      "Reserved filename found.":[ "Rezerve dosya adı bulundu." ],
      "Files whose names are reserved have been found in the add-on. Please refrain from using them and rename your files.":[ "Eklentide adı rezerve edilmiş dosyalar bulundu. Lütfen bu adları kullanmaktan kaçının ve dosyalarınızın adını değiştirin." ],
      "The package is invalid. It may contain entries using invalid characters, as an example using '\\' as a path separator is not allowed in Firefox. Try to recreate your add-on package (ZIP) and make sure all entries are using '/' as the path separator.":[ "" ],
      "We were unable to decompress the zip file.":[ "Zip dosyasını açamadık." ],
      "manifest.json was not found":[ "manifest.json dosyası bulunamadı" ],
      "No manifest.json was found at the root of the extension. The package file must be a ZIP of the extension's files themselves, not of the containing directory. See: https://mzl.la/2r2McKv for more on packaging.":[ "" ],
      "File is too large to parse.":[ "Dosya ayrıştırma için çok büyük." ],
      "This file is not binary and is too large to parse. Files larger than %(maxFileSizeToParseMB)sMB will not be parsed. Consider moving large lists of data out of JavaScript files and into JSON files, or splitting very large files into smaller ones.":[ "" ],
      "Hidden file flagged":[ "Gizli dosya işaretlendi" ],
      "Hidden files complicate the review process and can contain sensitive information about the system that generated the add-on. Please modify the packaging process so that these files aren't included.":[ "Gizli dosyalar inceleme işlemini zorlaştırır ve eklentiyi oluşturan sisteme dair hassas bilgiler içerebilir. Lütfen paketleme işlemini bu dosyalar eklenmeyecek şekilde değiştirin." ],
      "Package contains duplicate entries":[ "Paket yinelenen girdiler içermektedir" ],
      "Flagged filename found":[ "İşaretli dosya adı bulundu" ],
      "Files were found that are either unnecessary or have been included unintentionally. They should be removed.":[ "Gereksiz olan veya yanlışlıkla eklenmiş dosyalar bulundu. Bu dosyaların kaldırılması gerekiyor." ],
      "The package contains multiple entries with the same name. This practice has been banned. Try unzipping and re-zipping your add-on package and try again.":[ "" ],
      "Flagged file extensions found":[ "İşaretli dosya uzantıları bulundu" ],
      "Flagged file type found":[ "İşaretlenen dosya türü bulundu" ],
      "Files whose names end with flagged extensions have been found in the add-on. The extension of these files are flagged because they usually identify binary components. Please see https://bit.ly/review-policy for more information on the binary content review process.":[ "" ],
      "Package already signed":[ "Paket zaten imzanmış" ],
      "Add-ons which are already signed will be re-signed when published on AMO. This will replace any existing signatures on the add-on.":[ "Daha önce imzalanmış olan eklentiler AMO’da yayımlandığında yeniden imzalanacak. Bu işlem, eklentideki mevcut imzaları değiştirecek." ],
      "Firefox add-ons are not allowed to run coin miners.":[ "Firefox eklentilerinin sanal para madencileri çalıştırmasına izin verilmemektedir." ],
      "We do not allow coinminer scripts to be run inside WebExtensions. See https://github.com/mozilla/addons-linter/issues/1643 for more details.":[ "WebExtension’ların içinde coinminer betiklerinin çalıştırılmasına izin vermiyoruz. Daha fazla bilgi için https://github.com/mozilla/addons-linter/issues/1643 adresine bakabilirsiniz." ],
      "String name is reserved for a predefined message":[ "Dizgi adı önceden tanımlanmış bir iletiye ayrılmış" ],
      "String names starting with @@ get translated to built-in constants (https://mzl.la/2BL9ZjE).":[ "@@ karakterleriyle başlayan dizgi adları yerleşik sabitlere çevrilir (https://mzl.la/2BL9ZjE)." ],
      "String name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2Eztyi5).":[ "Dizgi adı sadece harf, rakam, _ ve @ karakterlerini içermelidir (https://mzl.la/2Eztyi5)." ],
      "Placeholder for message is missing":[ "Mesaj için yer tutucu eksik" ],
      "A placeholder used in the message is not defined.":[ "Mesajda kullanılan bir yer tutucu tanımlı değil." ],
      "Placeholder name contains invalid characters":[ "Yer tutucu adında geçersiz karakterler var" ],
      "Placeholder name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2ExbYez).":[ "Yer tutucu adı sadece harf, rakam, _ ve @ karakterlerini içermelidir (https://mzl.la/2ExbYez)." ],
      "Placeholder is missing the content property":[ "Yer tutucuda content özelliği eksik" ],
      "A placeholder needs a content property defining the replacement of it (https://mzl.la/2DT1MQd)":[ "Yer tutucular, yerini neyin alacağını tanımlayan bir içerik özelliğine ihtiyaç duyar (https://mzl.la/2DT1MQd)" ],
      "Translation string is missing the message property":[ "Çeviri dizgisinin message özelliği eksik" ],
      "No \"message\" message property is set for a string (https://mzl.la/2DSBTjA).":[ "Bir dizgi için \"message\" mesaj özelliği ayarlanmadı (https://mzl.la/2DSBTjA)." ],
      "The field is required.":[ "Bu alan zorunludur." ],
      "See https://mzl.la/1ZOhoEN (MDN Docs) for more information.":[ "Daha fazla bilgi için https://mzl.la/1ZOhoEN (MDN Docs) adresine bakabilirsiniz." ],
      "The permission type is unsupported.":[ "İzin türü desteklenmemektedir." ],
      "See https://mzl.la/1R1n1t0 (MDN Docs) for more information.":[ "Daha fazla bilgi için https://mzl.la/1R1n1t0 (MDN Docs) sayfasına bakın." ],
      "See https://mzl.la/2Qn0fWC (MDN Docs) for more information.":[ "Daha fazla bilgi için https://mzl.la/2Qn0fWC (MDN Docs) sayfasına bakın." ],
      "See https://mzl.la/3Woeqv4 (MDN Docs) for more information.":[ "Daha fazla bilgi için https://mzl.la/3Woeqv4 (MDN Docs) sayfasına bakın." ],
      "Unknown permission.":[ "Bilinmeyen izin." ],
      "%(instancePath)s: the following privileged permissions are only allowed in privileged extensions: %(privilegedPermissions)s.":[ "" ],
      "Invalid host permission.":[ "Geçersiz sunucu izni." ],
      "Invalid install origin.":[ "Geçersiz yükleme kaynağı." ],
      "Invalid install origin. A valid origin has - only - a scheme, hostname and optional port. See https://mzl.la/3TEbqbE (MDN Docs) for more information.":[ "" ],
      "The field is invalid.":[ "Alan geçersiz." ],
      "\"manifest_version\" in the manifest.json is not a valid value":[ "manifest.json dosyasındaki \"manifest_version\" geçerli bir değer değil" ],
      "See https://mzl.la/20PenXl (MDN Docs) for more information.":[ "Daha fazla bilgi için https://mzl.la/20PenXl (MDN Docs) sayfasına bakın." ],
      "\"%(property)s\" allows remote code execution in manifest.json":[ "manifest.json’daki \"%(property)s\" özelliği uzaktan kod yürütülmesine izin veriyor" ],
      "A custom \"%(property)s\" needs additional review.":[ "" ],
      "\"%(property)s\" allows 'eval', which has strong security and performance implications.":[ "" ],
      "In most cases the same result can be achieved differently, therefore it is generally prohibited":[ "" ],
      "The \"name\" property must be a string with no leading/trailing whitespaces.":[ "\"name\" özelliği başında ve sonunda boşluk olmayan bir dizgi olmalıdır." ],
      "See http://mzl.la/1STmr48 (MDN Docs) for more information.":[ "Daha fazla bilgi için http://mzl.la/1STmr48 (MDN Docs) sayfasına bakın." ],
      "\"update_url\" is not allowed.":[ "\"update_url\" kabul edilmemektedir." ],
      "\"applications.gecko.update_url\" or \"browser_specific_settings.gecko.update_url\" are not allowed for Mozilla-hosted add-ons.":[ "" ],
      "The \"update_url\" property is not used by Firefox.":[ "\"update_url\" özelliği Firefox tarafından kullanılmıyor." ],
      "The \"update_url\" is not used by Firefox in the root of a manifest; your add-on will be updated via the Add-ons site and not your \"update_url\". See: https://mzl.la/25zqk4O":[ "" ],
      "\"strict_max_version\" not required.":[ "\"strict_max_version\" gerekli değil." ],
      "\"strict_max_version\" shouldn't be used unless the add-on is expected not to work with future versions of Firefox.":[ "" ],
      "Manifest Version 3 is not fully supported on Firefox for Android.":[ "Manifest Sürüm 3, Android için Firefox’ta tam olarak desteklenmemektedir." ],
      "No \"%(property)s\" property found in manifest.json":[ "manifest.json içinde \"%(property)s\" özelliği bulunamadı" ],
      "\"%(property)s\" is required":[ "\"%(property)s\" zorunludur" ],
      "An icon defined in the manifest could not be found in the package.":[ "Manifest dosyasında tanımlanan bir simge paketin içinde bulunamadı." ],
      "Icon could not be found at \"%(path)s\".":[ "Simge \"%(path)s\" yolunda bulunamadı." ],
      "A background script defined in the manifest could not be found.":[ "Manifest dosyasında tanımlanan bir arka plan betiği bulunamadı." ],
      "A background page defined in the manifest could not be found.":[ "Manifest dosyasında tanımlanan bir arka plan sayfası bulunamadı." ],
      "Background script could not be found at \"%(path)s\".":[ "Arka plan betiği \"%(path)s\" yolunda bulunamadı." ],
      "Background page could not be found at \"%(path)s\".":[ "Arka plan sayfası \"%(path)s\" yolunda bulunamadı." ],
      "A content script defined in the manifest could not be found.":[ "Manifest dosyasında tanımlanan bir içerik betiği bulunamadı." ],
      "A content script css file defined in the manifest could not be found.":[ "Manifest dosyasında tanımlanan bir içerik betiği css dosyası bulunamadı." ],
      "Content script defined in the manifest could not be found at \"%(path)s\".":[ "Bildirgede tanımlanan içerik betiği \"%(path)s\" yolunda bulunamadı." ],
      "Content script css file defined in the manifest could not be found at \"%(path)s\".":[ "Bildirgede tanımlanan içerik betiği css dosyası \"%(path)s\" yolunda bulunamadı." ],
      "A dictionary file defined in the manifest could not be found.":[ "Manifest dosyasında tanımlanan bir sözlük dosyası bulunamadı." ],
      "Dictionary file defined in the manifest could not be found at \"%(path)s\".":[ "Manifest dosyasında tanımlanan sözlük dosyası \"%(path)s\" konumunda bulunamadı." ],
      "The manifest contains multiple dictionaries.":[ "Manifest birden fazla sözlük içeriyor." ],
      "Multiple dictionaries were defined in the manifest, which is unsupported.":[ "Manifest'te birden fazla sözlük tanımlandı. Bu durum desteklenmiyor." ],
      "The manifest contains a dictionaries object, but it is empty.":[ "Manifest'te dictionaries nesnesi var ama nesne boş." ],
      "A dictionaries object was defined in the manifest, but it was empty.":[ "Manifest'te dictionaries nesnesi tanımlanmış ama nesne boş." ],
      "The manifest contains a dictionary but no id property.":[ "Manifest bir sözlük içeriyor ancak id özelliği içermiyor." ],
      "A dictionary was found in the manifest, but there was no id set.":[ "Manifest'te bir sözlük bulundu, ancak id'si yoktu." ],
      "Please refer to https://github.com/mozilla-extensions/xpi-manifest to learn more about privileged extensions and signing.":[ "Ayrıcalıklı uzantılar ve imzalama hakkında daha fazla bilgi için https://github.com/mozilla-extensions/xpi-manifest adresine bakabilirsiniz." ],
      "Forbidden content found in add-on.":[ "Eklentide yasaklı içerik bulundu." ],
      "This add-on contains forbidden content.":[ "Bu eklentide yasaklı içerik bulunuyor." ],
      "Icons must be square.":[ "Simgeler kare olmalıdır." ],
      "Icon at \"%(path)s\" must be square.":[ "\"%(path)s\" yolundaki simge kare olmalıdır." ],
      "The size of the icon does not match the manifest.":[ "Simge dosyasının boyutu manifest dosyasındaki ile eşleşmiyor." ],
      "Expected icon at \"%(path)s\" to be %(expected)d pixels wide but was %(actual)d.":[ "" ],
      "\"%(fieldName)s\" is ignored for non-privileged add-ons.":[ "Ayrıcalıklı olmayan eklentilerde \"%(fieldName)s\" görmezden gelinir." ],
      "Corrupt image file":[ "Hasarlı resim dosyası" ],
      "Expected icon file at \"%(path)s\" is corrupted":[ "\"%(path)s\" yolundaki beklenilen simge dosyası bozuk." ],
      "This property has been deprecated.":[ "Bu özellik kullanımdan kaldırıldı." ],
      "This theme LWT alias has been removed in Firefox 70.":[ "Bu tema LWT alias'ı Firefox 70'te kaldırılmıştır." ],
      "See https://mzl.la/2T11Lkc (MDN Docs) for more information.":[ "Daha fazla bilgi için https://mzl.la/2T11Lkc (MDN Docs) sayfasına bakın." ],
      "Theme image for \"%(type)s\" could not be found at \"%(path)s\"":[ "\"%(type)s\" için tema görseli \"%(path)s\" konumunda bulunamadı" ],
      "\"%(fieldName)s\" manifest field is only used for privileged and temporarily installed extensions.":[ "\"%(fieldName)s\" manifest alanı yalnızca ayrıcalıklı ve geçici olarak yüklenmiş uzantılar için kullanılır." ],
      "Corrupted theme image file":[ "Bozuk tema resim dosyası" ],
      "Theme image file at \"%(path)s\" is corrupted":[ "\"%(path)s\" adresindeki tema resmi dosyası bozuk" ],
      "Theme image file has an unsupported file extension":[ "Tema resmi dosyası desteklenmeyen bir dosya uzantısına sahip" ],
      "Theme image file at \"%(path)s\" has an unsupported file extension":[ "\"%(path)s\" adresindeki tema resmi desteklenmeyen bir dosya uzantısına sahip" ],
      "Theme image file has an unsupported mime type":[ "Tema resmi desteklenmeyen bir mime türüne sahip" ],
      "Theme image file at \"%(path)s\" has the unsupported mime type \"%(mime)s\"":[ "\"%(path)s\" adresindeki tema resmi desteklenmeyen \"%(mime)s\" mime türüne sahip" ],
      "Theme image file mime type does not match its file extension":[ "Tema resmi dosyasının mime türü, dosya uzantısıyla eşleşmiyor" ],
      "Theme image file extension at \"%(path)s\" does not match its actual mime type \"%(mime)s\"":[ "\"%(path)s\" konumundaki tema resmi dosya uzantısı gerçek \"%(mime)s\" mime türüyle eşleşmiyor" ],
      "The \"default_locale\" is missing localizations.":[ "\"default_locale\" değerinde yerelleştirmeler eksik." ],
      "The \"default_locale\" value is specified in the manifest, but no matching \"messages.json\" in the \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "" ],
      "The \"default_locale\" is missing but \"_locales\" exist.":[ "\"default_locale\" eksik fakat \"_locales\" mevcut." ],
      "The \"default_locale\" value is not specifed in the manifest, but a \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "" ],
      "Unsupported image extension":[ "Desteklenmeyen resim uzantısı" ],
      "Icons should be one of JPG/JPEG, WebP, GIF, PNG or SVG.":[ "Simgeler JPG/JPEG, WebP, GIF, PNG ya da SVG türlerinden biri olmalıdır." ],
      "\"applications\" property overridden by \"browser_specific_settings\" property":[ "\"applications\" özelliği \"browser_spec_settings\" özelliği tarafından geçersiz kılındı" ],
      "The \"applications\" property is being ignored because it is superseded by the \"browser_specific_settings\" property which is also defined in your manifest. Consider removing applications.":[ "" ],
      "Empty language directory":[ "Dil klasörü boş" ],
      "messages.json file missing in \"%(path)s\"":[ "\"%(path)s\" klasöründe messages.json dosyası eksik" ],
      "Manifest key not supported by the specified minimum Firefox version":[ "Manifest anahtarı belirtilen minimum Firefox sürümü tarafından desteklenmiyor" ],
      "\"strict_min_version\" requires Firefox %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "" ],
      "\"%(fieldName)s\" is not supported in manifest versions %(versionRange)s.":[ "\"%(fieldName)s\", %(versionRange)s manifest sürümlerinde desteklenmiyor." ],
      "Permission not supported by the specified minimum Firefox version":[ "Bu İzin, belirtilen minimum Firefox sürümü tarafından desteklenmiyor" ],
      "\"%(fieldName)s\" is not supported.":[ "\"%(fieldName)s\" desteklenmiyor." ],
      "Manifest key not supported by the specified minimum Firefox for Android version":[ "Manifest anahtarı, Android için belirtilen minimum Firefox sürümü tarafından desteklenmiyor" ],
      "\"strict_min_version\" requires Firefox for Android %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "" ],
      "Permission not supported by the specified minimum Firefox for Android version":[ "Bu izin, Android için belirtilen minimum Firefox sürümü tarafından desteklenmiyor" ],
      "Linking to \"addons.mozilla.org\" is not allowed":[ "\"addons.mozilla.org\" adresine bağlantı veremezsiniz" ],
      "Links directing to \"addons.mozilla.org\" are not allowed to be used for homepage":[ "\"addons.mozilla.org\" adresine yönlendiren bağlantıları ana sayfa olarak kullanamazsınız" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above":[ "" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above. Please update your manifest.json version to specify a minimum Firefox version.":[ "" ],
      "The extension ID is required in Manifest Version 3 and above.":[ "Manifest Sürüm 3 ve üzeri sürümlerde uzantı kimliği zorunludur." ],
      "See https://mzl.la/3PLZYdo for more information.":[ "Daha fazla bilgi için https://mzl.la/3PLZYdo adresine bakın." ],
      "%(instancePath)s: Privileged extensions should declare privileged permissions.":[ "%(instancePath)s: Ayrıcalıklı uzantılar ayrıcalıklı izinler bildirmelidir." ],
      "This extension does not declare any privileged permission. It does not need to be signed with the privileged certificate. Please upload it directly to https://addons.mozilla.org/.":[ "" ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for privileged extensions.":[ "%(instancePath)s: Ayrıcalıklı uzantılar için \"mozillaAddons\" izni gereklidir." ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for extensions that include privileged manifest fields.":[ "%(instancePath)s: Ayrıcalıklı manifest alanları içeren uzantılar için \"mozillaAddons\" izni gereklidir." ],
      "%(instancePath)s: privileged manifest fields are only allowed in privileged extensions.":[ "%(instancePath)s: ayrıcalıklı manifest alanlarına yalnızca ayrıcalıklı uzantılarda izin verilir." ],
      "This extension does not include the \"mozillaAddons\" permission, which is required for privileged extensions.":[ "Bu uzantı, ayrıcalıklı uzantılar için gerekli olan \"mozillaAddons\" iznini içermiyor." ],
      "Cannot use actions in hidden add-ons.":[ "Gizli eklentilerde eylemler kullanılamaz." ],
      "The hidden and browser_action/page_action (or action in Manifest Version 3 and above) properties are mutually exclusive.":[ "" ],
      "Use \"browser_specific_settings\" instead of \"applications\".":[ "\"applications\" yerine \"browser_specific_settings\" kullanın." ],
      "The \"applications\" property in the manifest is deprecated and will no longer be accepted in Manifest Version 3 and above.":[ "Manifest dosyasındaki \"applications\" özelliği kullanımdan kaldırıldı ve artık Manifest Sürüm 3 ve üzerinde kabul edilmeyecek." ],
      "\"applications\" is no longer allowed in Manifest Version 3 and above.":[ "\"applications\" özelliğine Manifest Sürüm 3 ve üzerinde artık izin verilmiyor." ],
      "The \"applications\" property in the manifest is no longer allowed in Manifest Version 3 and above. Use \"browser_specific_settings\" instead.":[ "Manifest dosyasındaki \"applications\" özelliğine artık Manifest Sürüm 3 ve üzerinde izin verilmiyor. Onun yerine \"browser_specific_settings\" kullanabilirsiniz." ],
      "The version string should be simplified because it won't be compatible with Manifest Version 3 and above.":[ "Sürüm dizgisi, Manifest Sürüm 3 ve üzeri ile uyumlu olmayacağından basitleştirilmelidir." ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros will no longer be allowed. Letters will no longer be allowed either. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "" ],
      "The version string should be simplified.":[ "Sürüm dizgisi basitleştirilmelidir." ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros are not allowed. Letters are no longer allowed. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "" ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported in manifest versions %(versionRange)s.":[ "/%(fieldName)s: \"%(permissionName)s\", %(versionRange)s manifest sürümlerinde desteklenmiyor." ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported.":[ "/%(fieldName)s: \"%(permissionName)s\" desteklenmiyor." ] } } }