module.exports = { domain:"messages",
  locale_data:{ messages:{ "":{ domain:"messages",
        plural_forms:"nplurals=2; plural=(n != 1);",
        lang:"ka" },
      "Validation Summary:":[ "შემოწმების შეჯამება:" ],
      Code:[ "პირველწყარო" ],
      Message:[ "შეტყობინება" ],
      Description:[ "აღწერილობა" ],
      File:[ "ფაილი" ],
      Line:[ "სტრიქონი" ],
      Column:[ "სვეტი" ],
      "Invalid manifest version range requested: --min-manifest-version (currently set to %(minManifestVersion)s) should not be greater than --max-manifest-version (currently set to %(maxManifestVersion)s).":[ "მოთხოვნილია მანიფესტის ვერსიის უმართებულო შუალედი: --min-manifest-version (ამჟამად მითითებულია %(minManifestVersion)s) არ უნდა იყოს მეტი, ვიდრე --max-manifest-version ( ამჟამად მითითებულია %(maxManifestVersion)s)." ],
      "Your FTL is not valid.":[ "თქვენი FTL არამართებულია." ],
      "Your FTL file could not be parsed.":[ "თქვენი FTL ფაილი ვერ დამუშავდა." ],
      "Remote scripts are not allowed as per the Add-on Policies.":[ "გარეშე სკრიპტები არაა დაშვებული დამატების დებულებებით." ],
      "Please include all scripts in the add-on. For more information, refer to https://mzl.la/2uEOkYp.":[ "გთხოვთ, ყველა სკრიპტი დაურთოთ დამატებას. ვრცლად იხილეთ https://mzl.la/2uEOkYp." ],
      "Inline scripts blocked by default":[ "ხაზოვანი სკრიპტები შეზღუდულია ნაგულისხმევად" ],
      "Default CSP rules prevent inline JavaScript from running (https://mzl.la/2pn32nd).":[ "ნაგულისხმევი CSP-წესები კრძალავს სტრიქონიდან გაშვებულ JavaScript-ს (https://mzl.la/2pn32nd)." ],
      "Unadvised 3rd-party JS library":[ "არასასურველი გარეშე JS ბიბლიოთეკა" ],
      "Your add-on uses a JavaScript library we do not recommend. Read more: https://bit.ly/1TRIyZY":[ "თქვენი დამატება იყენებს JavaScript-ბიბლიოთეკას, რომელიც არასასურველია. იხილეთ ვრცლად: https://bit.ly/1TRIyZY" ],
      "Known JS library detected":[ "აღმოჩენილია ცნობილი JS ბიბლიოთეკა" ],
      "JavaScript libraries are discouraged for simple add-ons, but are generally accepted.":[ "JavaScript-ბიბლიოთეკები მხარდაუჭერელია მარტივი დამატებებისთვის, მაგრამ საერთოდ მისაღებია." ],
      "Due to both security and performance concerns, this may not be set using dynamic values which have not been adequately sanitized. This can lead to security issues or fairly serious performance degradation.":[ "უსაფრთხოებისა და წარმადობის საკითხებიდან გამომდინარე უმჯობესია, არ მიეთითოს ცვალებადი მნიშვნელობები, რომლებიც სათანადოდ დაზღვეული არ იქნება მავნებლობისგან. ეს შეიძლება უსაფრთხოების ხარვეზების ან წარმადობის მნიშვნელოვნად დაქვეითების წყაროდ იქცეს." ],
      "{{api}} is not supported":[ "{{api}} არაა მხარდაჭერილი" ],
      "This API has not been implemented by Firefox.":[ "ეს API არ გამოიყენება Firefox-ში." ],
      "\"{{api}}\" has been removed in Manifest Version 3 (`manifest_version` property)":[ "„{{api}}“ მოცილებულია მანიფესტის მე-3 ვერსიიდან (`manifest_version` – თვისება)" ],
      "{{api}} is deprecated":[ "{{api}} მოძველებულია" ],
      "This API has been deprecated by Firefox.":[ "ეს API მოძველებულია Firefox-ში." ],
      "Content script file could not be found.":[ "სკრიპტის შემცველი ფაილი ვერ მოიძებნა." ],
      "\"{{api}}\" is deprecated or unimplemented":[ "„{{api}}“ მოძველებულია და არ დანერგილა" ],
      "Content script file could not be found":[ "სკრიპტის შემცველი ფაილი ვერ მოიძებნა" ],
      "\"%(api)s\" can cause issues when loaded temporarily":[ "„%(api)s“ ხარვეზებს იწვევს ხოლმე დროებით ჩატვირთვისას" ],
      "This API can cause issues when loaded temporarily using about:debugging in Firefox unless you specify \"browser_specific_settings.gecko.id\" in the manifest. Please see: https://mzl.la/2hizK4a for more.":[ "ეს API ხარვეზებს იწვევს ხოლმე about:debugging-ის გამოყენებით დროებით ჩატვირთვისას Firefox-ში, თუ მანიფესტში არ მიეთითება „browser_specific_settings.gecko.id“. იხილეთ: https://mzl.la/2hizK4a ვრცლად." ],
      "{{api}} is not supported in Firefox version {{minVersion}}":[ "{{api}} არაა მხარდაჭერილი Firefox-ის {{minVersion}} ვერსიაში" ],
      "This API is not implemented by the given minimum Firefox version":[ "ეს API არ გამოიყენება Firefox-ის მოცემულ უმცირეს ვერსიაში" ],
      "{{api}} is not supported in Firefox for Android version {{minVersion}}":[ "{{api}} არაა მხარდაჭერილი Firefox-ის Android-ვერსიაში" ],
      "This API is not implemented by the given minimum Firefox for Android version":[ "ეს API არ გამოიყენება Firefox-ის მოცემულ უმცირეს Android-ვერსიაში" ],
      "Content script file name should not be empty.":[ "სკრიპტის შემცველი ფაილის სახელი ცარიელი არ უნდა იყოს." ],
      "Content script file name should not be empty":[ "სკრიპტის შემცველი ფაილის სახელი ცარიელი არ უნდა იყოს" ],
      "\"%(method)s\" called with a non-literal uri":[ "„%(method)s“ გამოძახებულია არასიტყვიერი URI-ით" ],
      "Calling \"%(method)s\" with variable parameters can result in potential security vulnerabilities if the variable contains a remote URI. Consider using 'window.open' with the 'chrome=no' flag.":[ "„%(method)s“ ცვალებადი პარამეტრებით გამოძახებისას, ქმნის უსაფრთხოების შესაძლო სისუსტეებს, თუ შეიცავს დაშორებულ URI-ს. უმჯობესია გამოიყენოთ 'windows.open' აღნიშვნით 'chrome=no'." ],
      "\"%(method)s\" called with non-local URI":[ "„%(method)s“ გამოძახებულია არაადგილობრივი URI-ით" ],
      "Calling \"%(method)s\" with a non-local URI will result in the dialog being opened with chrome privileges.":[ "„%(method)s“ არაადგილობრივი URI-ით გამოძახებისას იწვევს სარკმლის გახსნას chrome-ჩარჩოზე უფლებების მოპოვებით." ],
      "JavaScript syntax error":[ "JavaScript სინტაქსური შეცდომა" ],
      "There is a JavaScript syntax error in your code, which might be related to some experimental JavaScript features that aren't an official part of the language specification and therefore not supported yet. The validation cannot continue on this file.":[ "თქვენს კოდში JavaScript-ის სინტაქსური შეცდომაა, რომელიც შესაძლოა უკავშირდებოდეს JavaScript-ის ზოგიერთ საცდელ შესაძლებლობას, ოფიციალურად რომ არაა წარმოდგენილი ენის მახასიათებლებში და შესაბამისად, ჯერჯერობით ვერ იქნება მხარდაჭერილი. ამ ფაილის შემოწმებას ვეღარ განვაგრძობთ." ],
      "Evaluation of strings as code can lead to security vulnerabilities and performance issues, even in the most innocuous of circumstances. Please avoid using `eval` and the `Function` constructor when at all possible.":[ "სტრიქონების კოდად შეფასებამ შეიძლება გამოიწვიოს უსაფრთხოებისა და წარმადობის ხარვეზები მეტად უვნებელ პირობებშიც კი. გთხოვთ თავიდან აიცილოთ `eval` და `Function` კონსტრუქტორები, როცა კი შესაძლებელია." ],
      "setTimeout, setInterval and execScript functions should be called only with function expressions as their first argument":[ "setTimeout, setInterval და execScript ფუნქციები უნდა იყოს გამოძახებული მხოლოდ ფუნქციის გამოსახულებების მიწოდებით პირველ არგუმენტად" ],
      "Unexpected global passed as an argument":[ "არგუმენტი მოულოდნელი გადაიცა გლობალურად" ],
      "Passing a global as an argument is not recommended. Please make this a var instead.":[ "გლობალური ცვლადის არგუმენტად გადაცემა არსასურველია. გთხოვთ, სანაცვლოდ გამოიყენოთ Var." ],
      "Use of document.write strongly discouraged.":[ "document.write-ის გამოყენება, მეტად არასასურველია." ],
      "document.write will fail in many circumstances when used in extensions, and has potentially severe security repercussions when used improperly. Therefore, it should not be used.":[ "document.write უმეტესად ვერ მუშაობს გაფართოების შიგნით და წარმოშობს უსაფრთხოების კუთხით მძიმე შედეგებს არასათანადოდ გამოყენებისას. შესაბამისად, არიდებული უნდა იყოს." ],
      "Banned 3rd-party JS library":[ "აკრძალული გარეშე JS ბიბლიოთეკა" ],
      "Your add-on uses a JavaScript library we consider unsafe. Read more: https://bit.ly/1TRIyZY":[ "თქვენი დამატება იყენებს JavaScript-ბიბლიოთეკას, რომელიც საფრთხის შემცველად მიგვაჩნია. იხილეთ ვრცლად: https://bit.ly/1TRIyZY" ],
      "Your JSON contains block comments.":[ "თქვენი JSON შეიცავს მრავალხაზიან შენიშვნებს." ],
      "Only line comments (comments beginning with \"//\") are allowed in JSON files. Please remove block comments (comments beginning with \"/*\")":[ "JSON-ფაილებში მხოლოდ სტრიქონული სახის შენიშვნებია მიღებულია („//“ რომ აქვთ საწყისად). გთხოვთ მოაცილოთ მრავალსტრიქონიანი შენიშვნები („/*“ რომ აქვთ საწყისად)" ],
      "Duplicate keys are not allowed in JSON files.":[ "გაორმაგებული გასაღებები დაუშვებელია JSON ფაილებში." ],
      "Duplicate key found in JSON file.":[ "JSON-ფაილში აღმოჩენილია მეორებული გასაღები." ],
      "Your JSON is not valid.":[ "თქვენი JSON არამართებულია." ],
      "Your JSON file could not be parsed.":[ "თქვენი JSON ფაილის დამუშავება, ვერ ხერხდება." ],
      "Reserved filename found.":[ "აღმოჩენილია ფაილის დაკავებული სახელი." ],
      "Files whose names are reserved have been found in the add-on. Please refrain from using them and rename your files.":[ "ფაილები, რომელთა სახელებიც დაკავებულია, აღმოჩენილია დამატებაში. გთხოვთ, თავი აარიდოთ მათ გამოყენებას და გადაარქვათ სახელები." ],
      "The package is invalid. It may contain entries using invalid characters, as an example using '\\' as a path separator is not allowed in Firefox. Try to recreate your add-on package (ZIP) and make sure all entries are using '/' as the path separator.":[ "კრებული უმართებულოა. შეიძლება შეიცავდეს აკრძალულ სიმბოლოების შემცველ ჩანაწერებს, მაგალითად,  '\\', როგორც მისამართის გამყოფი, მიუღებელია Firefox-ში. სცადეთ თქვენი დამატების კრებულის (ZIP) ხელახლა შექმნა და დარწმუნდით, რომ ყველა ჩანაწერში გამოყენებულია '/' მისამართისთვის." ],
      "We were unable to decompress the zip file.":[ "Zip არქივის გაშიფვრა, ვერ ხერხდება." ],
      "manifest.json was not found":[ "manifest.json ვერ მოიძებნა" ],
      "No manifest.json was found at the root of the extension. The package file must be a ZIP of the extension's files themselves, not of the containing directory. See: https://mzl.la/2r2McKv for more on packaging.":[ "გაფართოების ძირეულ ნაწილში manifest.json ვერ მოინახა. კრეფულის ფაილი უნდა იყოს თავად გაფართოების ფაილების ZIP-არქივი და არა – შემცველი საქაღალდისა. იხილეთ ვრცლად: https://mzl.la/2r2McKv შეფუთვის შესახებ." ],
      "File is too large to parse.":[ "დასამუშავებელი ფაილი ზედმეტად დიდია." ],
      "This file is not binary and is too large to parse. Files larger than %(maxFileSizeToParseMB)sMB will not be parsed. Consider moving large lists of data out of JavaScript files and into JSON files, or splitting very large files into smaller ones.":[ "ეს ფაილი არაა ორობითი და ზედმეტად დიდია დასამუშავებლად. ფაილი თუ აჭარბებს %(maxFileSizeToParseMB)sმბაიტს, ვერ გაირჩევა. შეგიძლიათ გაიტანოთ მონაცემთა დიდი სიები JavaScript-ფაილებიდან JSON-ფაილებში ან დაყოთ ძალზედ დიდი ფაილები მცირე ნაწილებად." ],
      "Hidden file flagged":[ "გასაჩივრებული დამალული ფაილი" ],
      "Hidden files complicate the review process and can contain sensitive information about the system that generated the add-on. Please modify the packaging process so that these files aren't included.":[ "დამალული ფაილები აძნელებს გარჩევის პროცესს და შეიძლება შეიცავდეს საფრთხილო მონაცემებს შემქმნელი სისტემის შესახებ. გთხოვთ შეცვალოთ შეფუთვის ხერხები ისე, რომ ეს ფაილები აღარ დაერთოს." ],
      "Package contains duplicate entries":[ "ნაკრები შეიცავს გაორმაგებულ ერთეულებს" ],
      "Flagged filename found":[ "აღმოჩენილია გასაჩივრებული ფაილის სახელი" ],
      "Files were found that are either unnecessary or have been included unintentionally. They should be removed.":[ "ნაპოვნია ფაილები, რომლებიც ან ზედმეტია, ან შემთხვევით დაერთო. საჭიროა მათი მოცილება." ],
      "The package contains multiple entries with the same name. This practice has been banned. Try unzipping and re-zipping your add-on package and try again.":[ "კრებული შეიცავს რამდენიმე ჩანაწერს ერთი და იმავე სახელით. ამგვარი გადაწყვეტა აკრძალულია. სცადეთ zip-არქივის გახსნა და ხელახლა შეკუმშვა." ],
      "Flagged file extensions found":[ "აღმოჩენილია გასაჩივრებული ფაილის გაფართოება" ],
      "Flagged file type found":[ "აღმოჩენილია გასაჩივრებული ფაილის სახეობა" ],
      "Files whose names end with flagged extensions have been found in the add-on. The extension of these files are flagged because they usually identify binary components. Please see https://bit.ly/review-policy for more information on the binary content review process.":[ "დამატებაში ნაპოვნია ფაილები, რომელთა დასახელებაც საგანგებო გაფართოებებით ბოლოვდება. ფაილთა გაფართოებები საგანგებოდ მიჩნეულია, რადგან, ჩვეულებრივ, ორობითი შემადგენელების აღმნიშვნელია ხოლმე. გთხოვთ, იხილოთ https://bit.ly/review-policy ვრცლად ორობითი შიგთავსის მიმოხილვის შესახებ." ],
      "Package already signed":[ "ნაკრები უკვე დამოწმებულია" ],
      "Add-ons which are already signed will be re-signed when published on AMO. This will replace any existing signatures on the add-on.":[ "უკვე ხელმოწერილი დამატებები ხელახლა დამოწმდება ხელმოწერით AMO-ზე გამოქვეყნებისას. ამით ჩანაცვლდება ყველა არსებული ხელმოწერა დამატებაზე." ],
      "Firefox add-ons are not allowed to run coin miners.":[ "Firefox დამატებებით კრიპტოვალუტის წარმოება არაა დაშვებული." ],
      "We do not allow coinminer scripts to be run inside WebExtensions. See https://github.com/mozilla/addons-linter/issues/1643 for more details.":[ "ჩვენთა აკრძალულია ციფრული ფულის გამომმუშავებელი სკრიპტების გაშვება WebExtensions-ში. იხილეთ https://github.com/mozilla/addons-linter/issues/1643 ვრცლად." ],
      "String name is reserved for a predefined message":[ "სტრიქონის დასახელება დაკავებულია წინასწარ განსაზღვრული შეტყობინებისთვის" ],
      "String names starting with @@ get translated to built-in constants (https://mzl.la/2BL9ZjE).":[ "ჩანაწერები, რომელთაც საწყისად @@ ექნება, გარდაიქმნება ჩაშენებულ მუდმივებად (https://mzl.la/2BL9ZjE)." ],
      "String name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2Eztyi5).":[ "ჩანაწერში მისაღებია მხოლოდ ასოციფრული სიმბოლოები, _ და @ (https://mzl.la/2Eztyi5)." ],
      "Placeholder for message is missing":[ "შეტყობინების ჩანაცვლების არე ვერ მოიძებნა" ],
      "A placeholder used in the message is not defined.":[ "ამ შეტყობინებაში გამოყენებული ჩანაცვლების არე არაა განსაზღვრული." ],
      "Placeholder name contains invalid characters":[ "ჩანაცვლების არის დასახელება შეიცავს დაუშვებელ სიმბოლოებს" ],
      "Placeholder name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2ExbYez).":[ "ჩანაცვლების არეში მისაღებია მხოლოდ ასოციფრული სიმბოლოები, _ და @ (https://mzl.la/2ExbYez)." ],
      "Placeholder is missing the content property":[ "ჩანაცვლების არეს აკლია შიგთავსის თვისება" ],
      "A placeholder needs a content property defining the replacement of it (https://mzl.la/2DT1MQd)":[ "ჩანაცვლების არე საჭიროებს შიგთავსის თვისების განსაზღვრას მისი შემცვლელისთვის (https://mzl.la/2DT1MQd)" ],
      "Translation string is missing the message property":[ "თარგმნილ სტრიქონს არ გააჩნია შეტყობინების თვისება" ],
      "No \"message\" message property is set for a string (https://mzl.la/2DSBTjA).":[ "„message“ შეტყობინების თვისება არაა განსაზღვრული სტრიქონისთვის (https://mzl.la/2DSBTjA)." ],
      "The field is required.":[ "ველის შევსება აუცილებელია." ],
      "See https://mzl.la/1ZOhoEN (MDN Docs) for more information.":[ "იხილეთ https://mzl.la/1ZOhoEN (MDN დოკუმენტაცია) დამატებითი ინფორმაციისთვის." ],
      "The permission type is unsupported.":[ "ამ სახის ნებართვა არაა მხარდაჭერილი." ],
      "See https://mzl.la/1R1n1t0 (MDN Docs) for more information.":[ "იხილეთ https://mzl.la/1R1n1t0 (MDN მასალები), დამატებითი ინფორმაციისთვის." ],
      "See https://mzl.la/2Qn0fWC (MDN Docs) for more information.":[ "იხილეთ ვრცლად https://mzl.la/2Qn0fWC (MDN-მასალები)." ],
      "See https://mzl.la/3Woeqv4 (MDN Docs) for more information.":[ "იხილეთ https://mzl.la/3Woeqv4 (MDN-მასალები) ვრცლად." ],
      "Unknown permission.":[ "უცნობი სახის ნებართვა." ],
      "%(instancePath)s: the following privileged permissions are only allowed in privileged extensions: %(privilegedPermissions)s.":[ "%(instancePath)s: მოცემული აღმატებული ნებართვები მისაღებია მხოლოდ უპირატესობის მქონე გაფართოებებში: %(privilegedPermissions)s." ],
      "Invalid host permission.":[ "შენახვის უმართებულო უფლება." ],
      "Invalid install origin.":[ "ჩადგმის უმართებულო წარმომავლობა." ],
      "Invalid install origin. A valid origin has - only - a scheme, hostname and optional port. See https://mzl.la/3TEbqbE (MDN Docs) for more information.":[ "ჩადგმისთვის უმართებულო წარმომავლობა. მართებულ წარმომავლობის აქვს - მხოლოდ - სქემა, ჰოსტის სახელი და დამატებითი პორტი. იხილეთ https://mzl.la/3TEbqbE (MDN-მასალები) ვრცლად." ],
      "The field is invalid.":[ "ველი არამართებულია." ],
      "\"manifest_version\" in the manifest.json is not a valid value":[ "„manifest_version“-ს manifest.json ფაილში არამართებული მნიშვნელობა აქვს" ],
      "See https://mzl.la/20PenXl (MDN Docs) for more information.":[ "იხილეთ https://mzl.la/20PenXl (MDN მასალები), დამატებითი ინფორმაციისთვის." ],
      "\"%(property)s\" allows remote code execution in manifest.json":[ "„%(property)s“ კოდის დაშორებულად გაშვების საშუალებას იძლევა Manifest.json-ში" ],
      "A custom \"%(property)s\" needs additional review.":[ "მორგებული „%(property)s“ საჭიროებს დამატებით განხილვას." ],
      "\"%(property)s\" allows 'eval', which has strong security and performance implications.":[ "„%(property)s“ საშუალებას იძლევა, გამოიყენებოდეს 'eval', რომელსაც მძლავრი დაცვა და მაღალი წარმადობა აქვს." ],
      "In most cases the same result can be achieved differently, therefore it is generally prohibited":[ "უმეტეს შემთხვევაში ერთიდაიგივე შედეგის მიღება სხვადასხვა გზითაა შესაძლებელი, აქედან გამომდინარე, საერთოდ იკრძალება" ],
      "The \"name\" property must be a string with no leading/trailing whitespaces.":[ "თვისება „name“ უნდა წარმოადგენდეს ჩანაწერს თავში/ბოლოში გამოტოვებული ადგილების გარეშე." ],
      "See http://mzl.la/1STmr48 (MDN Docs) for more information.":[ "იხილეთ http://mzl.la/1STmr48 (MDN მასალები), დამატებითი ინფორმაციისთვის." ],
      "\"update_url\" is not allowed.":[ "„update_url“ არაა დაშვებული." ],
      "\"applications.gecko.update_url\" or \"browser_specific_settings.gecko.update_url\" are not allowed for Mozilla-hosted add-ons.":[ "„applications.gecko.update_url“ ან „browser_specific_settings.gecko.update_url“ აკრძალულია Mozilla-საიტზე განთავსებულ დამატებებზე." ],
      "The \"update_url\" property is not used by Firefox.":[ "„update_url“ თვისებას არ იყენებს Firefox." ],
      "The \"update_url\" is not used by Firefox in the root of a manifest; your add-on will be updated via the Add-ons site and not your \"update_url\". See: https://mzl.la/25zqk4O":[ "„update_url“ მითითებას არ იყენებს Firefox ძირეულ მანიფესტში; თქვენი დამატების განსაახლებლად გამოყენებული იქნება დამატებების საიტი და არა თქვენი „update_url“. იხილეთ: https://mzl.la/25zqk4O" ],
      "\"strict_max_version\" not required.":[ "\"strict_max_version\" არაა აუცილებელი." ],
      "\"strict_max_version\" shouldn't be used unless the add-on is expected not to work with future versions of Firefox.":[ "„strict_max_version“ უმჯობესია არ გამოიყენებოდეს, თუ არაა მოსალოდნელი, რომ დამატება ვერ იმუშავებს Firefox-ის მომდევნო ვერსიებთან." ],
      "Manifest Version 3 is not fully supported on Firefox for Android.":[ "მანიფესტის მე-3 ვერსია სრულად არაა მხარდაჭერილი Firefox-ში Android-ზე." ],
      "No \"%(property)s\" property found in manifest.json":[ "manifest.json-ში თვისება „%(property)s“ ვერ მოიძებნა" ],
      "\"%(property)s\" is required":[ "„%(property)s“ აუცილებელია" ],
      "An icon defined in the manifest could not be found in the package.":[ "მანიფესტში განსაზღვრული ხატულა კრებულში ვერ მოიძებნა." ],
      "Icon could not be found at \"%(path)s\".":[ "ხატულა, ვერ მოიძებნა მისამართზე „%(path)s“." ],
      "A background script defined in the manifest could not be found.":[ "მანიფესტში განსაზღვრული ფონური სკრიპტი ვერ მოიძებნა." ],
      "A background page defined in the manifest could not be found.":[ "მანიფესტში განსაზღვრული ფონური გვერდი ვერ მოიძებნა." ],
      "Background script could not be found at \"%(path)s\".":[ "ფონური სკრიპტი ვერ მოიძებნა მისამართზე „%(path)s“." ],
      "Background page could not be found at \"%(path)s\".":[ "ფონური გვერდი ვერ მოიძებნა მისამართზე „%(path)s“." ],
      "A content script defined in the manifest could not be found.":[ "მანიფესტში განსაზღვრული შიგთავსის სკრიპტი ვერ მოიძებნა." ],
      "A content script css file defined in the manifest could not be found.":[ "მანიფესტში განსაზღვრული შიგთავსის სკრიპტის css-ფაილი ვერ მოიძებნა." ],
      "Content script defined in the manifest could not be found at \"%(path)s\".":[ "მანიფესტში განსაზღვრული შიგთავსის სკრიპტი ვერ მოიძებნა მისამართზე „%(path)s“." ],
      "Content script css file defined in the manifest could not be found at \"%(path)s\".":[ "მანიფესტში განსაზღვრული შიგთავსის სკრიპტის css-ფაილი, ვერ მოიძებნა მისამართზე „%(path)s“." ],
      "A dictionary file defined in the manifest could not be found.":[ "მანიფესტში განსაზღვრული ლექსიკონის ფაილი ვერ მოიძებნა." ],
      "Dictionary file defined in the manifest could not be found at \"%(path)s\".":[ "მანიფესტში განსაზღვრული ლექსიკონის ფაილი, ვერ მოიძებნა მისამართზე „%(path)s“." ],
      "The manifest contains multiple dictionaries.":[ "მანიფესტი შეიცავს რამდენიმე ლექსიკონს." ],
      "Multiple dictionaries were defined in the manifest, which is unsupported.":[ "მანიფესტში განსაზღვრულია რამდენიმე ლექსიკონი, რაც არ არის მხარდაჭერილი." ],
      "The manifest contains a dictionaries object, but it is empty.":[ "მანიფესტი შეიცავს ლექსიკონების ობიექტს, მაგრამ იგი ცარიელია." ],
      "A dictionaries object was defined in the manifest, but it was empty.":[ "ლექსიკონების ობიექტი მანიფესტში განსაზღვრული, მაგრამ ცარიელი იყო." ],
      "The manifest contains a dictionary but no id property.":[ "მანიფესტი შეიცავს ლექსიკონს, მაგრამ id-თვისების გარეშე." ],
      "A dictionary was found in the manifest, but there was no id set.":[ "მანიფესტში ნაპოვნი იყო ლექსიკონი, მაგრამ მითითებული არ ჰქონდა id." ],
      "Please refer to https://github.com/mozilla-extensions/xpi-manifest to learn more about privileged extensions and signing.":[ "გთხოვთ იხილოთ https://github.com/mozilla-extensions/xpi-manifest უპირატესობის მქონე გაფართოებებისა და ხელმოწერების შესახებ ვრცლად." ],
      "Forbidden content found in add-on.":[ "დამატებაში აღმოჩენილია აკრძალული შიგთავსი." ],
      "This add-on contains forbidden content.":[ "დამატება შეიცავს აკრძალულ შიგთავსს." ],
      "Icons must be square.":[ "ხატულები უნდა იყოს კვადრატული ფორმის." ],
      "Icon at \"%(path)s\" must be square.":[ "ხატულა „%(path)s“ მისამართზე უნდა იყოს კვადრატული." ],
      "The size of the icon does not match the manifest.":[ "ხატულის ზომა არ ემთხვევა მანიფესტში განსაზღვრულს." ],
      "Expected icon at \"%(path)s\" to be %(expected)d pixels wide but was %(actual)d.":[ "ხატულა „%(path)s“ მისამართზე უნდა ყოფილიყო %(expected)d პიქსელის სიგანით, მაგრამ აღმოჩნდა %(actual)d." ],
      "\"%(fieldName)s\" is ignored for non-privileged add-ons.":[ "„%(fieldName)s“ უგულებელყოფილია უპირატესობის არმქონე დამატებებისთვის." ],
      "Corrupt image file":[ "სურათი დაზიანებულია" ],
      "Expected icon file at \"%(path)s\" is corrupted":[ "მითითებულ „%(path)s“ მისამართზე ხატულის ფაილი დაზიანებულია" ],
      "This property has been deprecated.":[ "ეს თვისება მოძველებულია." ],
      "This theme LWT alias has been removed in Firefox 70.":[ "თემის LWT-მეტსახელი მოცილებულია Firefox 70-იდან." ],
      "See https://mzl.la/2T11Lkc (MDN Docs) for more information.":[ "იხილეთ https://mzl.la/2T11Lkc (MDN-მასალები), დამატებითი ინფორმაციისთვის." ],
      "Theme image for \"%(type)s\" could not be found at \"%(path)s\"":[ "თემის გამოსახულება სახეობით „%(type)s“ ვერ მოიძებნა მისამართზე „%(path)s“" ],
      "\"%(fieldName)s\" manifest field is only used for privileged and temporarily installed extensions.":[ "„%(fieldName)s“ მანიფესტის ველი გამოიყენება მხოლოდ უპირატესობის მქონე და დროებით ჩადგმული გაფართოებებისთვის." ],
      "Corrupted theme image file":[ "თემის გამოსახულება დაზიანებულია" ],
      "Theme image file at \"%(path)s\" is corrupted":[ "თემის გამოსახულება მისამართზე „%(path)s“ დაზიანებულია" ],
      "Theme image file has an unsupported file extension":[ "თემის გამოსახულებას ფაილის მხარდაუჭერელი გაფართოება აქვს" ],
      "Theme image file at \"%(path)s\" has an unsupported file extension":[ "თემის გამოსახულებას მისამართზე „%(path)s“ ფაილის მხარდაუჭერელი გაფართოება აქვს" ],
      "Theme image file has an unsupported mime type":[ "თემის გამოსახულება მხარდაუჭერელი mime-სახისაა" ],
      "Theme image file at \"%(path)s\" has the unsupported mime type \"%(mime)s\"":[ "თემის გამოსახულება მისამართზე „%(path)s“ მხარდაუჭერელი mime-სახისაა „%(mime)s“" ],
      "Theme image file mime type does not match its file extension":[ "თემის გამოსახულების mime-სახეობა არ ემთხვევა ფაილის გაფართოებას" ],
      "Theme image file extension at \"%(path)s\" does not match its actual mime type \"%(mime)s\"":[ "თემის გამოსახულების გაფართოება მისამართზე „%(path)s“ არ ემთხვევა მის არსებულ mime-სახეობას „%(mime)s“" ],
      "The \"default_locale\" is missing localizations.":[ "„default_locale“ აკლია თარგმანებში." ],
      "The \"default_locale\" value is specified in the manifest, but no matching \"messages.json\" in the \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "„default_locale“ მნიშვნელობა მითითებულია მანიფესტში, მაგრამ შესაბამისი „messages.json“ ვერ მოინახა საქაღალდეში „_locales“. იხილეთ: https://mzl.la/2hjcaEE" ],
      "The \"default_locale\" is missing but \"_locales\" exist.":[ "„default_locale“ აკლია, თუმცა „_locales“ არსებობს." ],
      "The \"default_locale\" value is not specifed in the manifest, but a \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "„default_locale“ მნიშვნელობა არაა მითითებული მანიფესტში, მაგრამ საქაღალდე „_locales“ წარმოდგენილია. იხილეთ: https://mzl.la/2hjcaEE" ],
      "Unsupported image extension":[ "სურათის ეს გაფართოება არაა მხარდაჭერილი" ],
      "Icons should be one of JPG/JPEG, WebP, GIF, PNG or SVG.":[ "ხატულა უნდა იყოს JPG/JPEG, WebP, GIF, PNG, ან SVG." ],
      "\"applications\" property overridden by \"browser_specific_settings\" property":[ "„applications“ თვისებას ანაცვლებს „browser_specific_settings“ თვისება" ],
      "The \"applications\" property is being ignored because it is superseded by the \"browser_specific_settings\" property which is also defined in your manifest. Consider removing applications.":[ "„applications“ თვისება უგულებელყოფილია, ვინაიდან სანაცვლოდ გამოიყენება „browser_specific_settings“, რომელიც ასევე განსაზღვრულია თქვენს მანიფესტში. უმჯობესია მოაცილოთ „applications“." ],
      "Empty language directory":[ "ენის საქაღალდე ცარიელია" ],
      "messages.json file missing in \"%(path)s\"":[ "messages.json ფაილი ვერ მოიძებნა „%(path)s“ მისამართზე" ],
      "Manifest key not supported by the specified minimum Firefox version":[ "მანიფესტის გასაღები მხარდაუჭერელია Firefox-ის მითითებული უმცირესი ვერსიით" ],
      "\"strict_min_version\" requires Firefox %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "„strict_min_version“ საჭიროებს Firefox %(minVersion)s ვერსიას ანუ უფრო ადრინდელ გამოშვებას, ვიდრე %(versionAdded)s ვერსიაა, რომლიდან მოყოლებულიც მხარდაჭერილია „%(key)s“." ],
      "\"%(fieldName)s\" is not supported in manifest versions %(versionRange)s.":[ "„%(fieldName)s“ მხარდაუჭერელია მანიფესტის ვერსიაში %(versionRange)s." ],
      "Permission not supported by the specified minimum Firefox version":[ "ნებართვები მხარდაუჭერელია Firefox-ის მითითებული უმცირესი ვერსიით" ],
      "\"%(fieldName)s\" is not supported.":[ "„%(fieldName)s“ არაა მხარდაჭერილი." ],
      "Manifest key not supported by the specified minimum Firefox for Android version":[ "მანიფესტის გასაღები არ არის მხარდაჭერილი Firefox Android-ის მითითებული უმცირესი ვერსიით" ],
      "\"strict_min_version\" requires Firefox for Android %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "„strict_min_version“ საჭიროებს Firefox Android-ის %(minVersion)s ვერსიას ანუ უფრო ადრინდელ გამოშვებას, ვიდრე %(versionAdded)s ვერსიაა, რომლიდან მოყოლებულიც მხარდაჭერილია „%(key)s“." ],
      "Permission not supported by the specified minimum Firefox for Android version":[ "ნებართვები მხარდაუჭერელია Firefox Android-ის მითითებული უმცირესი ვერსიით" ],
      "Linking to \"addons.mozilla.org\" is not allowed":[ "ბმული „addons.mozilla.org“ მისამართთან აკრძალულია" ],
      "Links directing to \"addons.mozilla.org\" are not allowed to be used for homepage":[ "პირდაპირი ბმული „addons.mozilla.org“ მისამართზე, არ უნდა იდოს მთავარ გვერდზე" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above":[ "„%(permission)s“ ნებართვისთვის საჭიროა „strict_min_version“ მითითებული იყოს „%(minFirefoxVersion)s“ ან უფრო ზემოთ" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above. Please update your manifest.json version to specify a minimum Firefox version.":[ "„%(permission)s“ ნებართვისთვის საჭიროა „strict_min_version“ იყოს მითითებული „%(minFirefoxVersion)s“ ან უფრო ზემოთ. გთხოვთ განაახლოთ manifest.json ვერსია Firefox-ის უმცირესი ვერსიის მითითებით." ],
      "The extension ID is required in Manifest Version 3 and above.":[ "გაფართოებას ნომრით ID ესაჭიროება მე-3 ვერსიის ან უფრო ახალი Manifest." ],
      "See https://mzl.la/3PLZYdo for more information.":[ "იხილეთ https://mzl.la/3PLZYdo ვრცლად." ],
      "%(instancePath)s: Privileged extensions should declare privileged permissions.":[ "%(instancePath)s: უპირატესობის მქონე გაფართოებებს უნდა გააჩნდეს განაცხადი აღმატებული ნებართვებისთვის." ],
      "This extension does not declare any privileged permission. It does not need to be signed with the privileged certificate. Please upload it directly to https://addons.mozilla.org/.":[ "ამ გაფართოებაში არაა აღმატებული ნებართვის განაცხადები. არ საჭიროებს უპირატესი სერტიფიკატით ხელმოწერას. გთხოვთ, ასატვირთად პირდაპირ გამოიყენოთ https://addons.mozilla.org/." ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for privileged extensions.":[ "%(instancePath)s: „mozillaAddons“ ნებართვა საჭიროა უპირატესობის მქონე გაფართოებებისთვის." ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for extensions that include privileged manifest fields.":[ "%(instancePath)s: „mozillaAddons“ ნებართვა საჭიროა უპირატესობის მქონე გაფართოებებისთვის, რომლებიც შეიცავს აღმატებულ Manifest-ველებს." ],
      "%(instancePath)s: privileged manifest fields are only allowed in privileged extensions.":[ "%(instancePath)s: აღმატებული მანიფესტის ველები მისაღებია მხოლოდ უპირატესობის მქონე გაფართოებებში." ],
      "This extension does not include the \"mozillaAddons\" permission, which is required for privileged extensions.":[ "ამ გაფართოებაში არაა წარმოდგენილი „mozillaAddons“ ნებართვა, რომელიც საჭიროა უპირატესობის მქონე გაფართოებებისთვის." ],
      "Cannot use actions in hidden add-ons.":[ "მოქმედებების გამოყენება ფარულ დამატებებში შეუძლებელია." ],
      "The hidden and browser_action/page_action (or action in Manifest Version 3 and above) properties are mutually exclusive.":[ "„hidden“ და „browser_action/page_action“ (ანდა „action“ მანიფესტის ვერსია 3-ში ან ზემოთ) მახასიათებლები ურთიერთგამომრიცხავია." ],
      "Use \"browser_specific_settings\" instead of \"applications\".":[ "გამოიყენეთ „browser_specific_settings“ და არა – „applications“." ],
      "The \"applications\" property in the manifest is deprecated and will no longer be accepted in Manifest Version 3 and above.":[ "„applications“ თვისება manifest-ში მოძველებულია და აღარ იქნება მისაღები Manifest-ის მე-3 ვერსიიდან." ],
      "\"applications\" is no longer allowed in Manifest Version 3 and above.":[ "„applications“ აღარაა მისაღები Manifest-ის მე-3 ვერსიიდან." ],
      "The \"applications\" property in the manifest is no longer allowed in Manifest Version 3 and above. Use \"browser_specific_settings\" instead.":[ "„applications“ თვისება აღარაა მისაღები მანიფესტის მე-3 ვერსიიდან. სანაცვლოდ გამოიყენეთ „browser_specific_settings“." ],
      "The version string should be simplified because it won't be compatible with Manifest Version 3 and above.":[ "ვერსიის ჩანაწერი უნდა გამარტივდეს, რადგან შეუთავსებელი იქნება Manifest-ის მე-3 და უფრო მაღალ ვერსიებთან." ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros will no longer be allowed. Letters will no longer be allowed either. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "ვერსიის ჩანაწერს უნდა წარმოადგენდეს წერტილებით გამოყოფილი 1-დან 4 ერთეულამდე რიცხვი. თითოეული არაუმეტეს 9 ციფრისგან შემდგარი, წინ ნულები კი უკვე აღარაა მისაღები. აგრეთვე მიუღებელია ასოებიც. ვრცლად იხილეთ https://mzl.la/3h3mCRu (MDN-მასალები)." ],
      "The version string should be simplified.":[ "ვერსიის ჩანაწერი გამარტივებული უნდა იყოს." ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros are not allowed. Letters are no longer allowed. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "ვერსიის ჩანაწერს უნდა წარმოადგენდეს წერტილებით გამოყოფილი 1-დან 4 ერთეულამდე რიცხვი. თითოეული არაუმეტეს 9 ციფრისგან უნდა შედგებოდეს წინ ნულების გარეშე. ასოები აღარაა მისაღები. ვრცლად იხილეთ https://mzl.la/3h3mCRu (MDN-მასალები)." ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported in manifest versions %(versionRange)s.":[ "/%(fieldName)s: \"%(permissionName)s\" მხარდაუჭერელია მანიფესტის ვერსიაში %(versionRange)s." ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported.":[ "/%(fieldName)s: \"%(permissionName)s\" არაა მხარდაჭერილი." ] } } }