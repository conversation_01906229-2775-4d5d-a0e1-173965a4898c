module.exports = { domain:"messages",
  locale_data:{ messages:{ "":{ domain:"messages",
        plural_forms:"nplurals=3; plural=(n==1) ? 0 : (n>=2 && n<=4) ? 1 : 2;",
        lang:"sk" },
      "Validation Summary:":[ "Súhrn overenia:" ],
      Code:[ "Kód" ],
      Message:[ "Správa" ],
      Description:[ "Popis" ],
      File:[ "Súbor" ],
      Line:[ "Riadok" ],
      Column:[ "Stĺpec" ],
      "Invalid manifest version range requested: --min-manifest-version (currently set to %(minManifestVersion)s) should not be greater than --max-manifest-version (currently set to %(maxManifestVersion)s).":[ "Požadovaný neplatný rozsah verzie manifestu: --min-manifest-version (momentálne nastavené na %(minManifestVersion)s) by nemalo byť väčšie ako --max-manifest-version (momentálne nastavené na %(maxManifestVersion)s)." ],
      "Your FTL is not valid.":[ "Vaše FTL je neplatné." ],
      "Your FTL file could not be parsed.":[ "Vaše FTL nemohlo byť parsované." ],
      "Remote scripts are not allowed as per the Add-on Policies.":[ "Vzdialené skripty nie sú povolené bezpečnostnou politikou doplnkov." ],
      "Please include all scripts in the add-on. For more information, refer to https://mzl.la/2uEOkYp.":[ "Zahrňte do doplnku všetky skripty. Ďalšie informácie nájdete na https://mzl.la/2uEOkYp." ],
      "Inline scripts blocked by default":[ "Vložené skripty sú v predvolenom nastavení zakázané" ],
      "Default CSP rules prevent inline JavaScript from running (https://mzl.la/2pn32nd).":[ "Predvolené pravidlá CSP zabraňujú spusteniu vloženého JavaScriptu (https://mzl.la/2pn32nd)." ],
      "Unadvised 3rd-party JS library":[ "Neodporúčaná JS knižnica tretej strany" ],
      "Your add-on uses a JavaScript library we do not recommend. Read more: https://bit.ly/1TRIyZY":[ "Váš doplnok používa knižnicu JavaScriptu, ktorú neodporúčame. Ďalšie informácie: https://bit.ly/1TRIyZY" ],
      "Known JS library detected":[ "Bola rozpoznaná známa JS knižnica" ],
      "JavaScript libraries are discouraged for simple add-ons, but are generally accepted.":[ "Pre jednoduché doplnky sa neodporúčajú používať knižnice JavaScriptu, avšak sú vo všeobecnosti akceptované." ],
      "Due to both security and performance concerns, this may not be set using dynamic values which have not been adequately sanitized. This can lead to security issues or fairly serious performance degradation.":[ "Z dôvodov bezpečnosti a výkonu sa toto nemusí dať nastaviť pomocou dynamických hodnôt, ktoré neboli primerane sanitizované. Môže to viesť k problémom so zabezpečením alebo k pomerne vážnemu zníženiu výkonu." ],
      "{{api}} is not supported":[ "{{api}} nie je podporované" ],
      "This API has not been implemented by Firefox.":[ "Toto API nebolo Firefoxom implementované." ],
      "\"{{api}}\" has been removed in Manifest Version 3 (`manifest_version` property)":[ "\"{{api}}\" bolo odstránené v manifeste verzie 3 (vlastnosť `manifest_version`)" ],
      "{{api}} is deprecated":[ "{{api}} je zastarané" ],
      "This API has been deprecated by Firefox.":[ "Toto rozhranie API bolo pre Firefox zastarané." ],
      "Content script file could not be found.":[ "Obsah súboru so skriptom nebol nájdený." ],
      "\"{{api}}\" is deprecated or unimplemented":[ "\"{{api}}\" je zastarané alebo neimplementované" ],
      "Content script file could not be found":[ "Obsah súboru so skriptom nebol nájdený" ],
      "\"%(api)s\" can cause issues when loaded temporarily":[ "API \"%(api)s\" môže spôsobiť problémy pri dočasnom načítaní" ],
      "This API can cause issues when loaded temporarily using about:debugging in Firefox unless you specify \"browser_specific_settings.gecko.id\" in the manifest. Please see: https://mzl.la/2hizK4a for more.":[ "Toto API môže spôsobiť problémy pri dočasnom načítaní pomocou about:debugging vo Firefoxe, pokiaľ v manifeste nešpecifikujete „browser_specific_settings.gecko.id“. Ďalšie informácie na https://mzl.la/2hizK4a." ],
      "{{api}} is not supported in Firefox version {{minVersion}}":[ "{{api}} nie je podporované vo Firefoxe {{minVersion}}" ],
      "This API is not implemented by the given minimum Firefox version":[ "Toto API nebolo danou verziou Firefoxu implementované" ],
      "{{api}} is not supported in Firefox for Android version {{minVersion}}":[ "{{api}} nie je podporované vo Firefoxe pre Android {{minVersion}}" ],
      "This API is not implemented by the given minimum Firefox for Android version":[ "Toto API nebolo danou verziou Firefoxu pre Android implementované" ],
      "Content script file name should not be empty.":[ "Názov súboru so skriptom by nemal byť prázdny." ],
      "Content script file name should not be empty":[ "Názov súboru so skriptom by nemal byť prázdny" ],
      "\"%(method)s\" called with a non-literal uri":[ "\"%(method)s\" volané s iným než doslovným URI" ],
      "Calling \"%(method)s\" with variable parameters can result in potential security vulnerabilities if the variable contains a remote URI. Consider using 'window.open' with the 'chrome=no' flag.":[ "Volanie \"%(method)s\" s premennými parametrami môže viesť k potenciálnym bezpečnostným chybám, ak premenná obsahuje vzdialené URI. Zvážte použitie „window.open“ s príznakom „chrome=no“." ],
      "\"%(method)s\" called with non-local URI":[ "\"%(method)s\" volané s iným ako lokálnym URI" ],
      "Calling \"%(method)s\" with a non-local URI will result in the dialog being opened with chrome privileges.":[ "Volanie \"%(method)s\" s iným ako lokálnym URI bude mať za následok otvorenie dialógového okna s oprávneniami prehliadača." ],
      "JavaScript syntax error":[ "Chyba v syntaxi JavaScriptu" ],
      "There is a JavaScript syntax error in your code, which might be related to some experimental JavaScript features that aren't an official part of the language specification and therefore not supported yet. The validation cannot continue on this file.":[ "Vo vašom kóde je chyba syntaxe JavaScript, ktorá môže súvisieť s niektorými experimentálnymi funkciami JavaScriptu, ktoré nie sú oficiálnou súčasťou špecifikácie jazyka, a preto ešte nie sú podporované. Kontrola tohto súboru nemôže pokračovať." ],
      "Evaluation of strings as code can lead to security vulnerabilities and performance issues, even in the most innocuous of circumstances. Please avoid using `eval` and the `Function` constructor when at all possible.":[ "Vyhodnocovanie reťazcov ako kódu môže viesť k bezpečnostným chybám a problémom s výkonom, a to aj za tých zdanlivo najnevinnejších okolností. Ak je to možné, vyhnite sa používaniu konštruktorov `eval` a `Function`." ],
      "setTimeout, setInterval and execScript functions should be called only with function expressions as their first argument":[ "Funkcie setTimeout, setInterval a execScript by sa mali volať iba s výrazmi funkcií ako ich prvým argumentom" ],
      "Unexpected global passed as an argument":[ "Neočakávaná globálna premenná prešla ako parameter" ],
      "Passing a global as an argument is not recommended. Please make this a var instead.":[ "Neodporúča sa používať globálne premenné ako argument. Použite premennú." ],
      "Use of document.write strongly discouraged.":[ "Použitie document.write sa neodporúča." ],
      "document.write will fail in many circumstances when used in extensions, and has potentially severe security repercussions when used improperly. Therefore, it should not be used.":[ "document.write zlyháva za mnohých okolností, keď sa použije v rozšíreniach, a pri nesprávnom použití má potenciálne vážne dôsledky na bezpečnosť. Preto by sa nemal používať." ],
      "Banned 3rd-party JS library":[ "Zakázaná JS knižnica tretej strany" ],
      "Your add-on uses a JavaScript library we consider unsafe. Read more: https://bit.ly/1TRIyZY":[ "Váš doplnok používa knižnicu JavaScriptu, ktorú považujeme za nebezpečnú. Ďalšie informácie: https://bit.ly/1TRIyZY" ],
      "Your JSON contains block comments.":[ "Váš JSON obsahuje blokové komentáre." ],
      "Only line comments (comments beginning with \"//\") are allowed in JSON files. Please remove block comments (comments beginning with \"/*\")":[ "V súboroch JSON sú povolené iba riadkové komentáre (komentáre začínajúce znakom „//“). Odstráňte blokové komentáre (komentáre začínajúce na „/*“)" ],
      "Duplicate keys are not allowed in JSON files.":[ "V súboroch JSON nie sú povolené duplicitné kľúče." ],
      "Duplicate key found in JSON file.":[ "V súbore JSON sa našiel duplicitný kľúč." ],
      "Your JSON is not valid.":[ "Váš JSON nie je platný." ],
      "Your JSON file could not be parsed.":[ "Váš JSON nemohol byť parsovaný." ],
      "Reserved filename found.":[ "Bol nájdený súbor s rezervovaným názvom." ],
      "Files whose names are reserved have been found in the add-on. Please refrain from using them and rename your files.":[ "V doplnku sa našli súbory, ktorých názvy sú vyhradené. Zdržte sa ich používania a premenujte svoje súbory." ],
      "The package is invalid. It may contain entries using invalid characters, as an example using '\\' as a path separator is not allowed in Firefox. Try to recreate your add-on package (ZIP) and make sure all entries are using '/' as the path separator.":[ "Balík je neplatný. Môže obsahovať položky používajúce neplatné znaky, napríklad použitie znaku „\\“ ako oddeľovača cesty nie je vo Firefoxe povolené. Skúste znova vytvoriť balík so svojim doplnkom (ZIP) a uistite sa, že všetky položky používajú ako oddeľovač cesty znak „/“." ],
      "We were unable to decompress the zip file.":[ "Nepodarilo sa nám rozbaliť zip archív." ],
      "manifest.json was not found":[ "manifest.json sa nepodarilo nájsť" ],
      "No manifest.json was found at the root of the extension. The package file must be a ZIP of the extension's files themselves, not of the containing directory. See: https://mzl.la/2r2McKv for more on packaging.":[ "V koreňovom priečinku rozšírenia sa nenašiel súbor manifest.json. Súbor balíka musí byť ZIP so súbormi rozšírenia, nie s priečinkom, ktorý ich obsahuje. Viac o balení súborov nájdete na https://mzl.la/2r2McKv." ],
      "File is too large to parse.":[ "Súbor je na analýzu príliš veľký." ],
      "This file is not binary and is too large to parse. Files larger than %(maxFileSizeToParseMB)sMB will not be parsed. Consider moving large lists of data out of JavaScript files and into JSON files, or splitting very large files into smaller ones.":[ "Tento súbor nie je binárny a je príliš veľký na analýzu. Súbory väčšie ako %(maxFileSizeToParseMB)s MB nebudú analyzované. Zvážte presun veľkých zoznamov údajov zo súborov JavaScript do súborov JSON alebo rozdelenie veľmi veľkých súborov na menšie." ],
      "Hidden file flagged":[ "Bol označený skrytý súbor" ],
      "Hidden files complicate the review process and can contain sensitive information about the system that generated the add-on. Please modify the packaging process so that these files aren't included.":[ "Skryté súbory komplikujú proces kontroly a môžu obsahovať citlivé informácie o systéme, ktoré doplnok vygeneroval. Upravte proces balenia tak, aby tieto súbory neboli zahrnuté." ],
      "Package contains duplicate entries":[ "Balíček obsahuje duplicitné položky" ],
      "Flagged filename found":[ "Bol nájdený označený názov súboru" ],
      "Files were found that are either unnecessary or have been included unintentionally. They should be removed.":[ "Boli nájdené súbory, ktoré sú buď nepotrebné, alebo boli zahrnuté neúmyselne. Mali by byť odstránené." ],
      "The package contains multiple entries with the same name. This practice has been banned. Try unzipping and re-zipping your add-on package and try again.":[ "Balík obsahuje viacero položiek s rovnakým názvom. Táto prax bola zakázaná. Skúste rozbaliť a znova vytvoriť balíček, a skúste to znova." ],
      "Flagged file extensions found":[ "Bol nájdený označený súbor rozšírenia" ],
      "Flagged file type found":[ "Bol nájdený označený typ súboru" ],
      "Files whose names end with flagged extensions have been found in the add-on. The extension of these files are flagged because they usually identify binary components. Please see https://bit.ly/review-policy for more information on the binary content review process.":[ "V doplnku sa našli súbory, ktorých názvy končia tzv. \"označenými\" príponami. Prípony týchto súborov sú označené, pretože zvyčajne identifikujú binárne komponenty. Ďalšie informácie o procese kontroly binárneho obsahu nájdete na https://bit.ly/review-policy." ],
      "Package already signed":[ "Balíček už bol podpísaný" ],
      "Add-ons which are already signed will be re-signed when published on AMO. This will replace any existing signatures on the add-on.":[ "Doplnky, ktoré sú už podpísané, budú opätovne podpísané pri zverejnení na AMO. Tým sa nahradia všetky existujúce podpisy doplnku." ],
      "Firefox add-ons are not allowed to run coin miners.":[ "Doplnky Firefoxu nesmú ťažiť kryptomeny." ],
      "We do not allow coinminer scripts to be run inside WebExtensions. See https://github.com/mozilla/addons-linter/issues/1643 for more details.":[ "V rámci WebExtensions nepovoľujeme spúšťanie skriptov coinminer. Ďalšie informácie nájdete na https://github.com/mozilla/addons-linter/issues/1643." ],
      "String name is reserved for a predefined message":[ "Názov reťazca je rezervovaný pre preddefinovanú správu" ],
      "String names starting with @@ get translated to built-in constants (https://mzl.la/2BL9ZjE).":[ "Názvy reťazcov začínajúce @@ sa prekladajú na vstavané konštanty (https://mzl.la/2BL9ZjE)." ],
      "String name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2Eztyi5).":[ "Názov reťazca by mal obsahovať iba alfanumerické znaky, _ a @ (https://mzl.la/2Eztyi5)." ],
      "Placeholder for message is missing":[ "Placeholder pre správu chýba" ],
      "A placeholder used in the message is not defined.":[ "Placeholder použitý v správe nie je definovaný." ],
      "Placeholder name contains invalid characters":[ "Placeholder obsahuje neplatné znaky" ],
      "Placeholder name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2ExbYez).":[ "Názov premennej by mal obsahovať iba alfanumerické znaky, _ a @ (https://mzl.la/2ExbYez)." ],
      "Placeholder is missing the content property":[ "Placeholderu chýba vlastnosť content" ],
      "A placeholder needs a content property defining the replacement of it (https://mzl.la/2DT1MQd)":[ "Placeholder musí mať priradený atribút content, ktorý definuje jeho nahradenie (https://mzl.la/2DT1MQd)" ],
      "Translation string is missing the message property":[ "Reťazcu prekladu chýba vlastnosť message" ],
      "No \"message\" message property is set for a string (https://mzl.la/2DSBTjA).":[ "Pre reťazec nie je nastavená správa vo vlastnosti „message“ (https://mzl.la/2DSBTjA)." ],
      "The field is required.":[ "Toto pole je povinné." ],
      "See https://mzl.la/1ZOhoEN (MDN Docs) for more information.":[ "Ďalšie informácie nájdete na https://mzl.la/1ZOhoEN (dokumentácia na MDN)." ],
      "The permission type is unsupported.":[ "Tento typ povolenia nie je podporovaný." ],
      "See https://mzl.la/1R1n1t0 (MDN Docs) for more information.":[ "Ďalšie informácie nájdete na https://mzl.la/1R1n1t0 (dokumentácia na MDN)." ],
      "See https://mzl.la/2Qn0fWC (MDN Docs) for more information.":[ "Ďalšie informácie nájdete na https://mzl.la/2Qn0fWC (Dokumenty MDN)." ],
      "See https://mzl.la/3Woeqv4 (MDN Docs) for more information.":[ "Ďalšie informácie nájdete na stránke https://mzl.la/3Woeqv4 (dokumentácia na MDN)." ],
      "Unknown permission.":[ "Neznáme povolenie." ],
      "%(instancePath)s: the following privileged permissions are only allowed in privileged extensions: %(privilegedPermissions)s.":[ "%(instancePath)s: nasledujúce privilegované povolenia sú povolené len v privilegovaných rozšíreniach: %(privilegedPermissions)s." ],
      "Invalid host permission.":[ "Neplatné povolenie hostiteľa." ],
      "Invalid install origin.":[ "Neplatný pôvod inštalácie." ],
      "Invalid install origin. A valid origin has - only - a scheme, hostname and optional port. See https://mzl.la/3TEbqbE (MDN Docs) for more information.":[ "Neplatná cesta pôvodu inštalácie. Platná cesta je zložená s častí schéma, názov hostiteľa a voliteľne port. Ďalšie informácie nájdete na stránke https://mzl.la/3TEbqbE (Dokumenty MDN)." ],
      "The field is invalid.":[ "Pole je neplatné." ],
      "\"manifest_version\" in the manifest.json is not a valid value":[ "„manifest_version“ nie je v súbore manifest.json platná hodnota" ],
      "See https://mzl.la/20PenXl (MDN Docs) for more information.":[ "Ďalšie informácie nájdete na https://mzl.la/20PenXl (dokumentácia na MDN)." ],
      "\"%(property)s\" allows remote code execution in manifest.json":[ "Vlastnosť \"%(property)s\" umožňuje vzdialené spustenie kódu v manifest.json" ],
      "A custom \"%(property)s\" needs additional review.":[ "Vlastnosť „%(property)s“ vyžaduje ďalšiu kontrolu." ],
      "\"%(property)s\" allows 'eval', which has strong security and performance implications.":[ "Vlastnosť \"%(property)s\" umožňuje použitie funkcie 'eval', čo má silný vplyv na bezpečnosť a výkon." ],
      "In most cases the same result can be achieved differently, therefore it is generally prohibited":[ "Vo väčšine prípadov možno rovnaký výsledok dosiahnuť odlišne, preto je to všeobecne zakázané" ],
      "The \"name\" property must be a string with no leading/trailing whitespaces.":[ "Vlastnosť „name“ musí byť reťazec bez medzier na začiatku/na konci." ],
      "See http://mzl.la/1STmr48 (MDN Docs) for more information.":[ "Ďalšie informácie nájdete na http://mzl.la/1STmr48 (dokumentácia na MDN)." ],
      "\"update_url\" is not allowed.":[ "„update_url“ nie je povolené." ],
      "\"applications.gecko.update_url\" or \"browser_specific_settings.gecko.update_url\" are not allowed for Mozilla-hosted add-ons.":[ "Pre doplnky hostované Mozillou nie sú povolené \"applications.gecko.update_url\" alebo \"browser_specific_settings.gecko.update_url\"." ],
      "The \"update_url\" property is not used by Firefox.":[ "Vlastnosť „update_url“ nie je Firefoxom používaná." ],
      "The \"update_url\" is not used by Firefox in the root of a manifest; your add-on will be updated via the Add-ons site and not your \"update_url\". See: https://mzl.la/25zqk4O":[ "Firefox nepoužíva \"update_url\" definovanú v manifeste; váš doplnok bude aktualizovaný prostredníctvom stránky AMO a nie vašej \"update_url\". Ďalšie informácie: https://mzl.la/25zqk4O" ],
      "\"strict_max_version\" not required.":[ "„strict_max_version“ nie je vyžadované." ],
      "\"strict_max_version\" shouldn't be used unless the add-on is expected not to work with future versions of Firefox.":[ "\"strict_max_version\" by sa nemalo používať, pokiaľ sa neočakáva, že doplnok nebude fungovať s budúcimi verziami Firefoxu." ],
      "Manifest Version 3 is not fully supported on Firefox for Android.":[ "Manifest verzie 3 nie je vo Firefoxe pre Android plne podporovaný." ],
      "No \"%(property)s\" property found in manifest.json":[ "V súbore manifest.json sa nenašla žiadna \"%(property)s\"." ],
      "\"%(property)s\" is required":[ "Vlastnosť \"%(property)s\" je povinná" ],
      "An icon defined in the manifest could not be found in the package.":[ "Ikona definovaná v manifeste nebola v balíčku nájdená." ],
      "Icon could not be found at \"%(path)s\".":[ "V umiestnení „%(path)s“ nebola nájdená ikona." ],
      "A background script defined in the manifest could not be found.":[ "Skript obsahu definovaný v manifeste nebol nájdený." ],
      "A background page defined in the manifest could not be found.":[ "Ikona pozadia definovaná v manifeste nebola v balíčku nájdená." ],
      "Background script could not be found at \"%(path)s\".":[ "Skript nebol nájdený v „%(path)s“." ],
      "Background page could not be found at \"%(path)s\".":[ "Stránka pozadia nebola nájdená v „%(path)s“." ],
      "A content script defined in the manifest could not be found.":[ "Skript obsahu definovaný v manifeste nebol nájdený." ],
      "A content script css file defined in the manifest could not be found.":[ "Skript obsahu súboru css definovaný v manifeste nebol nájdený." ],
      "Content script defined in the manifest could not be found at \"%(path)s\".":[ "Skript obsahu definovaný v manifeste nebol nájdený v „%(path)s“." ],
      "Content script css file defined in the manifest could not be found at \"%(path)s\".":[ "Skript obsahu súboru css definovaný v manifeste nebol nájdený v „%(path)s“." ],
      "A dictionary file defined in the manifest could not be found.":[ "Slovník definovaný v manifeste nebol nájdený." ],
      "Dictionary file defined in the manifest could not be found at \"%(path)s\".":[ "Slovník definovaný v manifeste nebol nájdený v „%(path)s“." ],
      "The manifest contains multiple dictionaries.":[ "Manifest obsahuje viacero slovníkov." ],
      "Multiple dictionaries were defined in the manifest, which is unsupported.":[ "V manifeste boli uvedené viaceré slovníky. Toto nie je podporované." ],
      "The manifest contains a dictionaries object, but it is empty.":[ "Manifest obsahuje objekt so slovníkom, no ten je prázdny." ],
      "A dictionaries object was defined in the manifest, but it was empty.":[ "Objekt so slovníkom bol v manifeste definovaný, no je prázdny." ],
      "The manifest contains a dictionary but no id property.":[ "Manifest obsahuje slovník, no žiadne id." ],
      "A dictionary was found in the manifest, but there was no id set.":[ "V manifeste bol nájdený slovník, no nemá nastavené id." ],
      "Please refer to https://github.com/mozilla-extensions/xpi-manifest to learn more about privileged extensions and signing.":[ "Ďalšie informácie o privilegovaných rozšíreniach a podpisovaní nájdete na stránke https://github.com/mozilla-extensions/xpi-manifest." ],
      "Forbidden content found in add-on.":[ "V doplnku bol nájdený zakázaný obsah." ],
      "This add-on contains forbidden content.":[ "Doplnok obsahuje zakázaný obsah." ],
      "Icons must be square.":[ "Ikony musia byť štvorcové." ],
      "Icon at \"%(path)s\" must be square.":[ "Ikona v „%(path)s“ musí byť štvorcová." ],
      "The size of the icon does not match the manifest.":[ "Veľkosť ikony nezodpovedá manifestu." ],
      "Expected icon at \"%(path)s\" to be %(expected)d pixels wide but was %(actual)d.":[ "Očakáva sa, že ikona „%(path)s“ bude mať šírku %(expected)d pixelov, ale v skutočnosti má %(actual)d." ],
      "\"%(fieldName)s\" is ignored for non-privileged add-ons.":[ "\"%(fieldName)s\" sa ignoruje pre neprivilegované doplnky." ],
      "Corrupt image file":[ "Poškodený súbor s obrázkom" ],
      "Expected icon file at \"%(path)s\" is corrupted":[ "Očakávaný súbor ikony v „%(path)s“ je poškodený" ],
      "This property has been deprecated.":[ "Táto vlastnosť je zastaraná." ],
      "This theme LWT alias has been removed in Firefox 70.":[ "Tento LWT alias témy vzhľadu bol odstránený vo Firefoxe 70." ],
      "See https://mzl.la/2T11Lkc (MDN Docs) for more information.":[ "Ďalšie informácie nájdete na https://mzl.la/2T11Lkc (dokumentácia na MDN)." ],
      "Theme image for \"%(type)s\" could not be found at \"%(path)s\"":[ "Obrázok témy vzhľadu pre „%(type)s“ nebol v „%(path)s“ nájdený" ],
      "\"%(fieldName)s\" manifest field is only used for privileged and temporarily installed extensions.":[ "Pole manifestu \"%(fieldName)s\" sa používa iba pre privilegované a dočasne nainštalované rozšírenia." ],
      "Corrupted theme image file":[ "Poškodený súbor s obrázkom" ],
      "Theme image file at \"%(path)s\" is corrupted":[ "Obrázok témy vzhľadu v „%(path)s“ je poškodený" ],
      "Theme image file has an unsupported file extension":[ "Súbor s obrázkom témy vzhľadu obsahuje nepodporované rozšírenie" ],
      "Theme image file at \"%(path)s\" has an unsupported file extension":[ "Súbor s obrázkom témy vzhľadu v „%(path)s“ obsahuje nepodporované rozšírenie" ],
      "Theme image file has an unsupported mime type":[ "Súbor s obrázkom témy vzhľadu má nepodporovaný typ mime" ],
      "Theme image file at \"%(path)s\" has the unsupported mime type \"%(mime)s\"":[ "Súbor s obrázkom témy vzhľadu v „%(path)s“ má nepodporovaný typ mime „%(mime)s“" ],
      "Theme image file mime type does not match its file extension":[ "Typ mime súboru s obrázkom témy vzhľadu nezodpovedá svojmu rozšíreniu" ],
      "Theme image file extension at \"%(path)s\" does not match its actual mime type \"%(mime)s\"":[ "Rozšírenie obrázka témy vzhľadu v „%(path)s“ nezodpovedá typu mime „%(mime)s“" ],
      "The \"default_locale\" is missing localizations.":[ "Jazyku „default_locale“ chýbajú preklady." ],
      "The \"default_locale\" value is specified in the manifest, but no matching \"messages.json\" in the \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "Hodnota \"default_locale\" je prítomná v manifeste, ale v priečinku \"_locales\" neexistuje žiadny zodpovedajúci súbor  \"messages.json\". Ďalšie informácie: https://mzl.la/2hjcaEE" ],
      "The \"default_locale\" is missing but \"_locales\" exist.":[ "Jazyk „default_locale“ chýba, no „_locales“ existuje." ],
      "The \"default_locale\" value is not specifed in the manifest, but a \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "Hodnota \"default_locale\" nie je uvedená v manifeste, ale je prítomný priečinok \"_locales\". Ďalšie informácie: https://mzl.la/2hjcaEE" ],
      "Unsupported image extension":[ "Nepodporovaný formát obrázku" ],
      "Icons should be one of JPG/JPEG, WebP, GIF, PNG or SVG.":[ "Ikony by mala mať formát JPG/JPEG, WebP, GIF, PNG alebo SVG." ],
      "\"applications\" property overridden by \"browser_specific_settings\" property":[ "Vlastnosť \"applications\" prepísaná vlastnosťou \"browser_specific_settings\"" ],
      "The \"applications\" property is being ignored because it is superseded by the \"browser_specific_settings\" property which is also defined in your manifest. Consider removing applications.":[ "Vlastnosť \"applications\" sa ignoruje, pretože je nahradená vlastnosťou \"browser_specific_settings\", ktorá je tiež definovaná vo vašom manifeste. Zvážte odstránenie vlastnosti \"applications\"." ],
      "Empty language directory":[ "Prázdny priečinok s jazykmi" ],
      "messages.json file missing in \"%(path)s\"":[ "V „%(path)s“ chýba súbor messages.json" ],
      "Manifest key not supported by the specified minimum Firefox version":[ "Kľúč manifestu nie je podporovaný danou minimálnou verziou Firefoxu" ],
      "\"strict_min_version\" requires Firefox %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "\"strict_min_version\" vyžaduje Firefox %(minVersion)s, ktorý bol vydaný predtým, ako verzia %(versionAdded)s zaviedla podporu pre \"%(key)s\"." ],
      "\"%(fieldName)s\" is not supported in manifest versions %(versionRange)s.":[ "Pole \"%(fieldName)s\" nie je podporované vo verziách manifestu %(versionRange)s." ],
      "Permission not supported by the specified minimum Firefox version":[ "Povolenie nie je podporované danou minimálnou verziou Firefoxu" ],
      "\"%(fieldName)s\" is not supported.":[ "\"%(fieldName)s\" nie je podporované." ],
      "Manifest key not supported by the specified minimum Firefox for Android version":[ "Kľúč manifestu nie je podporovaný danou minimálnou verziou Firefoxu pre Android" ],
      "\"strict_min_version\" requires Firefox for Android %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "\"strict_min_version\" vyžaduje Firefox pre Android %(minVersion)s, ktorý bol vydaný predtým, ako verzia %(versionAdded)s zaviedla podporu pre \"%(key)s\"." ],
      "Permission not supported by the specified minimum Firefox for Android version":[ "Povolenie nie je podporované danou minimálnou verziou Firefoxu pre Android" ],
      "Linking to \"addons.mozilla.org\" is not allowed":[ "Odkaz na „addons.mozilla.org“ nie je povolený" ],
      "Links directing to \"addons.mozilla.org\" are not allowed to be used for homepage":[ "Odkazy smerujúce na \"addons.mozilla.org\" nie je možné použiť ako domovskú stránku" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above":[ "Povolenie \"%(permission)s\" vyžaduje, aby bola hodnota \"strict_min_version\" nastavená na \"%(minFirefoxVersion)s\" alebo vyššiu" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above. Please update your manifest.json version to specify a minimum Firefox version.":[ "Povolenie \"%(permission)s\" vyžaduje, aby bola hodnota \"strict_min_version\" nastavená na \"%(minFirefoxVersion)s\" alebo vyššiu. Aktualizujte svoju verziu manifest.json a uveďte minimálnu verziu Firefoxu." ],
      "The extension ID is required in Manifest Version 3 and above.":[ "Identifikátor rozšírenia sa vyžaduje vo verzii Manifest 3 a vyššej." ],
      "See https://mzl.la/3PLZYdo for more information.":[ "Ďalšie informácie nájdete na https://mzl.la/3PLZYdo." ],
      "%(instancePath)s: Privileged extensions should declare privileged permissions.":[ "%(instancePath)s: privilegované rozšírenia by mali deklarovať privilegované povolenia." ],
      "This extension does not declare any privileged permission. It does not need to be signed with the privileged certificate. Please upload it directly to https://addons.mozilla.org/.":[ "Toto rozšírenie nedeklaruje žiadne privilegované povolenie. Nemusí byť podpísané privilegovaným certifikátom. Nahrajte ho priamo na https://addons.mozilla.org/." ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for privileged extensions.":[ "%(instancePath)s: pre privilegované rozšírenia sa vyžaduje povolenie \"mozillaAddons\"." ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for extensions that include privileged manifest fields.":[ "%(instancePath)s: povolenie \"mozillaAddons\" sa vyžaduje pre rozšírenia, ktoré obsahujú privilegované polia manifestu." ],
      "%(instancePath)s: privileged manifest fields are only allowed in privileged extensions.":[ "%(instancePath)s: privilegované polia manifestu sú povolené len v privilegovaných rozšíreniach." ],
      "This extension does not include the \"mozillaAddons\" permission, which is required for privileged extensions.":[ "Toto rozšírenie neobsahuje povolenie \"mozillaAddons\", ktoré je vyžadovaným povolením pre privilegované rozšírenia." ],
      "Cannot use actions in hidden add-ons.":[ "V skrytých doplnkoch nie je možné použiť akcie." ],
      "The hidden and browser_action/page_action (or action in Manifest Version 3 and above) properties are mutually exclusive.":[ "Vlastnosti hidden a browser_action/page_action (alebo action v Manifeste verzie 3 a vyššej) sa navzájom vylučujú." ],
      "Use \"browser_specific_settings\" instead of \"applications\".":[ "Namiesto \"applications\" použite \"browser_specific_settings\"." ],
      "The \"applications\" property in the manifest is deprecated and will no longer be accepted in Manifest Version 3 and above.":[ "Vlastnosť „applications“ v manifeste je zastaraná a už nebude akceptovaná v manifeste verzie 3 a vyššej." ],
      "\"applications\" is no longer allowed in Manifest Version 3 and above.":[ "Vlastnosť „applications“ nie je povolená v Manifeste verzie 3 a vyššej." ],
      "The \"applications\" property in the manifest is no longer allowed in Manifest Version 3 and above. Use \"browser_specific_settings\" instead.":[ "Vlastnosť „applications“ už nie je povolená v manifeste verzie 3 a vyššej. Namiesto nej použite \"browser_specific_settings\"." ],
      "The version string should be simplified because it won't be compatible with Manifest Version 3 and above.":[ "Reťazec verzie by mal byť zjednodušený, pretože nebude kompatibilný s Manifestom verzie 3 a novšej." ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros will no longer be allowed. Letters will no longer be allowed either. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "Verzia by mala byť reťazec s 1 až 4 číslami oddelenými bodkami. Každé číslo môže mať až 9 číslic, ale úvodné nuly už nebudú povolené. Už nebudú povolené ani písmená. Ďalšie informácie nájdete na stránke https://mzl.la/3h3mCRu (Dokumenty MDN)." ],
      "The version string should be simplified.":[ "Reťazec verzie by mal byť zjednodušený." ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros are not allowed. Letters are no longer allowed. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "Verzia by mala byť reťazec s 1 až 4 číslami oddelenými bodkami. Každé číslo môže mať maximálne 9 číslic, ale úvodné nuly nie sú povolené. Povolené už nie sú ani písmená. Ďalšie informácie nájdete na stránke https://mzl.la/3h3mCRu (Dokumenty MDN)." ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported in manifest versions %(versionRange)s.":[ "Pole /%(fieldName)s: \"%(permissionName)s\" nie je podporované v manifeste verzií %(versionRange)s." ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported.":[ "Pole /%(fieldName)s: \"%(permissionName)s\" nie je podporované." ] } } }