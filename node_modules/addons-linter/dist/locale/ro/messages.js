module.exports = { domain:"messages",
  locale_data:{ messages:{ "":{ domain:"messages",
        plural_forms:"nplurals=3; plural=(n==1 ? 0 : (n==0 || (n%100 > 0 && n%100 < 20)) ? 1 : 2);",
        lang:"ro" },
      "Validation Summary:":[ "Rezumatul validărilor:" ],
      Code:[ "Cod" ],
      Message:[ "Mesaj" ],
      Description:[ "Descriere" ],
      File:[ "Fișier" ],
      Line:[ "Rând" ],
      Column:[ "Coloană" ],
      "Invalid manifest version range requested: --min-manifest-version (currently set to %(minManifestVersion)s) should not be greater than --max-manifest-version (currently set to %(maxManifestVersion)s).":[ "" ],
      "Your FTL is not valid.":[ "FTL-ul tău nu este valid." ],
      "Your FTL file could not be parsed.":[ "Fișierul FTL nu a putut fi analizat." ],
      "Remote scripts are not allowed as per the Add-on Policies.":[ "Scripturile la distanță nu sunt permise conform politicilor privind suplimentele." ],
      "Please include all scripts in the add-on. For more information, refer to https://mzl.la/2uEOkYp.":[ "" ],
      "Inline scripts blocked by default":[ "Scripturile inline sunt blocate implicit" ],
      "Default CSP rules prevent inline JavaScript from running (https://mzl.la/2pn32nd).":[ "" ],
      "Unadvised 3rd-party JS library":[ "Bibliotecă JS de la terți nerecomandată" ],
      "Your add-on uses a JavaScript library we do not recommend. Read more: https://bit.ly/1TRIyZY":[ "" ],
      "Known JS library detected":[ "S-a depistat o bibliotecă JS cunoscută" ],
      "JavaScript libraries are discouraged for simple add-ons, but are generally accepted.":[ "" ],
      "Due to both security and performance concerns, this may not be set using dynamic values which have not been adequately sanitized. This can lead to security issues or fairly serious performance degradation.":[ "" ],
      "{{api}} is not supported":[ "{{api}} nu are suport" ],
      "This API has not been implemented by Firefox.":[ "Acest API nu a fost implementat de Firefox." ],
      "\"{{api}}\" has been removed in Manifest Version 3 (`manifest_version` property)":[ "" ],
      "Content script file could not be found.":[ "Nu s-a găsit fișierul cu scriptul de conținut." ],
      "\"{{api}}\" is deprecated or unimplemented":[ "" ],
      "Content script file could not be found":[ "Nu s-a găsit fișierul cu scriptul de conținut" ],
      "\"%(api)s\" can cause issues when loaded temporarily":[ "" ],
      "This API can cause issues when loaded temporarily using about:debugging in Firefox unless you specify \"browser_specific_settings.gecko.id\" in the manifest. Please see: https://mzl.la/2hizK4a for more.":[ "" ],
      "{{api}} is not supported in Firefox version {{minVersion}}":[ "{{api}} nu are suport în versiunea Firefox {{minVersion}}" ],
      "This API is not implemented by the given minimum Firefox version":[ "Acest API nu este implementat de versiunea minimă indicată de Firefox" ],
      "{{api}} is not supported in Firefox for Android version {{minVersion}}":[ "{{api}} nu are suport în Firefox pentru versiunea Android {{minVersion}}" ],
      "This API is not implemented by the given minimum Firefox for Android version":[ "Acest API nu este implementat de versiunea minimă indicată de Firefox pentru Android" ],
      "Content script file name should not be empty.":[ "Fișierul cu scriptul de conținut nu trebuie să fie gol." ],
      "Content script file name should not be empty":[ "Numele fișierului cu scriptul de conținut nu poate fi lăsat gol" ],
      "\"%(method)s\" called with a non-literal uri":[ "" ],
      "Calling \"%(method)s\" with variable parameters can result in potential security vulnerabilities if the variable contains a remote URI. Consider using 'window.open' with the 'chrome=no' flag.":[ "" ],
      "\"%(method)s\" called with non-local URI":[ "" ],
      "Calling \"%(method)s\" with a non-local URI will result in the dialog being opened with chrome privileges.":[ "" ],
      "JavaScript syntax error":[ "Eroare de sintaxă JavaScript" ],
      "There is a JavaScript syntax error in your code, which might be related to some experimental JavaScript features that aren't an official part of the language specification and therefore not supported yet. The validation cannot continue on this file.":[ "" ],
      "Evaluation of strings as code can lead to security vulnerabilities and performance issues, even in the most innocuous of circumstances. Please avoid using `eval` and the `Function` constructor when at all possible.":[ "" ],
      "setTimeout, setInterval and execScript functions should be called only with function expressions as their first argument":[ "" ],
      "Unexpected global passed as an argument":[ "Variabilă globală neașteptată trecută ca argument" ],
      "Passing a global as an argument is not recommended. Please make this a var instead.":[ "" ],
      "Use of document.write strongly discouraged.":[ "Utilizarea document.write este puternic nerecomandată." ],
      "document.write will fail in many circumstances when used in extensions, and has potentially severe security repercussions when used improperly. Therefore, it should not be used.":[ "" ],
      "Banned 3rd-party JS library":[ "Bibliotecă JS de la terți interzisă" ],
      "Your add-on uses a JavaScript library we consider unsafe. Read more: https://bit.ly/1TRIyZY":[ "" ],
      "Your JSON contains block comments.":[ "Fișierul tău JSON conține blocuri de comentarii." ],
      "Only line comments (comments beginning with \"//\") are allowed in JSON files. Please remove block comments (comments beginning with \"/*\")":[ "" ],
      "Duplicate keys are not allowed in JSON files.":[ "În fișierele JSON nu se permit duplicate de chei." ],
      "Duplicate key found in JSON file.":[ "" ],
      "Your JSON is not valid.":[ "Fișierul tău JSON nu este valid." ],
      "Your JSON file could not be parsed.":[ "Fișierul tău JSON nu a putut fi analizat." ],
      "Reserved filename found.":[ "S-a depistat o denumire de fișier rezervat." ],
      "Files whose names are reserved have been found in the add-on. Please refrain from using them and rename your files.":[ "" ],
      "The package is invalid. It may contain entries using invalid characters, as an example using '\\' as a path separator is not allowed in Firefox. Try to recreate your add-on package (ZIP) and make sure all entries are using '/' as the path separator.":[ "" ],
      "We were unable to decompress the zip file.":[ "Nu am reușit să dezarhivăm fișierul zip." ],
      "manifest.json was not found":[ "Nu s-a găsit fișierul manifest.json" ],
      "No manifest.json was found at the root of the extension. The package file must be a ZIP of the extension's files themselves, not of the containing directory. See: https://mzl.la/2r2McKv for more on packaging.":[ "" ],
      "File is too large to parse.":[ "Fișierul este prea mare ca să poată fi analizat." ],
      "This file is not binary and is too large to parse. Files larger than %(maxFileSizeToParseMB)sMB will not be parsed. Consider moving large lists of data out of JavaScript files and into JSON files, or splitting very large files into smaller ones.":[ "" ],
      "Hidden file flagged":[ "S-a semnalat un fișier ascuns" ],
      "Hidden files complicate the review process and can contain sensitive information about the system that generated the add-on. Please modify the packaging process so that these files aren't included.":[ "" ],
      "Package contains duplicate entries":[ "Pachetul conține intrări duplicate" ],
      "Flagged filename found":[ "S-a depistat o denumire de fișier semnalat" ],
      "Files were found that are either unnecessary or have been included unintentionally. They should be removed.":[ "" ],
      "The package contains multiple entries with the same name. This practice has been banned. Try unzipping and re-zipping your add-on package and try again.":[ "" ],
      "Flagged file extensions found":[ "S-au depistat extensii de fișiere semnalate" ],
      "Flagged file type found":[ "S-a depistat un tip de fișier semnalat" ],
      "Files whose names end with flagged extensions have been found in the add-on. The extension of these files are flagged because they usually identify binary components. Please see https://bit.ly/review-policy for more information on the binary content review process.":[ "" ],
      "Package already signed":[ "Pachetul este deja semnat" ],
      "Add-ons which are already signed will be re-signed when published on AMO. This will replace any existing signatures on the add-on.":[ "" ],
      "Firefox add-ons are not allowed to run coin miners.":[ "Suplimentelor Firefox nu li se permite să ruleze mineri de criptomonede." ],
      "We do not allow coinminer scripts to be run inside WebExtensions. See https://github.com/mozilla/addons-linter/issues/1643 for more details.":[ "" ],
      "String name is reserved for a predefined message":[ "Denumirea șirului este rezervată pentru un mesaj predefinit" ],
      "String names starting with @@ get translated to built-in constants (https://mzl.la/2BL9ZjE).":[ "" ],
      "String name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2Eztyi5).":[ "" ],
      "Placeholder for message is missing":[ "Substituentul pentru mesaj lipsește" ],
      "A placeholder used in the message is not defined.":[ "Un substituent utilizat în mesaj nu este definit." ],
      "Placeholder name contains invalid characters":[ "Denumirea substituentului conține caractere nevalide" ],
      "Placeholder name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2ExbYez).":[ "" ],
      "Placeholder is missing the content property":[ "Substituentului îi lipsește proprietatea de conținut" ],
      "A placeholder needs a content property defining the replacement of it (https://mzl.la/2DT1MQd)":[ "" ],
      "Translation string is missing the message property":[ "Proprietatea mesajului lipsește în șirul de traducere" ],
      "No \"message\" message property is set for a string (https://mzl.la/2DSBTjA).":[ "Nu este setată nicio proprietate de mesaj „mesaj” pentru un șir (https://mzl.la/2DSBTjA)." ],
      "The field is required.":[ "Câmpul este necesar." ],
      "See https://mzl.la/1ZOhoEN (MDN Docs) for more information.":[ "Vezi https://mzl.la/1ZOhoEN (Documentația MDN) pentru mai multe informații.\n\n" ],
      "The permission type is unsupported.":[ "Tipul de permisiune nu are suport." ],
      "See https://mzl.la/1R1n1t0 (MDN Docs) for more information.":[ "Vezi https://mzl.la/1R1n1t0 (Documentația MDN) pentru mai multe informații." ],
      "See https://mzl.la/3Woeqv4 (MDN Docs) for more information.":[ "" ],
      "Unknown permission.":[ "Permisiune necunoscută." ],
      "%(instancePath)s: the following privileged permissions are only allowed in privileged extensions: %(privilegedPermissions)s.":[ "" ],
      "Invalid install origin. A valid origin has - only - a scheme, hostname and optional port. See https://mzl.la/3TEbqbE (MDN Docs) for more information.":[ "" ],
      "The field is invalid.":[ "Câmpul este nevalid." ],
      "\"manifest_version\" in the manifest.json is not a valid value":[ "„manifest_version\" din manifest.json nu este o valoare validă" ],
      "See https://mzl.la/20PenXl (MDN Docs) for more information.":[ "Vezi https://mzl.la/20PenXl (Documentația MDN) pentru mai multe informații." ],
      "\"%(property)s\" allows remote code execution in manifest.json":[ "" ],
      "A custom \"%(property)s\" needs additional review.":[ "" ],
      "\"%(property)s\" allows 'eval', which has strong security and performance implications.":[ "" ],
      "In most cases the same result can be achieved differently, therefore it is generally prohibited":[ "" ],
      "The \"name\" property must be a string with no leading/trailing whitespaces.":[ "" ],
      "See http://mzl.la/1STmr48 (MDN Docs) for more information.":[ "Vezi http://mzl.la/1STmr48 (Documentația MDN) pentru mai multe informații." ],
      "\"update_url\" is not allowed.":[ "Nu se permite „update_url\"." ],
      "\"applications.gecko.update_url\" or \"browser_specific_settings.gecko.update_url\" are not allowed for Mozilla-hosted add-ons.":[ "" ],
      "The \"update_url\" property is not used by Firefox.":[ "Proprietatea „update_url\" nu este folosită de Firefox." ],
      "The \"update_url\" is not used by Firefox in the root of a manifest; your add-on will be updated via the Add-ons site and not your \"update_url\". See: https://mzl.la/25zqk4O":[ "" ],
      "\"strict_max_version\" not required.":[ "„strict_max_version\" nu este necesar." ],
      "\"strict_max_version\" shouldn't be used unless the add-on is expected not to work with future versions of Firefox.":[ "" ],
      "Manifest Version 3 is not fully supported on Firefox for Android.":[ "" ],
      "No \"%(property)s\" property found in manifest.json":[ "" ],
      "\"%(property)s\" is required":[ "" ],
      "An icon defined in the manifest could not be found in the package.":[ "O pictogramă definită în manifest nu se regăsește în pachet." ],
      "Icon could not be found at \"%(path)s\".":[ "Pictograma nu a putut fi găsită la „%(path)s”." ],
      "A background script defined in the manifest could not be found.":[ "Un script de fundal definit în manifest nu a putut fi găsit." ],
      "A background page defined in the manifest could not be found.":[ "O pagină de fundal definită în manifest nu a putut fi găsită." ],
      "Background script could not be found at \"%(path)s\".":[ "Scriptul de fundal nu a putut fi găsit la „%(path)s”." ],
      "Background page could not be found at \"%(path)s\".":[ "Pagina de fundal nu a putut fi găsită la „%(path)s”." ],
      "A content script defined in the manifest could not be found.":[ "Un script de conținut definit în manifest nu a putut fi găsit." ],
      "A content script css file defined in the manifest could not be found.":[ "Un fișier css cu script de conținut, definit în manifest nu a putut fi găsit." ],
      "Content script defined in the manifest could not be found at \"%(path)s\".":[ "Scriptul de conținut definit în manifest nu a putut fi găsit la „%(path)s”." ],
      "Content script css file defined in the manifest could not be found at \"%(path)s\".":[ "Fișierul css cu script de conținut definit în manifest nu a putut fi găsit la „%(path)s”." ],
      "A dictionary file defined in the manifest could not be found.":[ "Un fișier dicționar definit în manifest nu a putut fi găsit." ],
      "Dictionary file defined in the manifest could not be found at \"%(path)s\".":[ "Fișierul dicționar definit în manifest nu a putut fi găsit la „%(path)s”." ],
      "The manifest contains multiple dictionaries.":[ "Manifestul conține mai multe dicționare." ],
      "Multiple dictionaries were defined in the manifest, which is unsupported.":[ "În manifest au fost definite mai multe dicționare care nu au suport." ],
      "The manifest contains a dictionaries object, but it is empty.":[ "Manifestul conține un obiect de dicționare, dar este gol." ],
      "A dictionaries object was defined in the manifest, but it was empty.":[ "În manifest a fost definit un obiect de dicționare, dar era gol." ],
      "The manifest contains a dictionary but no id property.":[ "Manifestul conține un dicționar, dar nu are nicio proprietate ID." ],
      "A dictionary was found in the manifest, but there was no id set.":[ "S-a găsit un dicționar în manifest, dar nu avea setat niciun ID." ],
      "Please refer to https://github.com/mozilla-extensions/xpi-manifest to learn more about privileged extensions and signing.":[ "" ],
      "Forbidden content found in add-on.":[ "S-a depistat conținut interzis în supliment." ],
      "This add-on contains forbidden content.":[ "Acest supliment are conținut interzis." ],
      "Icons must be square.":[ "Pictogramele trebuie să fie pătrate." ],
      "Icon at \"%(path)s\" must be square.":[ "Pictograma de la „%(path)s” trebuie să fie pătrată." ],
      "The size of the icon does not match the manifest.":[ "Mărimea pictogramei nu corespunde manifestului." ],
      "Expected icon at \"%(path)s\" to be %(expected)d pixels wide but was %(actual)d.":[ "" ],
      "\"%(fieldName)s\" is ignored for non-privileged add-ons.":[ "" ],
      "Corrupt image file":[ "Fișier de imagine corupt" ],
      "Expected icon file at \"%(path)s\" is corrupted":[ "Fișierul pictogramă așteptat, de la „%(path)s” este corupt" ],
      "This property has been deprecated.":[ "" ],
      "This theme LWT alias has been removed in Firefox 70.":[ "Acest alias LWT de temă a fost eliminat din Firefox 70." ],
      "See https://mzl.la/2T11Lkc (MDN Docs) for more information.":[ "Vezi https://mzl.la/2T11Lkc (Documentația MDN) pentru mai multe informații.\n\n" ],
      "Theme image for \"%(type)s\" could not be found at \"%(path)s\"":[ "Imaginea temei pentru „%(type)s” nu a putut fi găsită la „%(path)s”" ],
      "\"%(fieldName)s\" manifest field is only used for privileged and temporarily installed extensions.":[ "" ],
      "Corrupted theme image file":[ "Fișier imagine al temei corupt" ],
      "Theme image file at \"%(path)s\" is corrupted":[ "Fișierul imagine al temei de la „%(path)s” este corupt" ],
      "Theme image file has an unsupported file extension":[ "Fișierul de imagine al temei are o extensie de fișier fără suport" ],
      "Theme image file at \"%(path)s\" has an unsupported file extension":[ "Fișierul de imagine al temei de la „%(path)s” are o extensie de fișier fără suport" ],
      "Theme image file has an unsupported mime type":[ "Fișierul de imagine al temei are un tip mime fără suport" ],
      "Theme image file at \"%(path)s\" has the unsupported mime type \"%(mime)s\"":[ "Fișierul de imagine al temei de la „%(path)s” are un tip mime „%(mime)s” fără suport" ],
      "Theme image file mime type does not match its file extension":[ "Tipul mime al fișierului de imagine al temei nu se potrivește cu extensia fișierului" ],
      "Theme image file extension at \"%(path)s\" does not match its actual mime type \"%(mime)s\"":[ "Extensia fișierului de imagine al temei de la „%(path)s” nu se potrivește cu tipul mime „%(mime)s” efectiv" ],
      "The \"default_locale\" is missing localizations.":[ "„default_locale\" nu are traduceri." ],
      "The \"default_locale\" value is specified in the manifest, but no matching \"messages.json\" in the \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "" ],
      "The \"default_locale\" is missing but \"_locales\" exist.":[ "„default_locale\" lipsește, dar „_locales\" există." ],
      "The \"default_locale\" value is not specifed in the manifest, but a \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "" ],
      "Unsupported image extension":[ "Extensie nepermisă de imagine" ],
      "Icons should be one of JPG/JPEG, WebP, GIF, PNG or SVG.":[ "Pictogramele trebuie să fie fișiere JPG/JPEG, WebP, GIF, PNG sau SVG." ],
      "\"applications\" property overridden by \"browser_specific_settings\" property":[ "" ],
      "The \"applications\" property is being ignored because it is superseded by the \"browser_specific_settings\" property which is also defined in your manifest. Consider removing applications.":[ "" ],
      "Empty language directory":[ "Director de limbi gol" ],
      "messages.json file missing in \"%(path)s\"":[ "Fișierul messages.json lipsește în „%(path)s\"" ],
      "Manifest key not supported by the specified minimum Firefox version":[ "Cheia manifestului nu are suport în versiunea Firefox minimă specificată" ],
      "\"strict_min_version\" requires Firefox %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "" ],
      "\"%(fieldName)s\" is not supported in manifest versions %(versionRange)s.":[ "" ],
      "Permission not supported by the specified minimum Firefox version":[ "Permisiunea nu are suport în versiunea Firefox minimă specificată" ],
      "\"%(fieldName)s\" is not supported.":[ "" ],
      "Manifest key not supported by the specified minimum Firefox for Android version":[ "Cheia manifestului nu are suport în versiunea Firefox minimă specificată pentru versiunea Android" ],
      "\"strict_min_version\" requires Firefox for Android %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "" ],
      "Permission not supported by the specified minimum Firefox for Android version":[ "Permisiunea nu are suport în versiunea Firefox minimă specificată pentru versiunea Android" ],
      "Linking to \"addons.mozilla.org\" is not allowed":[ "" ],
      "Links directing to \"addons.mozilla.org\" are not allowed to be used for homepage":[ "" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above":[ "" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above. Please update your manifest.json version to specify a minimum Firefox version.":[ "" ],
      "The extension ID is required in Manifest Version 3 and above.":[ "" ],
      "See https://mzl.la/3PLZYdo for more information.":[ "" ],
      "%(instancePath)s: Privileged extensions should declare privileged permissions.":[ "" ],
      "This extension does not declare any privileged permission. It does not need to be signed with the privileged certificate. Please upload it directly to https://addons.mozilla.org/.":[ "" ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for privileged extensions.":[ "" ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for extensions that include privileged manifest fields.":[ "" ],
      "%(instancePath)s: privileged manifest fields are only allowed in privileged extensions.":[ "" ],
      "This extension does not include the \"mozillaAddons\" permission, which is required for privileged extensions.":[ "" ],
      "Cannot use actions in hidden add-ons.":[ "" ],
      "The hidden and browser_action/page_action (or action in Manifest Version 3 and above) properties are mutually exclusive.":[ "" ],
      "Use \"browser_specific_settings\" instead of \"applications\".":[ "" ],
      "The \"applications\" property in the manifest is deprecated and will no longer be accepted in Manifest Version 3 and above.":[ "" ],
      "\"applications\" is no longer allowed in Manifest Version 3 and above.":[ "" ],
      "The \"applications\" property in the manifest is no longer allowed in Manifest Version 3 and above. Use \"browser_specific_settings\" instead.":[ "" ],
      "The version string should be simplified because it won't be compatible with Manifest Version 3 and above.":[ "" ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros will no longer be allowed. Letters will no longer be allowed either. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "" ],
      "The version string should be simplified.":[ "" ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros are not allowed. Letters are no longer allowed. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "" ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported in manifest versions %(versionRange)s.":[ "" ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported.":[ "" ] } } }