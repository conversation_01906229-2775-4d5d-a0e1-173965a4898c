module.exports = { domain:"messages",
  locale_data:{ messages:{ "":{ domain:"messages",
        plural_forms:"nplurals=1; plural=0;",
        lang:"vi" },
      "Validation Summary:":[ "Tóm tắt xác nhận:" ],
      Code:[ "Đoạn mã" ],
      Message:[ "Tin nhắn" ],
      Description:[ "Mô tả" ],
      File:[ "Tập tin" ],
      Line:[ "Dòng" ],
      Column:[ "Cột" ],
      "Invalid manifest version range requested: --min-manifest-version (currently set to %(minManifestVersion)s) should not be greater than --max-manifest-version (currently set to %(maxManifestVersion)s).":[ "Yêu cầu phạm vi phiên bản tập tin manifest không hợp lệ: --min-manifest-version (hiện tại đặt thành %(minManifestVersion)s) không được lớn hơn --max-manifest-version (hiện tại đặt là %(maxManifestVersion)s)." ],
      "Your FTL is not valid.":[ "FTL của bạn không hợp lệ." ],
      "Your FTL file could not be parsed.":[ "Tệp FTL của bạn không thể phân tích cú pháp." ],
      "Remote scripts are not allowed as per the Add-on Policies.":[ "Các tập lệnh từ xa không được phép theo Chính sách tiện ích mở rộng." ],
      "Please include all scripts in the add-on. For more information, refer to https://mzl.la/2uEOkYp.":[ "Vui lòng bao gồm tất cả các tập lệnh trong tiện ích mở rộng. Để biết thêm thông tin, hãy tham khảo https://mzl.la/2uEOkYp." ],
      "Inline scripts blocked by default":[ "Các tập lệnh nội tuyến bị chặn theo mặc định" ],
      "Default CSP rules prevent inline JavaScript from running (https://mzl.la/2pn32nd).":[ "" ],
      "Unadvised 3rd-party JS library":[ "Thư viện JS của bên thứ 3 không được chấp nhận" ],
      "Your add-on uses a JavaScript library we do not recommend. Read more: https://bit.ly/1TRIyZY":[ "Tiện ích mở rộng của bạn sử dụng thư viện JavaScript mà chúng tôi không khuyên dùng. Đọc thêm: https://bit.ly/1TRIyZY" ],
      "Known JS library detected":[ "Đã phát hiện thư viện JS" ],
      "JavaScript libraries are discouraged for simple add-ons, but are generally accepted.":[ "Các thư viện JavaScript không được khuyến khích cho các tiện ích mở rộng đơn giản, nhưng thường được chấp nhận." ],
      "Due to both security and performance concerns, this may not be set using dynamic values which have not been adequately sanitized. This can lead to security issues or fairly serious performance degradation.":[ "" ],
      "{{api}} is not supported":[ "{{api}} không được hỗ trợ" ],
      "This API has not been implemented by Firefox.":[ "API này chưa được Firefox triển khai." ],
      "\"{{api}}\" has been removed in Manifest Version 3 (`manifest_version` property)":[ "\"{{api}}\" đã bị xóa trong Manifest phiên bản 3 (thuộc tính `manifest_version`)" ],
      "{{api}} is deprecated":[ "{{api}} không được hỗ trợ" ],
      "This API has been deprecated by Firefox.":[ "API này đã bị loại bỏ khỏi Firefox." ],
      "Content script file could not be found.":[ "Không thể tìm thấy tập lệnh nội dung." ],
      "\"{{api}}\" is deprecated or unimplemented":[ "\"{{api}}\" không được dùng nữa và sẽ không hỗ trợ" ],
      "Content script file could not be found":[ "Không thể tìm thấy tập lệnh nội dung" ],
      "\"%(api)s\" can cause issues when loaded temporarily":[ "\"%(api)s\" có thể gây ra sự cố khi tải tạm thời" ],
      "This API can cause issues when loaded temporarily using about:debugging in Firefox unless you specify \"browser_specific_settings.gecko.id\" in the manifest. Please see: https://mzl.la/2hizK4a for more.":[ "API này có thể gây ra sự cố khi được tải tạm thời bằng cách sử dụng about:debugging trong Firefox trừ khi bạn chỉ định \"browser_specific_settings.gecko.id\" trong manifest. Vui lòng xem tại https://mzl.la/2hizK4a để tìm hiểu thêm." ],
      "{{api}} is not supported in Firefox version {{minVersion}}":[ "{{api}} không được hỗ trợ trong Firefox phiên bản {{minVersion}}" ],
      "This API is not implemented by the given minimum Firefox version":[ "API này không được triển khai bởi phiên bản Firefox tối thiểu quy định" ],
      "{{api}} is not supported in Firefox for Android version {{minVersion}}":[ "{{api}} không được hỗ trợ trong Firefox dành cho Android phiên bản {{minVersion}}" ],
      "This API is not implemented by the given minimum Firefox for Android version":[ "API này không được triển khai bởi phiên bản Firefox tối thiểu dành cho Android" ],
      "Content script file name should not be empty.":[ "Tên tập tin nội dung không được để trống." ],
      "Content script file name should not be empty":[ "Tên tập tin nội dung không được để trống" ],
      "\"%(method)s\" called with a non-literal uri":[ "\"%(method)s\" được gọi với uri không theo nghĩa đen" ],
      "Calling \"%(method)s\" with variable parameters can result in potential security vulnerabilities if the variable contains a remote URI. Consider using 'window.open' with the 'chrome=no' flag.":[ "Việc gọi \"%(method)s\" với tham số biến có thể dẫn đến lỗ hổng bảo mật tiềm ẩn nếu biến chứa URI từ xa. Cân nhắc sử dụng 'window.open' với cờ 'chrome=no'." ],
      "\"%(method)s\" called with non-local URI":[ "\"%(method)s\" được gọi với URI không cục bộ" ],
      "Calling \"%(method)s\" with a non-local URI will result in the dialog being opened with chrome privileges.":[ "Gọi \"%(method)s\" bằng URI không cục bộ sẽ dẫn đến hộp thoại được mở bằng các đặc quyền của chrome." ],
      "JavaScript syntax error":[ "Lỗi cú pháp JavaScript" ],
      "There is a JavaScript syntax error in your code, which might be related to some experimental JavaScript features that aren't an official part of the language specification and therefore not supported yet. The validation cannot continue on this file.":[ "Có lỗi cú pháp JavaScript trong mã của bạn, có thể liên quan đến một số tính năng JavaScript thử nghiệm không phải là một phần chính thức của đặc tả ngôn ngữ và do đó chưa được hỗ trợ. Việc xác thực không thể tiếp tục trên tập tin này." ],
      "Evaluation of strings as code can lead to security vulnerabilities and performance issues, even in the most innocuous of circumstances. Please avoid using `eval` and the `Function` constructor when at all possible.":[ "Việc đánh giá các chuỗi dưới dạng mã có thể dẫn đến các lỗ hổng bảo mật và các vấn đề về hiệu suất, ngay cả trong những trường hợp vô hại nhất. Vui lòng tránh sử dụng hàm tạo `eval` và `Function` khi có thể." ],
      "setTimeout, setInterval and execScript functions should be called only with function expressions as their first argument":[ "Các hàm setTimeout, setInterval và execScript chỉ nên được gọi với các biểu thức hàm làm đối số đầu tiên của chúng" ],
      "Unexpected global passed as an argument":[ "Việc sử dụng biến toàn cục làm đối số không mong muốn" ],
      "Passing a global as an argument is not recommended. Please make this a var instead.":[ "Không nên chuyển global làm đối số. Thay vào đó, hãy biến điều này thành var." ],
      "Use of document.write strongly discouraged.":[ "Không khuyến khích sử dụng document.write." ],
      "document.write will fail in many circumstances when used in extensions, and has potentially severe security repercussions when used improperly. Therefore, it should not be used.":[ "document.write sẽ không thành công trong nhiều trường hợp khi được sử dụng trong các tiện ích mở rộng và có khả năng gây hậu quả bảo mật nghiêm trọng khi sử dụng không đúng cách. Do đó, nó không nên được sử dụng." ],
      "Banned 3rd-party JS library":[ "Thư viện JS của bên thứ 3 bị cấm" ],
      "Your add-on uses a JavaScript library we consider unsafe. Read more: https://bit.ly/1TRIyZY":[ "Tiện ích bổ sung của bạn sử dụng thư viện JavaScript mà chúng tôi cho là không an toàn. Đọc thêm: https://bit.ly/1TRIyZY" ],
      "Your JSON contains block comments.":[ "JSON của bạn chứa các bình luận (comment) bị chặn." ],
      "Only line comments (comments beginning with \"//\") are allowed in JSON files. Please remove block comments (comments beginning with \"/*\")":[ "Chỉ cho phép comment (bắt đầu bằng \"//\") trong tập tin JSON. Vui lòng xóa các block comment (bắt đầu bằng \"/*\")" ],
      "Duplicate keys are not allowed in JSON files.":[ "Các khóa trùng lặp không được phép trong các tệp JSON." ],
      "Duplicate key found in JSON file.":[ "Đã tìm thấy khóa trùng lặp trong tập tin JSON." ],
      "Your JSON is not valid.":[ "JSON của bạn không hợp lệ." ],
      "Your JSON file could not be parsed.":[ "Tập tin JSON của bạn không thể phân tích cú pháp." ],
      "Reserved filename found.":[ "Đã tìm thấy tên tập tin dành riêng." ],
      "Files whose names are reserved have been found in the add-on. Please refrain from using them and rename your files.":[ "Các tập tin có tên được đặt trước đã được tìm thấy trong tiện ích mở rộng. Vui lòng không sử dụng chúng và đổi tên các tập tin của bạn." ],
      "The package is invalid. It may contain entries using invalid characters, as an example using '\\' as a path separator is not allowed in Firefox. Try to recreate your add-on package (ZIP) and make sure all entries are using '/' as the path separator.":[ "Gói không hợp lệ. Nó có thể chứa các mục nhập sử dụng các ký tự không hợp lệ, chẳng hạn như sử dụng '\\' làm dấu tách đường dẫn không được phép trong Firefox. Hãy thử tạo lại gói tiện ích mở rộng (ZIP) của bạn và đảm bảo rằng tất cả các mục đang sử dụng '/' làm dấu tách đường dẫn." ],
      "We were unable to decompress the zip file.":[ "Chúng tôi không thể giải nén tập tin zip." ],
      "manifest.json was not found":[ "Không tìm thấy manifest.json" ],
      "No manifest.json was found at the root of the extension. The package file must be a ZIP of the extension's files themselves, not of the containing directory. See: https://mzl.la/2r2McKv for more on packaging.":[ "Không tìm thấy manifest.json ở thư mục gốc của tiện ích mở rộng. Tập tin gói phải là tập tin ZIP của chính các tập tin của tiện ích mở rộng, không phải của thư mục chứa nó. Xem: https://mzl.la/2r2McKv để biết thêm về đóng gói." ],
      "File is too large to parse.":[ "Tập tin quá lớn để phân tích cú pháp." ],
      "This file is not binary and is too large to parse. Files larger than %(maxFileSizeToParseMB)sMB will not be parsed. Consider moving large lists of data out of JavaScript files and into JSON files, or splitting very large files into smaller ones.":[ "Tập tin này không phải là tập tin nhị phân và quá lớn để phân tích cú pháp. Các tập tin lớn hơn %(maxFileSizeToParseMB)sMB sẽ không được phân tích cú pháp. Cân nhắc di chuyển các danh sách dữ liệu lớn ra khỏi tập tin JavaScript và sang tập tin JSON hoặc chia các tập tin rất lớn thành các tập tin nhỏ hơn." ],
      "Hidden file flagged":[ "Tập tin ẩn được gắn cờ" ],
      "Hidden files complicate the review process and can contain sensitive information about the system that generated the add-on. Please modify the packaging process so that these files aren't included.":[ "Các tập tin ẩn làm phức tạp quá trình xem xét và có thể chứa thông tin nhạy cảm về hệ thống đã tạo tiện ích mở rộng. Vui lòng sửa đổi quy trình đóng gói để không bao gồm các tập tin này." ],
      "Package contains duplicate entries":[ "Gói chứa các mục trùng lặp" ],
      "Flagged filename found":[ "Đã tìm thấy tên tập tin được gắn cờ" ],
      "Files were found that are either unnecessary or have been included unintentionally. They should be removed.":[ "Đã tìm thấy các tập tin không cần thiết hoặc đã được đưa vào ngoài ý muốn. Chúng nên được loại bỏ." ],
      "The package contains multiple entries with the same name. This practice has been banned. Try unzipping and re-zipping your add-on package and try again.":[ "Gói chứa nhiều mục có cùng tên. Điều này đã bị cấm. Hãy thử giải nén và nén lại gói tiện ích của bạn rồi thử lại." ],
      "Flagged file extensions found":[ "Đã tìm thấy phần mở rộng tập tin được gắn cờ" ],
      "Flagged file type found":[ "Đã tìm thấy loại tập tin được gắn cờ" ],
      "Files whose names end with flagged extensions have been found in the add-on. The extension of these files are flagged because they usually identify binary components. Please see https://bit.ly/review-policy for more information on the binary content review process.":[ "Các tập tin có tên kết thúc bằng phần mở rộng được gắn cờ đã được tìm thấy trong tiện ích mở rộng. Phần mở rộng của các tập tin này được gắn cờ vì chúng thường xác định các thành phần nhị phân. Vui lòng xem https://bit.ly/review-policy để biết thêm thông tin về quy trình xem xét nội dung nhị phân." ],
      "Package already signed":[ "Gói đã được ký" ],
      "Add-ons which are already signed will be re-signed when published on AMO. This will replace any existing signatures on the add-on.":[ "Các tiện ích mở rộng đã được ký sẽ được ký lại khi xuất bản trên AMO. Thao tác này sẽ thay thế mọi chữ ký hiện có trên tiện ích mở rộng." ],
      "Firefox add-ons are not allowed to run coin miners.":[ "Tiện ích Firefox không được phép chạy các công cụ khai thác tiền ảo (coin miners)." ],
      "We do not allow coinminer scripts to be run inside WebExtensions. See https://github.com/mozilla/addons-linter/issues/1643 for more details.":[ "Chúng tôi không cho phép các tập lệnh coinminer chạy bên trong WebExtensions. Xem https://github.com/mozilla/addons-linter/issues/1643 để biết thêm chi tiết." ],
      "String name is reserved for a predefined message":[ "Tên chuỗi được dành riêng cho một tin nhắn được xác định trước" ],
      "String names starting with @@ get translated to built-in constants (https://mzl.la/2BL9ZjE).":[ "Tên chuỗi bắt đầu bằng @@ được dịch sang hằng số tích hợp sẵn (https://mzl.la/2BL9ZjE)." ],
      "String name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2Eztyi5).":[ "Tên chuỗi chỉ được chứa các ký tự chữ và số, _ và @ (https://mzl.la/2Eztyi5)." ],
      "Placeholder for message is missing":[ "Trình giữ chỗ cho tin nhắn bị thiếu" ],
      "A placeholder used in the message is not defined.":[ "Placeholder được sử dụng trong tin nhắn không được xác định." ],
      "Placeholder name contains invalid characters":[ "Tên của placeholder chứa ký tự không hợp lệ" ],
      "Placeholder name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2ExbYez).":[ "Tên placeholder chỉ được chứa các ký tự chữ và số, _ và @ (https://mzl.la/2ExbYez)." ],
      "Placeholder is missing the content property":[ "Placeholder thiếu thuộc tính content" ],
      "A placeholder needs a content property defining the replacement of it (https://mzl.la/2DT1MQd)":[ "Placeholder cần một thuộc tính nội dung xác định việc thay thế nó (https://mzl.la/2DT1MQd)" ],
      "Translation string is missing the message property":[ "Chuỗi dịch bị thiếu thuộc tính tin nhắn" ],
      "No \"message\" message property is set for a string (https://mzl.la/2DSBTjA).":[ "Không có thuộc tính thông báo \"message\" nào được đặt cho một chuỗi (https://mzl.la/2DSBTjA)." ],
      "The field is required.":[ "Trường này là bắt buộc." ],
      "See https://mzl.la/1ZOhoEN (MDN Docs) for more information.":[ "Xem https://mzl.la/1ZOhoEN (Tài liệu MDN) để biết thêm thông tin." ],
      "The permission type is unsupported.":[ "Loại quyền hạn không được hỗ trợ." ],
      "See https://mzl.la/1R1n1t0 (MDN Docs) for more information.":[ "Xem tại https://mzl.la/1R1n1t0 (Tài liệu MDN) để biết thêm thông tin." ],
      "See https://mzl.la/2Qn0fWC (MDN Docs) for more information.":[ "Xem tại https://mzl.la/2Qn0fWC (Tài liệu MDN) để biết thêm thông tin." ],
      "See https://mzl.la/3Woeqv4 (MDN Docs) for more information.":[ "Xem tại https://mzl.la/3Woeqv4 (Tài liệu MDN) để biết thêm thông tin." ],
      "Unknown permission.":[ "Không rõ quyền hạn." ],
      "%(instancePath)s: the following privileged permissions are only allowed in privileged extensions: %(privilegedPermissions)s.":[ "%(instancePath)s: các quyền đặc quyền sau chỉ được phép trong các tiện ích mở rộng đặc quyền: %(privilegedPermissions)s." ],
      "Invalid host permission.":[ "Quyền hạn host không hợp lệ." ],
      "Invalid install origin.":[ "Nguồn gốc cài đặt không hợp lệ." ],
      "Invalid install origin. A valid origin has - only - a scheme, hostname and optional port. See https://mzl.la/3TEbqbE (MDN Docs) for more information.":[ "Origin cài đặt không hợp lệ. Một origin hợp lệ có - chỉ có - một scheme, hostname và port tùy chọn. Xem tại https://mzl.la/3TEbqbE (Tài liệu web MDN) để biết thêm thông tin." ],
      "The field is invalid.":[ "Trường không hợp lệ." ],
      "\"manifest_version\" in the manifest.json is not a valid value":[ "\"manifest_version\" trong manifest.json không phải là một giá trị hợp lệ" ],
      "See https://mzl.la/20PenXl (MDN Docs) for more information.":[ "Xem tại https://mzl.la/20PenXl (Tài liệu MDN) để biết thêm thông tin." ],
      "\"%(property)s\" allows remote code execution in manifest.json":[ "\"%(property)s\" cho phép thực thi mã từ xa trong manifest.json" ],
      "A custom \"%(property)s\" needs additional review.":[ "Một \"%(property)s\" tùy chỉnh cần xem xét bổ sung." ],
      "\"%(property)s\" allows 'eval', which has strong security and performance implications.":[ "\"%(property)s\" cho phép 'eval', có ý nghĩa mạnh mẽ về bảo mật và hiệu suất." ],
      "In most cases the same result can be achieved differently, therefore it is generally prohibited":[ "Trong hầu hết các trường hợp, cùng một kết quả có thể đạt được theo cách khác nhau, do đó, nó thường bị cấm" ],
      "The \"name\" property must be a string with no leading/trailing whitespaces.":[ "Thuộc tính \"name\" phải là một chuỗi không có khoảng trắng đầu/cuối." ],
      "See http://mzl.la/1STmr48 (MDN Docs) for more information.":[ "Xem tại http://mzl.la/1STmr48 (Tài liệu MDN) để biết thêm thông tin." ],
      "\"update_url\" is not allowed.":[ "\"update_url\" không được cho phép." ],
      "\"applications.gecko.update_url\" or \"browser_specific_settings.gecko.update_url\" are not allowed for Mozilla-hosted add-ons.":[ "\"applications.gecko.update_url\" hoặc \"browser_specific_settings.gecko.update_url\" không được phép cho các tiện ích mở rộng do Mozilla lưu trữ." ],
      "The \"update_url\" property is not used by Firefox.":[ "Thuộc tính \"update_url\" không được sử dụng bởi Firefox." ],
      "The \"update_url\" is not used by Firefox in the root of a manifest; your add-on will be updated via the Add-ons site and not your \"update_url\". See: https://mzl.la/25zqk4O":[ "\"update_url\" không được Firefox sử dụng trong thư mục gốc của tập tin manifest; tiện ích mở rộng của bạn sẽ được cập nhật qua trang web tiện ích mở rộng chứ không phải \"update_url\" của bạn. Xem: https://mzl.la/25zqk4O" ],
      "\"strict_max_version\" not required.":[ "\"strict_max_version\" không bắt buộc." ],
      "\"strict_max_version\" shouldn't be used unless the add-on is expected not to work with future versions of Firefox.":[ "Không nên sử dụng \"strict_max_version\" trừ khi tiện ích mở rộng được cho là sẽ không hoạt động với các phiên bản Firefox trong tương lai." ],
      "Manifest Version 3 is not fully supported on Firefox for Android.":[ "Manifest phiên bản 3 không được hỗ trợ đầy đủ trên Firefox dành cho Android." ],
      "No \"%(property)s\" property found in manifest.json":[ "Không tìm thấy thuộc tính \"%(property)s\" nào trong manifest.json" ],
      "\"%(property)s\" is required":[ "\"%(property)s\" là bắt buộc" ],
      "An icon defined in the manifest could not be found in the package.":[ "Không thể tìm thấy biểu tượng được xác định trong tập tin manifest trong gói." ],
      "Icon could not be found at \"%(path)s\".":[ "Không thể tìm thấy biểu tượng ở \"%(path)s\"." ],
      "A background script defined in the manifest could not be found.":[ "Không thể tìm thấy tập lệnh nền được xác định trong tập tin kê khai." ],
      "A background page defined in the manifest could not be found.":[ "Không thể tìm thấy một trang nền được xác định trong tập tin kê khai." ],
      "Background script could not be found at \"%(path)s\".":[ "Không thể tìm thấy tập lệnh nền tại \"%(path)s\"." ],
      "Background page could not be found at \"%(path)s\".":[ "Không thể tìm thấy trang nền tại \"%(path)s\"." ],
      "A content script defined in the manifest could not be found.":[ "Không thể tìm thấy tập lệnh nội dung được xác định trong tập tin kê khai." ],
      "A content script css file defined in the manifest could not be found.":[ "Không thể tìm thấy tập tin css tập lệnh nội dung được xác định trong tập tin kê khai." ],
      "Content script defined in the manifest could not be found at \"%(path)s\".":[ "Không thể tìm thấy tập lệnh nội dung được xác định trong tập tin manifest tại \"%(path)s\"." ],
      "Content script css file defined in the manifest could not be found at \"%(path)s\".":[ "Không thể tìm thấy tệp css tập lệnh nội dung được xác định trong tập tin manifest tại \"%(path)s\"." ],
      "A dictionary file defined in the manifest could not be found.":[ "Không thể tìm thấy tập tin từ điển được xác định trong tập tin kê khai." ],
      "Dictionary file defined in the manifest could not be found at \"%(path)s\".":[ "Không thể tìm thấy tập tin từ điển được xác định trong tập tin kê khai tại \"%(path)s\"." ],
      "The manifest contains multiple dictionaries.":[ "Tập tin kê khai chứa nhiều từ điển." ],
      "Multiple dictionaries were defined in the manifest, which is unsupported.":[ "Nhiều từ điển đã được định nghĩa trong kê khai, mà nó không được hỗ trợ." ],
      "The manifest contains a dictionaries object, but it is empty.":[ "Tập tin kê khai chứa một đối tượng từ điển, nhưng nó trống." ],
      "A dictionaries object was defined in the manifest, but it was empty.":[ "Một đối tượng từ điển đã được định nghĩa trong tập tin kê khai, nhưng nó trống." ],
      "The manifest contains a dictionary but no id property.":[ "Tập tin kê khai chứa một từ điển nhưng không có thuộc tính id." ],
      "A dictionary was found in the manifest, but there was no id set.":[ "Một từ điển đã được tìm thấy trong tập tin kê khai, nhưng không được đặt id." ],
      "Please refer to https://github.com/mozilla-extensions/xpi-manifest to learn more about privileged extensions and signing.":[ "Vui lòng tham khảo https://github.com/mozilla-extensions/xpi-manifest để tìm hiểu thêm về các tiện ích mở rộng đặc quyền và ký." ],
      "Forbidden content found in add-on.":[ "Đã tìm thấy nội dung bị cấm trong tiện ích." ],
      "This add-on contains forbidden content.":[ "Tiện ích này chứa nội dung bị cấm." ],
      "Icons must be square.":[ "Biểu tượng phải là hình vuông." ],
      "Icon at \"%(path)s\" must be square.":[ "Biểu tượng tại \"%(path)s\" phải là hình vuông." ],
      "The size of the icon does not match the manifest.":[ "Kích thước của biểu tượng không phù hợp với manifest." ],
      "Expected icon at \"%(path)s\" to be %(expected)d pixels wide but was %(actual)d.":[ "Biểu tượng tìm thấy tại \"%(path)s\" rộng %(expected)d pixel nhưng lại là %(actual)d." ],
      "\"%(fieldName)s\" is ignored for non-privileged add-ons.":[ "\"%(fieldName)s\" sẽ bị bỏ qua đối với các tiện ích mở rộng không có đặc quyền." ],
      "Corrupt image file":[ "Tập tin bị hỏng" ],
      "Expected icon file at \"%(path)s\" is corrupted":[ "Tập tin biểu tượng tại \"%(path)s\" bị hỏng" ],
      "This property has been deprecated.":[ "Thuộc tính này đã không còn được dùng nữa." ],
      "This theme LWT alias has been removed in Firefox 70.":[ "Bí danh chủ đề LWT này đã bị xóa trong Firefox 70." ],
      "See https://mzl.la/2T11Lkc (MDN Docs) for more information.":[ "Xem tại https://mzl.la/2T11Lkc (Tài liệu MDN) để biết thêm thông tin." ],
      "Theme image for \"%(type)s\" could not be found at \"%(path)s\"":[ "Không thể tìm thấy hình ảnh của chủ đề cho \"%(type)s\" tại \"%(path)s\"" ],
      "\"%(fieldName)s\" manifest field is only used for privileged and temporarily installed extensions.":[ "Trường tập tin manifest \"%(fieldName)s\" chỉ được sử dụng cho các tiện ích mở rộng được cài đặt tạm thời và có đặc quyền." ],
      "Corrupted theme image file":[ "Tập tin hình ảnh của chủ đề bị hỏng" ],
      "Theme image file at \"%(path)s\" is corrupted":[ "Tập tin hình ảnh của chủ đề tại \"%(path)s\" bị hỏng" ],
      "Theme image file has an unsupported file extension":[ "Tập tin hình ảnh chủ đề có phần mở rộng không được hỗ trợ" ],
      "Theme image file at \"%(path)s\" has an unsupported file extension":[ "Tập tin hình ảnh chủ đề tại \"%(path)s\" có phần mở rộng không được hỗ trợ" ],
      "Theme image file has an unsupported mime type":[ "Tập tin hình ảnh chủ đề có loại mime không được hỗ trợ" ],
      "Theme image file at \"%(path)s\" has the unsupported mime type \"%(mime)s\"":[ "Tập tin hình ảnh chủ đề tại \"%(path)s\" có loại mime không được hỗ trợ \"%(mime)s\"" ],
      "Theme image file mime type does not match its file extension":[ "Loại mime của tập tin hình ảnh chủ đề không khớp với phần mở rộng tập tin của nó" ],
      "Theme image file extension at \"%(path)s\" does not match its actual mime type \"%(mime)s\"":[ "Phần mở rộng Tập tin hình ảnh chủ đề tại \"%(path)s\" không khớp với loại mime thực của nó \"%(mime)s\"" ],
      "The \"default_locale\" is missing localizations.":[ "Thuộc tính \"default_locale\" bị thiếu bản địa hóa." ],
      "The \"default_locale\" value is specified in the manifest, but no matching \"messages.json\" in the \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "Giá trị \"default_locale\" được chỉ định trong manifest, nhưng không khớp \"messages.json\" trong thư mục tồn tại \"_locales\". Xem: https://mzl.la/2hjcaEE" ],
      "The \"default_locale\" is missing but \"_locales\" exist.":[ "Thuộc tính \"default_locale\" bị thiếu nhưng \"_locales\" tồn tại." ],
      "The \"default_locale\" value is not specifed in the manifest, but a \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "Giá trị \"default_locale\" không được chỉ định trong manifest, nhưng tồn tại thư mục \"_locales\". Xem: https://mzl.la/2hjcaEE" ],
      "Unsupported image extension":[ "Phần mở rộng của hình ảnh không được hỗ trợ" ],
      "Icons should be one of JPG/JPEG, WebP, GIF, PNG or SVG.":[ "Các biểu tượng phải là một trong JPG/JPEG, WebP, GIF, PNG hoặc SVG." ],
      "\"applications\" property overridden by \"browser_specific_settings\" property":[ "Thuộc tính \"applications\" bị ghi đè bởi thuộc tính \"browser_specific_settings\"" ],
      "The \"applications\" property is being ignored because it is superseded by the \"browser_specific_settings\" property which is also defined in your manifest. Consider removing applications.":[ "Thuộc tính \"applications\" đang bị bỏ qua vì thuộc tính này được thay thế bởi thuộc tính \"browser_specific_settings\" cũng được xác định trong manifest của bạn. Xem xét gỡ bỏ applications." ],
      "Empty language directory":[ "Thư mục ngôn ngữ trống" ],
      "messages.json file missing in \"%(path)s\"":[ "Thiếu tập tin messages.json trong \"%(path)s\"" ],
      "Manifest key not supported by the specified minimum Firefox version":[ "Khóa kê khai không được hỗ trợ bởi phiên bản Firefox tối thiểu đã chỉ định" ],
      "\"strict_min_version\" requires Firefox %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "\"strict_min_version\" yêu cầu Firefox %(minVersion)s, được phát hành trước khi phiên bản %(versionAdded)s giới thiệu hỗ trợ cho \"%(key)s\"." ],
      "\"%(fieldName)s\" is not supported in manifest versions %(versionRange)s.":[ "\"%(fieldName)s\" không được hỗ trợ trong các phiên bản %(versionRange)s của tập tin manifest." ],
      "Permission not supported by the specified minimum Firefox version":[ "Quyền hạn không được hỗ trợ bởi phiên bản Firefox tối thiểu đã chỉ định" ],
      "\"%(fieldName)s\" is not supported.":[ "\"%(fieldName)s\" không được hỗ trợ." ],
      "Manifest key not supported by the specified minimum Firefox for Android version":[ "Khóa kê khai không được hỗ trợ bởi Firefox dành cho Android phiên bản tối thiểu đã chỉ định" ],
      "\"strict_min_version\" requires Firefox for Android %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "\"strict_min_version\" yêu cầu Firefox dành cho Android %(minVersion)s, được phát hành trước khi phiên bản %(versionAdded)s giới thiệu hỗ trợ cho \"%(key)s\"." ],
      "Permission not supported by the specified minimum Firefox for Android version":[ "Quyền hạn không được hỗ trợ bởi Firefox dành cho Android phiên bản tối thiểu đã chỉ định" ],
      "Linking to \"addons.mozilla.org\" is not allowed":[ "Liên kết đến \"addons.mozilla.org\" không được phép" ],
      "Links directing to \"addons.mozilla.org\" are not allowed to be used for homepage":[ "Các liên kết trực tiếp đến \"addons.mozilla.org\" đều không được phép sử dụng cho trang chủ" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above":[ "Quyền \"%(permission)s\" yêu cầu đặt \"strict_min_version\" thành \"%(minFirefoxVersion)s\" hoặc cao hơn" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above. Please update your manifest.json version to specify a minimum Firefox version.":[ "Quyền \"%(permission)s\" yêu cầu đặt \"strict_min_version\" thành \"%(minFirefoxVersion)s\" hoặc cao hơn. Vui lòng cập nhật phiên bản manifest.json của bạn để chỉ định phiên bản Firefox tối thiểu." ],
      "The extension ID is required in Manifest Version 3 and above.":[ "ID tiện ích là bắt buộc trong tập tin manifest phiên bản 3 trở lên." ],
      "See https://mzl.la/3PLZYdo for more information.":[ "Xem tại https://mzl.la/3PLZYdo để biết thêm thông tin." ],
      "%(instancePath)s: Privileged extensions should declare privileged permissions.":[ "" ],
      "This extension does not declare any privileged permission. It does not need to be signed with the privileged certificate. Please upload it directly to https://addons.mozilla.org/.":[ "" ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for privileged extensions.":[ "" ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for extensions that include privileged manifest fields.":[ "" ],
      "%(instancePath)s: privileged manifest fields are only allowed in privileged extensions.":[ "" ],
      "This extension does not include the \"mozillaAddons\" permission, which is required for privileged extensions.":[ "" ],
      "Cannot use actions in hidden add-ons.":[ "Không thể sử dụng các hành động trong tiện ích mở rộng ẩn." ],
      "The hidden and browser_action/page_action (or action in Manifest Version 3 and above) properties are mutually exclusive.":[ "" ],
      "Use \"browser_specific_settings\" instead of \"applications\".":[ "Sử dụng \"browser_specific_settings\" thay thế \"applications\"." ],
      "The \"applications\" property in the manifest is deprecated and will no longer be accepted in Manifest Version 3 and above.":[ "Thuộc tính \"applications\" trong Manifest không được dùng nữa và sẽ không còn được chấp nhận trong phiên bản manifest 3 trở lên." ],
      "\"applications\" is no longer allowed in Manifest Version 3 and above.":[ "\"applications\" không còn được chấp nhận trong Manifest phiên bản 3 trở lên." ],
      "The \"applications\" property in the manifest is no longer allowed in Manifest Version 3 and above. Use \"browser_specific_settings\" instead.":[ "Thuộc tính \"applications\" trong Manifest không còn được phép trong Manifest phiên bản 3 trở lên. Thay vào đó, hãy sử dụng \"browser_specific_settings\"." ],
      "The version string should be simplified because it won't be compatible with Manifest Version 3 and above.":[ "Chuỗi phiên bản phải được đơn giản hóa vì chuỗi này sẽ không tương thích với Manifest phiên bản 3 trở lên." ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros will no longer be allowed. Letters will no longer be allowed either. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "Phiên bản phải là một chuỗi có các số từ 1 đến 4 được phân tách bằng dấu chấm. Mỗi số phải có tối đa 9 chữ số và các số 0 đứng đầu sẽ không còn được phép. Chữ cái cũng sẽ không được phép nữa. Xem https://mzl.la/3h3mCRu (Tài liệu MDN) để biết thêm thông tin." ],
      "The version string should be simplified.":[ "Chuỗi phiên bản nên được đơn giản hóa." ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros are not allowed. Letters are no longer allowed. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "Phiên bản phải là một chuỗi có các số từ 1 đến 4 được phân tách bằng dấu chấm. Mỗi số phải có tối đa 9 chữ số và không được phép có số 0 đứng đầu. Chữ cái không còn được phép. Xem https://mzl.la/3h3mCRu (Tài liệu MDN) để biết thêm thông tin." ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported in manifest versions %(versionRange)s.":[ "/%(fieldName)s: \"%(permissionName)s\" không được hỗ trợ trong các phiên bản %(versionRange)s của manifest." ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported.":[ "/%(fieldName)s: \"%(permissionName)s\" không được hỗ trợ." ] } } }