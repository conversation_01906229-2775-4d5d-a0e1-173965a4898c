module.exports = { domain:"messages",
  locale_data:{ messages:{ "":{ domain:"messages",
        plural_forms:"nplurals=2; plural=(n != 1);",
        lang:"it" },
      "Validation Summary:":[ "Riepilogo della convalida:" ],
      Code:[ "Codice" ],
      Message:[ "Messaggio" ],
      Description:[ "Descrizione" ],
      File:[ "File" ],
      Line:[ "Riga" ],
      Column:[ "Colonna" ],
      "Invalid manifest version range requested: --min-manifest-version (currently set to %(minManifestVersion)s) should not be greater than --max-manifest-version (currently set to %(maxManifestVersion)s).":[ "Richiesto intervallo di versioni del manifesto non valido: --min-manifest-version (attualmente impostato a %(minManifestVersion)s) non deve essere maggiore di --max-manifest-version (attualmente impostato a %(maxManifestVersion)s).--min-manifest-version" ],
      "Your FTL is not valid.":[ "Il file FTL non è valido." ],
      "Your FTL file could not be parsed.":[ "Impossibile analizzare il file FTL." ],
      "Remote scripts are not allowed as per the Add-on Policies.":[ "Gli script remoti non sono consentiti secondo la politica dei componenti aggiuntivi." ],
      "Please include all scripts in the add-on. For more information, refer to https://mzl.la/2uEOkYp.":[ "Includere tutti gli script nel componente aggiuntivo. Per ulteriori informazioni, fare riferimento a https://mzl.la/2uEOkYp." ],
      "Inline scripts blocked by default":[ "Gli script inline sono bloccati per impostazione predefinita" ],
      "Default CSP rules prevent inline JavaScript from running (https://mzl.la/2pn32nd).":[ "Le regole CSP predefinite impediscono l’esecuzione di JavaScript inline (https://mzl.la/2pn32nd)." ],
      "Unadvised 3rd-party JS library":[ "Librerie JS di terze parti sconsigliate" ],
      "Your add-on uses a JavaScript library we do not recommend. Read more: https://bit.ly/1TRIyZY":[ "Il componente aggiuntivo utilizza una libreria JavaScript non consigliata. Per ulteriori informazioni: https://bit.ly/1TRIyZY" ],
      "Known JS library detected":[ "Rilevata una libreria JS nota" ],
      "JavaScript libraries are discouraged for simple add-ons, but are generally accepted.":[ "Le librerie JavaScript sono sconsigliate per componenti aggiuntivi semplici, ma sono generalmente accettate." ],
      "Due to both security and performance concerns, this may not be set using dynamic values which have not been adequately sanitized. This can lead to security issues or fairly serious performance degradation.":[ "Per motivi di sicurezza e prestazioni, questo non può essere impostato utilizzando valori dinamici che non sono stati adeguatamente purificati. Ciò può causare problemi di sicurezza o un grave degrado delle prestazioni." ],
      "{{api}} is not supported":[ "{{api}} non è supportato" ],
      "This API has not been implemented by Firefox.":[ "Questa API non è stata implementata da Firefox." ],
      "\"{{api}}\" has been removed in Manifest Version 3 (`manifest_version` property)":[ "“{{api}}” è stato rimosso dalla versione 3 del manifesto (proprietà “manifest_version”)" ],
      "{{api}} is deprecated":[ "{{api}} è deprecato" ],
      "This API has been deprecated by Firefox.":[ "Questa API è deprecata in Firefox." ],
      "Content script file could not be found.":[ "Impossibile trovare il file content script." ],
      "\"{{api}}\" is deprecated or unimplemented":[ "“{{api}}” è deprecato o non implementato" ],
      "Content script file could not be found":[ "Impossibile trovare il file content script" ],
      "\"%(api)s\" can cause issues when loaded temporarily":[ "\"$(api)s\" può causare problemi se caricata temporaneamente" ],
      "This API can cause issues when loaded temporarily using about:debugging in Firefox unless you specify \"browser_specific_settings.gecko.id\" in the manifest. Please see: https://mzl.la/2hizK4a for more.":[ "Questa API può causare problemi se caricata temporaneamente utilizzando about:debugging in Firefox, a meno di specificare “browser_specific_settings.gecko.id” nel manifesto. Per ulteriori informazioni, consultare: https://mzl.la/2hizK4a." ],
      "{{api}} is not supported in Firefox version {{minVersion}}":[ "{{api}} non è supportata nella versione {{minVersion}} di Firefox" ],
      "This API is not implemented by the given minimum Firefox version":[ "Questa API non è stata implementata nella versione minima richiesta di Firefox" ],
      "{{api}} is not supported in Firefox for Android version {{minVersion}}":[ "{{api}} non è supportata nella versione {{minVersion}} di Firefox per Android" ],
      "This API is not implemented by the given minimum Firefox for Android version":[ "Questa API non è stata implementata nella versione minima richiesta di Firefox per Android" ],
      "Content script file name should not be empty.":[ "Il nome del file content script non può essere vuoto." ],
      "Content script file name should not be empty":[ "Il nome del file content script non può essere vuoto" ],
      "\"%(method)s\" called with a non-literal uri":[ "“%(method)s” chiamato con un URI non letterale" ],
      "Calling \"%(method)s\" with variable parameters can result in potential security vulnerabilities if the variable contains a remote URI. Consider using 'window.open' with the 'chrome=no' flag.":[ "La chiamata di “%(method)s” con parametri variabili può causare potenziali vulnerabilità di sicurezza se la variabile contiene un URI remoto. Considerare l’utilizzo di “window.open” con il flag “chrome=no”." ],
      "\"%(method)s\" called with non-local URI":[ "“%(method)s” chiamato con URI non locale" ],
      "Calling \"%(method)s\" with a non-local URI will result in the dialog being opened with chrome privileges.":[ "Se si chiama “%(method)s” con un URI non locale, la finestra di dialogo verrà aperta con i privilegi chrome." ],
      "JavaScript syntax error":[ "Errore di sintassi JavaScript" ],
      "There is a JavaScript syntax error in your code, which might be related to some experimental JavaScript features that aren't an official part of the language specification and therefore not supported yet. The validation cannot continue on this file.":[ "È presente un errore di sintassi JavaScript nel codice. Potrebbe trattarsi di funzionalità JavaScript sperimentali che non fanno parte delle specifiche del linguaggio e pertanto non sono ancora supportate. Impossibile continuare la convalida per questo file." ],
      "Evaluation of strings as code can lead to security vulnerabilities and performance issues, even in the most innocuous of circumstances. Please avoid using `eval` and the `Function` constructor when at all possible.":[ "La valutazione di stringhe come codice può portare a vulnerabilità di sicurezza e problemi di prestazioni, anche nelle circostanze più innocue. Quando possibile, evitare l’utilizzo di “eval“ e del costruttore “Function”." ],
      "setTimeout, setInterval and execScript functions should be called only with function expressions as their first argument":[ "Le funzioni setTimeout, setInterval ed execScript devono essere chiamate solo con espressioni di funzione come primo argomento" ],
      "Unexpected global passed as an argument":[ "Variabile globale inattesa passata come argomento" ],
      "Passing a global as an argument is not recommended. Please make this a var instead.":[ "Non è consigliabile passare un “global” come argomento. Al suo posto utilizzare “var”." ],
      "Use of document.write strongly discouraged.":[ "L'uso di document.write è sconsigliato." ],
      "document.write will fail in many circumstances when used in extensions, and has potentially severe security repercussions when used improperly. Therefore, it should not be used.":[ "document.write non funzionerà in molte circostanze quando utilizzato nelle estensioni e potrebbe avere gravi ripercussioni sulla sicurezza se utilizzato in modo improprio. Pertanto, non dovrebbe essere utilizzato." ],
      "Banned 3rd-party JS library":[ "Librerie JS di terze parti non consentite" ],
      "Your add-on uses a JavaScript library we consider unsafe. Read more: https://bit.ly/1TRIyZY":[ "Il tuo componente aggiuntivo utilizza una libreria JavaScript che consideriamo non sicura. Per saperne di più: https://bit.ly/1TRIyZY" ],
      "Your JSON contains block comments.":[ "Il file JSON contiene commenti di blocco." ],
      "Only line comments (comments beginning with \"//\") are allowed in JSON files. Please remove block comments (comments beginning with \"/*\")":[ "Nei file JSON sono consentiti solo commenti di riga (commenti che iniziano con “//”). Rimuovere i blocchi di commenti (commenti che iniziano con “/*”)" ],
      "Duplicate keys are not allowed in JSON files.":[ "Le chiavi duplicate non sono ammesse nei file JSON." ],
      "Duplicate key found in JSON file.":[ "È stata trovata una chiave duplicata nel file JSON." ],
      "Your JSON is not valid.":[ "Il file JSON non è valido." ],
      "Your JSON file could not be parsed.":[ "Impossibile analizzare il file JSON." ],
      "Reserved filename found.":[ "Rilevato un nome di file riservato." ],
      "Files whose names are reserved have been found in the add-on. Please refrain from using them and rename your files.":[ "Sono stati trovati dei file nel componente aggiuntivo con nomi riservati. Astenersi dall’utilizzare questi nomi e rinominare i file." ],
      "The package is invalid. It may contain entries using invalid characters, as an example using '\\' as a path separator is not allowed in Firefox. Try to recreate your add-on package (ZIP) and make sure all entries are using '/' as the path separator.":[ "Il pacchetto non è valido. Potrebbe contenere voci che utilizzano caratteri non validi, ad esempio l’utilizzo di “\\” come separatore di percorso non è consentito in Firefox. Prova a ricreare il pacchetto del tuo componente aggiuntivo (ZIP) e assicurati che tutte le voci utilizzino “/” come separatore di percorso." ],
      "We were unable to decompress the zip file.":[ "Non è stato possibile decomprimere il file zip." ],
      "manifest.json was not found":[ "Impossibile trovare manifest.json" ],
      "No manifest.json was found at the root of the extension. The package file must be a ZIP of the extension's files themselves, not of the containing directory. See: https://mzl.la/2r2McKv for more on packaging.":[ "Non è stato trovato alcun file manifest.json nella radice dell’estensione. Il file del pacchetto deve essere uno ZIP dei file dell’estensione stessi, non della directory che li contiene. Per ulteriori informazioni sui pacchetti, consultare: https://mzl.la/2r2McKv." ],
      "File is too large to parse.":[ "Il file è troppo grande per essere analizzato." ],
      "This file is not binary and is too large to parse. Files larger than %(maxFileSizeToParseMB)sMB will not be parsed. Consider moving large lists of data out of JavaScript files and into JSON files, or splitting very large files into smaller ones.":[ "Questo file non è binario ed è troppo grande per essere analizzato. I file di dimensioni superiori a %(maxFileSizeToParseMB)s MB non verranno analizzati. Prendi in considerazione la possibilità di spostare elenchi di dati di grandi dimensioni da JavaScript a JSON, o dividere file molto grandi in file più piccoli." ],
      "Hidden file flagged":[ "Segnalato file nascosto" ],
      "Hidden files complicate the review process and can contain sensitive information about the system that generated the add-on. Please modify the packaging process so that these files aren't included.":[ "I file nascosti complicano il processo di revisione e possono contenere informazioni riservate sul sistema che ha generato il componente aggiuntivo. Modificare il processo di creazione di pacchetti in modo che questi file non siano inclusi." ],
      "Package contains duplicate entries":[ "Il pacchetto contiene voci duplicate" ],
      "Flagged filename found":[ "Rilevato un nome di file segnalato" ],
      "Files were found that are either unnecessary or have been included unintentionally. They should be removed.":[ "Sono stati rilevati file non necessari o inclusi involontariamente. Dovrebbero essere rimossi." ],
      "The package contains multiple entries with the same name. This practice has been banned. Try unzipping and re-zipping your add-on package and try again.":[ "Il pacchetto contiene più voci con lo stesso nome. Questa pratica è stata vietata. Prova a decomprimere e ricomprimere il pacchetto del componente aggiuntivo e riprovare." ],
      "Flagged file extensions found":[ "Rilevate estensioni di file segnalate" ],
      "Flagged file type found":[ "Rilevato un tipo di file segnalato" ],
      "Files whose names end with flagged extensions have been found in the add-on. The extension of these files are flagged because they usually identify binary components. Please see https://bit.ly/review-policy for more information on the binary content review process.":[ "Nel componente aggiuntivo sono stati trovati file i cui nomi terminano con estensioni contrassegnate. L'estensione di questi file è contrassegnata perché in genere identifica componenti binari. Per ulteriori informazioni sul processo di revisione dei contenuti binari, consultare https://bit.ly/review-policy." ],
      "Package already signed":[ "Pacchetto già firmato" ],
      "Add-ons which are already signed will be re-signed when published on AMO. This will replace any existing signatures on the add-on.":[ "I componenti aggiuntivi già firmati verranno firmati nuovamente una volta pubblicati su AMO. Questo sostituirà tutte le firme esistenti nel componente aggiuntivo." ],
      "Firefox add-ons are not allowed to run coin miners.":[ "Il Bitcoin mining non è consentito nei componenti aggiuntivi per Firefox." ],
      "We do not allow coinminer scripts to be run inside WebExtensions. See https://github.com/mozilla/addons-linter/issues/1643 for more details.":[ "L’esecuzione di script coinminer all’interno di WebExtensions non è consentita. Per ulteriori dettagli, consultare https://github.com/mozilla/addons-linter/issues/1643." ],
      "String name is reserved for a predefined message":[ "Nome stringa riservato per un messaggio predefinito" ],
      "String names starting with @@ get translated to built-in constants (https://mzl.la/2BL9ZjE).":[ "I nomi di stringa che iniziano con @@ vengono convertiti in costanti integrate (https://mzl.la/2BL9ZjE)." ],
      "String name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2Eztyi5).":[ "Il nome della stringa deve contenere solo caratteri alfanumerici, _ e @ (https://mzl.la/2Eztyi5)." ],
      "Placeholder for message is missing":[ "Segnaposto per messaggio mancante" ],
      "A placeholder used in the message is not defined.":[ "Un segnaposto utilizzato nel messaggio non è stato definito." ],
      "Placeholder name contains invalid characters":[ "Il nome del segnaposto contiene caratteri non validi" ],
      "Placeholder name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2ExbYez).":[ "Il nome del segnaposto deve contenere solo caratteri alfanumerici, _ e @ (https://mzl.la/2ExbYez)." ],
      "Placeholder is missing the content property":[ "Proprietà “content“ mancante nel segnaposto" ],
      "A placeholder needs a content property defining the replacement of it (https://mzl.la/2DT1MQd)":[ "Un segnaposto necessita di una proprietà content che ne definisca la sostituzione (https://mzl.la/2DT1MQd)" ],
      "Translation string is missing the message property":[ "Proprietà “message“ mancante nella stringa tradotta" ],
      "No \"message\" message property is set for a string (https://mzl.la/2DSBTjA).":[ "Nessuna proprietà “message“ è impostata per una stringa (https://mzl.la/2DSBTjA)." ],
      "The field is required.":[ "Campo obbligatorio." ],
      "See https://mzl.la/1ZOhoEN (MDN Docs) for more information.":[ "Visita https://mzl.la/1ZOhoEN (Documentazione MDN) per maggiori informazioni." ],
      "The permission type is unsupported.":[ "Il tipo di permesso non è supportato." ],
      "See https://mzl.la/1R1n1t0 (MDN Docs) for more information.":[ "Visita https://mzl.la/1R1n1t0 (Documentazione MDN) per maggiori informazioni." ],
      "See https://mzl.la/2Qn0fWC (MDN Docs) for more information.":[ "Visita https://mzl.la/2Qn0fWC (Documentazione MDN) per maggiori informazioni." ],
      "See https://mzl.la/3Woeqv4 (MDN Docs) for more information.":[ "Visita https://mzl.la/3Woeqv4 (MDN Docs) per ulteriori informazioni." ],
      "Unknown permission.":[ "Permesso sconosciuto." ],
      "%(instancePath)s: the following privileged permissions are only allowed in privileged extensions: %(privilegedPermissions)s.":[ "%(instancePath)s: i seguenti permessi sono consentiti solo nelle estensioni con privilegi: %(privilegedPermissions)s." ],
      "Invalid host permission.":[ "“host permission\" non valido." ],
      "Invalid install origin.":[ "Origine dell’installazione non valida." ],
      "Invalid install origin. A valid origin has - only - a scheme, hostname and optional port. See https://mzl.la/3TEbqbE (MDN Docs) for more information.":[ "Origine installazione non valida. Un’origine valida ha solo uno schema, un nome host e una porta facoltativa. Per ulteriori informazioni, consultare https://mzl.la/3TEbqbE (MDN Docs)." ],
      "The field is invalid.":[ "Il campo è non valido." ],
      "\"manifest_version\" in the manifest.json is not a valid value":[ "\"manifest_version\" nel file manifest.json non è un valore valido" ],
      "See https://mzl.la/20PenXl (MDN Docs) for more information.":[ "Visita https://mzl.la/20PenXl (Documentazione MDN) per maggiori informazioni." ],
      "\"%(property)s\" allows remote code execution in manifest.json":[ "“%(property)s” consente l’esecuzione di codice in modalità remota in manifest.json" ],
      "A custom \"%(property)s\" needs additional review.":[ "Un “%(property)s” personalizzato necessita di ulteriore revisione." ],
      "\"%(property)s\" allows 'eval', which has strong security and performance implications.":[ "“%(property)s” consente “eval”, che ha implicazioni significative in termini di sicurezza e prestazioni." ],
      "In most cases the same result can be achieved differently, therefore it is generally prohibited":[ "Nella maggior parte dei casi lo stesso risultato può essere ottenuto in modo diverso, pertanto è generalmente vietato" ],
      "The \"name\" property must be a string with no leading/trailing whitespaces.":[ "La proprietà “name” deve essere una stringa senza spazi vuoti all’inizio o alla fine." ],
      "See http://mzl.la/1STmr48 (MDN Docs) for more information.":[ "Visita http://mzl.la/1STmr48 (Documentazione MDN) per maggiori informazioni." ],
      "\"update_url\" is not allowed.":[ "\"update_url\" non è consentito." ],
      "\"applications.gecko.update_url\" or \"browser_specific_settings.gecko.update_url\" are not allowed for Mozilla-hosted add-ons.":[ "“applications.gecko.update_url” o “browser_specific_settings.gecko.update_url” non sono consentiti per i componenti aggiuntivi ospitati da Mozilla." ],
      "The \"update_url\" property is not used by Firefox.":[ "La proprietà \"update_url\" non viene utilizzata da Firefox." ],
      "The \"update_url\" is not used by Firefox in the root of a manifest; your add-on will be updated via the Add-ons site and not your \"update_url\". See: https://mzl.la/25zqk4O":[ "\"update_url\" non è utilizzato da Firefox nella radice di un manifesto; il componente aggiuntivo verrà aggiornato tramite il sito dei componenti aggiuntivi e non tramite “update_url”. Consultare: https://mzl.la/25zqk4O" ],
      "\"strict_max_version\" not required.":[ "\"strict_max_version\" non richiesto." ],
      "\"strict_max_version\" shouldn't be used unless the add-on is expected not to work with future versions of Firefox.":[ "Non utilizzare “strict_max_version” a meno che non si preveda che il componente aggiuntivo non funzioni con le versioni future di Firefox." ],
      "Manifest Version 3 is not fully supported on Firefox for Android.":[ "La versione manifest 3 non è completamente supportata in Firefox per Android." ],
      "No \"%(property)s\" property found in manifest.json":[ "Nessuna proprietà “%(property)s” trovata in manifest.json" ],
      "\"%(property)s\" is required":[ "“%(property)s” è obbligatorio" ],
      "An icon defined in the manifest could not be found in the package.":[ "Non è possibile trovare nel pacchetto un'icona definita nel manifesto." ],
      "Icon could not be found at \"%(path)s\".":[ "Impossibile trovare l’icona in \"%(path)s\"." ],
      "A background script defined in the manifest could not be found.":[ "Impossibile trovare uno script di background definito nel file manifesto." ],
      "A background page defined in the manifest could not be found.":[ "Impossibile trovare una pagina di background definita nel file manifesto." ],
      "Background script could not be found at \"%(path)s\".":[ "Impossibile trovare in \"%(path)s\" il background script." ],
      "Background page could not be found at \"%(path)s\".":[ "Impossibile trovare in \"%(path)s\" la background page." ],
      "A content script defined in the manifest could not be found.":[ "Impossibile trovare uno script di contenuto definito nel file manifesto." ],
      "A content script css file defined in the manifest could not be found.":[ "Impossibile trovare il file css con uno script di contenuto definito nel file manifesto." ],
      "Content script defined in the manifest could not be found at \"%(path)s\".":[ "Impossibile trovare il content script definito nel manifesto in \"%(path)s\"." ],
      "Content script css file defined in the manifest could not be found at \"%(path)s\".":[ "Impossibile trovare il file content script css definito nel manifesto in \"%(path)s\"." ],
      "A dictionary file defined in the manifest could not be found.":[ "Impossibile trovare un file dizionario definito nel file manifesto." ],
      "Dictionary file defined in the manifest could not be found at \"%(path)s\".":[ "Impossibile trovare il file dizionario definito nel file manifesto in “%(path)s”." ],
      "The manifest contains multiple dictionaries.":[ "Il file manifesto contiene più dizionari." ],
      "Multiple dictionaries were defined in the manifest, which is unsupported.":[ "Nel file manifesto sono stati definiti più dizionari. Questa opzione non è supportata." ],
      "The manifest contains a dictionaries object, but it is empty.":[ "Il file manifesto contiene un oggetto dictionaries, che tuttavia risulta vuoto." ],
      "A dictionaries object was defined in the manifest, but it was empty.":[ "Nel file manifesto è stato definito un oggetto dictionaries, che tuttavia risulta vuoto." ],
      "The manifest contains a dictionary but no id property.":[ "Il file manifesto contiene un dizionario, ma è privo di proprietà ID." ],
      "A dictionary was found in the manifest, but there was no id set.":[ "Nel file manifesto è stato rilevato un dizionario senza set ID." ],
      "Please refer to https://github.com/mozilla-extensions/xpi-manifest to learn more about privileged extensions and signing.":[ "Fare riferimento a https://github.com/mozilla-extensions/xpi-manifest per ulteriori informazioni sulle estensioni con privilegi e sulla firma." ],
      "Forbidden content found in add-on.":[ "Rilevato contenuto non consentito nel componente aggiuntivo." ],
      "This add-on contains forbidden content.":[ "Questo componente aggiuntivo include un contenuto non consentito." ],
      "Icons must be square.":[ "Le icone devono essere quadrate." ],
      "Icon at \"%(path)s\" must be square.":[ "L'icona in \"%(path)s\" deve essere quadrata." ],
      "The size of the icon does not match the manifest.":[ "La grandezza dell'icona non corrisponde al manifesto." ],
      "Expected icon at \"%(path)s\" to be %(expected)d pixels wide but was %(actual)d.":[ "L’icona “%(path)s” dovrebbe avere una larghezza di %(expected)d pixel (attuale %(actual)d)." ],
      "\"%(fieldName)s\" is ignored for non-privileged add-ons.":[ "“%(fieldName)s” viene ignorato per i componenti aggiuntivi senza privilegi." ],
      "Corrupt image file":[ "File immagine danneggiato" ],
      "Expected icon file at \"%(path)s\" is corrupted":[ "Il file icona previsto in \"%(path)s\" è danneggiato" ],
      "This property has been deprecated.":[ "Questa proprietà è deprecata." ],
      "This theme LWT alias has been removed in Firefox 70.":[ "Il tema LWT alias è stato rimosso in Firefox 70." ],
      "See https://mzl.la/2T11Lkc (MDN Docs) for more information.":[ "Visita https://mzl.la/2T11Lkc (Documentazione MDN) per maggiori informazioni." ],
      "Theme image for \"%(type)s\" could not be found at \"%(path)s\"":[ "Impossibile trovare l’immagine del tema “%(type)s” in “%(path)s”" ],
      "\"%(fieldName)s\" manifest field is only used for privileged and temporarily installed extensions.":[ "Il campo del manifesto “%(fieldName)s” viene utilizzato solo per le estensioni con privilegi e installate temporaneamente." ],
      "Corrupted theme image file":[ "File immagine del tema danneggiato" ],
      "Theme image file at \"%(path)s\" is corrupted":[ "Il file immagine del tema in “%(path)s” è danneggiato" ],
      "Theme image file has an unsupported file extension":[ "L’estensione del file immagine del tema non è supportata" ],
      "Theme image file at \"%(path)s\" has an unsupported file extension":[ "L’estensione del file immagine del tema in “%(path)s” non è supportata" ],
      "Theme image file has an unsupported mime type":[ "Il tipo MIME del file immagine del tema non è supportato" ],
      "Theme image file at \"%(path)s\" has the unsupported mime type \"%(mime)s\"":[ "Il tipo MIME “%(mime)s” del file immagine del tema in “%(path)s” non è supportato" ],
      "Theme image file mime type does not match its file extension":[ "Il tipo MIME del file immagine del tema non corrisponde all’estensione del file" ],
      "Theme image file extension at \"%(path)s\" does not match its actual mime type \"%(mime)s\"":[ "L’estensione del file immagine del tema in “%(path)s” non corrisponde al tipo MIME effettivo “%(mime)s”" ],
      "The \"default_locale\" is missing localizations.":[ "La traduzione per \"default_locale\" è incompleta." ],
      "The \"default_locale\" value is specified in the manifest, but no matching \"messages.json\" in the \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "Il valore “default_locale\" è specificato nel manifesto, ma non esiste alcun “messages.json” corrispondente nella directory “_locales”. Consultare: https://mzl.la/2hjcaEE" ],
      "The \"default_locale\" is missing but \"_locales\" exist.":[ "Rilevato \"_locales\", ma non \"default_locale\"." ],
      "The \"default_locale\" value is not specifed in the manifest, but a \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "Il valore “default_locale” non è specificato nel manifesto, ma esiste una directory “_locales”. Consultare: https://mzl.la/2hjcaEE" ],
      "Unsupported image extension":[ "Estensione di immagine non supportata" ],
      "Icons should be one of JPG/JPEG, WebP, GIF, PNG or SVG.":[ "Le icone devono essere in formato JPG/JPEG, WebP, GIF, PNG o SVG." ],
      "\"applications\" property overridden by \"browser_specific_settings\" property":[ "Proprietà “applications” sostituita dalla proprietà “browser_specific_settings”" ],
      "The \"applications\" property is being ignored because it is superseded by the \"browser_specific_settings\" property which is also defined in your manifest. Consider removing applications.":[ "La proprietà “applications” è stata ignorata perché è stata sostituita dalla proprietà “browser_specific_settings”, anch'essa definita nel file manifesto. Valuta la possibilità di rimuovere “applications”." ],
      "Empty language directory":[ "Cartella lingue vuota" ],
      "messages.json file missing in \"%(path)s\"":[ "Il file messages.json non è presente in \"%(path)s\"" ],
      "Manifest key not supported by the specified minimum Firefox version":[ "Il manifest key non è supportato nella versione minima specificata di Firefox" ],
      "\"strict_min_version\" requires Firefox %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "“strict_min_version” richiede Firefox %(minVersion)s, che è stato rilasciato prima che la versione %(versionAdded)s introducesse il supporto per “%(key)s”." ],
      "\"%(fieldName)s\" is not supported in manifest versions %(versionRange)s.":[ "“%(fieldName)s” non è supportato nelle versioni del manifesto %(versionRange)s." ],
      "Permission not supported by the specified minimum Firefox version":[ "Il permesso non è supportato nella versione minima specificata di Firefox" ],
      "\"%(fieldName)s\" is not supported.":[ "“%(fieldName)s” non è supportato." ],
      "Manifest key not supported by the specified minimum Firefox for Android version":[ "Il manifest key non è supportato nella versione minima specificata di Firefox per Android" ],
      "\"strict_min_version\" requires Firefox for Android %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "“strict_min_version” richiede Firefox per Android %(minVersion)s, che è stato rilasciato prima che la versione %(versionAdded)s introducesse il supporto per “%(key)s”." ],
      "Permission not supported by the specified minimum Firefox for Android version":[ "Il permesso non è supportato nella versione minima specificata di Firefox per Android" ],
      "Linking to \"addons.mozilla.org\" is not allowed":[ "Il collegamento a “addons.mozilla.org” non è consentito" ],
      "Links directing to \"addons.mozilla.org\" are not allowed to be used for homepage":[ "Non è possibile utilizzare link che puntano a “addons.mozilla.org” come pagina iniziale" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above":[ "L’autorizzazione “%(permission)s” richiede che “strict_min_version” sia impostato su “%(minFirefoxVersion)s” o superiore" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above. Please update your manifest.json version to specify a minimum Firefox version.":[ "L’autorizzazione “%(permission)s” richiede che “strict_min_version” sia impostato su “%(minFirefoxVersion)s” o superiore. Aggiorna la versione di manifest.json per specificare una versione minima di Firefox." ],
      "The extension ID is required in Manifest Version 3 and above.":[ "L’ID dell’estensione è obbligatorio per il manifesto versione 3 e successive." ],
      "See https://mzl.la/3PLZYdo for more information.":[ "Visita https://mzl.la/3PLZYdo per ulteriori informazioni." ],
      "%(instancePath)s: Privileged extensions should declare privileged permissions.":[ "%(instancePath)s: le estensioni con privilegi devono dichiarare permessi con privilegi." ],
      "This extension does not declare any privileged permission. It does not need to be signed with the privileged certificate. Please upload it directly to https://addons.mozilla.org/.":[ "Questa estensione non dichiara alcun permesso con privilegi. Non è necessario firmare con il certificato privilegiato. Caricarla direttamente su https://addons.mozilla.org/." ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for privileged extensions.":[ "%(instancePath)s: per le estensioni con privilegi è richiesta l’autorizzazione “mozillaAddons”." ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for extensions that include privileged manifest fields.":[ "%(instancePath)s: l’autorizzazione “mozillaAddons” è richiesta per le estensioni che includono campi del manifesto con privilegi." ],
      "%(instancePath)s: privileged manifest fields are only allowed in privileged extensions.":[ "%(instancePath)s: i campi del manifesto con privilegi sono consentiti solo nelle estensioni con privilegi." ],
      "This extension does not include the \"mozillaAddons\" permission, which is required for privileged extensions.":[ "Questa estensione non include l’autorizzazione “mozillaAddons” necessaria per le estensioni con privilegi." ],
      "Cannot use actions in hidden add-ons.":[ "Impossibile utilizzare azioni in componenti aggiuntivi nascosti." ],
      "The hidden and browser_action/page_action (or action in Manifest Version 3 and above) properties are mutually exclusive.":[ "Le proprietà “hidden” e “browser_action/page_action” (o “action” nei manifesti versione 3 e successive) si escludono a vicenda." ],
      "Use \"browser_specific_settings\" instead of \"applications\".":[ "Utilizzare “browser_specific_settings” invece di “applications”." ],
      "The \"applications\" property in the manifest is deprecated and will no longer be accepted in Manifest Version 3 and above.":[ "La proprietà “applications” nel manifesto è deprecata e non sarà più accettata nei manifesti versione 3 e successive." ],
      "\"applications\" is no longer allowed in Manifest Version 3 and above.":[ "“applications” non è più consentito nei manifesti versione 3 e successive." ],
      "The \"applications\" property in the manifest is no longer allowed in Manifest Version 3 and above. Use \"browser_specific_settings\" instead.":[ "La proprietà “applications” nel manifesto non è più consentita nei manifesti versione 3 e successive. Al suo posto utilizzare “browser_specific_settings”." ],
      "The version string should be simplified because it won't be compatible with Manifest Version 3 and above.":[ "La stringa della versione dovrebbe essere semplificata in quanto non sarà compatibile con i manifesti versione 3 e successive." ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros will no longer be allowed. Letters will no longer be allowed either. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "La versione dovrebbe essere una stringa composta da 1 a 4 numeri separati da punti. Ogni numero deve contenere un massimo di 9 cifre e gli zeri iniziali non saranno più consentiti. Anche le lettere non saranno più consentite. Per ulteriori informazioni consultare: https://mzl.la/3h3mCRu (Documentazione MDN)." ],
      "The version string should be simplified.":[ "La stringa della versione deve essere semplificata." ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros are not allowed. Letters are no longer allowed. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "La versione dovrebbe essere una stringa composta da 1 a 4 numeri separati da punti. Ogni numero deve contenere fino a 9 cifre e gli zeri iniziali non sono consentiti. Le lettere non sono più consentite. Per ulteriori informazioni, consultare https://mzl.la/3h3mCRu (Documentazione MDN)." ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported in manifest versions %(versionRange)s.":[ "%(fieldName)s: “%(permissionName)s” non è supportato nelle versioni del manifesto %(versionRange)s." ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported.":[ "%(fieldName)s: “%(permissionName)s” non è supportato." ] } } }