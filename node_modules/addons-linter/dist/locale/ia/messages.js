module.exports = { domain:"messages",
  locale_data:{ messages:{ "":{ domain:"messages",
        plural_forms:"nplurals=2; plural=(n != 1);",
        lang:"ia" },
      "Validation Summary:":[ "Summario de validation:" ],
      Code:[ "Codice" ],
      Message:[ "Message" ],
      Description:[ "Description" ],
      File:[ "File" ],
      Line:[ "Linea" ],
      Column:[ "Columna" ],
      "Invalid manifest version range requested: --min-manifest-version (currently set to %(minManifestVersion)s) should not be greater than --max-manifest-version (currently set to %(maxManifestVersion)s).":[ "Campo de version requirite del manifesto non valide: --min-manifest-version(actualmente con le valor %(minManifestVersion)s) non pote esser major que ---max-manifest-version (actualmente con le valor %(maxManifestVersion)s)." ],
      "Your FTL is not valid.":[ "Tu FTL non es valide." ],
      "Your FTL file could not be parsed.":[ "Tu file FTL non pote ser analysate." ],
      "Remote scripts are not allowed as per the Add-on Policies.":[ "Le scripts remote non es permittite per le politicas concernente le additivos." ],
      "Please include all scripts in the add-on. For more information, refer to https://mzl.la/2uEOkYp.":[ "Include tote le scripts in le additivo. Pro altere informationes, referer se a https://mzl.la/2uEOkYp." ],
      "Inline scripts blocked by default":[ "Le scripts inline es blocate per predefinition" ],
      "Default CSP rules prevent inline JavaScript from running (https://mzl.la/2pn32nd).":[ "Le regulas CSP predefinite impedi al JavaScript in linea de exequer (https://mzl.la/2pn32nd)." ],
      "Unadvised 3rd-party JS library":[ "Bibliotheca JS de tertios disconsiliate" ],
      "Your add-on uses a JavaScript library we do not recommend. Read more: https://bit.ly/1TRIyZY":[ "Tu additivo usa un bibliotheca JavaScript que nos non recommenda. Lege plus: https://bit.ly/1TRIyZY" ],
      "Known JS library detected":[ "Revelate bibliotheca JS note" ],
      "JavaScript libraries are discouraged for simple add-ons, but are generally accepted.":[ "Le bibliothecas JavaScript es discoragiate pro simple additivos, ma es generalmente acceptate." ],
      "Due to both security and performance concerns, this may not be set using dynamic values which have not been adequately sanitized. This can lead to security issues or fairly serious performance degradation.":[ "Per preoccupationes ambe de securitate e prestation, isto pote non esser predefinite pro valores dynamic que non ha essite adequatemente mundate. Isto pote ducer a problemas de securitate o bastante serie degradation de prestation." ],
      "{{api}} is not supported":[ "{{api}} non es supportate" ],
      "This API has not been implemented by Firefox.":[ "Iste API non ha essite implementate per Firefox." ],
      "\"{{api}}\" has been removed in Manifest Version 3 (`manifest_version` property)":[ "\"{{api}}\" ha essite removite in File manifesto Version 3 (`manifest_version` property)" ],
      "{{api}} is deprecated":[ "{{api}} es obsolete" ],
      "This API has been deprecated by Firefox.":[ "Iste API ha essite obsolete per Firefox." ],
      "Content script file could not be found.":[ "Impossibile trovar le file script de contento." ],
      "\"{{api}}\" is deprecated or unimplemented":[ "\"{{api}}\" es obsolete o non  implementate" ],
      "Content script file could not be found":[ "Impossibile trovar le file script de contento" ],
      "\"%(api)s\" can cause issues when loaded temporarily":[ "\"%(api)s\" pote causar problemas quando cargate temporarimente" ],
      "This API can cause issues when loaded temporarily using about:debugging in Firefox unless you specify \"browser_specific_settings.gecko.id\" in the manifest. Please see: https://mzl.la/2hizK4a for more.":[ "Iste API pote causar problemas quando cargate temporarimente per re:depuration in Firefox si tu non specifica \"browser_specific_settings.gecko.id\" in le manifesto. Vide: https://mzl.la/2hizK4a pro altero." ],
      "{{api}} is not supported in Firefox version {{minVersion}}":[ "{{api}} non es supportate in le version {{minVersion}} de Firefox" ],
      "This API is not implemented by the given minimum Firefox version":[ "Iste API non ha essite implementate per version minime de Firefox indicate" ],
      "{{api}} is not supported in Firefox for Android version {{minVersion}}":[ "{{api}} non es supportate in le version {{minVersion}} de Firefox pro Android" ],
      "This API is not implemented by the given minimum Firefox for Android version":[ "Iste API non ha essite implementate per version minime de Firefox pro Android indicate" ],
      "Content script file name should not be empty.":[ "Le nomine de file script de contento non debe ser vacue." ],
      "Content script file name should not be empty":[ "Le nomine de file script de contento non debe ser vacue" ],
      "\"%(method)s\" called with a non-literal uri":[ "\"%(method)s\" vocate con un uri non-literal" ],
      "Calling \"%(method)s\" with variable parameters can result in potential security vulnerabilities if the variable contains a remote URI. Consider using 'window.open' with the 'chrome=no' flag.":[ "Vocar \"%(method)s\" con parametros variabile pote comportar potential vulnerabilitates de securitate si le variabile contine un URI remote. Considera le uso de 'window.open' con le bandiera 'chrome=no'." ],
      "\"%(method)s\" called with non-local URI":[ "\"%(method)s\" vocate con URI non-local" ],
      "Calling \"%(method)s\" with a non-local URI will result in the dialog being opened with chrome privileges.":[ "Vocar \"%(method)s\" con un URI non-local comportara que le fenestra de dialogo essera aperite con privilegios de chrome." ],
      "JavaScript syntax error":[ "Error de syntaxe de JavaScript" ],
      "There is a JavaScript syntax error in your code, which might be related to some experimental JavaScript features that aren't an official part of the language specification and therefore not supported yet. The validation cannot continue on this file.":[ "Il ha un error de syntaxe JavaScript in tu codice, que poterea esser correlate a alcun functiones JavaScript experimental que non es un parte official del specification del lingua e dunque non ancora supportate. Le validation non pote continuar sur iste file." ],
      "Evaluation of strings as code can lead to security vulnerabilities and performance issues, even in the most innocuous of circumstances. Please avoid using `eval` and the `Function` constructor when at all possible.":[ "Le evalutation de catenas como codice pote ducer a vulnerabilitates de securitate e problemas de prestation, mesmo in le plus innocue de circumstantias. Evita de usar `eval` e le `Function` constructor quando possibile." ],
      "setTimeout, setInterval and execScript functions should be called only with function expressions as their first argument":[ "Le functiones setTimeout, setInterval e execScript debe esser vocate solo con expressiones de function como lor prime argumento." ],
      "Unexpected global passed as an argument":[ "Variabile global non expectate passate como argumento" ],
      "Passing a global as an argument is not recommended. Please make this a var instead.":[ "Passar un global como argumento non es recommendate. In vice rende isto un var." ],
      "Use of document.write strongly discouraged.":[ "Le uso de document.write es fortemente discoragiate." ],
      "document.write will fail in many circumstances when used in extensions, and has potentially severe security repercussions when used improperly. Therefore, it should not be used.":[ "document.write fallera in multe circumstantias quando usate in extensiones, e ha potentialmente sever repercussiones de securitate quando usate incorrectemente. Dunque, illo non pote esser usate." ],
      "Banned 3rd-party JS library":[ "Bibliotheca JS de tertios prohibite" ],
      "Your add-on uses a JavaScript library we consider unsafe. Read more: https://bit.ly/1TRIyZY":[ "Tu additivo usa un bibliotheca JavaScript que nos no considera secur. Lege plus: https://bit.ly/1TRIyZY" ],
      "Your JSON contains block comments.":[ "Tu JSON contine commentos in bloco." ],
      "Only line comments (comments beginning with \"//\") are allowed in JSON files. Please remove block comments (comments beginning with \"/*\")":[ "Solo commentos linear (initiante con \"//\") es permittite in le files JSON. Remove le commentos in bloco (inter \"/*\" e \"*/\")" ],
      "Duplicate keys are not allowed in JSON files.":[ "Le claves duplicate non es permittite in le files JSON." ],
      "Duplicate key found in JSON file.":[ "Clave duplicate trovate in file JSON." ],
      "Your JSON is not valid.":[ "Tu JSON non es valide." ],
      "Your JSON file could not be parsed.":[ "Tu file JSON non pote ser analysate." ],
      "Reserved filename found.":[ "Nomine de file reservate trovate." ],
      "Files whose names are reserved have been found in the add-on. Please refrain from using them and rename your files.":[ "Files cuje nomines es reservate ha essite trovate in le additivo. Abstine te de usar los e renomina tu files." ],
      "The package is invalid. It may contain entries using invalid characters, as an example using '\\' as a path separator is not allowed in Firefox. Try to recreate your add-on package (ZIP) and make sure all entries are using '/' as the path separator.":[ "Le pacchetto non es valide. Illo pote continer entratas que usa characteres non valide, como exemplo '\\' como separator de route, que non es permittite in Firefox. Tenta de recrear tu pacchetto de additivo (ZIP) e verifica que tote le entratas usa '/' como separator de route." ],
      "We were unable to decompress the zip file.":[ "Nos non poteva decomprimer le file zip." ],
      "manifest.json was not found":[ "manifest.json non esseva trovate" ],
      "No manifest.json was found at the root of the extension. The package file must be a ZIP of the extension's files themselves, not of the containing directory. See: https://mzl.la/2r2McKv for more on packaging.":[ "Nulle manifest.json ha essite trovate al radice del extension. Le file pacchetto debe esser un ZIP del mesme files del extension, non del directorio que los contine. Vide: https://mzl.la/2r2McKv pro altero re le pacchettos." ],
      "File is too large to parse.":[ "Le file es troppo grande pro esser analysate." ],
      "This file is not binary and is too large to parse. Files larger than %(maxFileSizeToParseMB)sMB will not be parsed. Consider moving large lists of data out of JavaScript files and into JSON files, or splitting very large files into smaller ones.":[ "Iste file non es binari e es troppo grande pro interpretar. Le files plus grande que %(maxFileSizeToParseMB)sMB non sera tractate. Considera de mover grande lista de datos foras de files JavaScript e in files JSON, o scinder le files multo grande in plus micre." ],
      "Hidden file flagged":[ "File celate signalate" ],
      "Hidden files complicate the review process and can contain sensitive information about the system that generated the add-on. Please modify the packaging process so that these files aren't included.":[ "Le files occulte complica le processo de revision e pote continer datos sensibile re le systema que genera le additivo. Modifica le processo de impaccamento assi que iste files non es includite." ],
      "Package contains duplicate entries":[ "Le pacchetto contine entratas duplicate" ],
      "Flagged filename found":[ "Nomine de file signalate trovate" ],
      "Files were found that are either unnecessary or have been included unintentionally. They should be removed.":[ "Files ha essite trovate que es inutile, o ha essite includite involuntarimente. Illos deberea esser removite." ],
      "The package contains multiple entries with the same name. This practice has been banned. Try unzipping and re-zipping your add-on package and try again.":[ "Le pacchetto contine plure entratas con le mesme nomine. Iste practica ha essite bannite. Tenta de-zippar e re-zippar le pacchetto de tu additivo e retenta." ],
      "Flagged file extensions found":[ "Extensiones de file signalate trovate" ],
      "Flagged file type found":[ "Typo de file signalate trovate" ],
      "Files whose names end with flagged extensions have been found in the add-on. The extension of these files are flagged because they usually identify binary components. Please see https://bit.ly/review-policy for more information on the binary content review process.":[ "Files cuje nomines fini con extensiones signalate ha essite trovate in le additivo. Le extension de iste files es signalate perque illos usualmente identifica componentes binari. Vide https://bit.ly/review-policy pro altere informationes sur le processo de revision de contentos binari." ],
      "Package already signed":[ "Pacchetto ja signate" ],
      "Add-ons which are already signed will be re-signed when published on AMO. This will replace any existing signatures on the add-on.":[ "Le additivos jam signate sera re-signate quando publicate sur AMO. Isto reimplaciara ulle existente firmas sur le additivo." ],
      "Firefox add-ons are not allowed to run coin miners.":[ "Le additivos de Firefox non es autorisate a minar cryptomonetas." ],
      "We do not allow coinminer scripts to be run inside WebExtensions. See https://github.com/mozilla/addons-linter/issues/1643 for more details.":[ "Nos non permitte de facer functionar scripts excavatores de moneta intra extensiones web. Vider https://github.com/mozilla/addons-linter/issues/1643 pro altere detalios." ],
      "String name is reserved for a predefined message":[ "Nomine de catena reservate a un message predefinite" ],
      "String names starting with @@ get translated to built-in constants (https://mzl.la/2BL9ZjE).":[ "Le nomines de stringas comenciante con @@ es translatate a constantes integrate (https://mzl.la/2BL9ZjE)." ],
      "String name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2Eztyi5).":[ "Le nomine de stringa debe solo continer characteres alpha-numeric, _ e @ (https://mzl.la/2Eztyi5)." ],
      "Placeholder for message is missing":[ "Manca un spatio reservate del message" ],
      "A placeholder used in the message is not defined.":[ "Un spatio reservate usate in le message non es definite." ],
      "Placeholder name contains invalid characters":[ "Le nomine del spatio reservate contine characteres non valide" ],
      "Placeholder name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2ExbYez).":[ "Le nomine de marca-loco debe continer solo characteres alpha-numeric, _ e @ (https://mzl.la/2ExbYez)." ],
      "Placeholder is missing the content property":[ "Le spatio reservate non ha le proprietate de contento" ],
      "A placeholder needs a content property defining the replacement of it (https://mzl.la/2DT1MQd)":[ "Un marca-loco besonia de un proprietate de contento definiente su reimplaciamento (https://mzl.la/2DT1MQd)" ],
      "Translation string is missing the message property":[ "Catena de traduction mancante del proprietates del message" ],
      "No \"message\" message property is set for a string (https://mzl.la/2DSBTjA).":[ "Nulle proprietate de message \"message\" es definite pro un catena (https://mzl.la/2DSBTjA)." ],
      "The field is required.":[ "Le campo es obligatori." ],
      "See https://mzl.la/1ZOhoEN (MDN Docs) for more information.":[ "Vide https://mzl.la/1ZOhoEN (MDN Docs) pro plus de informationes." ],
      "The permission type is unsupported.":[ "Iste typo de permission non es supportate." ],
      "See https://mzl.la/1R1n1t0 (MDN Docs) for more information.":[ "Vide https://mzl.la/1R1n1t0 (MDN Docs) pro altere informationes." ],
      "See https://mzl.la/2Qn0fWC (MDN Docs) for more information.":[ "Vide https://mzl.la/2Qn0fWC (MDN Docs) pro altere informationes." ],
      "See https://mzl.la/3Woeqv4 (MDN Docs) for more information.":[ "Vide https://mzl.la/3Woeqv4 (MDN Docs) pro altere informationes." ],
      "Unknown permission.":[ "Permission incognite." ],
      "%(instancePath)s: the following privileged permissions are only allowed in privileged extensions: %(privilegedPermissions)s.":[ "%(instancePath)s: le sequente permissos privilegiate es solo permittite in le extensiones privilegiate: %(privilegedPermissions)s." ],
      "Invalid host permission.":[ "Permission de servitor invalide." ],
      "Invalid install origin.":[ "Fonte de installation invalide." ],
      "Invalid install origin. A valid origin has - only - a scheme, hostname and optional port. See https://mzl.la/3TEbqbE (MDN Docs) for more information.":[ "Origine de installation non valide. Un origine valide ha solo: schema, nomine de hospite e porta optional. Vide https://mzl.la/3TEbqbE (MDN Docs) pro altere informationes." ],
      "The field is invalid.":[ "Le campo es invalide." ],
      "\"manifest_version\" in the manifest.json is not a valid value":[ "\"manifest_version\" in le file manifest.json non es un valor valide" ],
      "See https://mzl.la/20PenXl (MDN Docs) for more information.":[ "Vide https://mzl.la/20PenXl (MDN Docs) pro altere informationes." ],
      "\"%(property)s\" allows remote code execution in manifest.json":[ "\"%(property)s\" permitte le execution de codice remote in manifest.json" ],
      "A custom \"%(property)s\" needs additional review.":[ "Un \"%(property)s\" personal besonia de un altere revista." ],
      "\"%(property)s\" allows 'eval', which has strong security and performance implications.":[ "\"%(property)s\" permitte 'eval', que ha forte implicationes pro securitate e prestationes." ],
      "In most cases the same result can be achieved differently, therefore it is generally prohibited":[ "In le major parte del casos le mesme resultato pote esser attingite differentemente, dunque illo es generalmente prohibite" ],
      "The \"name\" property must be a string with no leading/trailing whitespaces.":[ "La proprietate \"name\" debe esser un stringa sin spatios vacue initial o final." ],
      "See http://mzl.la/1STmr48 (MDN Docs) for more information.":[ "Vider http://mzl.la/1STmr48 (MDN Docs) pro altere informationes." ],
      "\"update_url\" is not allowed.":[ "\"update_url\" non es permittite." ],
      "\"applications.gecko.update_url\" or \"browser_specific_settings.gecko.update_url\" are not allowed for Mozilla-hosted add-ons.":[ "\"applications.gecko.update_url\" o \"browser_specific_settings.gecko.update_url\" non es permittite al additivos hospite de Mozilla." ],
      "The \"update_url\" property is not used by Firefox.":[ "Le proprietate \"update_url\" non es usate per Firefox." ],
      "The \"update_url\" is not used by Firefox in the root of a manifest; your add-on will be updated via the Add-ons site and not your \"update_url\". See: https://mzl.la/25zqk4O":[ "Le \"update_url\" non es usate per Firefox in le radice de un manifesto; tu additivo sera actualisate via le sito Additivos e non via tu \"update_url\". Vider: https://mzl.la/25zqk4O" ],
      "\"strict_max_version\" not required.":[ "\"strict_max_version\" non obligatori." ],
      "\"strict_max_version\" shouldn't be used unless the add-on is expected not to work with future versions of Firefox.":[ "\"strict_max_version\" non debe esser usate salvo que le additivo es expectate non laborar con le versiones futur de Firefox." ],
      "Manifest Version 3 is not fully supported on Firefox for Android.":[ "Le version 3 de Manifest non es totalmente supportate par Firefox pro Android." ],
      "No \"%(property)s\" property found in manifest.json":[ "Nulle proprietate \"%(property)s\" trovate in manifest.json" ],
      "\"%(property)s\" is required":[ "\"%(property)s\" es obligatori" ],
      "An icon defined in the manifest could not be found in the package.":[ "Un icone definite in le file manifesto non pote ser trovate in le pacchetto." ],
      "Icon could not be found at \"%(path)s\".":[ "Icone non pote ser trovate al \"%(path)s\"." ],
      "A background script defined in the manifest could not be found.":[ "Un script de fundo definite in le file manifesto non pote ser trovate." ],
      "A background page defined in the manifest could not be found.":[ "Un pagina de fundo definite in le file manifesto non pote ser trovate." ],
      "Background script could not be found at \"%(path)s\".":[ "Le script de fundo non pote ser trovate al \"%(path)s\"." ],
      "Background page could not be found at \"%(path)s\".":[ "Le pagina de fundo non pote ser trovate al \"%(path)s\"." ],
      "A content script defined in the manifest could not be found.":[ "Un script de contento definite in le file manifesto non pote ser trovate." ],
      "A content script css file defined in the manifest could not be found.":[ "Le file css script de contento definite in le file manifesto non pote ser trovate." ],
      "Content script defined in the manifest could not be found at \"%(path)s\".":[ "Le script de contento definite in le file manifesto non pote ser trovate al \"%(path)s\"." ],
      "Content script css file defined in the manifest could not be found at \"%(path)s\".":[ "Le file css script de contento definite in le file manifesto non pote ser trovate al \"%(path)s\"." ],
      "A dictionary file defined in the manifest could not be found.":[ "Un file dictionario definite in le file manifesto non pote ser trovate." ],
      "Dictionary file defined in the manifest could not be found at \"%(path)s\".":[ "Le file dictionario definite in le file manifesto non pote ser trovate in \"%(path)s\"." ],
      "The manifest contains multiple dictionaries.":[ "Le file manifesto contine plure dictionarios." ],
      "Multiple dictionaries were defined in the manifest, which is unsupported.":[ "In le file manifesto era definite plure dictionarios, ma isto non es supportate." ],
      "The manifest contains a dictionaries object, but it is empty.":[ "Le file manifesto contine un objecto dictionarios, ma illo es vacue." ],
      "A dictionaries object was defined in the manifest, but it was empty.":[ "Un objecto dictionarios ha essite definite in le file manifesto, ma illo era vacue." ],
      "The manifest contains a dictionary but no id property.":[ "Le manifesto contine un dictionario ma nulle proprietate id." ],
      "A dictionary was found in the manifest, but there was no id set.":[ "Un dictionario ha essite trovate in le manifesto, ma sin id definite." ],
      "Please refer to https://github.com/mozilla-extensions/xpi-manifest to learn more about privileged extensions and signing.":[ "Referer se a https://github.com/mozilla-extensions/xpi-manifest pro saper plus re extensiones privilegiate e signaturas." ],
      "Forbidden content found in add-on.":[ "Contento prohibite trovate in le additivo." ],
      "This add-on contains forbidden content.":[ "Iste additivo ha contento prohibite." ],
      "Icons must be square.":[ "Le icones debe ser quadrate." ],
      "Icon at \"%(path)s\" must be square.":[ "Le icone al \"%(path)s\" debe ser un quadrato." ],
      "The size of the icon does not match the manifest.":[ "Le dimension del icone non concorda con le manifesto." ],
      "Expected icon at \"%(path)s\" to be %(expected)d pixels wide but was %(actual)d.":[ "Le icone a \"%(path)s\" era expectate esser %(expected)d pixel large, ma era %(actual)d." ],
      "\"%(fieldName)s\" is ignored for non-privileged add-ons.":[ "\"%(fieldName)s\" es ignorate pro le additivos non-privilegiate." ],
      "Corrupt image file":[ "File imagine corrupte" ],
      "Expected icon file at \"%(path)s\" is corrupted":[ "Le file icone expectate al \"%(path)s\" es corrupte" ],
      "This property has been deprecated.":[ "Iste proprietate ha essite obsolete." ],
      "This theme LWT alias has been removed in Firefox 70.":[ "Iste thema LWT alias ha essite eliminate in Firefox 70." ],
      "See https://mzl.la/2T11Lkc (MDN Docs) for more information.":[ "Vide https://mzl.la/2T11Lkc (MDN Docs) pro saper plus." ],
      "Theme image for \"%(type)s\" could not be found at \"%(path)s\"":[ "Le imagine de thema pro \"%(type)s\" non pote ser trovate al \"%(path)s\"" ],
      "\"%(fieldName)s\" manifest field is only used for privileged and temporarily installed extensions.":[ "Le campo manifesto \"%(fieldName)s\" es solo usate pro extensiones privilegiate e temporarimente installate." ],
      "Corrupted theme image file":[ "File imagine de thema corrupte" ],
      "Theme image file at \"%(path)s\" is corrupted":[ "File imagine de thema corrupte a \"%(path)s\" es corrupte" ],
      "Theme image file has an unsupported file extension":[ "Le file imagine del thema ha un extension non supportate" ],
      "Theme image file at \"%(path)s\" has an unsupported file extension":[ "Le file imagine del thema a \"%(path)s\" ha un extension non supportate" ],
      "Theme image file has an unsupported mime type":[ "Le file imagine del thema ha un typo de mime non supportate" ],
      "Theme image file at \"%(path)s\" has the unsupported mime type \"%(mime)s\"":[ "Le file imagine del thema a \"%(path)s\" ha le typo de mime non tractate \"%(mime)s\"" ],
      "Theme image file mime type does not match its file extension":[ "Le typo de mime del file imagine del thema non concorda su extension" ],
      "Theme image file extension at \"%(path)s\" does not match its actual mime type \"%(mime)s\"":[ "Le extension del file imagine del thema a \"%(path)s\" non concorda su actual typo de mime \"%(mime)s\"" ],
      "The \"default_locale\" is missing localizations.":[ "Le \"default_locale\" manca de localisationes." ],
      "The \"default_locale\" value is specified in the manifest, but no matching \"messages.json\" in the \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "Le valor \"default_locale\" es specificate in le manifesto, ma nulle \"messages.json\" concordante existe in le directorio \"_localisationes\". Vider: https://mzl.la/2hjcaEE" ],
      "The \"default_locale\" is missing but \"_locales\" exist.":[ "Le \"default_locale\" manca, ma \"_locales\" existe." ],
      "The \"default_locale\" value is not specifed in the manifest, but a \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "Le valor \"default_locale\" non es specificate in le manifesto, ma existe un directorio \"_locales\". Vider: https://mzl.la/2hjcaEE" ],
      "Unsupported image extension":[ "Extension de imagine non supportate" ],
      "Icons should be one of JPG/JPEG, WebP, GIF, PNG or SVG.":[ "Icones debe ser uno de JPG/JPEG, WebP, GIF, PNG o SVG." ],
      "\"applications\" property overridden by \"browser_specific_settings\" property":[ "proprietate \"applicationes\" supplantate per le proprietate \"browser_specific_settings\"" ],
      "The \"applications\" property is being ignored because it is superseded by the \"browser_specific_settings\" property which is also defined in your manifest. Consider removing applications.":[ "Le proprietate \"applications\" ha essite ignorate perque illo es supplantate per le proprietate \"browser_specific_settings\" que es alsi definite in tu manifesto. Considera le remotion del applicationes." ],
      "Empty language directory":[ "Directorio de linguas vacue" ],
      "messages.json file missing in \"%(path)s\"":[ "File messages.json mancante in \"%(path)s\"" ],
      "Manifest key not supported by the specified minimum Firefox version":[ "Clave de file manifesto non supportate per le minime version de Firefox specificate" ],
      "\"strict_min_version\" requires Firefox %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "\"strict_min_version\" require Firefox %(minVersion)s, que era publicate ante que le version %(versionAdded)s introduceva le assistentia al \"%(key)s\"." ],
      "\"%(fieldName)s\" is not supported in manifest versions %(versionRange)s.":[ "\"%(fieldName)s\" non es supportate in le versiones del manifesto %(versionRange)s." ],
      "Permission not supported by the specified minimum Firefox version":[ "Permission non supportate per le version minime de Firefox specificate" ],
      "\"%(fieldName)s\" is not supported.":[ "\"%(fieldName)s\" non es supportate." ],
      "Manifest key not supported by the specified minimum Firefox for Android version":[ "Clave del file manifesto non supportate per le minime version specificate de Firefox pro Android" ],
      "\"strict_min_version\" requires Firefox for Android %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "\"strict_min_version\" require Firefox pro Android %(minVersion)s, que era publicate ante que le version %(versionAdded)s introduceva le assistentia al \"%(key)s\"." ],
      "Permission not supported by the specified minimum Firefox for Android version":[ "Permission non supportate per le version minime specificate de Firefox pro Android" ],
      "Linking to \"addons.mozilla.org\" is not allowed":[ "Le ligamine a \"addons.mozilla.org\" non es permittite" ],
      "Links directing to \"addons.mozilla.org\" are not allowed to be used for homepage":[ "Il non es permittite usar ligamines dirigente a \"addons.mozilla.org\" pro pagina principal" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above":[ "Le permisso \"%(permission)s\" require \"strict_min_version\" predefinite a \"%(minFirefoxVersion)s\" o superior" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above. Please update your manifest.json version to specify a minimum Firefox version.":[ "Le permisso \"%(permission)s\" require \"strict_min_version\" predefinite a \"%(minFirefoxVersion)s\" o superior. Actualisa tu version de manifest.json pro specificar un version de Firefox minime." ],
      "The extension ID is required in Manifest Version 3 and above.":[ "Le ID de extension es necesse in le version 3 e sequente de Manifest." ],
      "See https://mzl.la/3PLZYdo for more information.":[ "Vide https://mzl.la/3PLZYdo pro saper plus." ],
      "%(instancePath)s: Privileged extensions should declare privileged permissions.":[ "%(instancePath)s: extensiones privilegiate debe declarar permissos privilegiate." ],
      "This extension does not declare any privileged permission. It does not need to be signed with the privileged certificate. Please upload it directly to https://addons.mozilla.org/.":[ "Iste extension non declara ulle permisso privilegiate. Illo non besonia de esser firmate con le certificato privilegiate. Carga lo directemente a https://addons.mozilla.org/." ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for privileged extensions.":[ "%(instancePath)s: Le permisso de \"mozillaAddons\" es obligatori pro extensiones privilegiate." ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for extensions that include privileged manifest fields.":[ "%(instancePath)s: le permisso de \"mozillaAddons\" es obligatori pro extensiones que include campos del manifesto privilegiate." ],
      "%(instancePath)s: privileged manifest fields are only allowed in privileged extensions.":[ "%(instancePath)s: le campos del manifesto privilegiate es solo permittite in le extensiones privilegiate." ],
      "This extension does not include the \"mozillaAddons\" permission, which is required for privileged extensions.":[ "Iste extension non include le permisso de \"mozillaAddons\", que es necessari al extensiones privilegiate." ],
      "Cannot use actions in hidden add-ons.":[ "Impossibile usar actiones in additivos celate." ],
      "The hidden and browser_action/page_action (or action in Manifest Version 3 and above) properties are mutually exclusive.":[ "Le proprietates celate e browser_action/page_action (o action in le version 3 del Manifesto e superior) es mutualmente exclusive." ],
      "Use \"browser_specific_settings\" instead of \"applications\".":[ "Usa \"browser_specific_settings\" in vice de \"applications\"." ],
      "The \"applications\" property in the manifest is deprecated and will no longer be accepted in Manifest Version 3 and above.":[ "Le proprietate \"applications\" in le manifesto es obsolete e non sera plus acceptate in le version 3 del Manifesto e superior." ],
      "\"applications\" is no longer allowed in Manifest Version 3 and above.":[ "\"applications\" non es plus permittite in le version 3 del Manifesto e superior." ],
      "The \"applications\" property in the manifest is no longer allowed in Manifest Version 3 and above. Use \"browser_specific_settings\" instead.":[ "Le proprietate \"applications\" in le manifesto non es plus permittite in le version 3 del Manifesto e superior. Usar \"browser_specific_settings\" in vice." ],
      "The version string should be simplified because it won't be compatible with Manifest Version 3 and above.":[ "Le stringa de version debe esser simplificate perque illo non essera compatibile con version 3 del Manifesto e superior." ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros will no longer be allowed. Letters will no longer be allowed either. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "Le version debe esser un stringa con 1 - 4 numeros separate con punctos. Cata numero debe haber usque 9 digitos, e le zeros initial non sera plus permittite. Alsi le litteras non sera plus permittite. Vider https://mzl.la/3h3mCRu (Documentation MDN) pro altere informationes." ],
      "The version string should be simplified.":[ "Le catena de version debe esser simplificate." ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros are not allowed. Letters are no longer allowed. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "Le version debe esser un stringa con 1 - 4 numeros separate con punctos. Cata numero debe haber usque 9 digitos, e le zeros initial non es permittite. Le litteras non es plus permittite. Vider https://mzl.la/3h3mCRu (Documentation MDN) pro altere informationes." ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported in manifest versions %(versionRange)s.":[ "/%(fieldName)s: \"%(permissionName)s\" non es supportate in le versiones del manifesto %(versionRange)s." ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported.":[ "/%(fieldName)s: \"%(permissionName)s\" non es supportate." ] } } }