module.exports = { domain:"messages",
  locale_data:{ messages:{ "":{ domain:"messages",
        plural_forms:"nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);",
        lang:"dsb" },
      "Validation Summary:":[ "Pśeglědowańske zespominanje:" ],
      Code:[ "Kod" ],
      Message:[ "Powěsć" ],
      Description:[ "Wopisanje" ],
      File:[ "Dataja" ],
      Line:[ "Smužka" ],
      Column:[ "Słup" ],
      "Invalid manifest version range requested: --min-manifest-version (currently set to %(minManifestVersion)s) should not be greater than --max-manifest-version (currently set to %(maxManifestVersion)s).":[ "" ],
      "Your FTL is not valid.":[ "Waš FTL njejo płaśiwy." ],
      "Your FTL file could not be parsed.":[ "Waša FTL-dataja njejo dała se parsowaś." ],
      "Remote scripts are not allowed as per the Add-on Policies.":[ "Daloke skripty njejsu dowólone pó pšawidłach dodankow." ],
      "Please include all scripts in the add-on. For more information, refer to https://mzl.la/2uEOkYp.":[ "Pšosym zapśimjejśo wšykne skripty do dodanka. Za dalšne informacije glejśo https://mzl.la/2uEOkYp." ],
      "Inline scripts blocked by default":[ "Skripty inline se pó standarźe blokěruju" ],
      "Default CSP rules prevent inline JavaScript from running (https://mzl.la/2pn32nd).":[ "" ],
      "Unadvised 3rd-party JS library":[ "Njeznata JS-biblioteka tśeśego póbitowarja" ],
      "Your add-on uses a JavaScript library we do not recommend. Read more: https://bit.ly/1TRIyZY":[ "Waš dodank biblioteku JavaScript wužywa, kótaruž njepórucujomy. Glejśo wěcej: https://bit.ly/1TRIyZY" ],
      "Known JS library detected":[ "Znata JS-biblioteka namakana" ],
      "JavaScript libraries are discouraged for simple add-ons, but are generally accepted.":[ "" ],
      "Due to both security and performance concerns, this may not be set using dynamic values which have not been adequately sanitized. This can lead to security issues or fairly serious performance degradation.":[ "" ],
      "{{api}} is not supported":[ "{{api}} se njepódpěra" ],
      "This API has not been implemented by Firefox.":[ "Toś ten API njejo se implementěrował pśez Firefox." ],
      "\"{{api}}\" has been removed in Manifest Version 3 (`manifest_version` property)":[ "„{{api}}“ jo se wótwónoźeł we wersiji 3 manifest (kakosć `manifest_version`)" ],
      "{{api}} is deprecated":[ "{{api}} se wěcej njepódpěra" ],
      "This API has been deprecated by Firefox.":[ "Toś ten API se pśez Firefox wěcej njepódpěra." ],
      "Content script file could not be found.":[ "Wopśimjeśowa skriptowa dataja njejo se namakała." ],
      "\"{{api}}\" is deprecated or unimplemented":[ "„{{api}}“ se wěcej njepódpěra abo jo njeimplementěrowany" ],
      "Content script file could not be found":[ "Wopśimjeśowa skriptowa dataja njejo se namakała" ],
      "\"%(api)s\" can cause issues when loaded temporarily":[ "„%(api)s“ móžo problemy zawinowaś, gaž se nachylu zacytajo" ],
      "This API can cause issues when loaded temporarily using about:debugging in Firefox unless you specify \"browser_specific_settings.gecko.id\" in the manifest. Please see: https://mzl.la/2hizK4a for more.":[ "" ],
      "{{api}} is not supported in Firefox version {{minVersion}}":[ "{{api}} se we wersiji {{minVersion}} Firefox njepódpěra" ],
      "This API is not implemented by the given minimum Firefox version":[ "Toś ten API njejo se implementěrował pśez pódanu minimalnu wersiju Firefox" ],
      "{{api}} is not supported in Firefox for Android version {{minVersion}}":[ "{{api}} se we wersiji {{minVersion}} Firefox za Android njepódpěra" ],
      "This API is not implemented by the given minimum Firefox for Android version":[ "Toś ten API njejo se implementěrował pśez pódanu minimalnu wersiju Firefox za Android" ],
      "Content script file name should not be empty.":[ "Mě wopśimjeśoweje skriptoweje dataje njeměło prozne byś." ],
      "Content script file name should not be empty":[ "Mě wopśimjeśoweje skriptoweje dataje njeměło prozne byś" ],
      "\"%(method)s\" called with a non-literal uri":[ "" ],
      "Calling \"%(method)s\" with variable parameters can result in potential security vulnerabilities if the variable contains a remote URI. Consider using 'window.open' with the 'chrome=no' flag.":[ "" ],
      "\"%(method)s\" called with non-local URI":[ "" ],
      "Calling \"%(method)s\" with a non-local URI will result in the dialog being opened with chrome privileges.":[ "" ],
      "JavaScript syntax error":[ "Syntaksowa zmólka JavaScript" ],
      "There is a JavaScript syntax error in your code, which might be related to some experimental JavaScript features that aren't an official part of the language specification and therefore not supported yet. The validation cannot continue on this file.":[ "" ],
      "Evaluation of strings as code can lead to security vulnerabilities and performance issues, even in the most innocuous of circumstances. Please avoid using `eval` and the `Function` constructor when at all possible.":[ "" ],
      "setTimeout, setInterval and execScript functions should be called only with function expressions as their first argument":[ "" ],
      "Unexpected global passed as an argument":[ "Njewócakana globala jo se ako argument pśepódała" ],
      "Passing a global as an argument is not recommended. Please make this a var instead.":[ "" ],
      "Use of document.write strongly discouraged.":[ "Wót wužywanja document.write se wuraznje wótraźujo." ],
      "document.write will fail in many circumstances when used in extensions, and has potentially severe security repercussions when used improperly. Therefore, it should not be used.":[ "" ],
      "Banned 3rd-party JS library":[ "JS-biblioteka tśeśego póbitowarja zakazana" ],
      "Your add-on uses a JavaScript library we consider unsafe. Read more: https://bit.ly/1TRIyZY":[ "Waš dodank biblioteku JavaScript wužywa, kótaruž za njewěstu mamy. Glejśo wěcej: https://bit.ly/1TRIyZY" ],
      "Your JSON contains block comments.":[ "Waš JSON blokowe komentary wopśiámujo." ],
      "Only line comments (comments beginning with \"//\") are allowed in JSON files. Please remove block comments (comments beginning with \"/*\")":[ "" ],
      "Duplicate keys are not allowed in JSON files.":[ "Dwójne kluce njejsu dowólone w JSON-datajach." ],
      "Duplicate key found in JSON file.":[ "Dwójny kluc jo se namakał w dataji JSON." ],
      "Your JSON is not valid.":[ "Waš JSON njejo płaśiwy." ],
      "Your JSON file could not be parsed.":[ "Waša JSON-dataja njejo se dała parsowaś." ],
      "Reserved filename found.":[ "Rezerwěrowane datajowe mě namakane." ],
      "Files whose names are reserved have been found in the add-on. Please refrain from using them and rename your files.":[ "" ],
      "The package is invalid. It may contain entries using invalid characters, as an example using '\\' as a path separator is not allowed in Firefox. Try to recreate your add-on package (ZIP) and make sure all entries are using '/' as the path separator.":[ "" ],
      "We were unable to decompress the zip file.":[ "Njejsmy mógli zip-dataju rozpakowaś." ],
      "manifest.json was not found":[ "manifest.json njejo se namakał" ],
      "No manifest.json was found at the root of the extension. The package file must be a ZIP of the extension's files themselves, not of the containing directory. See: https://mzl.la/2r2McKv for more on packaging.":[ "" ],
      "File is too large to parse.":[ "Dataja jo pśewjelika za parsowanje." ],
      "This file is not binary and is too large to parse. Files larger than %(maxFileSizeToParseMB)sMB will not be parsed. Consider moving large lists of data out of JavaScript files and into JSON files, or splitting very large files into smaller ones.":[ "" ],
      "Hidden file flagged":[ "Schowana dataja wóznamjenjona" ],
      "Hidden files complicate the review process and can contain sensitive information about the system that generated the add-on. Please modify the packaging process so that these files aren't included.":[ "" ],
      "Package contains duplicate entries":[ "Pakśik dwójne zapiski wopśimujo" ],
      "Flagged filename found":[ "Wóznamjenjone datajowe mě namakane" ],
      "Files were found that are either unnecessary or have been included unintentionally. They should be removed.":[ "" ],
      "The package contains multiple entries with the same name. This practice has been banned. Try unzipping and re-zipping your add-on package and try again.":[ "" ],
      "Flagged file extensions found":[ "Woznamjenjone datajowe kóńcowki namakane" ],
      "Flagged file type found":[ "Wóznamjenjony datajowy typ namakany" ],
      "Files whose names end with flagged extensions have been found in the add-on. The extension of these files are flagged because they usually identify binary components. Please see https://bit.ly/review-policy for more information on the binary content review process.":[ "" ],
      "Package already signed":[ "Pakśik jo južo signěrowany" ],
      "Add-ons which are already signed will be re-signed when published on AMO. This will replace any existing signatures on the add-on.":[ "" ],
      "Firefox add-ons are not allowed to run coin miners.":[ "Dodanki Firefox njesměju pjenjezekopaki wuwjasć." ],
      "We do not allow coinminer scripts to be run inside WebExtensions. See https://github.com/mozilla/addons-linter/issues/1643 for more details.":[ "" ],
      "String name is reserved for a predefined message":[ "Mě znamuškowego rjeśazka jo wuměnjone za pśeddefiněrowanu powěźeńku" ],
      "String names starting with @@ get translated to built-in constants (https://mzl.la/2BL9ZjE).":[ "" ],
      "String name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2Eztyi5).":[ "" ],
      "Placeholder for message is missing":[ "Zastupujucy symbol za powěźeńku felujo" ],
      "A placeholder used in the message is not defined.":[ "Zastupujucy symbol, kótaryž se w powěźeńce wužywa, njejo definěrowany." ],
      "Placeholder name contains invalid characters":[ "Mě zastupujucego symbola njepłaśiwe znamuška wopśimujo" ],
      "Placeholder name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2ExbYez).":[ "" ],
      "Placeholder is missing the content property":[ "Zastupujucemu symboloju kakosć content felujo" ],
      "A placeholder needs a content property defining the replacement of it (https://mzl.la/2DT1MQd)":[ "" ],
      "Translation string is missing the message property":[ "Pśełožowańskemu znamuškowemu rjeśazkoju kakosć message felujo" ],
      "No \"message\" message property is set for a string (https://mzl.la/2DSBTjA).":[ "Zdźěleńska kakosć \"message\" njejo za znamuškowy rjeśazk nastajona (https://mzl.la/2DSBTjA)." ],
      "The field is required.":[ "Pólo jo trěbne." ],
      "See https://mzl.la/1ZOhoEN (MDN Docs) for more information.":[ "Glejśo https://mzl.la/1ZOhoEN (MDN Docs) za dalšne informacije." ],
      "The permission type is unsupported.":[ "Typ dowólnosći se njepódpěra." ],
      "See https://mzl.la/1R1n1t0 (MDN Docs) for more information.":[ "Glejśo https://mzl.la/1R1n1t0 (MDN Docs) za dalšne informacije." ],
      "See https://mzl.la/2Qn0fWC (MDN Docs) for more information.":[ "Glejśo https://mzl.la/2Qn0fWC (MDN Docs) za dalšne informacije." ],
      "See https://mzl.la/3Woeqv4 (MDN Docs) for more information.":[ "Glejśo https://mzl.la/3Woeqv4 (MDN Docs) za dalšne informacije." ],
      "Unknown permission.":[ "Njeznata dowólnosć." ],
      "%(instancePath)s: the following privileged permissions are only allowed in privileged extensions: %(privilegedPermissions)s.":[ "" ],
      "Invalid host permission.":[ "Njepłaśiwe hostowe pšawo" ],
      "Invalid install origin.":[ "Njepłaśiwy instalěrowański póchad." ],
      "Invalid install origin. A valid origin has - only - a scheme, hostname and optional port. See https://mzl.la/3TEbqbE (MDN Docs) for more information.":[ "" ],
      "The field is invalid.":[ "Pólo jo njepłaśiwe." ],
      "\"manifest_version\" in the manifest.json is not a valid value":[ "\"manifest_version\" w manifest.json njejo płaśiwa gódnota" ],
      "See https://mzl.la/20PenXl (MDN Docs) for more information.":[ "Glejśo https://mzl.la/20PenXl (MDN Docs) za dalšne informacije." ],
      "\"%(property)s\" allows remote code execution in manifest.json":[ "„%(property)s“ wuwjeźenje zdalonego koda w manifest.json dowólujo" ],
      "A custom \"%(property)s\" needs additional review.":[ "Swójska kakosć „%(property)s“ se pśidatne pśeglědanje pomina." ],
      "\"%(property)s\" allows 'eval', which has strong security and performance implications.":[ "" ],
      "In most cases the same result can be achieved differently, therefore it is generally prohibited":[ "" ],
      "The \"name\" property must be a string with no leading/trailing whitespaces.":[ "" ],
      "See http://mzl.la/1STmr48 (MDN Docs) for more information.":[ "Glejśo http://mzl.la/1STmr48 (MDN Docs) za dalšne informacije." ],
      "\"update_url\" is not allowed.":[ "\"update_url\" njejo dowólony." ],
      "\"applications.gecko.update_url\" or \"browser_specific_settings.gecko.update_url\" are not allowed for Mozilla-hosted add-ons.":[ "" ],
      "The \"update_url\" property is not used by Firefox.":[ "Kakosć \"update_url\" se wót Firefox njewužywa." ],
      "The \"update_url\" is not used by Firefox in the root of a manifest; your add-on will be updated via the Add-ons site and not your \"update_url\". See: https://mzl.la/25zqk4O":[ "" ],
      "\"strict_max_version\" not required.":[ "\"strict_max_version\" njejo trěbny." ],
      "\"strict_max_version\" shouldn't be used unless the add-on is expected not to work with future versions of Firefox.":[ "" ],
      "Manifest Version 3 is not fully supported on Firefox for Android.":[ "" ],
      "No \"%(property)s\" property found in manifest.json":[ "Kakosć „%(property)s“ njejo se namakała w manifest.json" ],
      "\"%(property)s\" is required":[ "Kakosć „%(property)s“ jo trjebna" ],
      "An icon defined in the manifest could not be found in the package.":[ "Symbol, kótaryž jo se w manifesće definěrował, njedajo se w pakśiku namakaś." ],
      "Icon could not be found at \"%(path)s\".":[ "Symbol njedajo se na \"%(path)s\" namakaś." ],
      "A background script defined in the manifest could not be found.":[ "Slězynowy skript, kótaryž jo se definěrował w manifesće, njedajo se namakaś." ],
      "A background page defined in the manifest could not be found.":[ "Slězynowy bok, kótaryž jo se definěrował w manifesće, njedajo se namakaś." ],
      "Background script could not be found at \"%(path)s\".":[ "Slězynowy skript njedajo se na \"%(path)s\" namakaś." ],
      "Background page could not be found at \"%(path)s\".":[ "Slězynowy bok njedajo se na \"%(path)s\" namakaś." ],
      "A content script defined in the manifest could not be found.":[ "Wopśimjeśowy skript, kótaryž jo se definěrował w manifesće, njedajo se namakaś." ],
      "A content script css file defined in the manifest could not be found.":[ "CSS-dataja wopśimjeśowego skripta, kótaraž jo se definěrowała w manifesće, njedajo se namakaś." ],
      "Content script defined in the manifest could not be found at \"%(path)s\".":[ "Wopśimjeśowy skript, kótaryž jo se w manifesće definěrował, njedajo se na \"%(path)s\" namakaś." ],
      "Content script css file defined in the manifest could not be found at \"%(path)s\".":[ "CSS-dataja wopśimjeśowego skripta, kótaraž jo se w manifesće definěrowała, njedajo se na \"%(path)s\" namakaś." ],
      "A dictionary file defined in the manifest could not be found.":[ "Słownikowa dataja, kótaraž jo se definěrowała w manifesće, njedajo se namakaś." ],
      "Dictionary file defined in the manifest could not be found at \"%(path)s\".":[ "Słownikowa dataja, kótaraž jo se definěrowała w manifesće, njedajo se na \"%(path)s\" namakaś." ],
      "The manifest contains multiple dictionaries.":[ "Manifest někotare słowniki wopśimujo." ],
      "Multiple dictionaries were defined in the manifest, which is unsupported.":[ "Někotre słowniki su se w manifesće definěrowali, což se njepódpěra." ],
      "The manifest contains a dictionaries object, but it is empty.":[ "Manifest słownikowy objekt wopśimujo, ale jo prozny." ],
      "A dictionaries object was defined in the manifest, but it was empty.":[ "Słownikowy objekt jo se w manifesće definěrował, ale jo był prozny." ],
      "The manifest contains a dictionary but no id property.":[ "Manifest słownik wopśimujo, ale nic kakosć ID." ],
      "A dictionary was found in the manifest, but there was no id set.":[ "Słownik jo se namakał w manifesće, ale njejo se póstajił žeden ID." ],
      "Please refer to https://github.com/mozilla-extensions/xpi-manifest to learn more about privileged extensions and signing.":[ "" ],
      "Forbidden content found in add-on.":[ "Zakazane wopśimjeśe w dodanku namakany." ],
      "This add-on contains forbidden content.":[ "Toś ten dodank zakazane wopśimjeśe wopśimujo." ],
      "Icons must be square.":[ "Symbole muse kwadratiske byś." ],
      "Icon at \"%(path)s\" must be square.":[ "Symbol na \"%(path)s\" musy kwadratiski byś." ],
      "The size of the icon does not match the manifest.":[ "Wjelikosć symbola manifestoju njewótpowědujo." ],
      "Expected icon at \"%(path)s\" to be %(expected)d pixels wide but was %(actual)d.":[ "" ],
      "\"%(fieldName)s\" is ignored for non-privileged add-ons.":[ "„%(fieldName)s“ se za njepriwilegěrowane dodanki ignorěrujo." ],
      "Corrupt image file":[ "Wobškóźona wobrazowa dataja" ],
      "Expected icon file at \"%(path)s\" is corrupted":[ "Wócakana symbolowa dataja na \"%(path)s\" jo wobškóźona" ],
      "This property has been deprecated.":[ "Toś ta kakosć se wěcej njepódpěra." ],
      "This theme LWT alias has been removed in Firefox 70.":[ "Alias LWT toś teje drastwy jo se wótwónoźeł w Firefox 70." ],
      "See https://mzl.la/2T11Lkc (MDN Docs) for more information.":[ "Glejśo https://mzl.la/2T11Lkc (MDN Docs) za dalšne informacije." ],
      "Theme image for \"%(type)s\" could not be found at \"%(path)s\"":[ "Drastwowy wobraz za \"%(type)s\" njedajo se na \"%(path)s\" namakaś" ],
      "\"%(fieldName)s\" manifest field is only used for privileged and temporarily installed extensions.":[ "" ],
      "Corrupted theme image file":[ "Wobškóźona wobrazowa dataja drastwy" ],
      "Theme image file at \"%(path)s\" is corrupted":[ "Wobrazowa dataja drastwy na \"%(path)s\" jo wobškóźona" ],
      "Theme image file has an unsupported file extension":[ "Wobrazowa dataja drastwy ma datajowu kóńcowku, kótaraž se njepódpěra" ],
      "Theme image file at \"%(path)s\" has an unsupported file extension":[ "Wobrazowa dataja drastwy na \"%(path)s\" ma datajowu kóńcowku, kótaraž se njepódpěra" ],
      "Theme image file has an unsupported mime type":[ "Wobrazowa dataja drastwy ma typ MIME, kótaryž se njepódpěra" ],
      "Theme image file at \"%(path)s\" has the unsupported mime type \"%(mime)s\"":[ "Wobrazowa dataja drastwy na \"%(path)s\" ma typ MIME \"%(mime)s\", kótaryž se njepódpěra" ],
      "Theme image file mime type does not match its file extension":[ "Typ MIME wobrazoweje dataje drastwy jeje datajowej kóńcowce njewótpowědujo" ],
      "Theme image file extension at \"%(path)s\" does not match its actual mime type \"%(mime)s\"":[ "Kóńcowka wobrazoweje dataje drastwy na \"%(path)s\" jeje napšawdnemu typoju MIME \"%(mime)s\" njewótpowěda" ],
      "The \"default_locale\" is missing localizations.":[ "W \"default_locale\" lokalizacije feluju." ],
      "The \"default_locale\" value is specified in the manifest, but no matching \"messages.json\" in the \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "" ],
      "The \"default_locale\" is missing but \"_locales\" exist.":[ "\"default_locale\" felujo, ale \"_locales\" eksistěruju." ],
      "The \"default_locale\" value is not specifed in the manifest, but a \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "" ],
      "Unsupported image extension":[ "Kóńcowka wobrazoweje dataje se njepódpěra" ],
      "Icons should be one of JPG/JPEG, WebP, GIF, PNG or SVG.":[ "Symbole měli dataje typa JPG/JPEG, WebP, GIF, PNG abo SVG byś." ],
      "\"applications\" property overridden by \"browser_specific_settings\" property":[ "Kakosć „applications“ jo se pśepisała pśez kakosć „browser_specific_settings“" ],
      "The \"applications\" property is being ignored because it is superseded by the \"browser_specific_settings\" property which is also defined in your manifest. Consider removing applications.":[ "" ],
      "Empty language directory":[ "Prozny zapis rěcow" ],
      "messages.json file missing in \"%(path)s\"":[ "Dataja messages.json w \"%(path)s\" felujo" ],
      "Manifest key not supported by the specified minimum Firefox version":[ "Manifestowy kluc se pśez pódanu minimalnu wersiju Firefox njepódpěra" ],
      "\"strict_min_version\" requires Firefox %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "" ],
      "\"%(fieldName)s\" is not supported in manifest versions %(versionRange)s.":[ "„%(fieldName)s“ se w manifestowych wersijach %(versionRange)s njepódpěra." ],
      "Permission not supported by the specified minimum Firefox version":[ "Pšawo se pśez pódanu minimalnu wersiju Firefox njepódpěra" ],
      "\"%(fieldName)s\" is not supported.":[ "„%(fieldName)s“ se njepódpěra." ],
      "Manifest key not supported by the specified minimum Firefox for Android version":[ "Manifestowy kluc se pśez pódanu minimalnu wersiju Firefox za Android njepódpěra" ],
      "\"strict_min_version\" requires Firefox for Android %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "" ],
      "Permission not supported by the specified minimum Firefox for Android version":[ "Pšawo se pśez pódanu minimalnu wersiju Firefox za Android njepódpěra" ],
      "Linking to \"addons.mozilla.org\" is not allowed":[ "Wótkazowanje na „addons.mozilla.org“ njejo dowólone" ],
      "Links directing to \"addons.mozilla.org\" are not allowed to be used for homepage":[ "Wótkaze, kótarež na „addons.mozilla.org“ pokazuju, njesměju se za startowy bok wužywaś." ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above":[ "" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above. Please update your manifest.json version to specify a minimum Firefox version.":[ "" ],
      "The extension ID is required in Manifest Version 3 and above.":[ "ID rozšyrjenja jo w manifesće wersije 3 a w nowšych trjebny." ],
      "See https://mzl.la/3PLZYdo for more information.":[ "Glejśo https://mzl.la/3PLZYdo za dalšne informacije." ],
      "%(instancePath)s: Privileged extensions should declare privileged permissions.":[ "" ],
      "This extension does not declare any privileged permission. It does not need to be signed with the privileged certificate. Please upload it directly to https://addons.mozilla.org/.":[ "" ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for privileged extensions.":[ "" ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for extensions that include privileged manifest fields.":[ "" ],
      "%(instancePath)s: privileged manifest fields are only allowed in privileged extensions.":[ "" ],
      "This extension does not include the \"mozillaAddons\" permission, which is required for privileged extensions.":[ "" ],
      "Cannot use actions in hidden add-ons.":[ "Akcije njedaju se w schowanych dodankach wužywaś." ],
      "The hidden and browser_action/page_action (or action in Manifest Version 3 and above) properties are mutually exclusive.":[ "" ],
      "Use \"browser_specific_settings\" instead of \"applications\".":[ "" ],
      "The \"applications\" property in the manifest is deprecated and will no longer be accepted in Manifest Version 3 and above.":[ "" ],
      "\"applications\" is no longer allowed in Manifest Version 3 and above.":[ "" ],
      "The \"applications\" property in the manifest is no longer allowed in Manifest Version 3 and above. Use \"browser_specific_settings\" instead.":[ "" ],
      "The version string should be simplified because it won't be compatible with Manifest Version 3 and above.":[ "" ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros will no longer be allowed. Letters will no longer be allowed either. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "" ],
      "The version string should be simplified.":[ "Wersijowy znamjenjowy rjeśazk měł se zjadnoriś." ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros are not allowed. Letters are no longer allowed. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "" ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported in manifest versions %(versionRange)s.":[ "/%(fieldName)s: \"%(permissionName)s\" se w manifestowych wersijach %(versionRange)s njepódpěra." ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported.":[ "/%(fieldName)s: \"%(permissionName)s\" se njepódpěra." ] } } }