module.exports = { domain:"messages",
  locale_data:{ messages:{ "":{ domain:"messages",
        plural_forms:"nplurals=2; plural=(n > 1);",
        lang:"pt_BR" },
      "Validation Summary:":[ "Resumo da validação:" ],
      Code:[ "Código" ],
      Message:[ "Mensagem" ],
      Description:[ "Descrição" ],
      File:[ "Arquivo" ],
      Line:[ "Linha" ],
      Column:[ "Coluna" ],
      "Invalid manifest version range requested: --min-manifest-version (currently set to %(minManifestVersion)s) should not be greater than --max-manifest-version (currently set to %(maxManifestVersion)s).":[ "Solicitado intervalo inválido de versões de manifesto: --min-manifest-version (atualmente definido como %(minManifestVersion)s) não deve ser maior que --max-manifest-version (atualmente definido como %(maxManifestVersion)s)." ],
      "Your FTL is not valid.":[ "O seu FTL não é válido." ],
      "Your FTL file could not be parsed.":[ "Seu arquivo FTL não pôde ser analisado." ],
      "Remote scripts are not allowed as per the Add-on Policies.":[ "Scripts remotos não são permitidos conforme as políticas de extensões." ],
      "Please include all scripts in the add-on. For more information, refer to https://mzl.la/2uEOkYp.":[ "Inclua todos os scripts na extensão. Consulte mais informações em https://mzl.la/2uEOkYp." ],
      "Inline scripts blocked by default":[ "Scripts em linha bloqueados por padrão" ],
      "Default CSP rules prevent inline JavaScript from running (https://mzl.la/2pn32nd).":[ "Regras CSP padrão impedem a execução de JavaScript inline (https://mzl.la/2pn32nd)." ],
      "Unadvised 3rd-party JS library":[ "Biblioteca JS de terceiros desaconselhada" ],
      "Your add-on uses a JavaScript library we do not recommend. Read more: https://bit.ly/1TRIyZY":[ "Sua extensão usa uma biblioteca JavaScript que não recomendamos. Saiba mais: https://bit.ly/1TRIyZY" ],
      "Known JS library detected":[ "Biblioteca JS conhecida detectada" ],
      "JavaScript libraries are discouraged for simple add-ons, but are generally accepted.":[ "É desencorajado o uso de bibliotecas JavaScript em extensões simples, mas geralmente são aceitas." ],
      "Due to both security and performance concerns, this may not be set using dynamic values which have not been adequately sanitized. This can lead to security issues or fairly serious performance degradation.":[ "Devido a questões de desempenho e segurança, isso pode não ser definido usando valores dinâmicos que não foram adequadamente sanitizados. Isso pode levar a problemas de segurança ou degradação bastante séria de desempenho." ],
      "{{api}} is not supported":[ "{{api}} não é suportada" ],
      "This API has not been implemented by Firefox.":[ "Esta API ainda não foi implementada pelo Firefox." ],
      "\"{{api}}\" has been removed in Manifest Version 3 (`manifest_version` property)":[ "\"{{api}}\" foi removida no Manifesto Versão 3 (propriedade `manifest_version`)" ],
      "{{api}} is deprecated":[ "{{api}} está obsoleta" ],
      "This API has been deprecated by Firefox.":[ "Esta API foi tornada obsoleta pelo Firefox." ],
      "Content script file could not be found.":[ "O arquivo de script de conteúdo não pôde ser encontrado." ],
      "\"{{api}}\" is deprecated or unimplemented":[ "\"{{api}}\" está obsoleto ou não foi implementado" ],
      "Content script file could not be found":[ "O arquivo de script de conteúdo não pôde ser encontrado" ],
      "\"%(api)s\" can cause issues when loaded temporarily":[ "\"%(api)s\" pode causar problemas quando carregada temporariamente" ],
      "This API can cause issues when loaded temporarily using about:debugging in Firefox unless you specify \"browser_specific_settings.gecko.id\" in the manifest. Please see: https://mzl.la/2hizK4a for more.":[ "Esta API pode causar problemas quando carregada temporariamente usando about:debugging no Firefox, a menos que você especifique \"browser_specific_settings.gecko.id\" no manifesto. Saiba mais em https://mzl.la/2hizK4a." ],
      "{{api}} is not supported in Firefox version {{minVersion}}":[ "{{api}} não suportado no Firefox versão {{minVersion}}" ],
      "This API is not implemented by the given minimum Firefox version":[ "Esta API não é implementada pela versão mínima indicada do Firefox" ],
      "{{api}} is not supported in Firefox for Android version {{minVersion}}":[ "{{api}} não suportado no Firefox para Android versão {{minVersion}}" ],
      "This API is not implemented by the given minimum Firefox for Android version":[ "Esta API não é implementada pela versão mínima indicada do Firefox para Android" ],
      "Content script file name should not be empty.":[ "O nome do arquivo de script de conteúdo não deve estar vazio." ],
      "Content script file name should not be empty":[ "O nome do arquivo de script de conteúdo não deve estar vazio" ],
      "\"%(method)s\" called with a non-literal uri":[ "\"%(method)s\" chamado com um uri não literal" ],
      "Calling \"%(method)s\" with variable parameters can result in potential security vulnerabilities if the variable contains a remote URI. Consider using 'window.open' with the 'chrome=no' flag.":[ "Chamar \"%(method)s\" com parâmetros variáveis pode resultar em possíveis vulnerabilidades de segurança se a variável contiver um URI remoto. Considere usar 'window.open' com o flag 'chrome=no'." ],
      "\"%(method)s\" called with non-local URI":[ "\"%(method)s\" chamado com URI não local" ],
      "Calling \"%(method)s\" with a non-local URI will result in the dialog being opened with chrome privileges.":[ "Chamar \"%(method)s\" com um URI não local resulta na abertura de diálogo com privilégios chrome." ],
      "JavaScript syntax error":[ "Erro de sintaxe JavaScript" ],
      "There is a JavaScript syntax error in your code, which might be related to some experimental JavaScript features that aren't an official part of the language specification and therefore not supported yet. The validation cannot continue on this file.":[ "Há um erro de sintaxe JavaScript em seu código, que pode estar relacionado a alguns recursos experimentais do JavaScript que não fazem parte da especificação oficial da linguagem, portanto ainda não são suportados. A validação não pode continuar neste arquivo." ],
      "Evaluation of strings as code can lead to security vulnerabilities and performance issues, even in the most innocuous of circumstances. Please avoid using `eval` and the `Function` constructor when at all possible.":[ "A execução de strings como código pode levar a vulnerabilidades de segurança e problemas de desempenho, mesmo nas circunstâncias mais inócuas. Evite usar `eval` e o construtor de `Function` sempre que possível." ],
      "setTimeout, setInterval and execScript functions should be called only with function expressions as their first argument":[ "As funções setTimeout, setInterval e execScript só devem ser chamadas com expressões de função como primeiro argumento" ],
      "Unexpected global passed as an argument":[ "Variável global inesperado passada como argumento" ],
      "Passing a global as an argument is not recommended. Please make this a var instead.":[ "Passar um global como argumento não é recomendado. Em vez disso, torne isso 'var'." ],
      "Use of document.write strongly discouraged.":[ "O uso de document.write é fortemente desencorajado." ],
      "document.write will fail in many circumstances when used in extensions, and has potentially severe security repercussions when used improperly. Therefore, it should not be used.":[ "document.write falha em muitas circunstâncias quando usado em extensões e tem repercussões de segurança potencialmente severas quando usado inadequadamente. Sendo assim, não deve ser usado." ],
      "Banned 3rd-party JS library":[ "Biblioteca JS de terceiros banida" ],
      "Your add-on uses a JavaScript library we consider unsafe. Read more: https://bit.ly/1TRIyZY":[ "Sua extensão usa uma biblioteca JavaScript que consideramos não segura. Saiba mais: https://bit.ly/1TRIyZY" ],
      "Your JSON contains block comments.":[ "Seu JSON contém comentários em bloco." ],
      "Only line comments (comments beginning with \"//\") are allowed in JSON files. Please remove block comments (comments beginning with \"/*\")":[ "Em arquivos JSON, só são permitidos comentários de linha (comentários começando com \"//\"). Remova comentários em bloco (entre \"/*\" e \"*/\")" ],
      "Duplicate keys are not allowed in JSON files.":[ "Chaves duplicadas não são permitidas em arquivos JSON." ],
      "Duplicate key found in JSON file.":[ "Chave duplicada encontrada no arquivo JSON." ],
      "Your JSON is not valid.":[ "Seu JSON não é válido." ],
      "Your JSON file could not be parsed.":[ "Seu arquivo JSON não pôde ser analisado." ],
      "Reserved filename found.":[ "Encontrado nome de arquivo reservado." ],
      "Files whose names are reserved have been found in the add-on. Please refrain from using them and rename your files.":[ "Arquivos cujos nomes são reservados foram encontrados na extensão. Evite isso, renomeie seus arquivos." ],
      "The package is invalid. It may contain entries using invalid characters, as an example using '\\' as a path separator is not allowed in Firefox. Try to recreate your add-on package (ZIP) and make sure all entries are using '/' as the path separator.":[ "O pacote é inválido. Ele pode conter itens com caracteres inválidos. Por exemplo, não é permitido usar '\\' como separador de path no Firefox. Experimente recriar o pacote (ZIP) da sua extensão e verifique se todos os itens usam '/' como separador de path." ],
      "We were unable to decompress the zip file.":[ "Não foi possível descompactar o arquivo zip." ],
      "manifest.json was not found":[ "manifest.json não foi encontrado" ],
      "No manifest.json was found at the root of the extension. The package file must be a ZIP of the extension's files themselves, not of the containing directory. See: https://mzl.la/2r2McKv for more on packaging.":[ "Não foi encontrado nenhum arquivo manifest.json na raiz da extensão. O arquivo do pacote deve ser um ZIP dos próprios arquivos da extensão, não do diretório que os contém. Consulte mais informações em https://mzl.la/2r2McKv sobre como empacotar." ],
      "File is too large to parse.":[ "Arquivo grande demais para ser analisado." ],
      "This file is not binary and is too large to parse. Files larger than %(maxFileSizeToParseMB)sMB will not be parsed. Consider moving large lists of data out of JavaScript files and into JSON files, or splitting very large files into smaller ones.":[ "Este arquivo não é binário e é grande demais para ser analisado. Arquivos maiores que %(maxFileSizeToParseMB)sMB não são analisados. Considere mover grandes listas de dados de arquivos JavaScript para arquivos JSON, ou dividir arquivos muito grandes em arquivos menores." ],
      "Hidden file flagged":[ "Arquivo oculto marcado" ],
      "Hidden files complicate the review process and can contain sensitive information about the system that generated the add-on. Please modify the packaging process so that these files aren't included.":[ "Arquivos ocultos complicam o processo de revisão e podem conter informações confidenciais sobre o sistema que gerou a extensão. Modifique o processo de empacotamento para não incluir esses arquivos." ],
      "Package contains duplicate entries":[ "O pacote contém entradas duplicadas" ],
      "Flagged filename found":[ "Nome do arquivo marcado encontrado" ],
      "Files were found that are either unnecessary or have been included unintentionally. They should be removed.":[ "Foram encontrados arquivos desnecessários ou que foram incluídos involuntariamente. Eles devem ser removidos." ],
      "The package contains multiple entries with the same name. This practice has been banned. Try unzipping and re-zipping your add-on package and try again.":[ "O pacote contém vários itens com o mesmo nome. Esta prática foi proibida. Experimente descompactar e voltar a compactar o pacote de sua extensão e tente novamente." ],
      "Flagged file extensions found":[ "Extensões de arquivo marcada encontrada" ],
      "Flagged file type found":[ "Tipo de arquivo marcado encontrado" ],
      "Files whose names end with flagged extensions have been found in the add-on. The extension of these files are flagged because they usually identify binary components. Please see https://bit.ly/review-policy for more information on the binary content review process.":[ "Foram encontrados arquivos cujos nomes terminam com extensões sinalizadas. As extensões desses arquivos são sinalizadas porque geralmente identificam componentes binários. Consulte mais informações em https://bit.ly/review-policy sobre o processo de revisão de conteúdo binário." ],
      "Package already signed":[ "Pacote já assinado" ],
      "Add-ons which are already signed will be re-signed when published on AMO. This will replace any existing signatures on the add-on.":[ "Extensões já assinadas são assinadas novamente quando publicadas no AMO. Isso substitui todas as assinaturas existentes na extensão." ],
      "Firefox add-ons are not allowed to run coin miners.":[ "As extensões do Firefox não têm permissão para executar mineradores de moeda." ],
      "We do not allow coinminer scripts to be run inside WebExtensions. See https://github.com/mozilla/addons-linter/issues/1643 for more details.":[ "Não permitimos que scripts mineradores de moedas digitais sejam executados dentro de WebExtensions. Consulte mais detalhes em https://github.com/mozilla/addons-linter/issues/1643." ],
      "String name is reserved for a predefined message":[ "O nome da string é reservado para uma mensagem predefinida" ],
      "String names starting with @@ get translated to built-in constants (https://mzl.la/2BL9ZjE).":[ "Nomes de strings começando com @@ são traduzidos para constantes internas (https://mzl.la/2BL9ZjE)." ],
      "String name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2Eztyi5).":[ "Nomes de strings devem conter apenas caracteres alfanuméricos, _ e @ (https://mzl.la/2Eztyi5)." ],
      "Placeholder for message is missing":[ "Está faltando o marcador de posição da mensagem" ],
      "A placeholder used in the message is not defined.":[ "Um marcador de posição utilizado na mensagem não está definido." ],
      "Placeholder name contains invalid characters":[ "O nome do marcador de posição contém caracteres inválidos" ],
      "Placeholder name should only contain alpha-numeric characters, _ and @ (https://mzl.la/2ExbYez).":[ "Nomes de campos devem conter apenas caracteres alfanuméricos, _ e @ (https://mzl.la/2ExbYez)." ],
      "Placeholder is missing the content property":[ "Está faltando a propriedade de conteúdo do marcador de posição" ],
      "A placeholder needs a content property defining the replacement of it (https://mzl.la/2DT1MQd)":[ "Um campo precisa de uma propriedade de conteúdo definindo a substituição dele (https://mzl.la/2DT1MQd)" ],
      "Translation string is missing the message property":[ "Está faltando a propriedade da mensagem de tradução da string" ],
      "No \"message\" message property is set for a string (https://mzl.la/2DSBTjA).":[ "Nenhuma propriedade de mensagem \"message\" está definida para uma string (https://mzl.la/2DSBTjA)." ],
      "The field is required.":[ "O campo é requerido." ],
      "See https://mzl.la/1ZOhoEN (MDN Docs) for more information.":[ "Consulte mais informações em https://mzl.la/1ZOhoEN (MDN Docs)." ],
      "The permission type is unsupported.":[ "O tipo de permissão não é suportado." ],
      "See https://mzl.la/1R1n1t0 (MDN Docs) for more information.":[ "Veja mais informações em https://developer.mozilla.org/pt-BR/docs/Mozilla/Add-ons/WebExtensions/manifest.json/permiss%C3%B5es (MDN Docs)." ],
      "See https://mzl.la/2Qn0fWC (MDN Docs) for more information.":[ "Consulte mais informações em https://mzl.la/2Qn0fWC (MDN Docs)." ],
      "See https://mzl.la/3Woeqv4 (MDN Docs) for more information.":[ "Consulte mais informações em https://mzl.la/3Woeqv4 (MDN Docs)." ],
      "Unknown permission.":[ "Permissão desconhecida." ],
      "%(instancePath)s: the following privileged permissions are only allowed in privileged extensions: %(privilegedPermissions)s.":[ "%(instancePath)s: as seguintes permissões privilegiadas só são permitidas em extensões privilegiadas: %(privilegedPermissions)s." ],
      "Invalid host permission.":[ "Permissão de servidor inválida." ],
      "Invalid install origin.":[ "Origem de instalação inválida." ],
      "Invalid install origin. A valid origin has - only - a scheme, hostname and optional port. See https://mzl.la/3TEbqbE (MDN Docs) for more information.":[ "Origem de instalação inválida. Uma origem válida tem - apenas - um esquema, um nome de host e opcionalmente uma porta. Consulte mais informações em https://mzl.la/3TEbqbE (MDN Docs)." ],
      "The field is invalid.":[ "O campo é inválido." ],
      "\"manifest_version\" in the manifest.json is not a valid value":[ "O \"manifest_version\" no manifest.json não é válido" ],
      "See https://mzl.la/20PenXl (MDN Docs) for more information.":[ "Consulte mais informações em https://mzl.la/20PenXl (MDN Docs)." ],
      "\"%(property)s\" allows remote code execution in manifest.json":[ "\"%(property)s\" permite execução remota de código em manifest.json" ],
      "A custom \"%(property)s\" needs additional review.":[ "Uma \"%(property)s\" personalizada precisa de revisão adicional." ],
      "\"%(property)s\" allows 'eval', which has strong security and performance implications.":[ "\"%(property)s\" permite 'eval', que tem fortes implicações de segurança e desempenho." ],
      "In most cases the same result can be achieved differently, therefore it is generally prohibited":[ "Na maioria dos casos, o mesmo resultado pode ser alcançado de forma diferente, portanto, geralmente é proibido" ],
      "The \"name\" property must be a string with no leading/trailing whitespaces.":[ "A propriedade \"name\" deve ser uma string sem espaços no início ou no final." ],
      "See http://mzl.la/1STmr48 (MDN Docs) for more information.":[ "Veja http://mzl.la/1STmr48 (Documentação MDN) para mais informações." ],
      "\"update_url\" is not allowed.":[ "\"update_url\" não é permitido." ],
      "\"applications.gecko.update_url\" or \"browser_specific_settings.gecko.update_url\" are not allowed for Mozilla-hosted add-ons.":[ "\"applications.gecko.update_url\" ou \"browser_specific_settings.gecko.update_url\" não são permitidos em extensões hospedadas pela Mozilla." ],
      "The \"update_url\" property is not used by Firefox.":[ "A propriedade \"update_url\" não é utilizada pelo Firefox." ],
      "The \"update_url\" is not used by Firefox in the root of a manifest; your add-on will be updated via the Add-ons site and not your \"update_url\". See: https://mzl.la/25zqk4O":[ "O \"update_url\" não é usado pelo Firefox na raiz de um manifesto. Sua extensão será atualizada por meio do site de extensões e não pelo seu \"update_url\". Consulte https://mzl.la/25zqk4O" ],
      "\"strict_max_version\" not required.":[ "\"strict_max_version\" não é obrigatório." ],
      "\"strict_max_version\" shouldn't be used unless the add-on is expected not to work with future versions of Firefox.":[ "\"strict_max_version\" não deve ser usado, a menos que se espere que a extensão não funcione em futuras versões do Firefox." ],
      "Manifest Version 3 is not fully supported on Firefox for Android.":[ "O Firefox para Android não oferece suporte completo à versão 3 do Manifest." ],
      "No \"%(property)s\" property found in manifest.json":[ "Não foi encontrada nenhuma propriedade \"%(property)s\" em manifest.json" ],
      "\"%(property)s\" is required":[ "\"%(property)s\" é obrigatório" ],
      "An icon defined in the manifest could not be found in the package.":[ "Um ícone definido no manifesto não foi encontrado no pacote." ],
      "Icon could not be found at \"%(path)s\".":[ "O ícone não pôde ser encontrado em \"%(path)s\"." ],
      "A background script defined in the manifest could not be found.":[ "Um script de segundo plano definido no manifesto não foi encontrado." ],
      "A background page defined in the manifest could not be found.":[ "Uma página de segundo plano definida no manifesto não foi encontrada." ],
      "Background script could not be found at \"%(path)s\".":[ "O script de segundo plano não foi encontrado em \"%(path)s\"." ],
      "Background page could not be found at \"%(path)s\".":[ "A página de segundo plano não foi encontrada em \"%(path)s\"." ],
      "A content script defined in the manifest could not be found.":[ "Um script de conteúdo definido no manifesto não foi encontrado." ],
      "A content script css file defined in the manifest could not be found.":[ "Um arquivo css de script de conteúdo definido no manifesto não foi encontrado." ],
      "Content script defined in the manifest could not be found at \"%(path)s\".":[ "O script de conteúdo definido no manifesto não foi encontrado em \"%(path)s\"." ],
      "Content script css file defined in the manifest could not be found at \"%(path)s\".":[ "O script de conteúdo do arquivo css definido no manifesto não foi encontrado em \"%(path)s\"." ],
      "A dictionary file defined in the manifest could not be found.":[ "Um arquivo de dicionário definido no manifesto não foi encontrado." ],
      "Dictionary file defined in the manifest could not be found at \"%(path)s\".":[ "Arquivo de dicionário definido no manifesto não foi encontrado em \"%(path)s\"." ],
      "The manifest contains multiple dictionaries.":[ "O manifesto contém vários dicionários." ],
      "Multiple dictionaries were defined in the manifest, which is unsupported.":[ "Vários dicionários foram definidos no manifesto, o que não é suportado." ],
      "The manifest contains a dictionaries object, but it is empty.":[ "O manifesto contém um objeto de dicionários, mas está vazio." ],
      "A dictionaries object was defined in the manifest, but it was empty.":[ "Um objeto de dicionários foi definido no manifesto, mas está vazio." ],
      "The manifest contains a dictionary but no id property.":[ "O manifesto contém um dicionário, mas nenhuma propriedade id." ],
      "A dictionary was found in the manifest, but there was no id set.":[ "Um dicionário foi encontrado no manifesto, mas não há nenhum id definido." ],
      "Please refer to https://github.com/mozilla-extensions/xpi-manifest to learn more about privileged extensions and signing.":[ "Consulte https://github.com/mozilla-extensions/xpi-manifest para saber mais sobre extensões privilegiadas e assinatura." ],
      "Forbidden content found in add-on.":[ "Conteúdo proibido encontrado na extensão." ],
      "This add-on contains forbidden content.":[ "Esta extensão contém conteúdo proibido." ],
      "Icons must be square.":[ "Os ícones devem ser quadrados." ],
      "Icon at \"%(path)s\" must be square.":[ "O ícone em \"%(path)s\" tem que ser quadrado." ],
      "The size of the icon does not match the manifest.":[ "O tamanho do ícone não corresponde ao manifesto." ],
      "Expected icon at \"%(path)s\" to be %(expected)d pixels wide but was %(actual)d.":[ "Era esperado que o ícone em \"%(path)s\" tivesse %(expected)d pixels de largura, mas tem %(actual)d." ],
      "\"%(fieldName)s\" is ignored for non-privileged add-ons.":[ "\"%(fieldName)s\" é ignorado em extensões não privilegiadas." ],
      "Corrupt image file":[ "Arquivo de imagem corrompido" ],
      "Expected icon file at \"%(path)s\" is corrupted":[ "O arquivo do ícone esperado em \"%(path)s\" está corrompido" ],
      "This property has been deprecated.":[ "Esta propriedade está obsoleta." ],
      "This theme LWT alias has been removed in Firefox 70.":[ "Este alias LWT de tema foi removido no Firefox 70." ],
      "See https://mzl.la/2T11Lkc (MDN Docs) for more information.":[ "Veja mais informações em https://mzl.la/2T11Lkc (MDN Docs)." ],
      "Theme image for \"%(type)s\" could not be found at \"%(path)s\"":[ "Imagem de tema de \"%(type)s\" não foi encontrada em \"%(path)s\"." ],
      "\"%(fieldName)s\" manifest field is only used for privileged and temporarily installed extensions.":[ "O campo de manifesto \"%(fieldName)s\" só é usado em extensões privilegiadas e instaladas temporariamente." ],
      "Corrupted theme image file":[ "Arquivo de imagem de tema corrompido" ],
      "Theme image file at \"%(path)s\" is corrupted":[ "Arquivo de imagem de tema em \"%(path)s\" está corrompido" ],
      "Theme image file has an unsupported file extension":[ "Arquivo de imagem de tema tem uma extensão de arquivo não suportada" ],
      "Theme image file at \"%(path)s\" has an unsupported file extension":[ "Arquivo de imagem de tema em \"%(path)s\" tem uma extensão de arquivo não suportada" ],
      "Theme image file has an unsupported mime type":[ "Arquivo de imagem de tema tem um tipo mime não suportado" ],
      "Theme image file at \"%(path)s\" has the unsupported mime type \"%(mime)s\"":[ "Arquivo de imagem de tema em \"%(path)s\" tem o tipo mime não suportado \"%(mime)s\"" ],
      "Theme image file mime type does not match its file extension":[ "Tipo mime do arquivo de imagem de tema não combina com sua extensão de arquivo" ],
      "Theme image file extension at \"%(path)s\" does not match its actual mime type \"%(mime)s\"":[ "Extensão de arquivo de imagem de tema em \"%(path)s\" não combina com seu verdadeiro tipo mime \"%(mime)s\"" ],
      "The \"default_locale\" is missing localizations.":[ "Falta idiomas no \"default_locale\"." ],
      "The \"default_locale\" value is specified in the manifest, but no matching \"messages.json\" in the \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "O valor \"default_locale\" é especificado no manifesto, mas não existe nenhum \"messages.json\" correspondente no diretório \"_locales\". Consulte https://mzl.la/2hjcaEE" ],
      "The \"default_locale\" is missing but \"_locales\" exist.":[ "Está faltando \"default_locale\" mas \"_locales\" existe." ],
      "The \"default_locale\" value is not specifed in the manifest, but a \"_locales\" directory exists. See: https://mzl.la/2hjcaEE":[ "O valor \"default_locale\" não é especificado no manifesto, mas existe um diretório \"_locales\". Consulte https://mzl.la/2hjcaEE" ],
      "Unsupported image extension":[ "Extensão de imagem não suportada" ],
      "Icons should be one of JPG/JPEG, WebP, GIF, PNG or SVG.":[ "Os ícones devem ter uma das extensões JPG/JPEG,, WebP, GIF, PNG ou SVG." ],
      "\"applications\" property overridden by \"browser_specific_settings\" property":[ "Propriedade \"applications\" substituída pela propriedade \"browser_specific_settings\"" ],
      "The \"applications\" property is being ignored because it is superseded by the \"browser_specific_settings\" property which is also defined in your manifest. Consider removing applications.":[ "A propriedade \"applications\" está sendo ignorada porque foi suplantada pela propriedade \"browser_specific_settings\" que também está definida em seu manifesto. Considere remover \"applications\"." ],
      "Empty language directory":[ "Diretório de idiomas vazio" ],
      "messages.json file missing in \"%(path)s\"":[ "Está faltando o arquivo messages.json em \"%(path)s\"" ],
      "Manifest key not supported by the specified minimum Firefox version":[ "Chave de manifesto não suportada pela versão mínima especificada do Firefox" ],
      "\"strict_min_version\" requires Firefox %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "\"strict_min_version\" requer Firefox %(minVersion)s, que foi lançado antes da versão %(versionAdded)s introduzir suporte para \"%(key)s\"." ],
      "\"%(fieldName)s\" is not supported in manifest versions %(versionRange)s.":[ "\"%(fieldName)s\" não é suportado em manifesto das versões %(versionRange)s." ],
      "Permission not supported by the specified minimum Firefox version":[ "Permissão não suportada pela versão mínima especificada do Firefox" ],
      "\"%(fieldName)s\" is not supported.":[ "\"%(fieldName)s\" não é suportado." ],
      "Manifest key not supported by the specified minimum Firefox for Android version":[ "Chave de manifesto não suportada pela versão mínima especificada do Firefox para Android" ],
      "\"strict_min_version\" requires Firefox for Android %(minVersion)s, which was released before version %(versionAdded)s introduced support for \"%(key)s\".":[ "\"strict_min_version\" requer Firefox para Android %(minVersion)s, que foi lançado antes da versão %(versionAdded)s introduzir suporte para \"%(key)s\"." ],
      "Permission not supported by the specified minimum Firefox for Android version":[ "Permissão não suportada pela versão mínima especificada do Firefox para Android" ],
      "Linking to \"addons.mozilla.org\" is not allowed":[ "Não é permitido link para \"addons.mozilla.org\"" ],
      "Links directing to \"addons.mozilla.org\" are not allowed to be used for homepage":[ "Links direcionando para \"addons.mozilla.org\" não são permitidos na página inicial" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above":[ "A permissão \"%(permission)s\" requer que \"strict_min_version\" seja definido como \"%(minFirefoxVersion)s\" ou superior" ],
      "The \"%(permission)s\" permission requires \"strict_min_version\" to be set to \"%(minFirefoxVersion)s\" or above. Please update your manifest.json version to specify a minimum Firefox version.":[ "A permissão \"%(permission)s\" requer que \"strict_min_version\" seja definido como \"%(minFirefoxVersion)s\" ou superior. Atualize a versão do seu manifest.json para especificar uma versão mínima do Firefox." ],
      "The extension ID is required in Manifest Version 3 and above.":[ "O ID da extensão é obrigatório no Manifesto Versão 3 em diante." ],
      "See https://mzl.la/3PLZYdo for more information.":[ "Consulte mais informações em https://mzl.la/3PLZYdo." ],
      "%(instancePath)s: Privileged extensions should declare privileged permissions.":[ "%(instancePath)s: Extensões privilegiadas devem declarar permissões privilegiadas." ],
      "This extension does not declare any privileged permission. It does not need to be signed with the privileged certificate. Please upload it directly to https://addons.mozilla.org/.":[ "Esta extensão não declara nenhuma permissão privilegiada. Ela não precisa ser assinada com o certificado privilegiado. Envie diretamente para https://addons.mozilla.org/." ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for privileged extensions.":[ "%(instancePath)s: A permissão \"mozillaAddons\" é obrigatória para extensões privilegiadas." ],
      "%(instancePath)s: The \"mozillaAddons\" permission is required for extensions that include privileged manifest fields.":[ "%(instancePath)s: A permissão \"mozillaAddons\" é obrigatória para extensões que incluem campos de manifesto privilegiados." ],
      "%(instancePath)s: privileged manifest fields are only allowed in privileged extensions.":[ "%(instancePath)s: campos de manifesto privilegiados só são permitidos em extensões privilegiadas." ],
      "This extension does not include the \"mozillaAddons\" permission, which is required for privileged extensions.":[ "Esta extensão não inclui a permissão \"mozillaAddons\", obrigatória para extensões privilegiadas." ],
      "Cannot use actions in hidden add-ons.":[ "Não é possível usar ações em extensões ocultas." ],
      "The hidden and browser_action/page_action (or action in Manifest Version 3 and above) properties are mutually exclusive.":[ "As propriedades hidden e browser_action/page_action (ou action no Manifest versão 3 em diante) são mutuamente exclusivas." ],
      "Use \"browser_specific_settings\" instead of \"applications\".":[ "Use \"browser_specific_settings\" em vez de \"applications\"." ],
      "The \"applications\" property in the manifest is deprecated and will no longer be accepted in Manifest Version 3 and above.":[ "A propriedade \"applications\" no manifesto está obsoleta e não será mais aceita no Manifest versão 3 em diante." ],
      "\"applications\" is no longer allowed in Manifest Version 3 and above.":[ "\"applications\" não é mais permitido no Manifest versão 3 em diante." ],
      "The \"applications\" property in the manifest is no longer allowed in Manifest Version 3 and above. Use \"browser_specific_settings\" instead.":[ "A propriedade \"applications\" no manifesto não é mais permitida no Manifest versão 3 em diante. Em vez dela, use \"browser_specific_settings\"." ],
      "The version string should be simplified because it won't be compatible with Manifest Version 3 and above.":[ "A string da versão deve ser simplificada porque não será compatível com o Manifest versão 3 em diante." ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros will no longer be allowed. Letters will no longer be allowed either. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "A versão deve ser uma string com 1 a 4 números separados por pontos. Cada número deve ter até 9 dígitos, zeros à esquerda não serão mais permitidos. Letras também não serão mais permitidas. Consulte mais informações em https://mzl.la/3h3mCRu (MDN Docs)." ],
      "The version string should be simplified.":[ "A string da versão deve ser simplificada." ],
      "The version should be a string with 1 to 4 numbers separated with dots. Each number should have up to 9 digits and leading zeros are not allowed. Letters are no longer allowed. See https://mzl.la/3h3mCRu (MDN Docs) for more information.":[ "A versão deve ser uma string com 1 a 4 números separados por pontos. Cada número deve ter até 9 dígitos, zeros à esquerda não são permitidos. Letras não são mais permitidas. Consulte mais informações em https://mzl.la/3h3mCRu (MDN Docs)." ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported in manifest versions %(versionRange)s.":[ "/%(fieldName)s: \"%(permissionName)s\" não é suportado em manifesto das versões %(versionRange)s." ],
      "/%(fieldName)s: \"%(permissionName)s\" is not supported.":[ "/%(fieldName)s: \"%(permissionName)s\" não é suportado." ] } } }