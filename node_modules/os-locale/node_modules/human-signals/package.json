{"name": "human-signals", "version": "1.1.1", "main": "build/src/main.js", "files": ["build/src", "!~"], "scripts": {"test": "gulp test"}, "husky": {"hooks": {"pre-push": "gulp check --full"}}, "description": "Human-friendly process signals", "keywords": ["signal", "signals", "handlers", "error-handling", "errors", "interrupts", "sigterm", "sigint", "irq", "process", "exit", "exit-code", "status", "operating-system", "es6", "javascript", "linux", "macos", "windows", "nodejs"], "license": "Apache-2.0", "homepage": "https://git.io/JeluP", "repository": "ehmicky/human-signals", "bugs": {"url": "https://github.com/ehmicky/human-signals/issues"}, "author": "ehmicky <<EMAIL>> (https://github.com/ehmicky)", "directories": {"lib": "src", "test": "test"}, "dependencies": {}, "devDependencies": {"@ehmicky/dev-tasks": "^0.30.48", "ajv": "^6.10.2", "ava": "^2.4.0", "fast-deep-equal": "^2.0.1", "gulp": "^4.0.2", "husky": "^3.0.9", "test-each": "^1.7.2"}, "engines": {"node": ">=8.12.0"}}