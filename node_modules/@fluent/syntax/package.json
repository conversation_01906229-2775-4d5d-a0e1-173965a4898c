{"name": "@fluent/syntax", "description": "AST and parser for Fluent", "version": "0.19.0", "homepage": "https://projectfluent.org", "author": "Mozilla <<EMAIL>>", "license": "Apache-2.0", "contributors": [{"name": "<PERSON><PERSON>", "email": "z<PERSON><PERSON><PERSON>@mozilla.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "main": "./index.js", "module": "./esm/index.js", "types": "./esm/index.d.ts", "repository": {"type": "git", "url": "https://github.com/projectfluent/fluent.js.git"}, "keywords": ["localization", "l10n", "internationalization", "i18n", "ftl", "plural", "gender", "locale", "language", "formatting", "translate", "translation", "format", "ast", "serializer", "parser"], "scripts": {"build": "tsc", "postbuild": "rollup -c ../rollup.config.mjs", "docs": "typedoc --options ../typedoc.config.cjs", "test": "mocha 'test/*_test.js'"}, "engines": {"node": ">=14.0.0", "npm": ">=7.0.0"}, "devDependencies": {"@fluent/dedent": "file:../fluent-dedent"}}