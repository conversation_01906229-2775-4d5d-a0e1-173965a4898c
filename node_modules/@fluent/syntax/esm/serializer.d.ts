import * as AST from "./ast.js";
import type { Resource, Entry, Expression, Placeable } from "./ast.js";
export interface FluentSerializerOptions {
    withJunk?: boolean;
}
export declare class FluentSerializer {
    withJunk: boolean;
    constructor({ withJunk }?: FluentSerializerOptions);
    serialize(resource: Resource): string;
    serializeEntry(entry: Entry, state?: number): string;
}
export declare function serializeExpression(expr: Expression | Placeable): string;
export declare function serializeVariantKey(key: AST.Identifier | AST.NumberLiteral): string;
