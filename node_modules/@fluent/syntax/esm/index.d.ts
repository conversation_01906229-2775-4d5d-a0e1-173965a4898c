import { Resource } from "./ast.js";
import { FluentParser, FluentParserOptions } from "./parser.js";
import { FluentSerializer, FluentSerializerOptions } from "./serializer.js";
export * from "./ast.js";
export { ParseError } from "./errors.js";
export type { Indent } from "./parser.js";
export { serializeExpression, serializeVariantKey } from "./serializer.js";
export { Transformer, Visitor } from "./visitor.js";
export { FluentParser, FluentParserOptions, FluentSerializer, FluentSerializerOptions, };
export declare function parse(source: string, opts: FluentParserOptions): Resource;
export declare function serialize(resource: Resource, opts: FluentSerializerOptions): string;
export declare function lineOffset(source: string, pos: number): number;
export declare function columnOffset(source: string, pos: number): number;
