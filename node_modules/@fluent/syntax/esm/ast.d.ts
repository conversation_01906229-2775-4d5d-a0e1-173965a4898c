/**
 * Base class for all Fluent AST nodes.
 *
 * All productions described in the ASDL subclass BaseNode, including Span and
 * Annotation.
 *
 */
export declare abstract class BaseNode {
    abstract type: string;
    [name: string]: unknown;
    equals(other: BaseNode, ignoredFields?: Array<string>): boolean;
    clone(): this;
}
/**
 * Base class for AST nodes which can have Spans.
 */
export declare abstract class SyntaxNode extends BaseNode {
    span?: Span;
    /** @ignore */
    addSpan(start: number, end: number): void;
}
export declare class Resource extends SyntaxNode {
    type: "Resource";
    body: Array<Entry>;
    constructor(body?: Array<Entry>);
}
export declare type Entry = Message | Term | Comments | Junk;
export declare class Message extends SyntaxNode {
    type: "Message";
    id: Identifier;
    value: Pattern | null;
    attributes: Array<Attribute>;
    comment: Comment | null;
    constructor(id: Identifier, value?: Pattern | null, attributes?: Array<Attribute>, comment?: Comment | null);
}
export declare class Term extends SyntaxNode {
    type: "Term";
    id: Identifier;
    value: Pattern;
    attributes: Array<Attribute>;
    comment: Comment | null;
    constructor(id: Identifier, value: Pattern, attributes?: Array<Attribute>, comment?: Comment | null);
}
export declare class Pattern extends SyntaxNode {
    type: "Pattern";
    elements: Array<PatternElement>;
    constructor(elements: Array<PatternElement>);
}
export declare type PatternElement = TextElement | Placeable;
export declare class TextElement extends SyntaxNode {
    type: "TextElement";
    value: string;
    constructor(value: string);
}
export declare class Placeable extends SyntaxNode {
    type: "Placeable";
    expression: Expression;
    constructor(expression: Expression);
}
/**
 * A subset of expressions which can be used as outside of Placeables.
 */
export type InlineExpression = StringLiteral | NumberLiteral | FunctionReference | MessageReference | TermReference | VariableReference | Placeable;
export declare type Expression = InlineExpression | SelectExpression;
export declare abstract class BaseLiteral extends SyntaxNode {
    value: string;
    constructor(value: string);
    abstract parse(): {
        value: unknown;
    };
}
export declare class StringLiteral extends BaseLiteral {
    type: "StringLiteral";
    parse(): {
        value: string;
    };
}
export declare class NumberLiteral extends BaseLiteral {
    type: "NumberLiteral";
    parse(): {
        value: number;
        precision: number;
    };
}
export declare type Literal = StringLiteral | NumberLiteral;
export declare class MessageReference extends SyntaxNode {
    type: "MessageReference";
    id: Identifier;
    attribute: Identifier | null;
    constructor(id: Identifier, attribute?: Identifier | null);
}
export declare class TermReference extends SyntaxNode {
    type: "TermReference";
    id: Identifier;
    attribute: Identifier | null;
    arguments: CallArguments | null;
    constructor(id: Identifier, attribute?: Identifier | null, args?: CallArguments | null);
}
export declare class VariableReference extends SyntaxNode {
    type: "VariableReference";
    id: Identifier;
    constructor(id: Identifier);
}
export declare class FunctionReference extends SyntaxNode {
    type: "FunctionReference";
    id: Identifier;
    arguments: CallArguments;
    constructor(id: Identifier, args: CallArguments);
}
export declare class SelectExpression extends SyntaxNode {
    type: "SelectExpression";
    selector: InlineExpression;
    variants: Array<Variant>;
    constructor(selector: InlineExpression, variants: Array<Variant>);
}
export declare class CallArguments extends SyntaxNode {
    type: "CallArguments";
    positional: Array<InlineExpression>;
    named: Array<NamedArgument>;
    constructor(positional?: Array<InlineExpression>, named?: Array<NamedArgument>);
}
export declare class Attribute extends SyntaxNode {
    type: "Attribute";
    id: Identifier;
    value: Pattern;
    constructor(id: Identifier, value: Pattern);
}
export declare class Variant extends SyntaxNode {
    type: "Variant";
    key: Identifier | NumberLiteral;
    value: Pattern;
    default: boolean;
    constructor(key: Identifier | NumberLiteral, value: Pattern, def: boolean);
}
export declare class NamedArgument extends SyntaxNode {
    type: "NamedArgument";
    name: Identifier;
    value: Literal;
    constructor(name: Identifier, value: Literal);
}
export declare class Identifier extends SyntaxNode {
    type: "Identifier";
    name: string;
    constructor(name: string);
}
export declare abstract class BaseComment extends SyntaxNode {
    content: string;
    constructor(content: string);
}
export declare class Comment extends BaseComment {
    type: "Comment";
}
export declare class GroupComment extends BaseComment {
    type: "GroupComment";
}
export declare class ResourceComment extends BaseComment {
    type: "ResourceComment";
}
export declare type Comments = Comment | GroupComment | ResourceComment;
export declare class Junk extends SyntaxNode {
    type: "Junk";
    annotations: Array<Annotation>;
    content: string;
    constructor(content: string);
    addAnnotation(annotation: Annotation): void;
}
export declare class Span extends BaseNode {
    type: "Span";
    start: number;
    end: number;
    constructor(start: number, end: number);
}
export declare class Annotation extends SyntaxNode {
    type: "Annotation";
    code: string;
    arguments: Array<unknown>;
    message: string;
    constructor(code: string, args: unknown[] | undefined, message: string);
}
