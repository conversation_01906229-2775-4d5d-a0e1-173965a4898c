import * as AST from "./ast.js";
import type { Resource, Entry } from "./ast.js";
import { FluentParserStream } from "./stream.js";
export interface FluentParserOptions {
    withSpans?: boolean;
}
export declare class FluentParser {
    withSpans: boolean;
    constructor({ withSpans }?: FluentParserOptions);
    parse(source: string): Resource;
    /**
     * Parse the first Message or Term in `source`.
     *
     * Skip all encountered comments and start parsing at the first Message or
     * Term start. Return Junk if the parsing is not successful.
     *
     * Preceding comments are ignored unless they contain syntax errors
     * themselves, in which case Junk for the invalid comment is returned.
     */
    parseEntry(source: string): Entry;
    getEntryOrJunk(ps: FluentParserStream): AST.Entry;
    getEntry(ps: FluentParserStream): AST.Entry;
    getComment(ps: FluentParserStream): AST.Comments;
    getMessage(ps: FluentParserStream): AST.Message;
    getTerm(ps: FluentParserStream): AST.Term;
    getAttribute(ps: FluentParserStream): AST.Attribute;
    getAttributes(ps: FluentParserStream): Array<AST.Attribute>;
    getIdentifier(ps: FluentParserStream): AST.Identifier;
    getVariantKey(ps: FluentParserStream): AST.Identifier | AST.NumberLiteral;
    getVariant(ps: FluentParserStream, hasDefault?: boolean): AST.Variant;
    getVariants(ps: FluentParserStream): Array<AST.Variant>;
    getDigits(ps: FluentParserStream): string;
    getNumber(ps: FluentParserStream): AST.NumberLiteral;
    /**
     * maybeGetPattern distinguishes between patterns which start on the same line
     * as the identifier (a.k.a. inline signleline patterns and inline multiline
     * patterns) and patterns which start on a new line (a.k.a. block multiline
     * patterns). The distinction is important for the dedentation logic: the
     * indent of the first line of a block pattern must be taken into account when
     * calculating the maximum common indent.
     */
    maybeGetPattern(ps: FluentParserStream): AST.Pattern | null;
    getPattern(ps: FluentParserStream, isBlock: boolean): AST.Pattern;
    /**
     * Create a token representing an indent. It's not part of the AST and it will
     * be trimmed and merged into adjacent TextElements, or turned into a new
     * TextElement, if it's surrounded by two Placeables.
     */
    getIndent(ps: FluentParserStream, value: string, start: number): Indent;
    /**
     * Dedent a list of elements by removing the maximum common indent from the
     * beginning of text lines. The common indent is calculated in getPattern.
     */
    dedent(elements: Array<AST.PatternElement | Indent>, commonIndent: number): Array<AST.PatternElement>;
    getTextElement(ps: FluentParserStream): AST.TextElement;
    getEscapeSequence(ps: FluentParserStream): string;
    getUnicodeEscapeSequence(ps: FluentParserStream, u: string, digits: number): string;
    getPlaceable(ps: FluentParserStream): AST.Placeable;
    getExpression(ps: FluentParserStream): AST.Expression | AST.Placeable;
    getInlineExpression(ps: FluentParserStream): AST.InlineExpression | AST.Placeable;
    getCallArgument(ps: FluentParserStream): AST.InlineExpression | AST.NamedArgument;
    getCallArguments(ps: FluentParserStream): AST.CallArguments;
    getString(ps: FluentParserStream): AST.StringLiteral;
    getLiteral(ps: FluentParserStream): AST.Literal;
}
export declare class Indent {
    type: string;
    span: AST.Span;
    value: string;
    /** @ignore */
    constructor(value: string, start: number, end: number);
}
