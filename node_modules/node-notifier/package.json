{"name": "node-notifier", "version": "10.0.1", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "scripts": {"pretest": "npm run lint", "test": "jest", "example": "node ./example/message.js", "example:mac": "node ./example/advanced.js", "example:mac:input": "node ./example/macInput.js", "example:windows": "node ./example/toaster.js", "example:windows:actions": "node ./example/toaster-with-actions.js", "example:windows:custom-path": "cross-env DEBUG=notifier node ./example/toaster-custom-path.js", "copy-resources": "copyfiles -u 2 ./vendor/snoreToast/snoretoast-x64.exe ./dist/example/resources/ && copyfiles -u 1 ./example/coulson.jpg ./dist/example/resources/", "preexample:windows:nexe-custom-path": "<PERSON><PERSON><PERSON> dist", "example:windows:nexe-custom-path": "nexe -t windows-x64-14.15.3 -i ./example/toaster-custom-path.js -o ./dist/toaster-custom-path.exe && npm run copy-resources", "postexample:windows:nexe-custom-path": "cross-env DEBUG=notifier ./dist/toaster-custom-path.exe", "lint": "eslint example/*.js lib/*.js notifiers/*.js test/**/*.js index.js"}, "jest": {"testRegex": "/test/[^_]*.js", "testEnvironment": "node", "setupFilesAfterEnv": ["./test/_test-matchers.js"]}, "repository": {"type": "git", "url": "git+ssh://**************/mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "devDependencies": {"copyfiles": "^2.4.1", "cross-env": "^7.0.3", "eslint": "^7.26.0", "eslint-config-semistandard": "^15.0.1", "eslint-config-standard": "^16.0.2", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.3.1", "husky": "^6.0.0", "jest": "^26.6.3", "lint-staged": "^11.0.0", "nexe": "^4.0.0-beta.19", "prettier": "^2.3.0", "rimraf": "^3.0.2"}, "dependencies": {"growly": "^1.3.0", "is-wsl": "^2.2.0", "semver": "^7.3.5", "shellwords": "^0.1.1", "uuid": "^8.3.2", "which": "^2.0.2"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,json,css,md}": ["prettier --write", "git add"]}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier#readme", "directories": {"example": "example", "test": "test"}}