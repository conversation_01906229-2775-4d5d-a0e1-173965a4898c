{"name": "jose", "version": "4.13.1", "description": "'JSON Web Almost Everything' - JWA, JWS, JWE, JWT, JWK, JWKS for Node.js, Browser, Cloudflare Workers, Deno, Bun, and other Web-interoperable runtimes", "keywords": ["browser", "bun", "cloudflare", "compact", "decode", "decrypt", "deno", "detached", "ec", "ecdsa", "eddsa", "edge", "electron", "embedded", "encrypt", "flattened", "general", "isomorphic", "jose", "json web token", "jsonwebtoken", "jwa", "jwe", "jwk", "jwks", "jws", "jwt", "netlify", "next", "nextjs", "oct", "okp", "payload", "pem", "pkcs8", "rsa", "secp256k1", "sign", "signature", "spki", "universal", "validate", "vercel", "verify", "webcrypto", "workers", "x509"], "homepage": "https://github.com/panva/jose", "repository": "panva/jose", "funding": {"url": "https://github.com/sponsors/panva"}, "license": "MIT", "author": "<PERSON><PERSON> <<EMAIL>>", "sideEffects": false, "exports": {".": {"types": "./dist/types/index.d.ts", "bun": "./dist/browser/index.js", "deno": "./dist/browser/index.js", "browser": "./dist/browser/index.js", "worker": "./dist/browser/index.js", "import": "./dist/node/esm/index.js", "require": "./dist/node/cjs/index.js"}, "./package.json": "./package.json"}, "main": "./dist/node/cjs/index.js", "browser": "./dist/browser/index.js", "types": "./dist/types/index.d.ts", "files": ["dist/**/package.json", "dist/**/*.js", "dist/types/**/*.d.ts", "!dist/**/*.bundle.js", "!dist/**/*.umd.js", "!dist/**/*.min.js", "!dist/node/webcrypto/**/*", "!dist/types/runtime/*", "!dist/types/lib/*", "!dist/deno/**/*"], "deno": "./dist/browser/index.js"}