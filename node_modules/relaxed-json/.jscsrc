{"requireCurlyBraces": ["if", "else", "for", "while", "do", "try", "catch"], "requireOperatorBeforeLineBreak": true, "requireCamelCaseOrUpperCaseIdentifiers": "ignoreProperties", "maximumLineLength": 300, "validateIndentation": 2, "validateQuoteMarks": {"escape": true, "mark": "\""}, "disallowMultipleLineStrings": true, "disallowMixedSpacesAndTabs": true, "disallowTrailingWhitespace": true, "disallowSpaceAfterPrefixUnaryOperators": true, "disallowMultipleVarDecl": true, "requireSpaceAfterKeywords": ["if", "else", "for", "while", "do", "switch", "return", "try", "catch"], "requireSpaceBeforeBinaryOperators": true, "requireSpaceAfterBinaryOperators": true, "requireSpacesInConditionalExpression": true, "requireSpaceBeforeBlockStatements": true, "requireLineFeedAtFileEnd": true, "disallowSpacesInsideObjectBrackets": null, "disallowSpacesInsideArrayBrackets": "all", "disallowSpacesInsideParentheses": true, "disallowMultipleLineBreaks": true, "requireParenthesesAroundIIFE": true, "disallowKeywords": ["with"], "requireSpacesInAnonymousFunctionExpression": {"beforeOpeningRoundBrace": true, "beforeOpeningCurlyBrace": true}, "requireSpacesInNamedFunctionExpression": {"beforeOpeningCurlyBrace": true}, "disallowSpacesInNamedFunctionExpression": {"beforeOpeningRoundBrace": true}, "requireSpacesInFunctionDeclaration": {"beforeOpeningCurlyBrace": true}, "disallowSpacesInFunctionDeclaration": {"beforeOpeningRoundBrace": true}}