{"name": "relaxed-json", "description": "Relaxed JSON is strict superset JSON, relaxing strictness of valilla JSON", "version": "1.0.3", "homepage": "https://github.com/phadej/relaxed-json", "author": {"name": "<PERSON><PERSON>", "email": "oleg.gren<PERSON>@iki.fi", "url": "http://oleg.fi/"}, "repository": {"type": "git", "url": "git://github.com/phadej/relaxed-json.git"}, "bugs": {"url": "https://github.com/phadej/relaxed-json/issues"}, "license": "BSD-3-<PERSON><PERSON>", "main": "relaxed-json.js", "bin": {"rjson": "./bin/rjson.js"}, "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "make"}, "devDependencies": {"david": "^11.0.0", "eslint": "^5.15.0", "nyc": "^13.3.0", "jsverify": "^0.8.4", "mocha": "^6.0.2", "uglify-js": "^3.4.9", "underscore": "^1.8.2"}, "keywords": ["json", "comments", "comment", "config"], "dependencies": {"chalk": "^2.4.2", "commander": "^2.6.0"}}