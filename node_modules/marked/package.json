{"name": "marked", "description": "A markdown parser built for speed", "author": "<PERSON>", "version": "4.3.0", "type": "module", "main": "./lib/marked.cjs", "module": "./lib/marked.esm.js", "browser": "./lib/marked.umd.js", "bin": {"marked": "bin/marked.js"}, "man": "./man/marked.1", "files": ["bin/", "lib/", "src/", "man/", "marked.min.js"], "exports": {".": {"import": "./lib/marked.esm.js", "default": "./lib/marked.cjs"}, "./bin/marked": "./bin/marked.js", "./package.json": "./package.json"}, "repository": "git://github.com/markedjs/marked.git", "homepage": "https://marked.js.org", "bugs": {"url": "http://github.com/markedjs/marked/issues"}, "license": "MIT", "keywords": ["markdown", "markup", "html"], "tags": ["markdown", "markup", "html"], "devDependencies": {"@babel/core": "^7.21.3", "@babel/preset-env": "^7.20.2", "@markedjs/html-differ": "^4.0.2", "@rollup/plugin-babel": "^6.0.3", "@semantic-release/commit-analyzer": "^9.0.2", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^8.0.7", "@semantic-release/npm": "^9.0.2", "@semantic-release/release-notes-generator": "^10.0.3", "cheerio": "^1.0.0-rc.12", "commonmark": "0.30.0", "eslint": "^8.36.0", "eslint-config-standard": "^17.0.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-n": "^15.6.1", "eslint-plugin-promise": "^6.1.1", "front-matter": "^4.0.2", "highlight.js": "^11.7.0", "jasmine": "^4.6.0", "markdown-it": "13.0.1", "node-fetch": "^3.3.1", "rollup": "^3.20.0", "semantic-release": "^20.1.3", "titleize": "^3.0.0", "uglify-js": "^3.17.4", "vuln-regex-detector": "^1.3.0"}, "scripts": {"test": "jasmine --config=jasmine.json", "test:all": "npm test && npm run test:lint", "test:unit": "npm test -- test/unit/**/*-spec.js", "test:specs": "npm test -- test/specs/**/*-spec.js", "test:lint": "eslint .", "test:redos": "node test/vuln-regex.js", "test:update": "node test/update-specs.js", "rules": "node test/rules.js", "bench": "npm run rollup && node test/bench.js", "lint": "eslint --fix .", "build:reset": "git checkout upstream/master lib/marked.cjs lib/marked.umd.js lib/marked.esm.js marked.min.js", "build": "npm run rollup && npm run minify", "build:docs": "node build-docs.js", "rollup": "rollup -c rollup.config.js", "minify": "uglifyjs lib/marked.umd.js -cm  --comments /Copyright/ -o marked.min.js"}, "engines": {"node": ">= 12"}}