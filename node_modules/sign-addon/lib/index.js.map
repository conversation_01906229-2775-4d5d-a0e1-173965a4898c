{"version": 3, "file": "index.js", "names": ["fs", "Client", "DefaultAMOClient", "signAddon", "xpiPath", "id", "version", "<PERSON><PERSON><PERSON><PERSON>", "apiSecret", "apiUrlPrefix", "apiJwtExpiresIn", "verbose", "channel", "timeout", "downloadDir", "apiProxy", "apiRequestConfig", "disableP<PERSON>ress<PERSON>ar", "AMOClient", "reportEmpty", "name", "Error", "stats", "stat", "isFile", "statError", "client", "debugLogging", "statusCheckTimeout", "proxyServer", "requestConfig", "sign", "guid", "signAddonAndExit", "options", "systemProcess", "process", "throwError", "logger", "console", "result", "log", "success", "exit", "err", "error", "stack"], "sources": ["../src/index.js"], "sourcesContent": ["import { fs } from 'mz';\n\nimport { Client as DefaultAMOClient } from './amo-client.js';\n\n/** @typedef {import(\"request\").OptionsWithUrl} RequestConfig */\n/** @typedef {import(\"./amo-client.js\").ClientParams} ClientParams */\n/** @typedef {import(\"./amo-client.js\").ReleaseChannel} ReleaseChannel */\n\n/**\n * @typedef {object} SignAddonParams\n * @property {string} xpiPath\n * @property {string} id\n * @property {string} version\n * @property {ClientParams['apiKey']} apiKey\n * @property {ClientParams['apiSecret']} apiSecret\n * @property {ClientParams['apiUrlPrefix']=} apiUrlPrefix\n * @property {ClientParams['apiJwtExpiresIn']=} apiJwtExpiresIn\n * @property {ClientParams['debugLogging']=} verbose\n * @property {ReleaseChannel=} channel\n * @property {ClientParams['statusCheckTimeout']=} timeout\n * @property {ClientParams['downloadDir']=} downloadDir\n * @property {ClientParams['proxyServer']=} apiProxy\n * @property {ClientParams['requestConfig']=} apiRequestConfig\n * @property {typeof DefaultAMOClient=} AMOClient\n * @property {ClientParams['disableProgressBar']=} disableProgressBar\n *\n * @param {SignAddonParams} params\n */\nexport const signAddon = async ({\n  // Absolute path to add-on XPI file.\n  xpiPath,\n  // The add-on ID as recognized by AMO. Example: my-addon@jetpack\n  id,\n  // The add-on version number for AMO.\n  version,\n  // Your API key (JWT issuer) from AMO Devhub.\n  apiKey,\n  // Your API secret (JWT secret) from AMO Devhub.\n  apiSecret,\n  // Optional arguments:\n  apiUrlPrefix = 'https://addons.mozilla.org/api/v4',\n  // Number of seconds until the JWT token for the API request expires.\n  // This must match the expiration time that the API server accepts.\n  apiJwtExpiresIn,\n  verbose = false,\n  // The release channel (listed or unlisted).\n  // Ignored for new add-ons, which are always unlisted.\n  // Defaults to most recently used channel.\n  channel,\n  // Number of milliseconds to wait before giving up on a\n  // response from Mozilla's web service.\n  timeout,\n  // Absolute directory to save downloaded files in.\n  downloadDir,\n  // Optional proxy to use for all API requests,\n  // such as \"http://yourproxy:6000\"\n  apiProxy,\n  // Optional object to pass into request() for additional configuration.\n  // Not all properties are guaranteed to be applied.\n  apiRequestConfig,\n  // Optional boolean passed to the AMO client to disable the progress bar.\n  disableProgressBar = false,\n  AMOClient = DefaultAMOClient,\n}) => {\n  /**\n   * @param {string} name\n   */\n  function reportEmpty(name) {\n    throw new Error(`required argument was empty: ${name}`);\n  }\n\n  if (!xpiPath) {\n    reportEmpty('xpiPath');\n  }\n\n  if (!version) {\n    reportEmpty('version');\n  }\n\n  if (!apiSecret) {\n    reportEmpty('apiSecret');\n  }\n\n  if (!apiKey) {\n    reportEmpty('apiKey');\n  }\n\n  try {\n    const stats = await fs.stat(xpiPath);\n\n    if (!stats.isFile) {\n      throw new Error(`not a file: ${xpiPath}`);\n    }\n  } catch (statError) {\n    throw new Error(`error with ${xpiPath}: ${statError}`);\n  }\n\n  const client = new AMOClient({\n    apiKey,\n    apiSecret,\n    apiUrlPrefix,\n    apiJwtExpiresIn,\n    downloadDir,\n    debugLogging: verbose,\n    statusCheckTimeout: timeout,\n    proxyServer: apiProxy,\n    requestConfig: apiRequestConfig,\n    disableProgressBar,\n  });\n\n  return client.sign({\n    xpiPath,\n    guid: id,\n    version,\n    channel,\n  });\n};\n\n/**\n * @param {SignAddonParams} options\n * @param {{\n *   systemProcess?: typeof process,\n *   throwError?: boolean,\n *   logger?: typeof console\n * }} extras\n * @returns {Promise<void>}\n */\nexport const signAddonAndExit = async (\n  options,\n  { systemProcess = process, throwError = false, logger = console },\n) => {\n  try {\n    const result = await signAddon(options);\n    logger.log(result.success ? 'SUCCESS' : 'FAIL');\n    systemProcess.exit(result.success ? 0 : 1);\n  } catch (/** @type {any} */ err) {\n    logger.error('FAIL');\n\n    if (throwError) {\n      throw err;\n    }\n\n    logger.error(err.stack);\n    systemProcess.exit(1);\n  }\n};\n\nexport default {\n  signAddon,\n  signAddonAndExit,\n};\n"], "mappings": "AAAA,SAASA,EAAE,QAAQ,IAAI;AAEvB,SAASC,MAAM,IAAIC,gBAAgB,QAAQ,iBAAiB;;AAE5D;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,SAAS,GAAG,MAAAA,CAAO;EAC9B;EACAC,OAAO;EACP;EACAC,EAAE;EACF;EACAC,OAAO;EACP;EACAC,MAAM;EACN;EACAC,SAAS;EACT;EACAC,YAAY,GAAG,mCAAmC;EAClD;EACA;EACAC,eAAe;EACfC,OAAO,GAAG,KAAK;EACf;EACA;EACA;EACAC,OAAO;EACP;EACA;EACAC,OAAO;EACP;EACAC,WAAW;EACX;EACA;EACAC,QAAQ;EACR;EACA;EACAC,gBAAgB;EAChB;EACAC,kBAAkB,GAAG,KAAK;EAC1BC,SAAS,GAAGhB;AACd,CAAC,KAAK;EACJ;AACF;AACA;EACE,SAASiB,WAAWA,CAACC,IAAI,EAAE;IACzB,MAAM,IAAIC,KAAK,CAAE,gCAA+BD,IAAK,EAAC,CAAC;EACzD;EAEA,IAAI,CAAChB,OAAO,EAAE;IACZe,WAAW,CAAC,SAAS,CAAC;EACxB;EAEA,IAAI,CAACb,OAAO,EAAE;IACZa,WAAW,CAAC,SAAS,CAAC;EACxB;EAEA,IAAI,CAACX,SAAS,EAAE;IACdW,WAAW,CAAC,WAAW,CAAC;EAC1B;EAEA,IAAI,CAACZ,MAAM,EAAE;IACXY,WAAW,CAAC,QAAQ,CAAC;EACvB;EAEA,IAAI;IACF,MAAMG,KAAK,GAAG,MAAMtB,EAAE,CAACuB,IAAI,CAACnB,OAAO,CAAC;IAEpC,IAAI,CAACkB,KAAK,CAACE,MAAM,EAAE;MACjB,MAAM,IAAIH,KAAK,CAAE,eAAcjB,OAAQ,EAAC,CAAC;IAC3C;EACF,CAAC,CAAC,OAAOqB,SAAS,EAAE;IAClB,MAAM,IAAIJ,KAAK,CAAE,cAAajB,OAAQ,KAAIqB,SAAU,EAAC,CAAC;EACxD;EAEA,MAAMC,MAAM,GAAG,IAAIR,SAAS,CAAC;IAC3BX,MAAM;IACNC,SAAS;IACTC,YAAY;IACZC,eAAe;IACfI,WAAW;IACXa,YAAY,EAAEhB,OAAO;IACrBiB,kBAAkB,EAAEf,OAAO;IAC3BgB,WAAW,EAAEd,QAAQ;IACrBe,aAAa,EAAEd,gBAAgB;IAC/BC;EACF,CAAC,CAAC;EAEF,OAAOS,MAAM,CAACK,IAAI,CAAC;IACjB3B,OAAO;IACP4B,IAAI,EAAE3B,EAAE;IACRC,OAAO;IACPM;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMqB,gBAAgB,GAAG,MAAAA,CAC9BC,OAAO,EACP;EAAEC,aAAa,GAAGC,OAAO;EAAEC,UAAU,GAAG,KAAK;EAAEC,MAAM,GAAGC;AAAQ,CAAC,KAC9D;EACH,IAAI;IACF,MAAMC,MAAM,GAAG,MAAMrC,SAAS,CAAC+B,OAAO,CAAC;IACvCI,MAAM,CAACG,GAAG,CAACD,MAAM,CAACE,OAAO,GAAG,SAAS,GAAG,MAAM,CAAC;IAC/CP,aAAa,CAACQ,IAAI,CAACH,MAAM,CAACE,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;EAC5C,CAAC,CAAC,QAAO,kBAAmBE,GAAG,EAAE;IAC/BN,MAAM,CAACO,KAAK,CAAC,MAAM,CAAC;IAEpB,IAAIR,UAAU,EAAE;MACd,MAAMO,GAAG;IACX;IAEAN,MAAM,CAACO,KAAK,CAACD,GAAG,CAACE,KAAK,CAAC;IACvBX,aAAa,CAACQ,IAAI,CAAC,CAAC,CAAC;EACvB;AACF,CAAC;AAED,eAAe;EACbxC,SAAS;EACT8B;AACF,CAAC"}