{"version": 3, "file": "amo-client.js", "names": ["defaultFs", "url", "path", "deepcopy", "defaultJwt", "defaultRequest", "oneLine", "PseudoProgress", "formatResponse", "response", "overrides", "options", "_stringify<PERSON><PERSON><PERSON><PERSON>", "JSON", "stringify", "max<PERSON><PERSON><PERSON>", "prettyResponse", "e", "length", "substring", "toString", "getUrlBasename", "absUrl", "url<PERSON><PERSON>", "basename", "parse", "parts", "split", "Client", "constructor", "<PERSON><PERSON><PERSON><PERSON>", "apiSecret", "apiUrlPrefix", "apiJwtExpiresIn", "debugLogging", "statusCheckInterval", "statusCheckTimeout", "logger", "console", "downloadDir", "process", "cwd", "fs", "request", "proxyServer", "requestConfig", "progressBar", "disableP<PERSON>ress<PERSON>ar", "_progressBar", "preamble", "_fs", "_request", "sign", "guid", "version", "channel", "xpiPath", "formData", "upload", "createReadStream", "addonUrl", "httpMethod", "put", "encodeURIComponent", "debug", "post", "warn", "bind", "throwOnBadResponse", "then", "httpResponse", "body", "acceptableStatuses", "receivedError", "error", "indexOf", "statusCode", "Promise", "resolve", "success", "id", "downloadedFiles", "errorCode", "errorDetails", "Error", "absoluteURL", "headers", "waitForSignedAddon", "statusUrl", "_clearTimeout", "clearTimeout", "_setAbortTimeout", "setTimeout", "_setStatusCheckTimeout", "reject", "_this$_progressBar6", "abortTimeout", "_this$_progressBar", "finish", "checkSignedStatus", "status", "get", "canBeAutoSigned", "automated_signing", "signedAndReady", "valid", "active", "reviewed", "files", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_this$_progressBar2", "log", "result", "downloadSignedFiles", "err", "checkValidationStatus", "processed", "_this$_progressBar3", "_this$_progressBar4", "_this$_progressBar5", "validation_url", "setPreamble", "animate", "signedFiles", "createWriteStream", "stdout", "allDownloads", "dataExpected", "dataReceived", "showProgress", "progress", "amount", "toFixed", "padding", "Array", "join", "write", "download", "fileUrl", "fileName", "out", "configureRequest", "method", "followRedirect", "on", "contentLength", "parseInt", "chunk", "pipe", "foundUnsignedFiles", "for<PERSON>ach", "file", "signed", "push", "download_url", "all", "replace", "requestConf", "patch", "delete", "urlString", "match", "config", "jwt", "String", "proxy", "authToken", "iss", "algorithm", "expiresIn", "timeout", "Authorization", "Accept", "toLowerCase", "toUpperCase", "requestMethod", "responseBody", "redact", "obj", "hdr", "Object", "keys", "key", "args", "prototype", "map", "call", "arguments", "val", "newVal"], "sources": ["../src/amo-client.js"], "sourcesContent": ["/* eslint max-classes-per-file: 0 */\nimport defaultFs from 'fs';\nimport url from 'url';\nimport path from 'path';\n\nimport deepcopy from 'deepcopy';\nimport defaultJwt from 'jsonwebtoken';\nimport defaultRequest from 'request';\nimport { oneLine } from 'common-tags';\n\nimport PseudoProgress from './PseudoProgress.js';\n\n/** @typedef {import(\"request\").OptionsWithUrl} RequestConfig */\n\n/** @typedef {import(\"request\").Response} Response */\n\n/**\n * @typedef {\"listed\" | \"unlisted\"} ReleaseChannel\n */\n\n/**\n * See: https://addons-server.readthedocs.io/en/latest/topics/api/signing.html#checking-the-status-of-your-upload\n *\n * @typedef {{\n *   guid: string,\n *   active: boolean,\n *   automated_signing: boolean,\n *   files: File[],\n *   passed_review: boolean,\n *   pk: string,\n *   processed: boolean,\n *   reviewed: boolean,\n *   url: string,\n *   valid: boolean,\n *   validation_results: object,\n *   validation_url: string,\n *   version: string,\n * }} SigningStatus\n */\n\n/**\n * @typedef {object} ClientParams\n * @property {string} apiKey - API key string from the Developer Hub\n * @property {string} apiSecret - API secret string from the Developer Hub\n * @property {string} apiUrlPrefix - API URL prefix, including any leading paths\n * @property {number=} apiJwtExpiresIn - Number of seconds until the JWT token for the API request expires. This must match the expiration time that the API server accepts\n * @property {boolean=} debugLogging - When true, log more information\n * @property {number=} statusCheckInterval - A period in millesconds between checks when waiting on add-on status\n * @property {number=} statusCheckTimeout -  A length in millesconds to give up if the add-on hasn't been validated and signed\n * @property {typeof console=} logger\n * @property {string=} downloadDir - Absolute path to save downloaded files to. The working directory will be used by default\n * @property {typeof defaultFs=} fs\n * @property {typeof defaultRequest=} request\n * @property {string=} proxyServer - Optional proxy server to use for all requests, such as \"http://yourproxy:6000\"\n * @property {RequestConfig=} requestConfig - Optional configuration object to pass to request(). Not all parameters are guaranteed to be applied\n * @property {PseudoProgress=} progressBar\n * @property {boolean=} disableProgressBar - When true, disables progress bar\n */\n\n/**\n * @typedef {object} SignParams\n * @property {string=} guid - optional add-on GUID (ID in install.rdf)\n * @property {string} version - add-on version string\n * @property {ReleaseChannel=} channel - release channel (listed or unlisted)\n * @property {string} xpiPath - path to xpi file\n */\n\n/**\n * @typedef {(\"SERVER_FAILURE\"|\"ADDON_NOT_AUTO_SIGNED\"|\"VALIDATION_FAILED\")} SignErrorCode\n */\n\n/**\n * @typedef {{\n *   success: boolean,\n *   id: string | null,\n *   downloadedFiles: string[] | null,\n *   errorCode: SignErrorCode | null,\n *   errorDetails: string | null\n * }} SignResult\n */\n\n/**\n * Returns a nicely formatted HTTP response.\n * This makes the response suitable for logging.\n *\n * @param {string|object} response - either the response's body or an object representing a JSON API response.\n * @param {object=} overrides\n * @returns {string}\n */\nexport function formatResponse(response, overrides = {}) {\n  const options = {\n    _stringifyToJson: JSON.stringify,\n    maxLength: 500,\n    ...overrides,\n  };\n  let prettyResponse = response;\n  const stringify = options._stringifyToJson || JSON.stringify;\n  if (typeof prettyResponse === 'object') {\n    try {\n      prettyResponse = stringify(prettyResponse);\n    } catch (e) {\n      //\n    }\n  }\n  if (typeof prettyResponse === 'string') {\n    if (prettyResponse.length > options.maxLength) {\n      prettyResponse = `${prettyResponse.substring(0, options.maxLength)}...`;\n    }\n  }\n  return prettyResponse.toString();\n}\n\n/**\n * Returns the basename of a URL, suitable for saving to disk.\n *\n * @param {string} absUrl\n * @returns {string}\n */\nexport function getUrlBasename(absUrl) {\n  // TODO: `url.parse()` might return `undefined` so we need to check that first.\n  // @ts-ignore\n  const urlPath = path.basename(url.parse(absUrl).path);\n  const parts = urlPath.split('?');\n\n  return parts[0];\n}\n/**\n * addons.mozilla.org API client.\n */\nexport class Client {\n  /**\n   * Type for `this.request()`.\n   *\n   * @typedef {object} RequestMethodOptions\n   * @property {boolean=} throwOnBadResponse - if true, an error will be thrown when response status is not 2xx\n   */\n\n  /**\n   * Type for `this.request()`.\n   *\n   * @typedef {Promise<[Response, SigningStatus]>} RequestMethodReturnValue\n   */\n\n  /**\n   * See: https://addons-server.readthedocs.io/en/latest/topics/api/signing.html#get--api-v4-addons-(string-guid)-versions-(string-version)-[uploads-(string-upload-pk)-]\n   *\n   * @typedef {{ signed: boolean, download_url: string, hash: string }} File\n   */\n\n  /**\n   * @param {ClientParams} params\n   */\n  constructor({\n    apiKey,\n    apiSecret,\n    apiUrlPrefix,\n    // TODO: put this back to something sane after we\n    // address the file upload issue on AMO:\n    // https://github.com/mozilla/addons-server/issues/3688\n    apiJwtExpiresIn = 60 * 5, // 5 minutes\n    debugLogging = false,\n    statusCheckInterval = 1000,\n    statusCheckTimeout = 900000, // 15 minutes.\n    logger = console,\n    downloadDir = process.cwd(),\n    fs = defaultFs,\n    request = defaultRequest,\n    proxyServer,\n    requestConfig,\n    progressBar,\n    disableProgressBar = false,\n  }) {\n    this.apiKey = apiKey;\n    this.apiSecret = apiSecret;\n    this.apiUrlPrefix = apiUrlPrefix; // default set in CLI options.\n    this.apiJwtExpiresIn = apiJwtExpiresIn;\n    this.statusCheckInterval = statusCheckInterval;\n    this.statusCheckTimeout = statusCheckTimeout;\n    this.debugLogging = debugLogging;\n    this.logger = logger;\n    this.downloadDir = downloadDir;\n    this.proxyServer = proxyServer;\n    this.requestConfig = requestConfig || {};\n\n    // Set up external dependencies, allowing for overrides.\n    if (!disableProgressBar) {\n      this._progressBar =\n        progressBar ||\n        new PseudoProgress({\n          preamble: 'Validating add-on',\n        });\n    }\n    this._fs = fs;\n    this._request = request;\n  }\n\n  /**\n   * Sign a new version of your add-on at addons.mozilla.org.\n   *\n   * @param {SignParams} signParams\n   * @returns {Promise<SignResult>}\n   */\n  sign({ guid, version, channel, xpiPath }) {\n    /**\n     * @type {{\n     *   upload: defaultFs.ReadStream;\n     *   channel?: string;\n     *   version?: string;\n     * }}\n     */\n    const formData = {\n      upload: this._fs.createReadStream(xpiPath),\n    };\n    let addonUrl = '/addons/';\n    let httpMethod = this.put;\n    if (guid) {\n      // PUT to a specific URL for this add-on + version.\n      addonUrl += `${encodeURIComponent(guid)}/versions/${encodeURIComponent(\n        version,\n      )}/`;\n      if (channel) {\n        formData.channel = channel;\n      }\n    } else {\n      // POST to a generic URL to create a new add-on.\n      this.debug('Signing add-on without an ID');\n      httpMethod = this.post;\n      formData.version = version;\n      if (channel) {\n        this.logger.warn(\n          'Specifying a channel for a new add-on is unsupported. ' +\n            'New add-ons are always in the unlisted channel.',\n        );\n      }\n    }\n\n    return httpMethod\n      .bind(this)(\n        {\n          url: addonUrl,\n          formData,\n        },\n        {\n          throwOnBadResponse: false,\n        },\n      )\n      .then(\n        /**\n         * @param {[\n         *   Response,\n         *   { error?: string, headers?: {[name: string]: string}, url: string }\n         * ]} requestValue\n         * @returns {Promise<SignResult>} result\n         */\n        ([httpResponse, body]) => {\n          const response = body;\n\n          const acceptableStatuses = [200, 201, 202];\n          const receivedError = !!response.error;\n          if (\n            acceptableStatuses.indexOf(httpResponse.statusCode) === -1 ||\n            receivedError\n          ) {\n            if (response.error) {\n              this.logger.error(\n                `Server response: ${response.error}`,\n                `(status: ${httpResponse.statusCode})`,\n              );\n              return Promise.resolve({\n                success: false,\n                id: null,\n                downloadedFiles: null,\n                errorCode: 'SERVER_FAILURE',\n                errorDetails: response.error,\n              });\n            }\n\n            throw new Error(\n              `Received bad response from the server while requesting ${this.absoluteURL(\n                addonUrl,\n              )}\\n\\n` +\n                `status: ${httpResponse.statusCode}\\n` +\n                `response: ${formatResponse(response)}\\n` +\n                `headers: ${JSON.stringify(httpResponse.headers || {})}\\n`,\n            );\n          }\n\n          return this.waitForSignedAddon(response.url);\n        },\n      );\n  }\n\n  /**\n   * Poll a status URL, waiting for the queued add-on to be signed.\n   *\n   * @typedef {object} WaitForSignedAddonParams\n   * @property {typeof clearTimeout=} _clearTimeout\n   * @property {typeof setTimeout=} _setAbortTimeout\n   * @property {typeof setTimeout=} _setStatusCheckTimeout\n   *\n   * @param {string} statusUrl - URL to GET for add-on status\n   * @param {WaitForSignedAddonParams} options\n   * @returns {Promise<SignResult>}\n   */\n  waitForSignedAddon(\n    statusUrl,\n    {\n      _clearTimeout = clearTimeout,\n      _setAbortTimeout = setTimeout,\n      _setStatusCheckTimeout = setTimeout,\n    } = {},\n  ) {\n    return new Promise((resolve, reject) => {\n      /** @type {NodeJS.Timer} */\n      let statusCheckTimeout;\n\n      /** @type {NodeJS.Timer} */\n      const abortTimeout = _setAbortTimeout(() => {\n        this._progressBar?.finish();\n        _clearTimeout(statusCheckTimeout);\n\n        reject(\n          new Error(oneLine`Signing is still pending, you will receive an email\n            once there is an update on the status of your submission. If you\n            don’t see the email after 24 hours, please check your Spam\n            folder.`),\n        );\n      }, this.statusCheckTimeout);\n\n      // This function polls the API until the add-on is signed or requires\n      // manual review. If the add-on is signed, we download the signed files.\n      //\n      // This function resolves the main `Promise` in both cases.\n      const checkSignedStatus = async () => {\n        try {\n          const [\n            // eslint-disable-next-line no-unused-vars\n            httpResponse,\n            status,\n          ] = await this.get({ url: statusUrl });\n          const canBeAutoSigned = status.automated_signing;\n          // The add-on passed validation and all files have been created. There\n          // are many checks for this state because the data will be updated\n          // incrementally by the API server.\n          const signedAndReady =\n            status.valid &&\n            status.active &&\n            status.reviewed &&\n            status.files &&\n            status.files.length > 0;\n          // The add-on is valid but requires a manual review before it can be\n          // signed.\n          const requiresManualReview = status.valid && !canBeAutoSigned;\n\n          if (signedAndReady || requiresManualReview) {\n            this._progressBar?.finish();\n            _clearTimeout(abortTimeout);\n\n            if (requiresManualReview) {\n              this.logger.log(oneLine`Your add-on has been submitted for review.\n              It passed validation but could not be automatically signed\n              because this is a listed add-on.`);\n\n              resolve({\n                success: false,\n                id: null,\n                downloadedFiles: null,\n                errorCode: 'ADDON_NOT_AUTO_SIGNED',\n                errorDetails: null,\n              });\n              return;\n            }\n\n            if (signedAndReady) {\n              // TODO: show some validation warnings if there are any. We should\n              // show things like \"missing update URL in manifest\"\n              const result = await this.downloadSignedFiles(status.files);\n              resolve({ ...result, id: status.guid });\n            }\n          } else {\n            // The add-on has not been fully processed yet.\n            statusCheckTimeout = _setStatusCheckTimeout(\n              checkSignedStatus,\n              this.statusCheckInterval,\n            );\n          }\n        } catch (err) {\n          _clearTimeout(abortTimeout);\n          reject(err);\n        }\n      };\n\n      // This function polls the API until the add-on is processed/validated.\n      // This function only rejects when the add-on is not valid. When the\n      // add-on is valid, we call `checkSignedStatus()`.\n      const checkValidationStatus = async () => {\n        try {\n          const [\n            // eslint-disable-next-line no-unused-vars\n            httpResponse,\n            status,\n          ] = await this.get({ url: statusUrl });\n          if (status.processed) {\n            this._progressBar?.finish();\n            this.logger.log('Validation results:', status.validation_url);\n            // Update pseudo progress preamble for the signing step.\n            this._progressBar?.setPreamble('Signing add-on');\n            this._progressBar?.animate();\n\n            if (status.valid) {\n              checkSignedStatus();\n            } else {\n              this.logger.log(\n                'Your add-on failed validation and could not be signed',\n              );\n\n              _clearTimeout(abortTimeout);\n\n              resolve({\n                success: false,\n                id: null,\n                downloadedFiles: null,\n                errorCode: 'VALIDATION_FAILED',\n                errorDetails: status.validation_url,\n              });\n            }\n          } else {\n            // Validation is not completed yet.\n            statusCheckTimeout = _setStatusCheckTimeout(\n              checkValidationStatus,\n              this.statusCheckInterval,\n            );\n          }\n        } catch (err) {\n          _clearTimeout(abortTimeout);\n          reject(err);\n        }\n      };\n\n      // Goooo\n      this._progressBar?.animate();\n      checkValidationStatus();\n    });\n  }\n\n  /**\n   * Download the signed files.\n   *\n   * @param {File[]} signedFiles - Array of file objects returned from the API.\n   * @param {{\n   *   createWriteStream?: typeof defaultFs.createWriteStream,\n   *   request?: typeof defaultRequest,\n   *   stdout?: typeof process.stdout\n   * }} options\n   * @returns {Promise<SignResult>}\n   */\n  async downloadSignedFiles(\n    signedFiles,\n    {\n      createWriteStream = defaultFs.createWriteStream,\n      request = this._request,\n      stdout = process.stdout,\n    } = {},\n  ) {\n    /** @type {Promise<string>[]} */\n    const allDownloads = [];\n    /** @type {null | number} */\n    let dataExpected = null;\n    let dataReceived = 0;\n\n    function showProgress() {\n      let progress = '...';\n      if (dataExpected !== null) {\n        const amount = ((dataReceived / dataExpected) * 100).toFixed();\n        // Pad the percentage amount so that the line length is consistent.\n        // This should do something like '  0%', ' 25%', '100%'\n        let padding = '';\n        try {\n          padding = Array(4 - amount.length).join(' ');\n        } catch (e) {\n          // Ignore Invalid array length and such.\n        }\n        progress = `${padding + amount}% `;\n      }\n      stdout.write(`\\rDownloading signed files: ${progress}`);\n    }\n\n    /**\n     * @param {string} fileUrl\n     * @returns {Promise<string>}\n     */\n    const download = (fileUrl) => {\n      return new Promise((resolve, reject) => {\n        // The API will give us a signed file named in a sane way.\n        const fileName = path.join(this.downloadDir, getUrlBasename(fileUrl));\n        const out = createWriteStream(fileName);\n\n        request(\n          this.configureRequest({\n            method: 'GET',\n            url: fileUrl,\n            followRedirect: true,\n          }),\n        )\n          .on('error', reject)\n          .on(\n            'response',\n            /**\n             * @param {Response} response\n             * @returns {void}\n             */\n            (response) => {\n              if (response.statusCode < 200 || response.statusCode >= 300) {\n                throw new Error(\n                  `Got a ${response.statusCode} response ` +\n                    `when downloading ${fileUrl}`,\n                );\n              }\n              const contentLength = response.headers['content-length'];\n              if (contentLength) {\n                if (dataExpected !== null) {\n                  dataExpected += parseInt(contentLength, 10);\n                } else {\n                  dataExpected = parseInt(contentLength, 10);\n                }\n              }\n            },\n          )\n          .on(\n            'data',\n            /**\n             * @param {string} chunk\n             * @returns {void}\n             */\n            (chunk) => {\n              dataReceived += chunk.length;\n              showProgress();\n            },\n          )\n          .pipe(out)\n          .on('error', reject);\n\n        out.on('finish', function () {\n          stdout.write('\\n'); // end the progress output\n          resolve(fileName);\n        });\n      });\n    };\n\n    let foundUnsignedFiles = false;\n    signedFiles.forEach((file) => {\n      if (file.signed) {\n        allDownloads.push(download(file.download_url));\n      } else {\n        this.debug('This file was not signed:', file);\n\n        foundUnsignedFiles = true;\n      }\n    });\n\n    let downloadedFiles;\n    if (allDownloads.length) {\n      if (foundUnsignedFiles) {\n        this.logger.log(oneLine`Some files were not signed. Re-run with\n        --verbose for details.`);\n      }\n\n      showProgress();\n\n      downloadedFiles = await Promise.all(allDownloads);\n    } else {\n      throw new Error(oneLine`The XPI was processed but no signed files were\n      found. Check your manifest and make sure it targets Firefox as an\n      application.`);\n    }\n\n    this.logger.log('Downloaded:');\n    downloadedFiles.forEach((fileName) => {\n      this.logger.log(`    ${fileName.replace(process.cwd(), '.')}`);\n    });\n\n    return {\n      success: true,\n      id: null,\n      downloadedFiles,\n      errorCode: null,\n      errorDetails: null,\n    };\n  }\n\n  /**\n   * Make a GET request.\n   *\n   * @param {RequestConfig} requestConf\n   * @param {RequestMethodOptions=} options\n   * @returns {RequestMethodReturnValue}\n   */\n  get(requestConf, options) {\n    return this.request('get', requestConf, options);\n  }\n\n  /**\n   * Make a POST request.\n   *\n   * @param {RequestConfig} requestConf\n   * @param {RequestMethodOptions=} options\n   * @returns {RequestMethodReturnValue}\n   */\n  post(requestConf, options) {\n    return this.request('post', requestConf, options);\n  }\n\n  /**\n   * Make a PUT request.\n   *\n   * @param {RequestConfig} requestConf\n   * @param {RequestMethodOptions=} options\n   * @returns {RequestMethodReturnValue}\n   */\n  put(requestConf, options) {\n    return this.request('put', requestConf, options);\n  }\n\n  /**\n   * Make a PATCH request.\n   *\n   * @param {RequestConfig} requestConf\n   * @param {RequestMethodOptions=} options\n   * @returns {RequestMethodReturnValue}\n   */\n  patch(requestConf, options) {\n    return this.request('patch', requestConf, options);\n  }\n\n  /**\n   * Make a DELETE request.\n   *\n   * @param {RequestConfig} requestConf\n   * @param {RequestMethodOptions=} options\n   * @returns {RequestMethodReturnValue}\n   */\n  delete(requestConf, options) {\n    return this.request('delete', requestConf, options);\n  }\n\n  /**\n   * Returns a URL that is guaranteed to be absolute.\n   *\n   * @param {string} urlString - a relative or already absolute URL\n   * @returns {string} url - an absolute URL, prefixed by the API prefix if necessary.\n   */\n  absoluteURL(urlString) {\n    if (!urlString.match(/^http/i)) {\n      return this.apiUrlPrefix + urlString;\n    }\n\n    return urlString;\n  }\n\n  /**\n   * Configures a request with defaults such as authentication headers.\n   *\n   * @param {RequestConfig} config - as accepted by the `request` module\n   * @param {{ jwt?: typeof defaultJwt}} options\n   * @returns {RequestConfig}\n   */\n  configureRequest(config, { jwt = defaultJwt } = {}) {\n    const requestConf = {\n      ...this.requestConfig,\n      ...config,\n    };\n\n    if (!requestConf.url) {\n      throw new Error('request URL was not specified');\n    }\n\n    // eslint-disable-next-line no-param-reassign\n    requestConf.url = this.absoluteURL(String(requestConf.url));\n\n    if (this.proxyServer) {\n      // eslint-disable-next-line no-param-reassign\n      requestConf.proxy = this.proxyServer;\n    }\n\n    const authToken = jwt.sign({ iss: this.apiKey }, this.apiSecret, {\n      algorithm: 'HS256',\n      expiresIn: this.apiJwtExpiresIn,\n    });\n\n    // Make sure the request won't time out before the JWT expires.\n    // This may be useful for slow file uploads.\n    // eslint-disable-next-line no-param-reassign\n    requestConf.timeout = this.apiJwtExpiresIn * 1000 + 500;\n\n    // eslint-disable-next-line no-param-reassign\n    requestConf.headers = {\n      Authorization: `JWT ${authToken}`,\n      Accept: 'application/json',\n      ...requestConf.headers,\n    };\n\n    return requestConf;\n  }\n\n  /**\n   * Make any HTTP request to the addons.mozilla.org API.\n   *\n   * This includes the necessary authorization header.\n   *\n   * The returned promise will be resolved with an array of arguments that\n   * match the arguments sent to the callback as specified in the `request`\n   * module.\n   *\n   * @param {string} httpMethod - HTTP method name.\n   * @param {RequestConfig} config - options accepted by the `request` module\n   * @param {RequestMethodOptions} options\n   * @returns {RequestMethodReturnValue}\n   */\n  async request(httpMethod, config, { throwOnBadResponse = true } = {}) {\n    const method = httpMethod.toLowerCase();\n    const requestConf = this.configureRequest(config);\n\n    let [\n      // eslint-disable-next-line prefer-const\n      httpResponse,\n      body,\n    ] = await new Promise((resolve, reject) => {\n      this.debug(`[API] ${method.toUpperCase()} request:\\n`, requestConf);\n\n      // Get the caller, like request.get(), request.put() ...\n      // @ts-ignore\n      const requestMethod = this._request[method].bind(this._request);\n      // Wrap the request callback in a promise. Here is an example without\n      // promises:\n      //\n      // request.put(requestConf, function(err, httpResponse, body) {\n      //   // promise gets resolved here\n      // })\n      requestMethod(\n        /** @type RequestConfig */\n        requestConf,\n        /**\n         * @param {Error} error\n         * @param {Response} response\n         * @param {string} responseBody\n         */\n        (error, response, responseBody) => {\n          if (error) {\n            reject(error);\n            return;\n          }\n\n          resolve([response, responseBody]);\n        },\n      );\n    });\n\n    if (throwOnBadResponse) {\n      if (httpResponse.statusCode > 299 || httpResponse.statusCode < 200) {\n        throw new Error(\n          `Received bad response from ${this.absoluteURL(\n            String(requestConf.url),\n          )}; ` +\n            `status: ${httpResponse.statusCode}; ` +\n            `response: ${formatResponse(body)}`,\n        );\n      }\n    }\n\n    if (\n      httpResponse.headers &&\n      httpResponse.headers['content-type'] === 'application/json' &&\n      typeof body === 'string'\n    ) {\n      try {\n        body = JSON.parse(body);\n      } catch (e) {\n        this.logger.log('Failed to parse JSON response from server:', e);\n      }\n    }\n\n    this.debug(\n      `[API] ${method.toUpperCase()} response:\\n`,\n      `Status: ${httpResponse.statusCode}\\n`,\n      { headers: httpResponse.headers, response: body },\n    );\n\n    return [httpResponse, body];\n  }\n\n  /**\n   * Output some debugging info if this instance is configured for it.\n   */\n  debug() {\n    if (!this.debugLogging) {\n      return;\n    }\n\n    /**\n     * @param {{ headers: {[key: string]: string} } & {[prop: string]: any}} obj\n     */\n    function redact(obj) {\n      if (typeof obj !== 'object' || !obj) {\n        return obj;\n      }\n      if (obj.headers) {\n        ['Authorization', 'cookie', 'set-cookie'].forEach(function (hdr) {\n          if (obj.headers[hdr]) {\n            // eslint-disable-next-line no-param-reassign\n            obj.headers[hdr] = '<REDACTED>';\n          }\n        });\n      }\n\n      Object.keys(obj).forEach(function (key) {\n        // eslint-disable-next-line no-param-reassign\n        obj[key] = redact(obj[key]);\n      });\n\n      return obj;\n    }\n\n    // TODO: remove the use of `arguments`\n    // eslint-disable-next-line prefer-rest-params\n    const args = Array.prototype.map.call(arguments, function (val) {\n      let newVal = val;\n      if (typeof newVal === 'object') {\n        newVal = deepcopy(newVal);\n        newVal = redact(newVal);\n      }\n      return newVal;\n    });\n    this.logger.log('[sign-addon]', ...args);\n  }\n}\n"], "mappings": "AAAA;AACA,OAAOA,SAAS,MAAM,IAAI;AAC1B,OAAOC,GAAG,MAAM,KAAK;AACrB,OAAOC,IAAI,MAAM,MAAM;AAEvB,OAAOC,QAAQ,MAAM,UAAU;AAC/B,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,cAAc,MAAM,SAAS;AACpC,SAASC,OAAO,QAAQ,aAAa;AAErC,OAAOC,cAAc,MAAM,qBAAqB;;AAEhD;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,QAAQ,EAAEC,SAAS,GAAG,CAAC,CAAC,EAAE;EACvD,MAAMC,OAAO,GAAG;IACdC,gBAAgB,EAAEC,IAAI,CAACC,SAAS;IAChCC,SAAS,EAAE,GAAG;IACd,GAAGL;EACL,CAAC;EACD,IAAIM,cAAc,GAAGP,QAAQ;EAC7B,MAAMK,SAAS,GAAGH,OAAO,CAACC,gBAAgB,IAAIC,IAAI,CAACC,SAAS;EAC5D,IAAI,OAAOE,cAAc,KAAK,QAAQ,EAAE;IACtC,IAAI;MACFA,cAAc,GAAGF,SAAS,CAACE,cAAc,CAAC;IAC5C,CAAC,CAAC,OAAOC,CAAC,EAAE;MACV;IAAA;EAEJ;EACA,IAAI,OAAOD,cAAc,KAAK,QAAQ,EAAE;IACtC,IAAIA,cAAc,CAACE,MAAM,GAAGP,OAAO,CAACI,SAAS,EAAE;MAC7CC,cAAc,GAAI,GAAEA,cAAc,CAACG,SAAS,CAAC,CAAC,EAAER,OAAO,CAACI,SAAS,CAAE,KAAI;IACzE;EACF;EACA,OAAOC,cAAc,CAACI,QAAQ,EAAE;AAClC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,MAAM,EAAE;EACrC;EACA;EACA,MAAMC,OAAO,GAAGrB,IAAI,CAACsB,QAAQ,CAACvB,GAAG,CAACwB,KAAK,CAACH,MAAM,CAAC,CAACpB,IAAI,CAAC;EACrD,MAAMwB,KAAK,GAAGH,OAAO,CAACI,KAAK,CAAC,GAAG,CAAC;EAEhC,OAAOD,KAAK,CAAC,CAAC,CAAC;AACjB;AACA;AACA;AACA;AACA,OAAO,MAAME,MAAM,CAAC;EAClB;AACF;AACA;AACA;AACA;AACA;;EAEE;AACF;AACA;AACA;AACA;;EAEE;AACF;AACA;AACA;AACA;;EAEE;AACF;AACA;EACEC,WAAWA,CAAC;IACVC,MAAM;IACNC,SAAS;IACTC,YAAY;IACZ;IACA;IACA;IACAC,eAAe,GAAG,EAAE,GAAG,CAAC;IAAE;IAC1BC,YAAY,GAAG,KAAK;IACpBC,mBAAmB,GAAG,IAAI;IAC1BC,kBAAkB,GAAG,MAAM;IAAE;IAC7BC,MAAM,GAAGC,OAAO;IAChBC,WAAW,GAAGC,OAAO,CAACC,GAAG,EAAE;IAC3BC,EAAE,GAAG1C,SAAS;IACd2C,OAAO,GAAGtC,cAAc;IACxBuC,WAAW;IACXC,aAAa;IACbC,WAAW;IACXC,kBAAkB,GAAG;EACvB,CAAC,EAAE;IACD,IAAI,CAACjB,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,YAAY,GAAGA,YAAY,CAAC,CAAC;IAClC,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACE,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACF,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACG,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACE,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACK,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,aAAa,GAAGA,aAAa,IAAI,CAAC,CAAC;;IAExC;IACA,IAAI,CAACE,kBAAkB,EAAE;MACvB,IAAI,CAACC,YAAY,GACfF,WAAW,IACX,IAAIvC,cAAc,CAAC;QACjB0C,QAAQ,EAAE;MACZ,CAAC,CAAC;IACN;IACA,IAAI,CAACC,GAAG,GAAGR,EAAE;IACb,IAAI,CAACS,QAAQ,GAAGR,OAAO;EACzB;;EAEA;AACF;AACA;AACA;AACA;AACA;EACES,IAAIA,CAAC;IAAEC,IAAI;IAAEC,OAAO;IAAEC,OAAO;IAAEC;EAAQ,CAAC,EAAE;IACxC;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,MAAMC,QAAQ,GAAG;MACfC,MAAM,EAAE,IAAI,CAACR,GAAG,CAACS,gBAAgB,CAACH,OAAO;IAC3C,CAAC;IACD,IAAII,QAAQ,GAAG,UAAU;IACzB,IAAIC,UAAU,GAAG,IAAI,CAACC,GAAG;IACzB,IAAIT,IAAI,EAAE;MACR;MACAO,QAAQ,IAAK,GAAEG,kBAAkB,CAACV,IAAI,CAAE,aAAYU,kBAAkB,CACpET,OAAO,CACP,GAAE;MACJ,IAAIC,OAAO,EAAE;QACXE,QAAQ,CAACF,OAAO,GAAGA,OAAO;MAC5B;IACF,CAAC,MAAM;MACL;MACA,IAAI,CAACS,KAAK,CAAC,8BAA8B,CAAC;MAC1CH,UAAU,GAAG,IAAI,CAACI,IAAI;MACtBR,QAAQ,CAACH,OAAO,GAAGA,OAAO;MAC1B,IAAIC,OAAO,EAAE;QACX,IAAI,CAAClB,MAAM,CAAC6B,IAAI,CACd,wDAAwD,GACtD,iDAAiD,CACpD;MACH;IACF;IAEA,OAAOL,UAAU,CACdM,IAAI,CAAC,IAAI,CAAC,CACT;MACElE,GAAG,EAAE2D,QAAQ;MACbH;IACF,CAAC,EACD;MACEW,kBAAkB,EAAE;IACtB,CAAC,CACF,CACAC,IAAI;IACH;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,CAAC,CAACC,YAAY,EAAEC,IAAI,CAAC,KAAK;MACxB,MAAM9D,QAAQ,GAAG8D,IAAI;MAErB,MAAMC,kBAAkB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC1C,MAAMC,aAAa,GAAG,CAAC,CAAChE,QAAQ,CAACiE,KAAK;MACtC,IACEF,kBAAkB,CAACG,OAAO,CAACL,YAAY,CAACM,UAAU,CAAC,KAAK,CAAC,CAAC,IAC1DH,aAAa,EACb;QACA,IAAIhE,QAAQ,CAACiE,KAAK,EAAE;UAClB,IAAI,CAACrC,MAAM,CAACqC,KAAK,CACd,oBAAmBjE,QAAQ,CAACiE,KAAM,EAAC,EACnC,YAAWJ,YAAY,CAACM,UAAW,GAAE,CACvC;UACD,OAAOC,OAAO,CAACC,OAAO,CAAC;YACrBC,OAAO,EAAE,KAAK;YACdC,EAAE,EAAE,IAAI;YACRC,eAAe,EAAE,IAAI;YACrBC,SAAS,EAAE,gBAAgB;YAC3BC,YAAY,EAAE1E,QAAQ,CAACiE;UACzB,CAAC,CAAC;QACJ;QAEA,MAAM,IAAIU,KAAK,CACZ,0DAAyD,IAAI,CAACC,WAAW,CACxEzB,QAAQ,CACR,MAAK,GACJ,WAAUU,YAAY,CAACM,UAAW,IAAG,GACrC,aAAYpE,cAAc,CAACC,QAAQ,CAAE,IAAG,GACxC,YAAWI,IAAI,CAACC,SAAS,CAACwD,YAAY,CAACgB,OAAO,IAAI,CAAC,CAAC,CAAE,IAAG,CAC7D;MACH;MAEA,OAAO,IAAI,CAACC,kBAAkB,CAAC9E,QAAQ,CAACR,GAAG,CAAC;IAC9C,CAAC,CACF;EACL;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEsF,kBAAkBA,CAChBC,SAAS,EACT;IACEC,aAAa,GAAGC,YAAY;IAC5BC,gBAAgB,GAAGC,UAAU;IAC7BC,sBAAsB,GAAGD;EAC3B,CAAC,GAAG,CAAC,CAAC,EACN;IACA,OAAO,IAAIf,OAAO,CAAC,CAACC,OAAO,EAAEgB,MAAM,KAAK;MAAA,IAAAC,mBAAA;MACtC;MACA,IAAI3D,kBAAkB;;MAEtB;MACA,MAAM4D,YAAY,GAAGL,gBAAgB,CAAC,MAAM;QAAA,IAAAM,kBAAA;QAC1C,CAAAA,kBAAA,OAAI,CAACjD,YAAY,cAAAiD,kBAAA,uBAAjBA,kBAAA,CAAmBC,MAAM,EAAE;QAC3BT,aAAa,CAACrD,kBAAkB,CAAC;QAEjC0D,MAAM,CACJ,IAAIV,KAAK,CAAC9E,OAAQ;AAC5B;AACA;AACA,oBAAoB,CAAC,CACZ;MACH,CAAC,EAAE,IAAI,CAAC8B,kBAAkB,CAAC;;MAE3B;MACA;MACA;MACA;MACA,MAAM+D,iBAAiB,GAAG,MAAAA,CAAA,KAAY;QACpC,IAAI;UACF,MAAM;UACJ;UACA7B,YAAY,EACZ8B,MAAM,CACP,GAAG,MAAM,IAAI,CAACC,GAAG,CAAC;YAAEpG,GAAG,EAAEuF;UAAU,CAAC,CAAC;UACtC,MAAMc,eAAe,GAAGF,MAAM,CAACG,iBAAiB;UAChD;UACA;UACA;UACA,MAAMC,cAAc,GAClBJ,MAAM,CAACK,KAAK,IACZL,MAAM,CAACM,MAAM,IACbN,MAAM,CAACO,QAAQ,IACfP,MAAM,CAACQ,KAAK,IACZR,MAAM,CAACQ,KAAK,CAAC1F,MAAM,GAAG,CAAC;UACzB;UACA;UACA,MAAM2F,oBAAoB,GAAGT,MAAM,CAACK,KAAK,IAAI,CAACH,eAAe;UAE7D,IAAIE,cAAc,IAAIK,oBAAoB,EAAE;YAAA,IAAAC,mBAAA;YAC1C,CAAAA,mBAAA,OAAI,CAAC9D,YAAY,cAAA8D,mBAAA,uBAAjBA,mBAAA,CAAmBZ,MAAM,EAAE;YAC3BT,aAAa,CAACO,YAAY,CAAC;YAE3B,IAAIa,oBAAoB,EAAE;cACxB,IAAI,CAACxE,MAAM,CAAC0E,GAAG,CAACzG,OAAQ;AACtC;AACA,+CAA+C,CAAC;cAElCwE,OAAO,CAAC;gBACNC,OAAO,EAAE,KAAK;gBACdC,EAAE,EAAE,IAAI;gBACRC,eAAe,EAAE,IAAI;gBACrBC,SAAS,EAAE,uBAAuB;gBAClCC,YAAY,EAAE;cAChB,CAAC,CAAC;cACF;YACF;YAEA,IAAIqB,cAAc,EAAE;cAClB;cACA;cACA,MAAMQ,MAAM,GAAG,MAAM,IAAI,CAACC,mBAAmB,CAACb,MAAM,CAACQ,KAAK,CAAC;cAC3D9B,OAAO,CAAC;gBAAE,GAAGkC,MAAM;gBAAEhC,EAAE,EAAEoB,MAAM,CAAC/C;cAAK,CAAC,CAAC;YACzC;UACF,CAAC,MAAM;YACL;YACAjB,kBAAkB,GAAGyD,sBAAsB,CACzCM,iBAAiB,EACjB,IAAI,CAAChE,mBAAmB,CACzB;UACH;QACF,CAAC,CAAC,OAAO+E,GAAG,EAAE;UACZzB,aAAa,CAACO,YAAY,CAAC;UAC3BF,MAAM,CAACoB,GAAG,CAAC;QACb;MACF,CAAC;;MAED;MACA;MACA;MACA,MAAMC,qBAAqB,GAAG,MAAAA,CAAA,KAAY;QACxC,IAAI;UACF,MAAM;UACJ;UACA7C,YAAY,EACZ8B,MAAM,CACP,GAAG,MAAM,IAAI,CAACC,GAAG,CAAC;YAAEpG,GAAG,EAAEuF;UAAU,CAAC,CAAC;UACtC,IAAIY,MAAM,CAACgB,SAAS,EAAE;YAAA,IAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA;YACpB,CAAAF,mBAAA,OAAI,CAACrE,YAAY,cAAAqE,mBAAA,uBAAjBA,mBAAA,CAAmBnB,MAAM,EAAE;YAC3B,IAAI,CAAC7D,MAAM,CAAC0E,GAAG,CAAC,qBAAqB,EAAEX,MAAM,CAACoB,cAAc,CAAC;YAC7D;YACA,CAAAF,mBAAA,OAAI,CAACtE,YAAY,cAAAsE,mBAAA,uBAAjBA,mBAAA,CAAmBG,WAAW,CAAC,gBAAgB,CAAC;YAChD,CAAAF,mBAAA,OAAI,CAACvE,YAAY,cAAAuE,mBAAA,uBAAjBA,mBAAA,CAAmBG,OAAO,EAAE;YAE5B,IAAItB,MAAM,CAACK,KAAK,EAAE;cAChBN,iBAAiB,EAAE;YACrB,CAAC,MAAM;cACL,IAAI,CAAC9D,MAAM,CAAC0E,GAAG,CACb,uDAAuD,CACxD;cAEDtB,aAAa,CAACO,YAAY,CAAC;cAE3BlB,OAAO,CAAC;gBACNC,OAAO,EAAE,KAAK;gBACdC,EAAE,EAAE,IAAI;gBACRC,eAAe,EAAE,IAAI;gBACrBC,SAAS,EAAE,mBAAmB;gBAC9BC,YAAY,EAAEiB,MAAM,CAACoB;cACvB,CAAC,CAAC;YACJ;UACF,CAAC,MAAM;YACL;YACApF,kBAAkB,GAAGyD,sBAAsB,CACzCsB,qBAAqB,EACrB,IAAI,CAAChF,mBAAmB,CACzB;UACH;QACF,CAAC,CAAC,OAAO+E,GAAG,EAAE;UACZzB,aAAa,CAACO,YAAY,CAAC;UAC3BF,MAAM,CAACoB,GAAG,CAAC;QACb;MACF,CAAC;;MAED;MACA,CAAAnB,mBAAA,OAAI,CAAC/C,YAAY,cAAA+C,mBAAA,uBAAjBA,mBAAA,CAAmB2B,OAAO,EAAE;MAC5BP,qBAAqB,EAAE;IACzB,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMF,mBAAmBA,CACvBU,WAAW,EACX;IACEC,iBAAiB,GAAG5H,SAAS,CAAC4H,iBAAiB;IAC/CjF,OAAO,GAAG,IAAI,CAACQ,QAAQ;IACvB0E,MAAM,GAAGrF,OAAO,CAACqF;EACnB,CAAC,GAAG,CAAC,CAAC,EACN;IACA;IACA,MAAMC,YAAY,GAAG,EAAE;IACvB;IACA,IAAIC,YAAY,GAAG,IAAI;IACvB,IAAIC,YAAY,GAAG,CAAC;IAEpB,SAASC,YAAYA,CAAA,EAAG;MACtB,IAAIC,QAAQ,GAAG,KAAK;MACpB,IAAIH,YAAY,KAAK,IAAI,EAAE;QACzB,MAAMI,MAAM,GAAG,CAAEH,YAAY,GAAGD,YAAY,GAAI,GAAG,EAAEK,OAAO,EAAE;QAC9D;QACA;QACA,IAAIC,OAAO,GAAG,EAAE;QAChB,IAAI;UACFA,OAAO,GAAGC,KAAK,CAAC,CAAC,GAAGH,MAAM,CAACjH,MAAM,CAAC,CAACqH,IAAI,CAAC,GAAG,CAAC;QAC9C,CAAC,CAAC,OAAOtH,CAAC,EAAE;UACV;QAAA;QAEFiH,QAAQ,GAAI,GAAEG,OAAO,GAAGF,MAAO,IAAG;MACpC;MACAN,MAAM,CAACW,KAAK,CAAE,+BAA8BN,QAAS,EAAC,CAAC;IACzD;;IAEA;AACJ;AACA;AACA;IACI,MAAMO,QAAQ,GAAIC,OAAO,IAAK;MAC5B,OAAO,IAAI7D,OAAO,CAAC,CAACC,OAAO,EAAEgB,MAAM,KAAK;QACtC;QACA,MAAM6C,QAAQ,GAAGzI,IAAI,CAACqI,IAAI,CAAC,IAAI,CAAChG,WAAW,EAAElB,cAAc,CAACqH,OAAO,CAAC,CAAC;QACrE,MAAME,GAAG,GAAGhB,iBAAiB,CAACe,QAAQ,CAAC;QAEvChG,OAAO,CACL,IAAI,CAACkG,gBAAgB,CAAC;UACpBC,MAAM,EAAE,KAAK;UACb7I,GAAG,EAAEyI,OAAO;UACZK,cAAc,EAAE;QAClB,CAAC,CAAC,CACH,CACEC,EAAE,CAAC,OAAO,EAAElD,MAAM,CAAC,CACnBkD,EAAE,CACD,UAAU;QACV;AACZ;AACA;AACA;QACavI,QAAQ,IAAK;UACZ,IAAIA,QAAQ,CAACmE,UAAU,GAAG,GAAG,IAAInE,QAAQ,CAACmE,UAAU,IAAI,GAAG,EAAE;YAC3D,MAAM,IAAIQ,KAAK,CACZ,SAAQ3E,QAAQ,CAACmE,UAAW,YAAW,GACrC,oBAAmB8D,OAAQ,EAAC,CAChC;UACH;UACA,MAAMO,aAAa,GAAGxI,QAAQ,CAAC6E,OAAO,CAAC,gBAAgB,CAAC;UACxD,IAAI2D,aAAa,EAAE;YACjB,IAAIlB,YAAY,KAAK,IAAI,EAAE;cACzBA,YAAY,IAAImB,QAAQ,CAACD,aAAa,EAAE,EAAE,CAAC;YAC7C,CAAC,MAAM;cACLlB,YAAY,GAAGmB,QAAQ,CAACD,aAAa,EAAE,EAAE,CAAC;YAC5C;UACF;QACF,CAAC,CACF,CACAD,EAAE,CACD,MAAM;QACN;AACZ;AACA;AACA;QACaG,KAAK,IAAK;UACTnB,YAAY,IAAImB,KAAK,CAACjI,MAAM;UAC5B+G,YAAY,EAAE;QAChB,CAAC,CACF,CACAmB,IAAI,CAACR,GAAG,CAAC,CACTI,EAAE,CAAC,OAAO,EAAElD,MAAM,CAAC;QAEtB8C,GAAG,CAACI,EAAE,CAAC,QAAQ,EAAE,YAAY;UAC3BnB,MAAM,CAACW,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;UACpB1D,OAAO,CAAC6D,QAAQ,CAAC;QACnB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IAED,IAAIU,kBAAkB,GAAG,KAAK;IAC9B1B,WAAW,CAAC2B,OAAO,CAAEC,IAAI,IAAK;MAC5B,IAAIA,IAAI,CAACC,MAAM,EAAE;QACf1B,YAAY,CAAC2B,IAAI,CAAChB,QAAQ,CAACc,IAAI,CAACG,YAAY,CAAC,CAAC;MAChD,CAAC,MAAM;QACL,IAAI,CAAC1F,KAAK,CAAC,2BAA2B,EAAEuF,IAAI,CAAC;QAE7CF,kBAAkB,GAAG,IAAI;MAC3B;IACF,CAAC,CAAC;IAEF,IAAIpE,eAAe;IACnB,IAAI6C,YAAY,CAAC5G,MAAM,EAAE;MACvB,IAAImI,kBAAkB,EAAE;QACtB,IAAI,CAAChH,MAAM,CAAC0E,GAAG,CAACzG,OAAQ;AAChC,+BAA+B,CAAC;MAC1B;MAEA2H,YAAY,EAAE;MAEdhD,eAAe,GAAG,MAAMJ,OAAO,CAAC8E,GAAG,CAAC7B,YAAY,CAAC;IACnD,CAAC,MAAM;MACL,MAAM,IAAI1C,KAAK,CAAC9E,OAAQ;AAC9B;AACA,mBAAmB,CAAC;IAChB;IAEA,IAAI,CAAC+B,MAAM,CAAC0E,GAAG,CAAC,aAAa,CAAC;IAC9B9B,eAAe,CAACqE,OAAO,CAAEX,QAAQ,IAAK;MACpC,IAAI,CAACtG,MAAM,CAAC0E,GAAG,CAAE,OAAM4B,QAAQ,CAACiB,OAAO,CAACpH,OAAO,CAACC,GAAG,EAAE,EAAE,GAAG,CAAE,EAAC,CAAC;IAChE,CAAC,CAAC;IAEF,OAAO;MACLsC,OAAO,EAAE,IAAI;MACbC,EAAE,EAAE,IAAI;MACRC,eAAe;MACfC,SAAS,EAAE,IAAI;MACfC,YAAY,EAAE;IAChB,CAAC;EACH;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEkB,GAAGA,CAACwD,WAAW,EAAElJ,OAAO,EAAE;IACxB,OAAO,IAAI,CAACgC,OAAO,CAAC,KAAK,EAAEkH,WAAW,EAAElJ,OAAO,CAAC;EAClD;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEsD,IAAIA,CAAC4F,WAAW,EAAElJ,OAAO,EAAE;IACzB,OAAO,IAAI,CAACgC,OAAO,CAAC,MAAM,EAAEkH,WAAW,EAAElJ,OAAO,CAAC;EACnD;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEmD,GAAGA,CAAC+F,WAAW,EAAElJ,OAAO,EAAE;IACxB,OAAO,IAAI,CAACgC,OAAO,CAAC,KAAK,EAAEkH,WAAW,EAAElJ,OAAO,CAAC;EAClD;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEmJ,KAAKA,CAACD,WAAW,EAAElJ,OAAO,EAAE;IAC1B,OAAO,IAAI,CAACgC,OAAO,CAAC,OAAO,EAAEkH,WAAW,EAAElJ,OAAO,CAAC;EACpD;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEoJ,MAAMA,CAACF,WAAW,EAAElJ,OAAO,EAAE;IAC3B,OAAO,IAAI,CAACgC,OAAO,CAAC,QAAQ,EAAEkH,WAAW,EAAElJ,OAAO,CAAC;EACrD;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE0E,WAAWA,CAAC2E,SAAS,EAAE;IACrB,IAAI,CAACA,SAAS,CAACC,KAAK,CAAC,QAAQ,CAAC,EAAE;MAC9B,OAAO,IAAI,CAACjI,YAAY,GAAGgI,SAAS;IACtC;IAEA,OAAOA,SAAS;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEnB,gBAAgBA,CAACqB,MAAM,EAAE;IAAEC,GAAG,GAAG/J;EAAW,CAAC,GAAG,CAAC,CAAC,EAAE;IAClD,MAAMyJ,WAAW,GAAG;MAClB,GAAG,IAAI,CAAChH,aAAa;MACrB,GAAGqH;IACL,CAAC;IAED,IAAI,CAACL,WAAW,CAAC5J,GAAG,EAAE;MACpB,MAAM,IAAImF,KAAK,CAAC,+BAA+B,CAAC;IAClD;;IAEA;IACAyE,WAAW,CAAC5J,GAAG,GAAG,IAAI,CAACoF,WAAW,CAAC+E,MAAM,CAACP,WAAW,CAAC5J,GAAG,CAAC,CAAC;IAE3D,IAAI,IAAI,CAAC2C,WAAW,EAAE;MACpB;MACAiH,WAAW,CAACQ,KAAK,GAAG,IAAI,CAACzH,WAAW;IACtC;IAEA,MAAM0H,SAAS,GAAGH,GAAG,CAAC/G,IAAI,CAAC;MAAEmH,GAAG,EAAE,IAAI,CAACzI;IAAO,CAAC,EAAE,IAAI,CAACC,SAAS,EAAE;MAC/DyI,SAAS,EAAE,OAAO;MAClBC,SAAS,EAAE,IAAI,CAACxI;IAClB,CAAC,CAAC;;IAEF;IACA;IACA;IACA4H,WAAW,CAACa,OAAO,GAAG,IAAI,CAACzI,eAAe,GAAG,IAAI,GAAG,GAAG;;IAEvD;IACA4H,WAAW,CAACvE,OAAO,GAAG;MACpBqF,aAAa,EAAG,OAAML,SAAU,EAAC;MACjCM,MAAM,EAAE,kBAAkB;MAC1B,GAAGf,WAAW,CAACvE;IACjB,CAAC;IAED,OAAOuE,WAAW;EACpB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMlH,OAAOA,CAACkB,UAAU,EAAEqG,MAAM,EAAE;IAAE9F,kBAAkB,GAAG;EAAK,CAAC,GAAG,CAAC,CAAC,EAAE;IACpE,MAAM0E,MAAM,GAAGjF,UAAU,CAACgH,WAAW,EAAE;IACvC,MAAMhB,WAAW,GAAG,IAAI,CAAChB,gBAAgB,CAACqB,MAAM,CAAC;IAEjD,IAAI;IACF;IACA5F,YAAY,EACZC,IAAI,CACL,GAAG,MAAM,IAAIM,OAAO,CAAC,CAACC,OAAO,EAAEgB,MAAM,KAAK;MACzC,IAAI,CAAC9B,KAAK,CAAE,SAAQ8E,MAAM,CAACgC,WAAW,EAAG,aAAY,EAAEjB,WAAW,CAAC;;MAEnE;MACA;MACA,MAAMkB,aAAa,GAAG,IAAI,CAAC5H,QAAQ,CAAC2F,MAAM,CAAC,CAAC3E,IAAI,CAAC,IAAI,CAAChB,QAAQ,CAAC;MAC/D;MACA;MACA;MACA;MACA;MACA;MACA4H,aAAa,EACX;MACAlB,WAAW;MACX;AACR;AACA;AACA;AACA;MACQ,CAACnF,KAAK,EAAEjE,QAAQ,EAAEuK,YAAY,KAAK;QACjC,IAAItG,KAAK,EAAE;UACToB,MAAM,CAACpB,KAAK,CAAC;UACb;QACF;QAEAI,OAAO,CAAC,CAACrE,QAAQ,EAAEuK,YAAY,CAAC,CAAC;MACnC,CAAC,CACF;IACH,CAAC,CAAC;IAEF,IAAI5G,kBAAkB,EAAE;MACtB,IAAIE,YAAY,CAACM,UAAU,GAAG,GAAG,IAAIN,YAAY,CAACM,UAAU,GAAG,GAAG,EAAE;QAClE,MAAM,IAAIQ,KAAK,CACZ,8BAA6B,IAAI,CAACC,WAAW,CAC5C+E,MAAM,CAACP,WAAW,CAAC5J,GAAG,CAAC,CACvB,IAAG,GACF,WAAUqE,YAAY,CAACM,UAAW,IAAG,GACrC,aAAYpE,cAAc,CAAC+D,IAAI,CAAE,EAAC,CACtC;MACH;IACF;IAEA,IACED,YAAY,CAACgB,OAAO,IACpBhB,YAAY,CAACgB,OAAO,CAAC,cAAc,CAAC,KAAK,kBAAkB,IAC3D,OAAOf,IAAI,KAAK,QAAQ,EACxB;MACA,IAAI;QACFA,IAAI,GAAG1D,IAAI,CAACY,KAAK,CAAC8C,IAAI,CAAC;MACzB,CAAC,CAAC,OAAOtD,CAAC,EAAE;QACV,IAAI,CAACoB,MAAM,CAAC0E,GAAG,CAAC,4CAA4C,EAAE9F,CAAC,CAAC;MAClE;IACF;IAEA,IAAI,CAAC+C,KAAK,CACP,SAAQ8E,MAAM,CAACgC,WAAW,EAAG,cAAa,EAC1C,WAAUxG,YAAY,CAACM,UAAW,IAAG,EACtC;MAAEU,OAAO,EAAEhB,YAAY,CAACgB,OAAO;MAAE7E,QAAQ,EAAE8D;IAAK,CAAC,CAClD;IAED,OAAO,CAACD,YAAY,EAAEC,IAAI,CAAC;EAC7B;;EAEA;AACF;AACA;EACEP,KAAKA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAAC9B,YAAY,EAAE;MACtB;IACF;;IAEA;AACJ;AACA;IACI,SAAS+I,MAAMA,CAACC,GAAG,EAAE;MACnB,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,CAACA,GAAG,EAAE;QACnC,OAAOA,GAAG;MACZ;MACA,IAAIA,GAAG,CAAC5F,OAAO,EAAE;QACf,CAAC,eAAe,EAAE,QAAQ,EAAE,YAAY,CAAC,CAACgE,OAAO,CAAC,UAAU6B,GAAG,EAAE;UAC/D,IAAID,GAAG,CAAC5F,OAAO,CAAC6F,GAAG,CAAC,EAAE;YACpB;YACAD,GAAG,CAAC5F,OAAO,CAAC6F,GAAG,CAAC,GAAG,YAAY;UACjC;QACF,CAAC,CAAC;MACJ;MAEAC,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,CAAC5B,OAAO,CAAC,UAAUgC,GAAG,EAAE;QACtC;QACAJ,GAAG,CAACI,GAAG,CAAC,GAAGL,MAAM,CAACC,GAAG,CAACI,GAAG,CAAC,CAAC;MAC7B,CAAC,CAAC;MAEF,OAAOJ,GAAG;IACZ;;IAEA;IACA;IACA,MAAMK,IAAI,GAAGjD,KAAK,CAACkD,SAAS,CAACC,GAAG,CAACC,IAAI,CAACC,SAAS,EAAE,UAAUC,GAAG,EAAE;MAC9D,IAAIC,MAAM,GAAGD,GAAG;MAChB,IAAI,OAAOC,MAAM,KAAK,QAAQ,EAAE;QAC9BA,MAAM,GAAG1L,QAAQ,CAAC0L,MAAM,CAAC;QACzBA,MAAM,GAAGZ,MAAM,CAACY,MAAM,CAAC;MACzB;MACA,OAAOA,MAAM;IACf,CAAC,CAAC;IACF,IAAI,CAACxJ,MAAM,CAAC0E,GAAG,CAAC,cAAc,EAAE,GAAGwE,IAAI,CAAC;EAC1C;AACF"}