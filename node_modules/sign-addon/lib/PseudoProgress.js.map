{"version": 3, "file": "PseudoProgress.js", "names": ["PseudoProgress", "constructor", "_clearInterval", "clearInterval", "_setInterval", "setInterval", "preamble", "stdout", "process", "interval", "motionCounter", "bucket", "emptyBucketPointers", "setPreamble", "addendum", "shell<PERSON>idth", "isTTY", "Number", "columns", "bucketSize", "length", "i", "push", "animate", "animateConfig", "conf", "speed", "bucketIsFull", "moveBucket", "randomlyFillBucket", "finish", "fillBucket", "write", "randomIndex", "Math", "floor", "random", "showBucket", "isFull", "newPointers", "for<PERSON>ach", "pointer", "map", "join"], "sources": ["../src/PseudoProgress.js"], "sourcesContent": ["/**\n * @typedef {{\n *   isTTY: boolean;\n *   columns: number;\n *   write: (buffer: string) => boolean;\n * }} Stdout\n */\n\n/**\n * A pseudo progress indicator.\n *\n * This is just a silly shell animation that was meant to simulate how lots of\n * tests would be run on an add-on file. It sort of looks like a torrent file\n * randomly getting filled in.\n */\nclass PseudoProgress {\n  /**\n   * @typedef {object} PseudoProgressParams\n   * @property {string=} preamble\n   * @property {typeof clearInterval=} _clearInterval\n   * @property {Stdout=} stdout\n   * @property {typeof setInterval=} _setInterval\n   *\n   * @param {PseudoProgressParams} params\n   */\n  constructor({\n    _clearInterval = clearInterval,\n    _setInterval = setInterval,\n    preamble = '',\n    stdout = process.stdout,\n  } = {}) {\n    this.interval = null;\n    this.motionCounter = 1;\n\n    this.setInterval = _setInterval;\n    this.clearInterval = _clearInterval;\n    this.stdout = stdout;\n\n    /** @type {string[]} */\n    this.bucket = [];\n    /** @type {number[]} */\n    this.emptyBucketPointers = [];\n\n    this.setPreamble(preamble);\n  }\n\n  /**\n   * @param {string} preamble\n   */\n  setPreamble(preamble) {\n    this.preamble = `${preamble} [`;\n    this.addendum = ']';\n\n    let shellWidth = 80;\n    if (this.stdout.isTTY) {\n      shellWidth = Number(this.stdout.columns);\n    }\n\n    this.emptyBucketPointers = [];\n    this.bucket = [];\n\n    const bucketSize = shellWidth - this.preamble.length - this.addendum.length;\n    for (let i = 0; i < bucketSize; i++) {\n      this.bucket.push(' ');\n      this.emptyBucketPointers.push(i);\n    }\n  }\n\n  /**\n   * @typedef {object} AnimateConfig\n   * @property {number} speed\n   *\n   * @param {AnimateConfig=} animateConfig\n   */\n  animate(animateConfig) {\n    const conf = {\n      speed: 100,\n      ...animateConfig,\n    };\n    let bucketIsFull = false;\n    this.interval = this.setInterval(() => {\n      if (bucketIsFull) {\n        this.moveBucket();\n      } else {\n        bucketIsFull = this.randomlyFillBucket();\n      }\n    }, conf.speed);\n  }\n\n  finish() {\n    if (this.interval) {\n      this.clearInterval(this.interval);\n    }\n\n    this.fillBucket();\n    // The bucket has already filled to the terminal width at this point\n    // but for copy/paste purposes, add a new line:\n    this.stdout.write('\\n');\n  }\n\n  randomlyFillBucket() {\n    // randomly fill a bucket (the width of the shell) with dots.\n    const randomIndex = Math.floor(\n      Math.random() * this.emptyBucketPointers.length,\n    );\n    this.bucket[this.emptyBucketPointers[randomIndex]] = '.';\n\n    this.showBucket();\n\n    let isFull = true;\n    /** @type {number[]} */\n    const newPointers = [];\n    this.emptyBucketPointers.forEach((pointer) => {\n      if (this.bucket[pointer] === ' ') {\n        isFull = false;\n        newPointers.push(pointer);\n      }\n    });\n    this.emptyBucketPointers = newPointers;\n\n    return isFull;\n  }\n\n  fillBucket() {\n    // fill the whole bucket with dots to indicate completion.\n    this.bucket = this.bucket.map(function () {\n      return '.';\n    });\n    this.showBucket();\n  }\n\n  moveBucket() {\n    // animate dots moving in a forward motion.\n    for (let i = 0; i < this.bucket.length; i++) {\n      this.bucket[i] = (i - this.motionCounter) % 3 ? ' ' : '.';\n    }\n    this.showBucket();\n\n    this.motionCounter++;\n  }\n\n  showBucket() {\n    this.stdout.write(\n      `\\r${this.preamble}${this.bucket.join('')}${this.addendum}`,\n    );\n  }\n}\n\nexport default PseudoProgress;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,cAAc,CAAC;EACnB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAAC;IACVC,cAAc,GAAGC,aAAa;IAC9BC,YAAY,GAAGC,WAAW;IAC1BC,QAAQ,GAAG,EAAE;IACbC,MAAM,GAAGC,OAAO,CAACD;EACnB,CAAC,GAAG,CAAC,CAAC,EAAE;IACN,IAAI,CAACE,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,aAAa,GAAG,CAAC;IAEtB,IAAI,CAACL,WAAW,GAAGD,YAAY;IAC/B,IAAI,CAACD,aAAa,GAAGD,cAAc;IACnC,IAAI,CAACK,MAAM,GAAGA,MAAM;;IAEpB;IACA,IAAI,CAACI,MAAM,GAAG,EAAE;IAChB;IACA,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAE7B,IAAI,CAACC,WAAW,CAACP,QAAQ,CAAC;EAC5B;;EAEA;AACF;AACA;EACEO,WAAWA,CAACP,QAAQ,EAAE;IACpB,IAAI,CAACA,QAAQ,GAAI,GAAEA,QAAS,IAAG;IAC/B,IAAI,CAACQ,QAAQ,GAAG,GAAG;IAEnB,IAAIC,UAAU,GAAG,EAAE;IACnB,IAAI,IAAI,CAACR,MAAM,CAACS,KAAK,EAAE;MACrBD,UAAU,GAAGE,MAAM,CAAC,IAAI,CAACV,MAAM,CAACW,OAAO,CAAC;IAC1C;IAEA,IAAI,CAACN,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAACD,MAAM,GAAG,EAAE;IAEhB,MAAMQ,UAAU,GAAGJ,UAAU,GAAG,IAAI,CAACT,QAAQ,CAACc,MAAM,GAAG,IAAI,CAACN,QAAQ,CAACM,MAAM;IAC3E,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,UAAU,EAAEE,CAAC,EAAE,EAAE;MACnC,IAAI,CAACV,MAAM,CAACW,IAAI,CAAC,GAAG,CAAC;MACrB,IAAI,CAACV,mBAAmB,CAACU,IAAI,CAACD,CAAC,CAAC;IAClC;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEE,OAAOA,CAACC,aAAa,EAAE;IACrB,MAAMC,IAAI,GAAG;MACXC,KAAK,EAAE,GAAG;MACV,GAAGF;IACL,CAAC;IACD,IAAIG,YAAY,GAAG,KAAK;IACxB,IAAI,CAAClB,QAAQ,GAAG,IAAI,CAACJ,WAAW,CAAC,MAAM;MACrC,IAAIsB,YAAY,EAAE;QAChB,IAAI,CAACC,UAAU,EAAE;MACnB,CAAC,MAAM;QACLD,YAAY,GAAG,IAAI,CAACE,kBAAkB,EAAE;MAC1C;IACF,CAAC,EAAEJ,IAAI,CAACC,KAAK,CAAC;EAChB;EAEAI,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACrB,QAAQ,EAAE;MACjB,IAAI,CAACN,aAAa,CAAC,IAAI,CAACM,QAAQ,CAAC;IACnC;IAEA,IAAI,CAACsB,UAAU,EAAE;IACjB;IACA;IACA,IAAI,CAACxB,MAAM,CAACyB,KAAK,CAAC,IAAI,CAAC;EACzB;EAEAH,kBAAkBA,CAAA,EAAG;IACnB;IACA,MAAMI,WAAW,GAAGC,IAAI,CAACC,KAAK,CAC5BD,IAAI,CAACE,MAAM,EAAE,GAAG,IAAI,CAACxB,mBAAmB,CAACQ,MAAM,CAChD;IACD,IAAI,CAACT,MAAM,CAAC,IAAI,CAACC,mBAAmB,CAACqB,WAAW,CAAC,CAAC,GAAG,GAAG;IAExD,IAAI,CAACI,UAAU,EAAE;IAEjB,IAAIC,MAAM,GAAG,IAAI;IACjB;IACA,MAAMC,WAAW,GAAG,EAAE;IACtB,IAAI,CAAC3B,mBAAmB,CAAC4B,OAAO,CAAEC,OAAO,IAAK;MAC5C,IAAI,IAAI,CAAC9B,MAAM,CAAC8B,OAAO,CAAC,KAAK,GAAG,EAAE;QAChCH,MAAM,GAAG,KAAK;QACdC,WAAW,CAACjB,IAAI,CAACmB,OAAO,CAAC;MAC3B;IACF,CAAC,CAAC;IACF,IAAI,CAAC7B,mBAAmB,GAAG2B,WAAW;IAEtC,OAAOD,MAAM;EACf;EAEAP,UAAUA,CAAA,EAAG;IACX;IACA,IAAI,CAACpB,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC+B,GAAG,CAAC,YAAY;MACxC,OAAO,GAAG;IACZ,CAAC,CAAC;IACF,IAAI,CAACL,UAAU,EAAE;EACnB;EAEAT,UAAUA,CAAA,EAAG;IACX;IACA,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACV,MAAM,CAACS,MAAM,EAAEC,CAAC,EAAE,EAAE;MAC3C,IAAI,CAACV,MAAM,CAACU,CAAC,CAAC,GAAG,CAACA,CAAC,GAAG,IAAI,CAACX,aAAa,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG;IAC3D;IACA,IAAI,CAAC2B,UAAU,EAAE;IAEjB,IAAI,CAAC3B,aAAa,EAAE;EACtB;EAEA2B,UAAUA,CAAA,EAAG;IACX,IAAI,CAAC9B,MAAM,CAACyB,KAAK,CACd,KAAI,IAAI,CAAC1B,QAAS,GAAE,IAAI,CAACK,MAAM,CAACgC,IAAI,CAAC,EAAE,CAAE,GAAE,IAAI,CAAC7B,QAAS,EAAC,CAC5D;EACH;AACF;AAEA,eAAed,cAAc"}