{"name": "sign-addon", "version": "5.3.0", "description": "Signs a Firefox add-on using Mozilla's web service", "type": "module", "main": "lib/index.js", "files": ["lib/**"], "scripts": {"build": "rimraf dist/ && babel --source-maps=true src/ -d lib/", "eslint": "eslint . --ext mjs --ext js", "jest": "node --experimental-vm-modules node_modules/jest/bin/jest.js", "lint": "npm run eslint", "prettier": "prettier --write '**'", "prettier-ci": "prettier --list-different '**' || (echo '\n\nThis failure means you did not run `npm run prettier-dev` before committing\n\n' && exit 1)", "prettier-dev": "pretty-quick --branch master", "test": "npm run jest", "test-ci": "npm run test -- --coverage", "typecheck": "tsc"}, "dependencies": {"common-tags": "1.8.2", "core-js": "3.29.0", "deepcopy": "2.1.0", "es6-error": "4.1.1", "es6-promisify": "7.0.0", "jsonwebtoken": "9.0.0", "mz": "2.7.0", "request": "2.88.2", "source-map-support": "0.5.21", "stream-to-promise": "3.0.0"}, "devDependencies": {"@babel/cli": "7.21.0", "@babel/core": "7.21.0", "@babel/eslint-parser": "7.19.1", "@babel/preset-env": "7.20.2", "@babel/register": "7.21.0", "@jest/globals": "29.5.0", "@types/common-tags": "1.8.1", "@types/jest": "29.4.0", "@types/jsonwebtoken": "9.0.1", "@types/mz": "2.7.4", "@types/request": "2.48.8", "@types/shelljs": "0.8.11", "@types/tmp": "0.2.3", "babel-jest": "29.5.0", "babel-loader": "9.1.2", "eslint": "8.35.0", "eslint-config-amo": "5.8.0", "eslint-plugin-amo": "1.22.0", "jest": "29.5.0", "prettier": "2.8.4", "pretty-quick": "3.1.3", "rimraf": "4.3.1", "shelljs": "0.8.5", "tmp": "0.2.1", "typescript": "4.9.5"}, "repository": {"type": "git", "url": "git+https://github.com/mozilla/sign-addon.git"}, "author": "<PERSON> and the Add-ons Team", "license": "MPL-2.0", "bugs": {"url": "https://github.com/mozilla/sign-addon/issues"}, "homepage": "https://github.com/mozilla/sign-addon#readme", "keywords": ["AddOn", "Add-on", "sign", "firefox", "WebService"]}