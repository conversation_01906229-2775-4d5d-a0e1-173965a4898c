{"name": "web-ext", "version": "7.12.0", "description": "A command line tool to help build, run, and test web extensions", "type": "module", "main": "index.js", "exports": {".": "./index.js", "./util/adb": "./lib/util/adb.js", "./util/logger": "./lib/util/logger.js"}, "files": ["index.js", "lib/**"], "engines": {"node": ">=14.0.0", "npm": ">=6.9.0"}, "engine-strict": true, "bin": {"web-ext": "bin/web-ext.js"}, "scripts": {"build": "node scripts/build", "start": "node scripts/develop", "test": "node scripts/test", "test-coverage": "node scripts/test --coverage", "test-functional": "node scripts/test-functional", "audit-deps": "node ./scripts/audit-deps", "changelog": "npx conventional-changelog-cli -p angular -u", "changelog-lint": "commitlint --from master", "changelog-lint-from-stdin": "commitlint", "github-pr-title-lint": "node ./scripts/github-pr-title-lint", "gen-contributing-toc": "npx doctoc CONTRIBUTING.md", "prettier": "prettier --write '**'", "prettier-ci": "prettier --list-different '**' || (echo '\n\nThis failure means you did not run `npm run prettier-dev` before committing\n\n' && exit 1)", "prettier-dev": "pretty-quick --branch master"}, "homepage": "https://github.com/mozilla/web-ext", "repository": {"type": "git", "url": "git://github.com/mozilla/web-ext.git"}, "bugs": {"url": "http://github.com/mozilla/web-ext/issues"}, "keywords": ["web", "extensions", "web extensions", "browser extensions", "firefox", "mozilla", "add-ons", "google", "chrome", "opera"], "dependencies": {"@babel/runtime": "7.21.0", "@devicefarmer/adbkit": "3.2.3", "addons-linter": "6.28.0", "bunyan": "1.8.15", "camelcase": "7.0.1", "chrome-launcher": "0.15.1", "debounce": "1.2.1", "decamelize": "6.0.0", "es6-error": "4.1.1", "firefox-profile": "4.3.2", "fs-extra": "11.1.0", "fx-runner": "1.4.0", "import-fresh": "3.3.0", "jose": "4.13.1", "mkdirp": "1.0.4", "multimatch": "6.0.0", "mz": "2.7.0", "node-fetch": "3.3.1", "node-notifier": "10.0.1", "open": "8.4.2", "parse-json": "6.0.2", "promise-toolbox": "0.21.0", "sign-addon": "5.3.0", "source-map-support": "0.5.21", "strip-bom": "5.0.0", "strip-json-comments": "5.0.0", "tmp": "0.2.1", "update-notifier": "6.0.2", "watchpack": "2.4.0", "ws": "8.13.0", "yargs": "17.7.1", "zip-dir": "2.0.0"}, "devDependencies": {"@babel/cli": "7.21.0", "@babel/core": "7.21.3", "@babel/eslint-parser": "7.21.3", "@babel/preset-env": "7.20.2", "@babel/preset-flow": "7.18.6", "@babel/register": "7.21.0", "@commitlint/cli": "17.4.4", "@commitlint/config-conventional": "17.4.4", "babel-plugin-istanbul": "6.1.1", "babel-plugin-transform-inline-environment-variables": "0.4.4", "chai": "4.3.7", "chai-as-promised": "7.1.1", "copy-dir": "1.3.0", "cross-env": "7.0.3", "deepcopy": "2.1.0", "eslint": "8.57.0", "eslint-plugin-async-await": "0.0.0", "eslint-plugin-ft-flow": "2.0.3", "eslint-plugin-import": "2.27.5", "flow-bin": "0.182.0", "git-rev-sync": "3.0.2", "html-entities": "2.3.3", "mocha": "10.2.0", "nyc": "15.1.0", "prettier": "2.8.5", "pretty-quick": "3.1.3", "prettyjson": "1.2.5", "shelljs": "0.8.5", "sinon": "15.0.2", "testdouble": "3.16.8", "yauzl": "2.10.0"}, "author": "<PERSON>", "license": "MPL-2.0", "nyc": {"include": "src/**/*.js", "reporter": ["lcov", "text"]}}