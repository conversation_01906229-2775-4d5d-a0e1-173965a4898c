{"version": 3, "file": "rdp-client.js", "names": ["net", "EventEmitter", "domain", "DEFAULT_PORT", "DEFAULT_HOST", "UNSOLICITED_EVENTS", "Set", "parseRDPMessage", "data", "str", "toString", "sepIdx", "indexOf", "byteLen", "parseInt", "slice", "isNaN", "error", "Error", "fatal", "length", "msg", "rdpMessage", "JSON", "parse", "connectToFirefox", "port", "client", "FirefoxRDPClient", "connect", "then", "constructor", "_incoming", "<PERSON><PERSON><PERSON>", "alloc", "_pending", "_active", "Map", "_onData", "args", "onData", "_onError", "onError", "_onEnd", "onEnd", "_onTimeout", "onTimeout", "Promise", "resolve", "reject", "d", "create", "once", "run", "conn", "createConnection", "host", "_rdpConnection", "on", "_expectReply", "disconnect", "off", "end", "_rejectAllRequests", "activeDef<PERSON>red", "values", "clear", "deferred", "request", "requestProps", "to", "type", "push", "_flushPendingRequests", "filter", "has", "stringify", "from", "write", "err", "targetActor", "set", "_handleMessage", "rdpData", "emit", "get", "delete", "_readMessage", "String", "concat"], "sources": ["../../src/firefox/rdp-client.js"], "sourcesContent": ["/* @flow */\nimport net from 'net';\nimport EventEmitter from 'events';\nimport domain from 'domain';\n\nexport type RDPRequest = {\n  to: string,\n  type: string,\n};\n\nexport type RDPResult = {\n  from: string,\n  type: string,\n};\n\nexport type Deferred = {|\n  resolve: Function,\n  reject: Function,\n|};\n\ntype ParseResult = {|\n  data: Buffer,\n  rdpMessage?: Object,\n  error?: Error,\n  fatal?: boolean,\n|};\n\nexport const DEFAULT_PORT = 6000;\nexport const DEFAULT_HOST = '127.0.0.1';\n\nconst UNSOLICITED_EVENTS = new Set([\n  'tabNavigated',\n  'styleApplied',\n  'propertyChange',\n  'networkEventUpdate',\n  'networkEvent',\n  'propertyChange',\n  'newMutations',\n  'frameUpdate',\n  'tabListChanged',\n]);\n\n// Parse RDP packets: BYTE_LENGTH + ':' + DATA.\nexport function parseRDPMessage(data: Buffer): ParseResult {\n  const str = data.toString();\n  const sepIdx = str.indexOf(':');\n  if (sepIdx < 1) {\n    return { data };\n  }\n\n  const byteLen = parseInt(str.slice(0, sepIdx));\n  if (isNaN(byteLen)) {\n    const error = new Error('Error parsing RDP message length');\n    return { data, error, fatal: true };\n  }\n\n  if (data.length - (sepIdx + 1) < byteLen) {\n    // Can't parse yet, will retry once more data has been received.\n    return { data };\n  }\n\n  data = data.slice(sepIdx + 1);\n  const msg = data.slice(0, byteLen);\n  data = data.slice(byteLen);\n\n  try {\n    return { data, rdpMessage: JSON.parse(msg.toString()) };\n  } catch (error) {\n    return { data, error, fatal: false };\n  }\n}\n\nexport function connectToFirefox(port: number): Promise<FirefoxRDPClient> {\n  const client = new FirefoxRDPClient();\n  return client.connect(port).then(() => client);\n}\n\nexport default class FirefoxRDPClient extends EventEmitter {\n  _incoming: Buffer;\n  _pending: Array<{| request: RDPRequest, deferred: Deferred |}>;\n  _active: Map<string, Deferred>;\n  _rdpConnection: net.Socket;\n  _onData: Function;\n  _onError: Function;\n  _onEnd: Function;\n  _onTimeout: Function;\n\n  constructor() {\n    super();\n    this._incoming = Buffer.alloc(0);\n    this._pending = [];\n    this._active = new Map();\n\n    this._onData = (...args) => this.onData(...args);\n    this._onError = (...args) => this.onError(...args);\n    this._onEnd = (...args) => this.onEnd(...args);\n    this._onTimeout = (...args) => this.onTimeout(...args);\n  }\n\n  connect(port: number): Promise<void> {\n    return new Promise((resolve, reject) => {\n      // Create a domain to wrap the errors that may be triggered\n      // by creating the client connection (e.g. ECONNREFUSED)\n      // so that we can reject the promise returned instead of\n      // exiting the entire process.\n      const d = domain.create();\n      d.once('error', reject);\n      d.run(() => {\n        const conn = net.createConnection({\n          port,\n          host: DEFAULT_HOST,\n        });\n\n        this._rdpConnection = conn;\n        conn.on('data', this._onData);\n        conn.on('error', this._onError);\n        conn.on('end', this._onEnd);\n        conn.on('timeout', this._onTimeout);\n\n        // Resolve once the expected initial root message\n        // has been received.\n        this._expectReply('root', { resolve, reject });\n      });\n    });\n  }\n\n  disconnect(): void {\n    if (!this._rdpConnection) {\n      return;\n    }\n\n    const conn = this._rdpConnection;\n    conn.off('data', this._onData);\n    conn.off('error', this._onError);\n    conn.off('end', this._onEnd);\n    conn.off('timeout', this._onTimeout);\n    conn.end();\n\n    this._rejectAllRequests(new Error('RDP connection closed'));\n  }\n\n  _rejectAllRequests(error: Error) {\n    for (const activeDeferred of this._active.values()) {\n      activeDeferred.reject(error);\n    }\n    this._active.clear();\n\n    for (const { deferred } of this._pending) {\n      deferred.reject(error);\n    }\n    this._pending = [];\n  }\n\n  async request(requestProps: string | RDPRequest): Promise<RDPResult> {\n    let request: RDPRequest;\n\n    if (typeof requestProps === 'string') {\n      request = { to: 'root', type: requestProps };\n    } else {\n      request = requestProps;\n    }\n\n    if (request.to == null) {\n      throw new Error(\n        `Unexpected RDP request without target actor: ${request.type}`\n      );\n    }\n\n    return new Promise((resolve, reject) => {\n      const deferred = { resolve, reject };\n      this._pending.push({ request, deferred });\n      this._flushPendingRequests();\n    });\n  }\n\n  _flushPendingRequests(): void {\n    this._pending = this._pending.filter(({ request, deferred }) => {\n      if (this._active.has(request.to)) {\n        // Keep in the pending requests until there are no requests\n        // active on the target RDP actor.\n        return true;\n      }\n\n      const conn = this._rdpConnection;\n      if (!conn) {\n        throw new Error('RDP connection closed');\n      }\n\n      try {\n        let str = JSON.stringify(request);\n        str = `${Buffer.from(str).length}:${str}`;\n        conn.write(str);\n        this._expectReply(request.to, deferred);\n      } catch (err) {\n        deferred.reject(err);\n      }\n\n      // Remove the pending request from the queue.\n      return false;\n    });\n  }\n\n  _expectReply(targetActor: string, deferred: Deferred): void {\n    if (this._active.has(targetActor)) {\n      throw new Error(`${targetActor} does already have an active request`);\n    }\n\n    this._active.set(targetActor, deferred);\n  }\n\n  _handleMessage(rdpData: Object): void {\n    if (rdpData.from == null) {\n      if (rdpData.error) {\n        this.emit('rdp-error', rdpData);\n        return;\n      }\n\n      this.emit(\n        'error',\n        new Error(\n          `Received an RDP message without a sender actor: ${JSON.stringify(\n            rdpData\n          )}`\n        )\n      );\n      return;\n    }\n\n    if (UNSOLICITED_EVENTS.has(rdpData.type)) {\n      this.emit('unsolicited-event', rdpData);\n      return;\n    }\n\n    if (this._active.has(rdpData.from)) {\n      const deferred = this._active.get(rdpData.from);\n      this._active.delete(rdpData.from);\n      if (rdpData.error) {\n        deferred?.reject(rdpData);\n      } else {\n        deferred?.resolve(rdpData);\n      }\n      this._flushPendingRequests();\n      return;\n    }\n\n    this.emit(\n      'error',\n      new Error(`Unexpected RDP message received: ${JSON.stringify(rdpData)}`)\n    );\n  }\n\n  _readMessage(): boolean {\n    const { data, rdpMessage, error, fatal } = parseRDPMessage(this._incoming);\n\n    this._incoming = data;\n\n    if (error) {\n      this.emit(\n        'error',\n        new Error(`Error parsing RDP packet: ${String(error)}`)\n      );\n      // Disconnect automatically on a fatal error.\n      if (fatal) {\n        this.disconnect();\n      }\n      // Caller can parse the next message if the error wasn't fatal\n      // (e.g. the RDP packet that couldn't be parsed has been already\n      // removed from the incoming data buffer).\n      return !fatal;\n    }\n\n    if (!rdpMessage) {\n      // Caller will need to wait more data to parse the next message.\n      return false;\n    }\n\n    this._handleMessage(rdpMessage);\n    // Caller can try to parse the next message from the remining data.\n    return true;\n  }\n\n  onData(data: Buffer) {\n    this._incoming = Buffer.concat([this._incoming, data]);\n    while (this._readMessage()) {\n      // Keep parsing and handling messages until readMessage\n      // returns false.\n    }\n  }\n\n  onError(error: Error) {\n    this.emit('error', error);\n  }\n\n  onEnd() {\n    this.emit('end');\n  }\n\n  onTimeout() {\n    this.emit('timeout');\n  }\n}\n"], "mappings": "AACA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,YAAY,MAAM,QAAQ;AACjC,OAAOC,MAAM,MAAM,QAAQ;AAwB3B,OAAO,MAAMC,YAAY,GAAG,IAAI;AAChC,OAAO,MAAMC,YAAY,GAAG,WAAW;AAEvC,MAAMC,kBAAkB,GAAG,IAAIC,GAAG,CAAC,CACjC,cAAc,EACd,cAAc,EACd,gBAAgB,EAChB,oBAAoB,EACpB,cAAc,EACd,gBAAgB,EAChB,cAAc,EACd,aAAa,EACb,gBAAgB,CACjB,CAAC;;AAEF;AACA,OAAO,SAASC,eAAeA,CAACC,IAAY,EAAe;EACzD,MAAMC,GAAG,GAAGD,IAAI,CAACE,QAAQ,CAAC,CAAC;EAC3B,MAAMC,MAAM,GAAGF,GAAG,CAACG,OAAO,CAAC,GAAG,CAAC;EAC/B,IAAID,MAAM,GAAG,CAAC,EAAE;IACd,OAAO;MAAEH;IAAK,CAAC;EACjB;EAEA,MAAMK,OAAO,GAAGC,QAAQ,CAACL,GAAG,CAACM,KAAK,CAAC,CAAC,EAAEJ,MAAM,CAAC,CAAC;EAC9C,IAAIK,KAAK,CAACH,OAAO,CAAC,EAAE;IAClB,MAAMI,KAAK,GAAG,IAAIC,KAAK,CAAC,kCAAkC,CAAC;IAC3D,OAAO;MAAEV,IAAI;MAAES,KAAK;MAAEE,KAAK,EAAE;IAAK,CAAC;EACrC;EAEA,IAAIX,IAAI,CAACY,MAAM,IAAIT,MAAM,GAAG,CAAC,CAAC,GAAGE,OAAO,EAAE;IACxC;IACA,OAAO;MAAEL;IAAK,CAAC;EACjB;EAEAA,IAAI,GAAGA,IAAI,CAACO,KAAK,CAACJ,MAAM,GAAG,CAAC,CAAC;EAC7B,MAAMU,GAAG,GAAGb,IAAI,CAACO,KAAK,CAAC,CAAC,EAAEF,OAAO,CAAC;EAClCL,IAAI,GAAGA,IAAI,CAACO,KAAK,CAACF,OAAO,CAAC;EAE1B,IAAI;IACF,OAAO;MAAEL,IAAI;MAAEc,UAAU,EAAEC,IAAI,CAACC,KAAK,CAACH,GAAG,CAACX,QAAQ,CAAC,CAAC;IAAE,CAAC;EACzD,CAAC,CAAC,OAAOO,KAAK,EAAE;IACd,OAAO;MAAET,IAAI;MAAES,KAAK;MAAEE,KAAK,EAAE;IAAM,CAAC;EACtC;AACF;AAEA,OAAO,SAASM,gBAAgBA,CAACC,IAAY,EAA6B;EACxE,MAAMC,MAAM,GAAG,IAAIC,gBAAgB,CAAC,CAAC;EACrC,OAAOD,MAAM,CAACE,OAAO,CAACH,IAAI,CAAC,CAACI,IAAI,CAAC,MAAMH,MAAM,CAAC;AAChD;AAEA,eAAe,MAAMC,gBAAgB,SAAS3B,YAAY,CAAC;EAUzD8B,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,SAAS,GAAGC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAChC,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;IAExB,IAAI,CAACC,OAAO,GAAG,CAAC,GAAGC,IAAI,KAAK,IAAI,CAACC,MAAM,CAAC,GAAGD,IAAI,CAAC;IAChD,IAAI,CAACE,QAAQ,GAAG,CAAC,GAAGF,IAAI,KAAK,IAAI,CAACG,OAAO,CAAC,GAAGH,IAAI,CAAC;IAClD,IAAI,CAACI,MAAM,GAAG,CAAC,GAAGJ,IAAI,KAAK,IAAI,CAACK,KAAK,CAAC,GAAGL,IAAI,CAAC;IAC9C,IAAI,CAACM,UAAU,GAAG,CAAC,GAAGN,IAAI,KAAK,IAAI,CAACO,SAAS,CAAC,GAAGP,IAAI,CAAC;EACxD;EAEAV,OAAOA,CAACH,IAAY,EAAiB;IACnC,OAAO,IAAIqB,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC;MACA;MACA;MACA;MACA,MAAMC,CAAC,GAAGhD,MAAM,CAACiD,MAAM,CAAC,CAAC;MACzBD,CAAC,CAACE,IAAI,CAAC,OAAO,EAAEH,MAAM,CAAC;MACvBC,CAAC,CAACG,GAAG,CAAC,MAAM;QACV,MAAMC,IAAI,GAAGtD,GAAG,CAACuD,gBAAgB,CAAC;UAChC7B,IAAI;UACJ8B,IAAI,EAAEpD;QACR,CAAC,CAAC;QAEF,IAAI,CAACqD,cAAc,GAAGH,IAAI;QAC1BA,IAAI,CAACI,EAAE,CAAC,MAAM,EAAE,IAAI,CAACpB,OAAO,CAAC;QAC7BgB,IAAI,CAACI,EAAE,CAAC,OAAO,EAAE,IAAI,CAACjB,QAAQ,CAAC;QAC/Ba,IAAI,CAACI,EAAE,CAAC,KAAK,EAAE,IAAI,CAACf,MAAM,CAAC;QAC3BW,IAAI,CAACI,EAAE,CAAC,SAAS,EAAE,IAAI,CAACb,UAAU,CAAC;;QAEnC;QACA;QACA,IAAI,CAACc,YAAY,CAAC,MAAM,EAAE;UAAEX,OAAO;UAAEC;QAAO,CAAC,CAAC;MAChD,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAW,UAAUA,CAAA,EAAS;IACjB,IAAI,CAAC,IAAI,CAACH,cAAc,EAAE;MACxB;IACF;IAEA,MAAMH,IAAI,GAAG,IAAI,CAACG,cAAc;IAChCH,IAAI,CAACO,GAAG,CAAC,MAAM,EAAE,IAAI,CAACvB,OAAO,CAAC;IAC9BgB,IAAI,CAACO,GAAG,CAAC,OAAO,EAAE,IAAI,CAACpB,QAAQ,CAAC;IAChCa,IAAI,CAACO,GAAG,CAAC,KAAK,EAAE,IAAI,CAAClB,MAAM,CAAC;IAC5BW,IAAI,CAACO,GAAG,CAAC,SAAS,EAAE,IAAI,CAAChB,UAAU,CAAC;IACpCS,IAAI,CAACQ,GAAG,CAAC,CAAC;IAEV,IAAI,CAACC,kBAAkB,CAAC,IAAI7C,KAAK,CAAC,uBAAuB,CAAC,CAAC;EAC7D;EAEA6C,kBAAkBA,CAAC9C,KAAY,EAAE;IAC/B,KAAK,MAAM+C,cAAc,IAAI,IAAI,CAAC5B,OAAO,CAAC6B,MAAM,CAAC,CAAC,EAAE;MAClDD,cAAc,CAACf,MAAM,CAAChC,KAAK,CAAC;IAC9B;IACA,IAAI,CAACmB,OAAO,CAAC8B,KAAK,CAAC,CAAC;IAEpB,KAAK,MAAM;MAAEC;IAAS,CAAC,IAAI,IAAI,CAAChC,QAAQ,EAAE;MACxCgC,QAAQ,CAAClB,MAAM,CAAChC,KAAK,CAAC;IACxB;IACA,IAAI,CAACkB,QAAQ,GAAG,EAAE;EACpB;EAEA,MAAMiC,OAAOA,CAACC,YAAiC,EAAsB;IACnE,IAAID,OAAmB;IAEvB,IAAI,OAAOC,YAAY,KAAK,QAAQ,EAAE;MACpCD,OAAO,GAAG;QAAEE,EAAE,EAAE,MAAM;QAAEC,IAAI,EAAEF;MAAa,CAAC;IAC9C,CAAC,MAAM;MACLD,OAAO,GAAGC,YAAY;IACxB;IAEA,IAAID,OAAO,CAACE,EAAE,IAAI,IAAI,EAAE;MACtB,MAAM,IAAIpD,KAAK,CACZ,gDAA+CkD,OAAO,CAACG,IAAK,EAC/D,CAAC;IACH;IAEA,OAAO,IAAIxB,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC,MAAMkB,QAAQ,GAAG;QAAEnB,OAAO;QAAEC;MAAO,CAAC;MACpC,IAAI,CAACd,QAAQ,CAACqC,IAAI,CAAC;QAAEJ,OAAO;QAAED;MAAS,CAAC,CAAC;MACzC,IAAI,CAACM,qBAAqB,CAAC,CAAC;IAC9B,CAAC,CAAC;EACJ;EAEAA,qBAAqBA,CAAA,EAAS;IAC5B,IAAI,CAACtC,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACuC,MAAM,CAAC,CAAC;MAAEN,OAAO;MAAED;IAAS,CAAC,KAAK;MAC9D,IAAI,IAAI,CAAC/B,OAAO,CAACuC,GAAG,CAACP,OAAO,CAACE,EAAE,CAAC,EAAE;QAChC;QACA;QACA,OAAO,IAAI;MACb;MAEA,MAAMhB,IAAI,GAAG,IAAI,CAACG,cAAc;MAChC,IAAI,CAACH,IAAI,EAAE;QACT,MAAM,IAAIpC,KAAK,CAAC,uBAAuB,CAAC;MAC1C;MAEA,IAAI;QACF,IAAIT,GAAG,GAAGc,IAAI,CAACqD,SAAS,CAACR,OAAO,CAAC;QACjC3D,GAAG,GAAI,GAAEwB,MAAM,CAAC4C,IAAI,CAACpE,GAAG,CAAC,CAACW,MAAO,IAAGX,GAAI,EAAC;QACzC6C,IAAI,CAACwB,KAAK,CAACrE,GAAG,CAAC;QACf,IAAI,CAACkD,YAAY,CAACS,OAAO,CAACE,EAAE,EAAEH,QAAQ,CAAC;MACzC,CAAC,CAAC,OAAOY,GAAG,EAAE;QACZZ,QAAQ,CAAClB,MAAM,CAAC8B,GAAG,CAAC;MACtB;;MAEA;MACA,OAAO,KAAK;IACd,CAAC,CAAC;EACJ;EAEApB,YAAYA,CAACqB,WAAmB,EAAEb,QAAkB,EAAQ;IAC1D,IAAI,IAAI,CAAC/B,OAAO,CAACuC,GAAG,CAACK,WAAW,CAAC,EAAE;MACjC,MAAM,IAAI9D,KAAK,CAAE,GAAE8D,WAAY,sCAAqC,CAAC;IACvE;IAEA,IAAI,CAAC5C,OAAO,CAAC6C,GAAG,CAACD,WAAW,EAAEb,QAAQ,CAAC;EACzC;EAEAe,cAAcA,CAACC,OAAe,EAAQ;IACpC,IAAIA,OAAO,CAACN,IAAI,IAAI,IAAI,EAAE;MACxB,IAAIM,OAAO,CAAClE,KAAK,EAAE;QACjB,IAAI,CAACmE,IAAI,CAAC,WAAW,EAAED,OAAO,CAAC;QAC/B;MACF;MAEA,IAAI,CAACC,IAAI,CACP,OAAO,EACP,IAAIlE,KAAK,CACN,mDAAkDK,IAAI,CAACqD,SAAS,CAC/DO,OACF,CAAE,EACJ,CACF,CAAC;MACD;IACF;IAEA,IAAI9E,kBAAkB,CAACsE,GAAG,CAACQ,OAAO,CAACZ,IAAI,CAAC,EAAE;MACxC,IAAI,CAACa,IAAI,CAAC,mBAAmB,EAAED,OAAO,CAAC;MACvC;IACF;IAEA,IAAI,IAAI,CAAC/C,OAAO,CAACuC,GAAG,CAACQ,OAAO,CAACN,IAAI,CAAC,EAAE;MAClC,MAAMV,QAAQ,GAAG,IAAI,CAAC/B,OAAO,CAACiD,GAAG,CAACF,OAAO,CAACN,IAAI,CAAC;MAC/C,IAAI,CAACzC,OAAO,CAACkD,MAAM,CAACH,OAAO,CAACN,IAAI,CAAC;MACjC,IAAIM,OAAO,CAAClE,KAAK,EAAE;QACjBkD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAElB,MAAM,CAACkC,OAAO,CAAC;MAC3B,CAAC,MAAM;QACLhB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEnB,OAAO,CAACmC,OAAO,CAAC;MAC5B;MACA,IAAI,CAACV,qBAAqB,CAAC,CAAC;MAC5B;IACF;IAEA,IAAI,CAACW,IAAI,CACP,OAAO,EACP,IAAIlE,KAAK,CAAE,oCAAmCK,IAAI,CAACqD,SAAS,CAACO,OAAO,CAAE,EAAC,CACzE,CAAC;EACH;EAEAI,YAAYA,CAAA,EAAY;IACtB,MAAM;MAAE/E,IAAI;MAAEc,UAAU;MAAEL,KAAK;MAAEE;IAAM,CAAC,GAAGZ,eAAe,CAAC,IAAI,CAACyB,SAAS,CAAC;IAE1E,IAAI,CAACA,SAAS,GAAGxB,IAAI;IAErB,IAAIS,KAAK,EAAE;MACT,IAAI,CAACmE,IAAI,CACP,OAAO,EACP,IAAIlE,KAAK,CAAE,6BAA4BsE,MAAM,CAACvE,KAAK,CAAE,EAAC,CACxD,CAAC;MACD;MACA,IAAIE,KAAK,EAAE;QACT,IAAI,CAACyC,UAAU,CAAC,CAAC;MACnB;MACA;MACA;MACA;MACA,OAAO,CAACzC,KAAK;IACf;IAEA,IAAI,CAACG,UAAU,EAAE;MACf;MACA,OAAO,KAAK;IACd;IAEA,IAAI,CAAC4D,cAAc,CAAC5D,UAAU,CAAC;IAC/B;IACA,OAAO,IAAI;EACb;EAEAkB,MAAMA,CAAChC,IAAY,EAAE;IACnB,IAAI,CAACwB,SAAS,GAAGC,MAAM,CAACwD,MAAM,CAAC,CAAC,IAAI,CAACzD,SAAS,EAAExB,IAAI,CAAC,CAAC;IACtD,OAAO,IAAI,CAAC+E,YAAY,CAAC,CAAC,EAAE;MAC1B;MACA;IAAA;EAEJ;EAEA7C,OAAOA,CAACzB,KAAY,EAAE;IACpB,IAAI,CAACmE,IAAI,CAAC,OAAO,EAAEnE,KAAK,CAAC;EAC3B;EAEA2B,KAAKA,CAAA,EAAG;IACN,IAAI,CAACwC,IAAI,CAAC,KAAK,CAAC;EAClB;EAEAtC,SAASA,CAAA,EAAG;IACV,IAAI,CAACsC,IAAI,CAAC,SAAS,CAAC;EACtB;AACF"}