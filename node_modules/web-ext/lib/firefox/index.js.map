{"version": 3, "file": "index.js", "names": ["nodeFs", "path", "promisify", "default", "defaultFxRunner", "FirefoxProfile", "fs", "fromEvent", "isDirectory", "isErrorWithCode", "UsageError", "WebExtError", "getPrefs", "defaultPrefGetter", "getManifestId", "findFreeTcpPort", "defaultRemotePortFinder", "createLogger", "log", "import", "meta", "url", "defaultAsyncFsStat", "stat", "bind", "defaultUserProfileCopier", "copyFromUserProfile", "defaultFirefoxEnv", "XPCOM_DEBUG_BREAK", "NS_TRACE_MALLOC_DISABLE_STACKS", "run", "profile", "fx<PERSON><PERSON>ner", "findRemotePort", "firefoxBinary", "binaryArgs", "extensions", "devtools", "debug", "remotePort", "startsWith", "flatpakAppId", "substring", "map", "sourceDir", "concat", "results", "binary", "listen", "foreground", "env", "process", "verbose", "firefox", "args", "join", "on", "error", "info", "stderr", "data", "toString", "trim", "stdout", "debuggerPort", "DEFAULT_PROFILES_NAMES", "isDefaultProfile", "profilePathOrName", "ProfileFinder", "Finder", "fsStat", "includes", "baseProfileDir", "locateUserDirectory", "profilesIniPath", "finder", "readProfiles", "normalizedProfileDirPath", "normalize", "resolve", "sep", "profiles", "Name", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "IsRelative", "Path", "configureProfile", "app", "customPrefs", "prefs", "Object", "keys", "for<PERSON>ach", "pref", "setPreference", "length", "customPrefsStr", "JSON", "stringify", "custom", "updatePreferences", "Promise", "defaultCreateProfileFinder", "userDirectoryPath", "FxProfile", "<PERSON><PERSON><PERSON>", "profileName", "hasProfileName", "filter", "profileDef", "warn", "useProfile", "profilePath", "configureThisProfile", "isFirefoxDefaultProfile", "createProfileFinder", "isForbiddenProfile", "destinationDirectory", "getProfilePath", "profileIsDir<PERSON><PERSON>", "createProfile", "copyProfile", "profileDirectory", "copy", "copyByName", "dirExists", "name", "installExtension", "asProxy", "manifestData", "extensionPath", "asyncFsStat", "extensionsDir", "mkdir", "id", "isDir", "destPath", "writeStream", "createWriteStream", "write", "end", "readStream", "createReadStream", "pipe", "all"], "sources": ["../../src/firefox/index.js"], "sourcesContent": ["/* @flow */\nimport nodeFs from 'fs';\nimport path from 'path';\nimport { promisify } from 'util';\n\nimport { default as defaultFxRunner } from 'fx-runner';\nimport FirefoxProfile from 'firefox-profile';\nimport { fs } from 'mz';\nimport fromEvent from 'promise-toolbox/fromEvent';\n\nimport isDirectory from '../util/is-directory.js';\nimport { isErrorWithCode, UsageError, WebExtError } from '../errors.js';\nimport { getPrefs as defaultPrefGetter } from './preferences.js';\nimport { getManifestId } from '../util/manifest.js';\nimport { findFreeTcpPort as defaultRemotePortFinder } from './remote.js';\nimport { createLogger } from '../util/logger.js';\n// Import flow types\nimport type {\n  PreferencesAppName,\n  PreferencesGetterFn,\n  FirefoxPreferences,\n} from './preferences';\nimport type { ExtensionManifest } from '../util/manifest.js';\nimport type { Extension } from '../extension-runners/base.js';\n\nconst log = createLogger(import.meta.url);\n\nconst defaultAsyncFsStat: typeof fs.stat = fs.stat.bind(fs);\n\nconst defaultUserProfileCopier = FirefoxProfile.copyFromUserProfile;\n\nexport const defaultFirefoxEnv = {\n  XPCOM_DEBUG_BREAK: 'stack',\n  NS_TRACE_MALLOC_DISABLE_STACKS: '1',\n};\n\n// defaultRemotePortFinder types and implementation.\n\nexport type RemotePortFinderFn = () => Promise<number>;\n\n// Declare the needed 'fx-runner' module flow types.\n\nexport type FirefoxRunnerParams = {|\n  binary: ?string,\n  profile?: string,\n  'new-instance'?: boolean,\n  'no-remote'?: boolean,\n  foreground?: boolean,\n  listen: number,\n  'binary-args'?: Array<string> | string,\n  'binary-args-first'?: boolean,\n  env?: {\n    // This match the flowtype signature for process.env (and prevent flow\n    // from complaining about differences between their type signature)\n    [key: string]: string | void,\n  },\n  verbose?: boolean,\n|};\n\nexport interface FirefoxProcess extends events$EventEmitter {\n  stderr: events$EventEmitter;\n  stdout: events$EventEmitter;\n  kill: Function;\n}\n\nexport type FirefoxRunnerResults = {|\n  process: FirefoxProcess,\n  binary: string,\n  args: Array<string>,\n|};\n\nexport type FirefoxRunnerFn = (\n  params: FirefoxRunnerParams\n) => Promise<FirefoxRunnerResults>;\n\nexport type FirefoxInfo = {|\n  firefox: FirefoxProcess,\n  debuggerPort: number,\n|};\n\n// Run command types and implementaion.\n\nexport type FirefoxRunOptions = {\n  fxRunner?: FirefoxRunnerFn,\n  findRemotePort?: RemotePortFinderFn,\n  firefoxBinary?: string,\n  binaryArgs?: Array<string>,\n  args?: Array<any>,\n  extensions: Array<Extension>,\n  devtools: boolean,\n};\n\n/*\n * Runs Firefox with the given profile object and resolves a promise on exit.\n */\nexport async function run(\n  profile: FirefoxProfile,\n  {\n    fxRunner = defaultFxRunner,\n    findRemotePort = defaultRemotePortFinder,\n    firefoxBinary,\n    binaryArgs,\n    extensions,\n    devtools,\n  }: FirefoxRunOptions = {}\n): Promise<FirefoxInfo> {\n  log.debug(`Running Firefox with profile at ${profile.path()}`);\n\n  const remotePort = await findRemotePort();\n\n  if (firefoxBinary && firefoxBinary.startsWith('flatpak:')) {\n    const flatpakAppId = firefoxBinary.substring(8);\n    log.debug(`Configuring Firefox with flatpak: appId=${flatpakAppId}`);\n\n    // This should be resolved by the fx-runner.\n    firefoxBinary = 'flatpak';\n    binaryArgs = [\n      'run',\n      `--filesystem=${profile.path()}`,\n      ...extensions.map(({ sourceDir }) => `--filesystem=${sourceDir}:ro`),\n      // We need to share the network namespace because we want to connect to\n      // Firefox with the remote protocol. There is no way to tell flatpak to\n      // only expose a port AFAIK.\n      '--share=network',\n      // Kill the entire sandbox when the launching process dies, which is what\n      // we want since exiting web-ext involves `kill` and the process executed\n      // here is `flatpak run`.\n      '--die-with-parent',\n      flatpakAppId,\n    ].concat(...(binaryArgs || []));\n  }\n\n  const results = await fxRunner({\n    // if this is falsey, fxRunner tries to find the default one.\n    binary: firefoxBinary,\n    'binary-args': binaryArgs,\n    // For Flatpak we need to respect the order of the command arguments because\n    // we have arguments for Flapack (first) and then Firefox.\n    'binary-args-first': firefoxBinary === 'flatpak',\n    // This ensures a new instance of Firefox is created. It has nothing\n    // to do with the devtools remote debugger.\n    'no-remote': true,\n    listen: remotePort,\n    foreground: true,\n    profile: profile.path(),\n    env: {\n      ...process.env,\n      ...defaultFirefoxEnv,\n    },\n    verbose: true,\n  });\n\n  const firefox = results.process;\n\n  log.debug(`Executing Firefox binary: ${results.binary}`);\n  log.debug(`Firefox args: ${results.args.join(' ')}`);\n\n  firefox.on('error', (error) => {\n    // TODO: show a nice error when it can't find Firefox.\n    // if (/No such file/.test(err) || err.code === 'ENOENT') {\n    log.error(`Firefox error: ${error}`);\n    throw error;\n  });\n\n  if (!devtools) {\n    log.info('Use --verbose or --devtools to see logging');\n  }\n  if (devtools) {\n    log.info('More info about WebExtensions debugging:');\n    log.info('https://extensionworkshop.com/documentation/develop/debugging/');\n  }\n\n  firefox.stderr.on('data', (data) => {\n    log.debug(`Firefox stderr: ${data.toString().trim()}`);\n  });\n\n  firefox.stdout.on('data', (data) => {\n    log.debug(`Firefox stdout: ${data.toString().trim()}`);\n  });\n\n  firefox.on('close', () => {\n    log.debug('Firefox closed');\n  });\n\n  return { firefox, debuggerPort: remotePort };\n}\n\n// isDefaultProfile types and implementation.\n\nconst DEFAULT_PROFILES_NAMES = ['default', 'dev-edition-default'];\n\nexport type IsDefaultProfileFn = (\n  profilePathOrName: string,\n  ProfileFinder?: typeof FirefoxProfile.Finder,\n  fsStat?: typeof fs.stat\n) => Promise<boolean>;\n\n/*\n * Tests if a profile is a default Firefox profile (both as a profile name or\n * profile path).\n *\n * Returns a promise that resolves to true if the profile is one of default Firefox profile.\n */\nexport async function isDefaultProfile(\n  profilePathOrName: string,\n  ProfileFinder?: typeof FirefoxProfile.Finder = FirefoxProfile.Finder,\n  fsStat?: typeof fs.stat = fs.stat\n): Promise<boolean> {\n  if (DEFAULT_PROFILES_NAMES.includes(profilePathOrName)) {\n    return true;\n  }\n\n  const baseProfileDir = ProfileFinder.locateUserDirectory();\n  const profilesIniPath = path.join(baseProfileDir, 'profiles.ini');\n  try {\n    await fsStat(profilesIniPath);\n  } catch (error) {\n    if (isErrorWithCode('ENOENT', error)) {\n      log.debug(`profiles.ini not found: ${error}`);\n\n      // No profiles exist yet, default to false (the default profile name contains a\n      // random generated component).\n      return false;\n    }\n\n    // Re-throw any unexpected exception.\n    throw error;\n  }\n\n  // Check for profile dir path.\n  const finder = new ProfileFinder(baseProfileDir);\n  const readProfiles = promisify((...args) => finder.readProfiles(...args));\n\n  await readProfiles();\n\n  const normalizedProfileDirPath = path.normalize(\n    path.join(path.resolve(profilePathOrName), path.sep)\n  );\n\n  for (const profile of finder.profiles) {\n    // Check if the profile dir path or name is one of the default profiles\n    // defined in the profiles.ini file.\n    if (\n      DEFAULT_PROFILES_NAMES.includes(profile.Name) ||\n      profile.Default === '1'\n    ) {\n      let profileFullPath;\n\n      // Check for profile name.\n      if (profile.Name === profilePathOrName) {\n        return true;\n      }\n\n      // Check for profile path.\n      if (profile.IsRelative === '1') {\n        profileFullPath = path.join(baseProfileDir, profile.Path, path.sep);\n      } else {\n        profileFullPath = path.join(profile.Path, path.sep);\n      }\n\n      if (path.normalize(profileFullPath) === normalizedProfileDirPath) {\n        return true;\n      }\n    }\n  }\n\n  // Profile directory not found.\n  return false;\n}\n\n// configureProfile types and implementation.\n\nexport type ConfigureProfileOptions = {\n  app?: PreferencesAppName,\n  getPrefs?: PreferencesGetterFn,\n  customPrefs?: FirefoxPreferences,\n};\n\nexport type ConfigureProfileFn = (\n  profile: FirefoxProfile,\n  options?: ConfigureProfileOptions\n) => Promise<FirefoxProfile>;\n\n/*\n * Configures a profile with common preferences that are required to\n * activate extension development.\n *\n * Returns a promise that resolves with the original profile object.\n */\nexport function configureProfile(\n  profile: FirefoxProfile,\n  {\n    app = 'firefox',\n    getPrefs = defaultPrefGetter,\n    customPrefs = {},\n  }: ConfigureProfileOptions = {}\n): Promise<FirefoxProfile> {\n  // Set default preferences. Some of these are required for the add-on to\n  // operate, such as disabling signatures.\n  const prefs = getPrefs(app);\n  Object.keys(prefs).forEach((pref) => {\n    profile.setPreference(pref, prefs[pref]);\n  });\n  if (Object.keys(customPrefs).length > 0) {\n    const customPrefsStr = JSON.stringify(customPrefs, null, 2);\n    log.info(`Setting custom Firefox preferences: ${customPrefsStr}`);\n    Object.keys(customPrefs).forEach((custom) => {\n      profile.setPreference(custom, customPrefs[custom]);\n    });\n  }\n  profile.updatePreferences();\n  return Promise.resolve(profile);\n}\n\nexport type getProfileFn = (profileName: string) => Promise<string | void>;\n\nexport type CreateProfileFinderParams = {\n  userDirectoryPath?: string,\n  FxProfile?: typeof FirefoxProfile,\n};\n\nexport function defaultCreateProfileFinder({\n  userDirectoryPath,\n  FxProfile = FirefoxProfile,\n}: CreateProfileFinderParams = {}): getProfileFn {\n  const finder = new FxProfile.Finder(userDirectoryPath);\n  const readProfiles = promisify((...args) => finder.readProfiles(...args));\n  const getPath = promisify((...args) => finder.getPath(...args));\n  return async (profileName: string): Promise<string | void> => {\n    try {\n      await readProfiles();\n      const hasProfileName =\n        finder.profiles.filter((profileDef) => profileDef.Name === profileName)\n          .length !== 0;\n      if (hasProfileName) {\n        return await getPath(profileName);\n      }\n    } catch (error) {\n      if (!isErrorWithCode('ENOENT', error)) {\n        throw error;\n      }\n      log.warn('Unable to find Firefox profiles.ini');\n    }\n  };\n}\n\n// useProfile types and implementation.\n\nexport type UseProfileParams = {\n  app?: PreferencesAppName,\n  configureThisProfile?: ConfigureProfileFn,\n  isFirefoxDefaultProfile?: IsDefaultProfileFn,\n  customPrefs?: FirefoxPreferences,\n  createProfileFinder?: typeof defaultCreateProfileFinder,\n};\n\n// Use the target path as a Firefox profile without cloning it\n\nexport async function useProfile(\n  profilePath: string,\n  {\n    app,\n    configureThisProfile = configureProfile,\n    isFirefoxDefaultProfile = isDefaultProfile,\n    customPrefs = {},\n    createProfileFinder = defaultCreateProfileFinder,\n  }: UseProfileParams = {}\n): Promise<FirefoxProfile> {\n  const isForbiddenProfile = await isFirefoxDefaultProfile(profilePath);\n  if (isForbiddenProfile) {\n    throw new UsageError(\n      'Cannot use --keep-profile-changes on a default profile' +\n        ` (\"${profilePath}\")` +\n        ' because web-ext will make it insecure and unsuitable for daily use.' +\n        '\\nSee https://github.com/mozilla/web-ext/issues/1005'\n    );\n  }\n\n  let destinationDirectory;\n  const getProfilePath = createProfileFinder();\n\n  const profileIsDirPath = await isDirectory(profilePath);\n  if (profileIsDirPath) {\n    log.debug(`Using profile directory \"${profilePath}\"`);\n    destinationDirectory = profilePath;\n  } else {\n    log.debug(`Assuming ${profilePath} is a named profile`);\n    destinationDirectory = await getProfilePath(profilePath);\n    if (!destinationDirectory) {\n      throw new UsageError(\n        `The request \"${profilePath}\" profile name ` +\n          'cannot be resolved to a profile path'\n      );\n    }\n  }\n\n  const profile = new FirefoxProfile({ destinationDirectory });\n  return await configureThisProfile(profile, { app, customPrefs });\n}\n\n// createProfile types and implementation.\n\nexport type CreateProfileParams = {\n  app?: PreferencesAppName,\n  configureThisProfile?: ConfigureProfileFn,\n  customPrefs?: FirefoxPreferences,\n};\n\n/*\n * Creates a new temporary profile and resolves with the profile object.\n *\n * The profile will be deleted when the system process exits.\n */\nexport async function createProfile({\n  app,\n  configureThisProfile = configureProfile,\n  customPrefs = {},\n}: CreateProfileParams = {}): Promise<FirefoxProfile> {\n  const profile = new FirefoxProfile();\n  return await configureThisProfile(profile, { app, customPrefs });\n}\n\n// copyProfile types and implementation.\n\nexport type CopyProfileOptions = {\n  app?: PreferencesAppName,\n  configureThisProfile?: ConfigureProfileFn,\n  copyFromUserProfile?: Function,\n  customPrefs?: FirefoxPreferences,\n};\n\n/*\n * Copies an existing Firefox profile and creates a new temporary profile.\n * The new profile will be configured with some preferences required to\n * activate extension development.\n *\n * It resolves with the new profile object.\n *\n * The temporary profile will be deleted when the system process exits.\n *\n * The existing profile can be specified as a directory path or a name of\n * one that exists in the current user's Firefox directory.\n */\nexport async function copyProfile(\n  profileDirectory: string,\n  {\n    app,\n    configureThisProfile = configureProfile,\n    copyFromUserProfile = defaultUserProfileCopier,\n    customPrefs = {},\n  }: CopyProfileOptions = {}\n): Promise<FirefoxProfile> {\n  const copy = promisify(FirefoxProfile.copy);\n  const copyByName = promisify(copyFromUserProfile);\n\n  try {\n    const dirExists = await isDirectory(profileDirectory);\n\n    let profile;\n\n    if (dirExists) {\n      log.debug(`Copying profile directory from \"${profileDirectory}\"`);\n      profile = await copy({ profileDirectory });\n    } else {\n      log.debug(`Assuming ${profileDirectory} is a named profile`);\n      profile = await copyByName({ name: profileDirectory });\n    }\n\n    return configureThisProfile(profile, { app, customPrefs });\n  } catch (error) {\n    throw new WebExtError(\n      `Could not copy Firefox profile from ${profileDirectory}: ${error}`\n    );\n  }\n}\n\n// installExtension types and implementation.\n\nexport type InstallExtensionParams = {|\n  asProxy?: boolean,\n  manifestData: ExtensionManifest,\n  profile: FirefoxProfile,\n  extensionPath: string,\n  asyncFsStat?: typeof defaultAsyncFsStat,\n|};\n\n/*\n * Installs an extension into the given Firefox profile object.\n * Resolves when complete.\n *\n * The extension is copied into a special location and you need to turn\n * on some preferences to allow this. See extensions.autoDisableScopes in\n * ./preferences.js.\n *\n * When asProxy is true, a special proxy file will be installed. This is a\n * text file that contains the path to the extension source.\n */\nexport async function installExtension({\n  asProxy = false,\n  manifestData,\n  profile,\n  extensionPath,\n  asyncFsStat = defaultAsyncFsStat,\n}: InstallExtensionParams): Promise<any> {\n  // This more or less follows\n  // https://github.com/saadtazi/firefox-profile-js/blob/master/lib/firefox_profile.js#L531\n  // (which is broken for web extensions).\n  // TODO: maybe uplift a patch that supports web extensions instead?\n\n  if (!profile.extensionsDir) {\n    throw new WebExtError('profile.extensionsDir was unexpectedly empty');\n  }\n\n  try {\n    await asyncFsStat(profile.extensionsDir);\n  } catch (error) {\n    if (isErrorWithCode('ENOENT', error)) {\n      log.debug(`Creating extensions directory: ${profile.extensionsDir}`);\n      await fs.mkdir(profile.extensionsDir);\n    } else {\n      throw error;\n    }\n  }\n\n  const id = getManifestId(manifestData);\n  if (!id) {\n    throw new UsageError(\n      'An explicit extension ID is required when installing to ' +\n        'a profile (applications.gecko.id not found in manifest.json)'\n    );\n  }\n\n  if (asProxy) {\n    log.debug(`Installing as an extension proxy; source: ${extensionPath}`);\n\n    const isDir = await isDirectory(extensionPath);\n    if (!isDir) {\n      throw new WebExtError(\n        'proxy install: extensionPath must be the extension source ' +\n          `directory; got: ${extensionPath}`\n      );\n    }\n\n    // Write a special extension proxy file containing the source\n    // directory. See:\n    // https://developer.mozilla.org/en-US/Add-ons/Setting_up_extension_development_environment#Firefox_extension_proxy_file\n    const destPath = path.join(profile.extensionsDir, `${id}`);\n    const writeStream = nodeFs.createWriteStream(destPath);\n    writeStream.write(extensionPath);\n    writeStream.end();\n    return await fromEvent(writeStream, 'close');\n  } else {\n    // Write the XPI file to the profile.\n    const readStream = nodeFs.createReadStream(extensionPath);\n    const destPath = path.join(profile.extensionsDir, `${id}.xpi`);\n    const writeStream = nodeFs.createWriteStream(destPath);\n\n    log.debug(`Installing extension from ${extensionPath} to ${destPath}`);\n    readStream.pipe(writeStream);\n\n    return await Promise.all([\n      fromEvent(readStream, 'close'),\n      fromEvent(writeStream, 'close'),\n    ]);\n  }\n}\n"], "mappings": "AACA,OAAOA,MAAM,MAAM,IAAI;AACvB,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,SAAS,QAAQ,MAAM;AAEhC,SAASC,OAAO,IAAIC,eAAe,QAAQ,WAAW;AACtD,OAAOC,cAAc,MAAM,iBAAiB;AAC5C,SAASC,EAAE,QAAQ,IAAI;AACvB,OAAOC,SAAS,MAAM,2BAA2B;AAEjD,OAAOC,WAAW,MAAM,yBAAyB;AACjD,SAASC,eAAe,EAAEC,UAAU,EAAEC,WAAW,QAAQ,cAAc;AACvE,SAASC,QAAQ,IAAIC,iBAAiB,QAAQ,kBAAkB;AAChE,SAASC,aAAa,QAAQ,qBAAqB;AACnD,SAASC,eAAe,IAAIC,uBAAuB,QAAQ,aAAa;AACxE,SAASC,YAAY,QAAQ,mBAAmB;AAChD;;AASA,MAAMC,GAAG,GAAGD,YAAY,CAACE,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC;AAEzC,MAAMC,kBAAkC,GAAGhB,EAAE,CAACiB,IAAI,CAACC,IAAI,CAAClB,EAAE,CAAC;AAE3D,MAAMmB,wBAAwB,GAAGpB,cAAc,CAACqB,mBAAmB;AAEnE,OAAO,MAAMC,iBAAiB,GAAG;EAC/BC,iBAAiB,EAAE,OAAO;EAC1BC,8BAA8B,EAAE;AAClC,CAAC;;AAED;;AAIA;;AAwCA;;AAYA;AACA;AACA;AACA,OAAO,eAAeC,GAAGA,CACvBC,OAAuB,EACvB;EACEC,QAAQ,GAAG5B,eAAe;EAC1B6B,cAAc,GAAGjB,uBAAuB;EACxCkB,aAAa;EACbC,UAAU;EACVC,UAAU;EACVC;AACiB,CAAC,GAAG,CAAC,CAAC,EACH;EACtBnB,GAAG,CAACoB,KAAK,CAAE,mCAAkCP,OAAO,CAAC9B,IAAI,CAAC,CAAE,EAAC,CAAC;EAE9D,MAAMsC,UAAU,GAAG,MAAMN,cAAc,CAAC,CAAC;EAEzC,IAAIC,aAAa,IAAIA,aAAa,CAACM,UAAU,CAAC,UAAU,CAAC,EAAE;IACzD,MAAMC,YAAY,GAAGP,aAAa,CAACQ,SAAS,CAAC,CAAC,CAAC;IAC/CxB,GAAG,CAACoB,KAAK,CAAE,2CAA0CG,YAAa,EAAC,CAAC;;IAEpE;IACAP,aAAa,GAAG,SAAS;IACzBC,UAAU,GAAG,CACX,KAAK,EACJ,gBAAeJ,OAAO,CAAC9B,IAAI,CAAC,CAAE,EAAC,EAChC,GAAGmC,UAAU,CAACO,GAAG,CAAC,CAAC;MAAEC;IAAU,CAAC,KAAM,gBAAeA,SAAU,KAAI,CAAC;IACpE;IACA;IACA;IACA,iBAAiB;IACjB;IACA;IACA;IACA,mBAAmB,EACnBH,YAAY,CACb,CAACI,MAAM,CAAC,IAAIV,UAAU,IAAI,EAAE,CAAC,CAAC;EACjC;EAEA,MAAMW,OAAO,GAAG,MAAMd,QAAQ,CAAC;IAC7B;IACAe,MAAM,EAAEb,aAAa;IACrB,aAAa,EAAEC,UAAU;IACzB;IACA;IACA,mBAAmB,EAAED,aAAa,KAAK,SAAS;IAChD;IACA;IACA,WAAW,EAAE,IAAI;IACjBc,MAAM,EAAET,UAAU;IAClBU,UAAU,EAAE,IAAI;IAChBlB,OAAO,EAAEA,OAAO,CAAC9B,IAAI,CAAC,CAAC;IACvBiD,GAAG,EAAE;MACH,GAAGC,OAAO,CAACD,GAAG;MACd,GAAGvB;IACL,CAAC;IACDyB,OAAO,EAAE;EACX,CAAC,CAAC;EAEF,MAAMC,OAAO,GAAGP,OAAO,CAACK,OAAO;EAE/BjC,GAAG,CAACoB,KAAK,CAAE,6BAA4BQ,OAAO,CAACC,MAAO,EAAC,CAAC;EACxD7B,GAAG,CAACoB,KAAK,CAAE,iBAAgBQ,OAAO,CAACQ,IAAI,CAACC,IAAI,CAAC,GAAG,CAAE,EAAC,CAAC;EAEpDF,OAAO,CAACG,EAAE,CAAC,OAAO,EAAGC,KAAK,IAAK;IAC7B;IACA;IACAvC,GAAG,CAACuC,KAAK,CAAE,kBAAiBA,KAAM,EAAC,CAAC;IACpC,MAAMA,KAAK;EACb,CAAC,CAAC;EAEF,IAAI,CAACpB,QAAQ,EAAE;IACbnB,GAAG,CAACwC,IAAI,CAAC,4CAA4C,CAAC;EACxD;EACA,IAAIrB,QAAQ,EAAE;IACZnB,GAAG,CAACwC,IAAI,CAAC,0CAA0C,CAAC;IACpDxC,GAAG,CAACwC,IAAI,CAAC,gEAAgE,CAAC;EAC5E;EAEAL,OAAO,CAACM,MAAM,CAACH,EAAE,CAAC,MAAM,EAAGI,IAAI,IAAK;IAClC1C,GAAG,CAACoB,KAAK,CAAE,mBAAkBsB,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,IAAI,CAAC,CAAE,EAAC,CAAC;EACxD,CAAC,CAAC;EAEFT,OAAO,CAACU,MAAM,CAACP,EAAE,CAAC,MAAM,EAAGI,IAAI,IAAK;IAClC1C,GAAG,CAACoB,KAAK,CAAE,mBAAkBsB,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,IAAI,CAAC,CAAE,EAAC,CAAC;EACxD,CAAC,CAAC;EAEFT,OAAO,CAACG,EAAE,CAAC,OAAO,EAAE,MAAM;IACxBtC,GAAG,CAACoB,KAAK,CAAC,gBAAgB,CAAC;EAC7B,CAAC,CAAC;EAEF,OAAO;IAAEe,OAAO;IAAEW,YAAY,EAAEzB;EAAW,CAAC;AAC9C;;AAEA;;AAEA,MAAM0B,sBAAsB,GAAG,CAAC,SAAS,EAAE,qBAAqB,CAAC;AAQjE;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,eAAeC,gBAAgBA,CACpCC,iBAAyB,EACzBC,aAA4C,GAAG/D,cAAc,CAACgE,MAAM,EACpEC,MAAuB,GAAGhE,EAAE,CAACiB,IAAI,EACf;EAClB,IAAI0C,sBAAsB,CAACM,QAAQ,CAACJ,iBAAiB,CAAC,EAAE;IACtD,OAAO,IAAI;EACb;EAEA,MAAMK,cAAc,GAAGJ,aAAa,CAACK,mBAAmB,CAAC,CAAC;EAC1D,MAAMC,eAAe,GAAGzE,IAAI,CAACsD,IAAI,CAACiB,cAAc,EAAE,cAAc,CAAC;EACjE,IAAI;IACF,MAAMF,MAAM,CAACI,eAAe,CAAC;EAC/B,CAAC,CAAC,OAAOjB,KAAK,EAAE;IACd,IAAIhD,eAAe,CAAC,QAAQ,EAAEgD,KAAK,CAAC,EAAE;MACpCvC,GAAG,CAACoB,KAAK,CAAE,2BAA0BmB,KAAM,EAAC,CAAC;;MAE7C;MACA;MACA,OAAO,KAAK;IACd;;IAEA;IACA,MAAMA,KAAK;EACb;;EAEA;EACA,MAAMkB,MAAM,GAAG,IAAIP,aAAa,CAACI,cAAc,CAAC;EAChD,MAAMI,YAAY,GAAG1E,SAAS,CAAC,CAAC,GAAGoD,IAAI,KAAKqB,MAAM,CAACC,YAAY,CAAC,GAAGtB,IAAI,CAAC,CAAC;EAEzE,MAAMsB,YAAY,CAAC,CAAC;EAEpB,MAAMC,wBAAwB,GAAG5E,IAAI,CAAC6E,SAAS,CAC7C7E,IAAI,CAACsD,IAAI,CAACtD,IAAI,CAAC8E,OAAO,CAACZ,iBAAiB,CAAC,EAAElE,IAAI,CAAC+E,GAAG,CACrD,CAAC;EAED,KAAK,MAAMjD,OAAO,IAAI4C,MAAM,CAACM,QAAQ,EAAE;IACrC;IACA;IACA,IACEhB,sBAAsB,CAACM,QAAQ,CAACxC,OAAO,CAACmD,IAAI,CAAC,IAC7CnD,OAAO,CAACoD,OAAO,KAAK,GAAG,EACvB;MACA,IAAIC,eAAe;;MAEnB;MACA,IAAIrD,OAAO,CAACmD,IAAI,KAAKf,iBAAiB,EAAE;QACtC,OAAO,IAAI;MACb;;MAEA;MACA,IAAIpC,OAAO,CAACsD,UAAU,KAAK,GAAG,EAAE;QAC9BD,eAAe,GAAGnF,IAAI,CAACsD,IAAI,CAACiB,cAAc,EAAEzC,OAAO,CAACuD,IAAI,EAAErF,IAAI,CAAC+E,GAAG,CAAC;MACrE,CAAC,MAAM;QACLI,eAAe,GAAGnF,IAAI,CAACsD,IAAI,CAACxB,OAAO,CAACuD,IAAI,EAAErF,IAAI,CAAC+E,GAAG,CAAC;MACrD;MAEA,IAAI/E,IAAI,CAAC6E,SAAS,CAACM,eAAe,CAAC,KAAKP,wBAAwB,EAAE;QAChE,OAAO,IAAI;MACb;IACF;EACF;;EAEA;EACA,OAAO,KAAK;AACd;;AAEA;;AAaA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASU,gBAAgBA,CAC9BxD,OAAuB,EACvB;EACEyD,GAAG,GAAG,SAAS;EACf5E,QAAQ,GAAGC,iBAAiB;EAC5B4E,WAAW,GAAG,CAAC;AACQ,CAAC,GAAG,CAAC,CAAC,EACN;EACzB;EACA;EACA,MAAMC,KAAK,GAAG9E,QAAQ,CAAC4E,GAAG,CAAC;EAC3BG,MAAM,CAACC,IAAI,CAACF,KAAK,CAAC,CAACG,OAAO,CAAEC,IAAI,IAAK;IACnC/D,OAAO,CAACgE,aAAa,CAACD,IAAI,EAAEJ,KAAK,CAACI,IAAI,CAAC,CAAC;EAC1C,CAAC,CAAC;EACF,IAAIH,MAAM,CAACC,IAAI,CAACH,WAAW,CAAC,CAACO,MAAM,GAAG,CAAC,EAAE;IACvC,MAAMC,cAAc,GAAGC,IAAI,CAACC,SAAS,CAACV,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;IAC3DvE,GAAG,CAACwC,IAAI,CAAE,uCAAsCuC,cAAe,EAAC,CAAC;IACjEN,MAAM,CAACC,IAAI,CAACH,WAAW,CAAC,CAACI,OAAO,CAAEO,MAAM,IAAK;MAC3CrE,OAAO,CAACgE,aAAa,CAACK,MAAM,EAAEX,WAAW,CAACW,MAAM,CAAC,CAAC;IACpD,CAAC,CAAC;EACJ;EACArE,OAAO,CAACsE,iBAAiB,CAAC,CAAC;EAC3B,OAAOC,OAAO,CAACvB,OAAO,CAAChD,OAAO,CAAC;AACjC;AASA,OAAO,SAASwE,0BAA0BA,CAAC;EACzCC,iBAAiB;EACjBC,SAAS,GAAGpG;AACa,CAAC,GAAG,CAAC,CAAC,EAAgB;EAC/C,MAAMsE,MAAM,GAAG,IAAI8B,SAAS,CAACpC,MAAM,CAACmC,iBAAiB,CAAC;EACtD,MAAM5B,YAAY,GAAG1E,SAAS,CAAC,CAAC,GAAGoD,IAAI,KAAKqB,MAAM,CAACC,YAAY,CAAC,GAAGtB,IAAI,CAAC,CAAC;EACzE,MAAMoD,OAAO,GAAGxG,SAAS,CAAC,CAAC,GAAGoD,IAAI,KAAKqB,MAAM,CAAC+B,OAAO,CAAC,GAAGpD,IAAI,CAAC,CAAC;EAC/D,OAAO,MAAOqD,WAAmB,IAA6B;IAC5D,IAAI;MACF,MAAM/B,YAAY,CAAC,CAAC;MACpB,MAAMgC,cAAc,GAClBjC,MAAM,CAACM,QAAQ,CAAC4B,MAAM,CAAEC,UAAU,IAAKA,UAAU,CAAC5B,IAAI,KAAKyB,WAAW,CAAC,CACpEX,MAAM,KAAK,CAAC;MACjB,IAAIY,cAAc,EAAE;QAClB,OAAO,MAAMF,OAAO,CAACC,WAAW,CAAC;MACnC;IACF,CAAC,CAAC,OAAOlD,KAAK,EAAE;MACd,IAAI,CAAChD,eAAe,CAAC,QAAQ,EAAEgD,KAAK,CAAC,EAAE;QACrC,MAAMA,KAAK;MACb;MACAvC,GAAG,CAAC6F,IAAI,CAAC,qCAAqC,CAAC;IACjD;EACF,CAAC;AACH;;AAEA;;AAUA;;AAEA,OAAO,eAAeC,UAAUA,CAC9BC,WAAmB,EACnB;EACEzB,GAAG;EACH0B,oBAAoB,GAAG3B,gBAAgB;EACvC4B,uBAAuB,GAAGjD,gBAAgB;EAC1CuB,WAAW,GAAG,CAAC,CAAC;EAChB2B,mBAAmB,GAAGb;AACN,CAAC,GAAG,CAAC,CAAC,EACC;EACzB,MAAMc,kBAAkB,GAAG,MAAMF,uBAAuB,CAACF,WAAW,CAAC;EACrE,IAAII,kBAAkB,EAAE;IACtB,MAAM,IAAI3G,UAAU,CAClB,wDAAwD,GACrD,MAAKuG,WAAY,IAAG,GACrB,sEAAsE,GACtE,sDACJ,CAAC;EACH;EAEA,IAAIK,oBAAoB;EACxB,MAAMC,cAAc,GAAGH,mBAAmB,CAAC,CAAC;EAE5C,MAAMI,gBAAgB,GAAG,MAAMhH,WAAW,CAACyG,WAAW,CAAC;EACvD,IAAIO,gBAAgB,EAAE;IACpBtG,GAAG,CAACoB,KAAK,CAAE,4BAA2B2E,WAAY,GAAE,CAAC;IACrDK,oBAAoB,GAAGL,WAAW;EACpC,CAAC,MAAM;IACL/F,GAAG,CAACoB,KAAK,CAAE,YAAW2E,WAAY,qBAAoB,CAAC;IACvDK,oBAAoB,GAAG,MAAMC,cAAc,CAACN,WAAW,CAAC;IACxD,IAAI,CAACK,oBAAoB,EAAE;MACzB,MAAM,IAAI5G,UAAU,CACjB,gBAAeuG,WAAY,iBAAgB,GAC1C,sCACJ,CAAC;IACH;EACF;EAEA,MAAMlF,OAAO,GAAG,IAAI1B,cAAc,CAAC;IAAEiH;EAAqB,CAAC,CAAC;EAC5D,OAAO,MAAMJ,oBAAoB,CAACnF,OAAO,EAAE;IAAEyD,GAAG;IAAEC;EAAY,CAAC,CAAC;AAClE;;AAEA;;AAQA;AACA;AACA;AACA;AACA;AACA,OAAO,eAAegC,aAAaA,CAAC;EAClCjC,GAAG;EACH0B,oBAAoB,GAAG3B,gBAAgB;EACvCE,WAAW,GAAG,CAAC;AACI,CAAC,GAAG,CAAC,CAAC,EAA2B;EACpD,MAAM1D,OAAO,GAAG,IAAI1B,cAAc,CAAC,CAAC;EACpC,OAAO,MAAM6G,oBAAoB,CAACnF,OAAO,EAAE;IAAEyD,GAAG;IAAEC;EAAY,CAAC,CAAC;AAClE;;AAEA;;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,eAAeiC,WAAWA,CAC/BC,gBAAwB,EACxB;EACEnC,GAAG;EACH0B,oBAAoB,GAAG3B,gBAAgB;EACvC7D,mBAAmB,GAAGD,wBAAwB;EAC9CgE,WAAW,GAAG,CAAC;AACG,CAAC,GAAG,CAAC,CAAC,EACD;EACzB,MAAMmC,IAAI,GAAG1H,SAAS,CAACG,cAAc,CAACuH,IAAI,CAAC;EAC3C,MAAMC,UAAU,GAAG3H,SAAS,CAACwB,mBAAmB,CAAC;EAEjD,IAAI;IACF,MAAMoG,SAAS,GAAG,MAAMtH,WAAW,CAACmH,gBAAgB,CAAC;IAErD,IAAI5F,OAAO;IAEX,IAAI+F,SAAS,EAAE;MACb5G,GAAG,CAACoB,KAAK,CAAE,mCAAkCqF,gBAAiB,GAAE,CAAC;MACjE5F,OAAO,GAAG,MAAM6F,IAAI,CAAC;QAAED;MAAiB,CAAC,CAAC;IAC5C,CAAC,MAAM;MACLzG,GAAG,CAACoB,KAAK,CAAE,YAAWqF,gBAAiB,qBAAoB,CAAC;MAC5D5F,OAAO,GAAG,MAAM8F,UAAU,CAAC;QAAEE,IAAI,EAAEJ;MAAiB,CAAC,CAAC;IACxD;IAEA,OAAOT,oBAAoB,CAACnF,OAAO,EAAE;MAAEyD,GAAG;MAAEC;IAAY,CAAC,CAAC;EAC5D,CAAC,CAAC,OAAOhC,KAAK,EAAE;IACd,MAAM,IAAI9C,WAAW,CAClB,uCAAsCgH,gBAAiB,KAAIlE,KAAM,EACpE,CAAC;EACH;AACF;;AAEA;;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,eAAeuE,gBAAgBA,CAAC;EACrCC,OAAO,GAAG,KAAK;EACfC,YAAY;EACZnG,OAAO;EACPoG,aAAa;EACbC,WAAW,GAAG9G;AACQ,CAAC,EAAgB;EACvC;EACA;EACA;EACA;;EAEA,IAAI,CAACS,OAAO,CAACsG,aAAa,EAAE;IAC1B,MAAM,IAAI1H,WAAW,CAAC,8CAA8C,CAAC;EACvE;EAEA,IAAI;IACF,MAAMyH,WAAW,CAACrG,OAAO,CAACsG,aAAa,CAAC;EAC1C,CAAC,CAAC,OAAO5E,KAAK,EAAE;IACd,IAAIhD,eAAe,CAAC,QAAQ,EAAEgD,KAAK,CAAC,EAAE;MACpCvC,GAAG,CAACoB,KAAK,CAAE,kCAAiCP,OAAO,CAACsG,aAAc,EAAC,CAAC;MACpE,MAAM/H,EAAE,CAACgI,KAAK,CAACvG,OAAO,CAACsG,aAAa,CAAC;IACvC,CAAC,MAAM;MACL,MAAM5E,KAAK;IACb;EACF;EAEA,MAAM8E,EAAE,GAAGzH,aAAa,CAACoH,YAAY,CAAC;EACtC,IAAI,CAACK,EAAE,EAAE;IACP,MAAM,IAAI7H,UAAU,CAClB,0DAA0D,GACxD,8DACJ,CAAC;EACH;EAEA,IAAIuH,OAAO,EAAE;IACX/G,GAAG,CAACoB,KAAK,CAAE,6CAA4C6F,aAAc,EAAC,CAAC;IAEvE,MAAMK,KAAK,GAAG,MAAMhI,WAAW,CAAC2H,aAAa,CAAC;IAC9C,IAAI,CAACK,KAAK,EAAE;MACV,MAAM,IAAI7H,WAAW,CACnB,4DAA4D,GACzD,mBAAkBwH,aAAc,EACrC,CAAC;IACH;;IAEA;IACA;IACA;IACA,MAAMM,QAAQ,GAAGxI,IAAI,CAACsD,IAAI,CAACxB,OAAO,CAACsG,aAAa,EAAG,GAAEE,EAAG,EAAC,CAAC;IAC1D,MAAMG,WAAW,GAAG1I,MAAM,CAAC2I,iBAAiB,CAACF,QAAQ,CAAC;IACtDC,WAAW,CAACE,KAAK,CAACT,aAAa,CAAC;IAChCO,WAAW,CAACG,GAAG,CAAC,CAAC;IACjB,OAAO,MAAMtI,SAAS,CAACmI,WAAW,EAAE,OAAO,CAAC;EAC9C,CAAC,MAAM;IACL;IACA,MAAMI,UAAU,GAAG9I,MAAM,CAAC+I,gBAAgB,CAACZ,aAAa,CAAC;IACzD,MAAMM,QAAQ,GAAGxI,IAAI,CAACsD,IAAI,CAACxB,OAAO,CAACsG,aAAa,EAAG,GAAEE,EAAG,MAAK,CAAC;IAC9D,MAAMG,WAAW,GAAG1I,MAAM,CAAC2I,iBAAiB,CAACF,QAAQ,CAAC;IAEtDvH,GAAG,CAACoB,KAAK,CAAE,6BAA4B6F,aAAc,OAAMM,QAAS,EAAC,CAAC;IACtEK,UAAU,CAACE,IAAI,CAACN,WAAW,CAAC;IAE5B,OAAO,MAAMpC,OAAO,CAAC2C,GAAG,CAAC,CACvB1I,SAAS,CAACuI,UAAU,EAAE,OAAO,CAAC,EAC9BvI,SAAS,CAACmI,WAAW,EAAE,OAAO,CAAC,CAChC,CAAC;EACJ;AACF"}