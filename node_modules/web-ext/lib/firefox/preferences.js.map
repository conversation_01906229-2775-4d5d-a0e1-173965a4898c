{"version": 3, "file": "preferences.js", "names": ["WebExtError", "UsageError", "createLogger", "log", "import", "meta", "url", "nonOverridablePreferences", "prefsCommon", "prefsFennec", "prefsFirefox", "prefs", "common", "fennec", "firefox", "getPrefs", "app", "appPrefs", "coerceCLICustomPreference", "cliPrefs", "customPrefs", "pref", "prefsAry", "split", "length", "key", "value", "slice", "join", "test", "parseInt", "includes", "warn"], "sources": ["../../src/firefox/preferences.js"], "sourcesContent": ["/* @flow */\nimport { WebExtError, UsageError } from '../errors.js';\nimport { createLogger } from '../util/logger.js';\n\nconst log = createLogger(import.meta.url);\n\nexport const nonOverridablePreferences = [\n  'devtools.debugger.remote-enabled',\n  'devtools.debugger.prompt-connection',\n  'xpinstall.signatures.required',\n];\n\n// Flow Types\n\nexport type FirefoxPreferences = {\n  [key: string]: boolean | string | number,\n};\n\nexport type PreferencesAppName = 'firefox' | 'fennec';\n\n// Preferences Maps\n\nconst prefsCommon: FirefoxPreferences = {\n  // Allow debug output via dump to be printed to the system console\n  'browser.dom.window.dump.enabled': true,\n\n  // From:\n  // https://firefox-source-docs.mozilla.org/toolkit/components/telemetry/internals/preferences.html#data-choices-notification\n  // This is the data submission master kill switch. If disabled, no policy is shown or upload takes place, ever.\n  'datareporting.policy.dataSubmissionEnabled': false,\n\n  // Allow remote connections to the debugger.\n  'devtools.debugger.remote-enabled': true,\n  // Disable the prompt for allowing connections.\n  'devtools.debugger.prompt-connection': false,\n  // Allow extensions to log messages on browser's console.\n  'devtools.browserconsole.contentMessages': true,\n\n  // Turn off platform logging because it is a lot of info.\n  'extensions.logging.enabled': false,\n\n  // Disable extension updates and notifications.\n  'extensions.checkCompatibility.nightly': false,\n  'extensions.update.enabled': false,\n  'extensions.update.notifyUser': false,\n\n  // From:\n  // http://hg.mozilla.org/mozilla-central/file/1dd81c324ac7/build/automation.py.in//l372\n  // Only load extensions from the application and user profile.\n  // AddonManager.SCOPE_PROFILE + AddonManager.SCOPE_APPLICATION\n  'extensions.enabledScopes': 5,\n  // Disable metadata caching for installed add-ons by default.\n  'extensions.getAddons.cache.enabled': false,\n  // Disable intalling any distribution add-ons.\n  'extensions.installDistroAddons': false,\n  // Allow installing extensions dropped into the profile folder.\n  'extensions.autoDisableScopes': 10,\n\n  // Disable app update.\n  'app.update.enabled': false,\n\n  // Allow unsigned add-ons.\n  'xpinstall.signatures.required': false,\n\n  // browser.link.open_newwindow is changed from 3 to 2 in:\n  // https://github.com/saadtazi/firefox-profile-js/blob/cafc793d940a779d280103ae17d02a92de862efc/lib/firefox_profile.js#L32\n  // Restore original value to avoid https://github.com/mozilla/web-ext/issues/1592\n  'browser.link.open_newwindow': 3,\n};\n\n// Prefs specific to Firefox for Android.\nconst prefsFennec: FirefoxPreferences = {\n  'browser.console.showInPanel': true,\n  'browser.firstrun.show.uidiscovery': false,\n  'devtools.remote.usb.enabled': true,\n};\n\n// Prefs specific to Firefox for desktop.\nconst prefsFirefox: FirefoxPreferences = {\n  'browser.startup.homepage': 'about:blank',\n  'startup.homepage_welcome_url': 'about:blank',\n  'startup.homepage_welcome_url.additional': '',\n  'devtools.errorconsole.enabled': true,\n  'devtools.chrome.enabled': true,\n\n  // From:\n  // http://hg.mozilla.org/mozilla-central/file/1dd81c324ac7/build/automation.py.in//l388\n  // Make url-classifier updates so rare that they won't affect tests.\n  'urlclassifier.updateinterval': 172800,\n  // Point the url-classifier to a nonexistent local URL for fast failures.\n  'browser.safebrowsing.provider.0.gethashURL':\n    'http://localhost/safebrowsing-dummy/gethash',\n  'browser.safebrowsing.provider.0.keyURL':\n    'http://localhost/safebrowsing-dummy/newkey',\n  'browser.safebrowsing.provider.0.updateURL':\n    'http://localhost/safebrowsing-dummy/update',\n\n  // Disable self repair/SHIELD\n  'browser.selfsupport.url': 'https://localhost/selfrepair',\n  // Disable Reader Mode UI tour\n  'browser.reader.detectedFirstArticle': true,\n\n  // Set the policy firstURL to an empty string to prevent\n  // the privacy info page to be opened on every \"web-ext run\".\n  // (See #1114 for rationale)\n  'datareporting.policy.firstRunURL': '',\n};\n\nconst prefs = {\n  common: prefsCommon,\n  fennec: prefsFennec,\n  firefox: prefsFirefox,\n};\n\n// Module exports\n\nexport type PreferencesGetterFn = (\n  appName: PreferencesAppName\n) => FirefoxPreferences;\n\nexport function getPrefs(\n  app: PreferencesAppName = 'firefox'\n): FirefoxPreferences {\n  const appPrefs = prefs[app];\n  if (!appPrefs) {\n    throw new WebExtError(`Unsupported application: ${app}`);\n  }\n  return {\n    ...prefsCommon,\n    ...appPrefs,\n  };\n}\n\nexport function coerceCLICustomPreference(\n  cliPrefs: Array<string>\n): FirefoxPreferences {\n  const customPrefs = {};\n\n  for (const pref of cliPrefs) {\n    const prefsAry = pref.split('=');\n\n    if (prefsAry.length < 2) {\n      throw new UsageError(\n        `Incomplete custom preference: \"${pref}\". ` +\n          'Syntax expected: \"prefname=prefvalue\".'\n      );\n    }\n\n    const key = prefsAry[0];\n    let value = prefsAry.slice(1).join('=');\n\n    if (/[^\\w{@}.-]/.test(key)) {\n      throw new UsageError(`Invalid custom preference name: ${key}`);\n    }\n\n    if (value === `${parseInt(value)}`) {\n      value = parseInt(value, 10);\n    } else if (value === 'true' || value === 'false') {\n      value = value === 'true';\n    }\n\n    if (nonOverridablePreferences.includes(key)) {\n      log.warn(`'${key}' preference cannot be customized.`);\n      continue;\n    }\n    customPrefs[`${key}`] = value;\n  }\n\n  return customPrefs;\n}\n"], "mappings": "AACA,SAASA,WAAW,EAAEC,UAAU,QAAQ,cAAc;AACtD,SAASC,YAAY,QAAQ,mBAAmB;AAEhD,MAAMC,GAAG,GAAGD,YAAY,CAACE,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC;AAEzC,OAAO,MAAMC,yBAAyB,GAAG,CACvC,kCAAkC,EAClC,qCAAqC,EACrC,+BAA+B,CAChC;;AAED;;AAQA;;AAEA,MAAMC,WAA+B,GAAG;EACtC;EACA,iCAAiC,EAAE,IAAI;EAEvC;EACA;EACA;EACA,4CAA4C,EAAE,KAAK;EAEnD;EACA,kCAAkC,EAAE,IAAI;EACxC;EACA,qCAAqC,EAAE,KAAK;EAC5C;EACA,yCAAyC,EAAE,IAAI;EAE/C;EACA,4BAA4B,EAAE,KAAK;EAEnC;EACA,uCAAuC,EAAE,KAAK;EAC9C,2BAA2B,EAAE,KAAK;EAClC,8BAA8B,EAAE,KAAK;EAErC;EACA;EACA;EACA;EACA,0BAA0B,EAAE,CAAC;EAC7B;EACA,oCAAoC,EAAE,KAAK;EAC3C;EACA,gCAAgC,EAAE,KAAK;EACvC;EACA,8BAA8B,EAAE,EAAE;EAElC;EACA,oBAAoB,EAAE,KAAK;EAE3B;EACA,+BAA+B,EAAE,KAAK;EAEtC;EACA;EACA;EACA,6BAA6B,EAAE;AACjC,CAAC;;AAED;AACA,MAAMC,WAA+B,GAAG;EACtC,6BAA6B,EAAE,IAAI;EACnC,mCAAmC,EAAE,KAAK;EAC1C,6BAA6B,EAAE;AACjC,CAAC;;AAED;AACA,MAAMC,YAAgC,GAAG;EACvC,0BAA0B,EAAE,aAAa;EACzC,8BAA8B,EAAE,aAAa;EAC7C,yCAAyC,EAAE,EAAE;EAC7C,+BAA+B,EAAE,IAAI;EACrC,yBAAyB,EAAE,IAAI;EAE/B;EACA;EACA;EACA,8BAA8B,EAAE,MAAM;EACtC;EACA,4CAA4C,EAC1C,6CAA6C;EAC/C,wCAAwC,EACtC,4CAA4C;EAC9C,2CAA2C,EACzC,4CAA4C;EAE9C;EACA,yBAAyB,EAAE,8BAA8B;EACzD;EACA,qCAAqC,EAAE,IAAI;EAE3C;EACA;EACA;EACA,kCAAkC,EAAE;AACtC,CAAC;AAED,MAAMC,KAAK,GAAG;EACZC,MAAM,EAAEJ,WAAW;EACnBK,MAAM,EAAEJ,WAAW;EACnBK,OAAO,EAAEJ;AACX,CAAC;;AAED;;AAMA,OAAO,SAASK,QAAQA,CACtBC,GAAuB,GAAG,SAAS,EACf;EACpB,MAAMC,QAAQ,GAAGN,KAAK,CAACK,GAAG,CAAC;EAC3B,IAAI,CAACC,QAAQ,EAAE;IACb,MAAM,IAAIjB,WAAW,CAAE,4BAA2BgB,GAAI,EAAC,CAAC;EAC1D;EACA,OAAO;IACL,GAAGR,WAAW;IACd,GAAGS;EACL,CAAC;AACH;AAEA,OAAO,SAASC,yBAAyBA,CACvCC,QAAuB,EACH;EACpB,MAAMC,WAAW,GAAG,CAAC,CAAC;EAEtB,KAAK,MAAMC,IAAI,IAAIF,QAAQ,EAAE;IAC3B,MAAMG,QAAQ,GAAGD,IAAI,CAACE,KAAK,CAAC,GAAG,CAAC;IAEhC,IAAID,QAAQ,CAACE,MAAM,GAAG,CAAC,EAAE;MACvB,MAAM,IAAIvB,UAAU,CACjB,kCAAiCoB,IAAK,KAAI,GACzC,wCACJ,CAAC;IACH;IAEA,MAAMI,GAAG,GAAGH,QAAQ,CAAC,CAAC,CAAC;IACvB,IAAII,KAAK,GAAGJ,QAAQ,CAACK,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IAEvC,IAAI,YAAY,CAACC,IAAI,CAACJ,GAAG,CAAC,EAAE;MAC1B,MAAM,IAAIxB,UAAU,CAAE,mCAAkCwB,GAAI,EAAC,CAAC;IAChE;IAEA,IAAIC,KAAK,KAAM,GAAEI,QAAQ,CAACJ,KAAK,CAAE,EAAC,EAAE;MAClCA,KAAK,GAAGI,QAAQ,CAACJ,KAAK,EAAE,EAAE,CAAC;IAC7B,CAAC,MAAM,IAAIA,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,OAAO,EAAE;MAChDA,KAAK,GAAGA,KAAK,KAAK,MAAM;IAC1B;IAEA,IAAInB,yBAAyB,CAACwB,QAAQ,CAACN,GAAG,CAAC,EAAE;MAC3CtB,GAAG,CAAC6B,IAAI,CAAE,IAAGP,GAAI,oCAAmC,CAAC;MACrD;IACF;IACAL,WAAW,CAAE,GAAEK,GAAI,EAAC,CAAC,GAAGC,KAAK;EAC/B;EAEA,OAAON,WAAW;AACpB"}