{"version": 3, "file": "remote.js", "names": ["net", "FirefoxRDPClient", "connectToFirefox", "defaultFirefoxConnector", "createLogger", "isErrorWithCode", "RemoteTempInstallNotSupported", "UsageError", "WebExtError", "log", "import", "meta", "url", "requestErrorToMessage", "err", "Error", "String", "error", "message", "RemoteFirefox", "constructor", "client", "checkedForAddonReloading", "on", "debug", "info", "JSON", "stringify", "rdpError", "disconnect", "addonRequest", "addon", "request", "response", "to", "actor", "type", "getAddonsActor", "addonsActor", "Promise", "reject", "installTemporaryAddon", "addon<PERSON>ath", "openDevTools", "getInstalledAddon", "addonId", "addons", "id", "map", "a", "checkForAddonReloading", "requestTypes", "indexOf", "supportedRequestTypes", "reloadAddon", "process", "stdout", "write", "Date", "toTimeString", "connect", "port", "connectWithMaxRetries", "maxRetries", "retryInterval", "establishConnection", "lastError", "retries", "resolve", "setTimeout", "stack", "findFreeTcpPort", "srv", "createServer", "listen", "freeTcpPort", "address", "close"], "sources": ["../../src/firefox/remote.js"], "sourcesContent": ["/* @flow */\nimport net from 'net';\n\nimport FirefoxRDPClient, {\n  connectToFirefox as defaultFirefoxConnector,\n} from './rdp-client.js';\nimport { createLogger } from '../util/logger.js';\nimport {\n  isErrorWithCode,\n  RemoteTempInstallNotSupported,\n  UsageError,\n  WebExtError,\n} from '../errors.js';\n\nconst log = createLogger(import.meta.url);\n\nexport type FirefoxConnectorFn = (port: number) => Promise<FirefoxRDPClient>;\n\nexport type FirefoxRDPAddonActor = {|\n  id: string,\n  actor: string,\n|};\n\nexport type FirefoxRDPResponseError = {|\n  error: string,\n  message: string,\n|};\n\nexport type FirefoxRDPResponseAddon = {|\n  addon: FirefoxRDPAddonActor,\n|};\n\nexport type FirefoxRDPResponseRequestTypes = {|\n  requestTypes: Array<string>,\n|};\n\n// NOTE: this type aliases Object to catch any other possible response.\nexport type FirefoxRDPResponseAny = Object;\n\nexport type FirefoxRDPResponseMaybe =\n  | FirefoxRDPResponseRequestTypes\n  | FirefoxRDPResponseAny;\n\n// Convert a request rejection to a message string.\nfunction requestErrorToMessage(err: Error | FirefoxRDPResponseError) {\n  if (err instanceof Error) {\n    return String(err);\n  }\n  return `${err.error}: ${err.message}`;\n}\n\nexport class RemoteFirefox {\n  client: Object;\n  checkedForAddonReloading: boolean;\n\n  constructor(client: FirefoxRDPClient) {\n    this.client = client;\n    this.checkedForAddonReloading = false;\n\n    client.on('disconnect', () => {\n      log.debug('Received \"disconnect\" from Firefox client');\n    });\n    client.on('end', () => {\n      log.debug('Received \"end\" from Firefox client');\n    });\n    client.on('unsolicited-event', (info) => {\n      log.debug(`Received message from client: ${JSON.stringify(info)}`);\n    });\n    client.on('rdp-error', (rdpError) => {\n      log.debug(`Received error from client: ${JSON.stringify(rdpError)}`);\n    });\n    client.on('error', (error) => {\n      log.debug(`Received error from client: ${String(error)}`);\n    });\n  }\n\n  disconnect() {\n    this.client.disconnect();\n  }\n\n  async addonRequest(\n    addon: FirefoxRDPAddonActor,\n    request: string\n  ): Promise<FirefoxRDPResponseMaybe> {\n    try {\n      const response = await this.client.request({\n        to: addon.actor,\n        type: request,\n      });\n      return response;\n    } catch (err) {\n      log.debug(`Client responded to '${request}' request with error:`, err);\n      const message = requestErrorToMessage(err);\n      throw new WebExtError(`Remote Firefox: addonRequest() error: ${message}`);\n    }\n  }\n\n  async getAddonsActor(): Promise<string> {\n    try {\n      // getRoot should work since Firefox 55 (bug 1352157).\n      const response = await this.client.request('getRoot');\n      if (response.addonsActor == null) {\n        return Promise.reject(\n          new RemoteTempInstallNotSupported(\n            'This version of Firefox does not provide an add-ons actor for ' +\n              'remote installation.'\n          )\n        );\n      }\n      return response.addonsActor;\n    } catch (err) {\n      // Fallback to listTabs otherwise, Firefox 49 - 77 (bug 1618691).\n      log.debug('Falling back to listTabs because getRoot failed', err);\n    }\n\n    try {\n      const response = await this.client.request('listTabs');\n      // addonsActor was added to listTabs in Firefox 49 (bug 1273183).\n      if (response.addonsActor == null) {\n        log.debug(\n          'listTabs returned a falsey addonsActor: ' +\n            `${JSON.stringify(response)}`\n        );\n        return Promise.reject(\n          new RemoteTempInstallNotSupported(\n            'This is an older version of Firefox that does not provide an ' +\n              'add-ons actor for remote installation. Try Firefox 49 or ' +\n              'higher.'\n          )\n        );\n      }\n      return response.addonsActor;\n    } catch (err) {\n      log.debug('listTabs error', err);\n      const message = requestErrorToMessage(err);\n      throw new WebExtError(`Remote Firefox: listTabs() error: ${message}`);\n    }\n  }\n\n  async installTemporaryAddon(\n    addonPath: string,\n    openDevTools?: boolean\n  ): Promise<FirefoxRDPResponseAddon> {\n    const addonsActor = await this.getAddonsActor();\n\n    try {\n      const response = await this.client.request({\n        to: addonsActor,\n        type: 'installTemporaryAddon',\n        addonPath,\n        openDevTools,\n      });\n      log.debug(`installTemporaryAddon: ${JSON.stringify(response)}`);\n      log.info(`Installed ${addonPath} as a temporary add-on`);\n      return response;\n    } catch (err) {\n      const message = requestErrorToMessage(err);\n      throw new WebExtError(`installTemporaryAddon: Error: ${message}`);\n    }\n  }\n\n  async getInstalledAddon(addonId: string): Promise<FirefoxRDPAddonActor> {\n    try {\n      const response = await this.client.request('listAddons');\n      for (const addon of response.addons) {\n        if (addon.id === addonId) {\n          return addon;\n        }\n      }\n      log.debug(\n        `Remote Firefox has these addons: ${response.addons.map((a) => a.id)}`\n      );\n      return Promise.reject(\n        new WebExtError(\n          'The remote Firefox does not have your extension installed'\n        )\n      );\n    } catch (err) {\n      const message = requestErrorToMessage(err);\n      throw new WebExtError(`Remote Firefox: listAddons() error: ${message}`);\n    }\n  }\n\n  async checkForAddonReloading(\n    addon: FirefoxRDPAddonActor\n  ): Promise<FirefoxRDPAddonActor> {\n    if (this.checkedForAddonReloading) {\n      // We only need to check once if reload() is supported.\n      return addon;\n    } else {\n      const response = await this.addonRequest(addon, 'requestTypes');\n\n      if (response.requestTypes.indexOf('reload') === -1) {\n        const supportedRequestTypes = JSON.stringify(response.requestTypes);\n        log.debug(`Remote Firefox only supports: ${supportedRequestTypes}`);\n        throw new UsageError(\n          'This Firefox version does not support add-on reloading. ' +\n            'Re-run with --no-reload'\n        );\n      } else {\n        this.checkedForAddonReloading = true;\n        return addon;\n      }\n    }\n  }\n\n  async reloadAddon(addonId: string): Promise<void> {\n    const addon = await this.getInstalledAddon(addonId);\n    await this.checkForAddonReloading(addon);\n    await this.addonRequest(addon, 'reload');\n    process.stdout.write(\n      `\\rLast extension reload: ${new Date().toTimeString()}`\n    );\n    log.debug('\\n');\n  }\n}\n\n// Connect types and implementation\n\nexport type ConnectOptions = {\n  connectToFirefox: FirefoxConnectorFn,\n};\n\nexport async function connect(\n  port: number,\n  { connectToFirefox = defaultFirefoxConnector }: ConnectOptions = {}\n): Promise<RemoteFirefox> {\n  log.debug(`Connecting to Firefox on port ${port}`);\n  const client = await connectToFirefox(port);\n  log.debug(`Connected to the remote Firefox debugger on port ${port}`);\n  return new RemoteFirefox(client);\n}\n\n// ConnectWithMaxRetries types and implementation\n\nexport type ConnectWithMaxRetriesParams = {|\n  maxRetries?: number,\n  retryInterval?: number,\n  port: number,\n|};\n\nexport type ConnectWithMaxRetriesDeps = {\n  connectToFirefox: typeof connect,\n};\n\nexport async function connectWithMaxRetries(\n  // A max of 250 will try connecting for 30 seconds.\n  { maxRetries = 250, retryInterval = 120, port }: ConnectWithMaxRetriesParams,\n  { connectToFirefox = connect }: ConnectWithMaxRetriesDeps = {}\n): Promise<RemoteFirefox> {\n  async function establishConnection() {\n    var lastError;\n\n    for (let retries = 0; retries <= maxRetries; retries++) {\n      try {\n        return await connectToFirefox(port);\n      } catch (error) {\n        if (isErrorWithCode('ECONNREFUSED', error)) {\n          // Wait for `retryInterval` ms.\n          await new Promise((resolve) => {\n            setTimeout(resolve, retryInterval);\n          });\n\n          lastError = error;\n          log.debug(\n            `Retrying Firefox (${retries}); connection error: ${error}`\n          );\n        } else {\n          log.error(error.stack);\n          throw error;\n        }\n      }\n    }\n\n    log.debug('Connect to Firefox debugger: too many retries');\n    throw lastError;\n  }\n\n  log.debug('Connecting to the remote Firefox debugger');\n  return establishConnection();\n}\n\nexport function findFreeTcpPort(): Promise<number> {\n  return new Promise((resolve) => {\n    const srv = net.createServer();\n    // $FlowFixMe: signature for listen() is missing - see https://github.com/facebook/flow/pull/8290\n    srv.listen(0, '127.0.0.1', () => {\n      const freeTcpPort = srv.address().port;\n      srv.close(() => resolve(freeTcpPort));\n    });\n  });\n}\n"], "mappings": "AACA,OAAOA,GAAG,MAAM,KAAK;AAErB,OAAOC,gBAAgB,IACrBC,gBAAgB,IAAIC,uBAAuB,QACtC,iBAAiB;AACxB,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SACEC,eAAe,EACfC,6BAA6B,EAC7BC,UAAU,EACVC,WAAW,QACN,cAAc;AAErB,MAAMC,GAAG,GAAGL,YAAY,CAACM,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC;;AAsBzC;;AAOA;AACA,SAASC,qBAAqBA,CAACC,GAAoC,EAAE;EACnE,IAAIA,GAAG,YAAYC,KAAK,EAAE;IACxB,OAAOC,MAAM,CAACF,GAAG,CAAC;EACpB;EACA,OAAQ,GAAEA,GAAG,CAACG,KAAM,KAAIH,GAAG,CAACI,OAAQ,EAAC;AACvC;AAEA,OAAO,MAAMC,aAAa,CAAC;EAIzBC,WAAWA,CAACC,MAAwB,EAAE;IACpC,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,wBAAwB,GAAG,KAAK;IAErCD,MAAM,CAACE,EAAE,CAAC,YAAY,EAAE,MAAM;MAC5Bd,GAAG,CAACe,KAAK,CAAC,2CAA2C,CAAC;IACxD,CAAC,CAAC;IACFH,MAAM,CAACE,EAAE,CAAC,KAAK,EAAE,MAAM;MACrBd,GAAG,CAACe,KAAK,CAAC,oCAAoC,CAAC;IACjD,CAAC,CAAC;IACFH,MAAM,CAACE,EAAE,CAAC,mBAAmB,EAAGE,IAAI,IAAK;MACvChB,GAAG,CAACe,KAAK,CAAE,iCAAgCE,IAAI,CAACC,SAAS,CAACF,IAAI,CAAE,EAAC,CAAC;IACpE,CAAC,CAAC;IACFJ,MAAM,CAACE,EAAE,CAAC,WAAW,EAAGK,QAAQ,IAAK;MACnCnB,GAAG,CAACe,KAAK,CAAE,+BAA8BE,IAAI,CAACC,SAAS,CAACC,QAAQ,CAAE,EAAC,CAAC;IACtE,CAAC,CAAC;IACFP,MAAM,CAACE,EAAE,CAAC,OAAO,EAAGN,KAAK,IAAK;MAC5BR,GAAG,CAACe,KAAK,CAAE,+BAA8BR,MAAM,CAACC,KAAK,CAAE,EAAC,CAAC;IAC3D,CAAC,CAAC;EACJ;EAEAY,UAAUA,CAAA,EAAG;IACX,IAAI,CAACR,MAAM,CAACQ,UAAU,CAAC,CAAC;EAC1B;EAEA,MAAMC,YAAYA,CAChBC,KAA2B,EAC3BC,OAAe,EACmB;IAClC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM,IAAI,CAACZ,MAAM,CAACW,OAAO,CAAC;QACzCE,EAAE,EAAEH,KAAK,CAACI,KAAK;QACfC,IAAI,EAAEJ;MACR,CAAC,CAAC;MACF,OAAOC,QAAQ;IACjB,CAAC,CAAC,OAAOnB,GAAG,EAAE;MACZL,GAAG,CAACe,KAAK,CAAE,wBAAuBQ,OAAQ,uBAAsB,EAAElB,GAAG,CAAC;MACtE,MAAMI,OAAO,GAAGL,qBAAqB,CAACC,GAAG,CAAC;MAC1C,MAAM,IAAIN,WAAW,CAAE,yCAAwCU,OAAQ,EAAC,CAAC;IAC3E;EACF;EAEA,MAAMmB,cAAcA,CAAA,EAAoB;IACtC,IAAI;MACF;MACA,MAAMJ,QAAQ,GAAG,MAAM,IAAI,CAACZ,MAAM,CAACW,OAAO,CAAC,SAAS,CAAC;MACrD,IAAIC,QAAQ,CAACK,WAAW,IAAI,IAAI,EAAE;QAChC,OAAOC,OAAO,CAACC,MAAM,CACnB,IAAIlC,6BAA6B,CAC/B,gEAAgE,GAC9D,sBACJ,CACF,CAAC;MACH;MACA,OAAO2B,QAAQ,CAACK,WAAW;IAC7B,CAAC,CAAC,OAAOxB,GAAG,EAAE;MACZ;MACAL,GAAG,CAACe,KAAK,CAAC,iDAAiD,EAAEV,GAAG,CAAC;IACnE;IAEA,IAAI;MACF,MAAMmB,QAAQ,GAAG,MAAM,IAAI,CAACZ,MAAM,CAACW,OAAO,CAAC,UAAU,CAAC;MACtD;MACA,IAAIC,QAAQ,CAACK,WAAW,IAAI,IAAI,EAAE;QAChC7B,GAAG,CAACe,KAAK,CACP,0CAA0C,GACvC,GAAEE,IAAI,CAACC,SAAS,CAACM,QAAQ,CAAE,EAChC,CAAC;QACD,OAAOM,OAAO,CAACC,MAAM,CACnB,IAAIlC,6BAA6B,CAC/B,+DAA+D,GAC7D,2DAA2D,GAC3D,SACJ,CACF,CAAC;MACH;MACA,OAAO2B,QAAQ,CAACK,WAAW;IAC7B,CAAC,CAAC,OAAOxB,GAAG,EAAE;MACZL,GAAG,CAACe,KAAK,CAAC,gBAAgB,EAAEV,GAAG,CAAC;MAChC,MAAMI,OAAO,GAAGL,qBAAqB,CAACC,GAAG,CAAC;MAC1C,MAAM,IAAIN,WAAW,CAAE,qCAAoCU,OAAQ,EAAC,CAAC;IACvE;EACF;EAEA,MAAMuB,qBAAqBA,CACzBC,SAAiB,EACjBC,YAAsB,EACY;IAClC,MAAML,WAAW,GAAG,MAAM,IAAI,CAACD,cAAc,CAAC,CAAC;IAE/C,IAAI;MACF,MAAMJ,QAAQ,GAAG,MAAM,IAAI,CAACZ,MAAM,CAACW,OAAO,CAAC;QACzCE,EAAE,EAAEI,WAAW;QACfF,IAAI,EAAE,uBAAuB;QAC7BM,SAAS;QACTC;MACF,CAAC,CAAC;MACFlC,GAAG,CAACe,KAAK,CAAE,0BAAyBE,IAAI,CAACC,SAAS,CAACM,QAAQ,CAAE,EAAC,CAAC;MAC/DxB,GAAG,CAACgB,IAAI,CAAE,aAAYiB,SAAU,wBAAuB,CAAC;MACxD,OAAOT,QAAQ;IACjB,CAAC,CAAC,OAAOnB,GAAG,EAAE;MACZ,MAAMI,OAAO,GAAGL,qBAAqB,CAACC,GAAG,CAAC;MAC1C,MAAM,IAAIN,WAAW,CAAE,iCAAgCU,OAAQ,EAAC,CAAC;IACnE;EACF;EAEA,MAAM0B,iBAAiBA,CAACC,OAAe,EAAiC;IACtE,IAAI;MACF,MAAMZ,QAAQ,GAAG,MAAM,IAAI,CAACZ,MAAM,CAACW,OAAO,CAAC,YAAY,CAAC;MACxD,KAAK,MAAMD,KAAK,IAAIE,QAAQ,CAACa,MAAM,EAAE;QACnC,IAAIf,KAAK,CAACgB,EAAE,KAAKF,OAAO,EAAE;UACxB,OAAOd,KAAK;QACd;MACF;MACAtB,GAAG,CAACe,KAAK,CACN,oCAAmCS,QAAQ,CAACa,MAAM,CAACE,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACF,EAAE,CAAE,EACvE,CAAC;MACD,OAAOR,OAAO,CAACC,MAAM,CACnB,IAAIhC,WAAW,CACb,2DACF,CACF,CAAC;IACH,CAAC,CAAC,OAAOM,GAAG,EAAE;MACZ,MAAMI,OAAO,GAAGL,qBAAqB,CAACC,GAAG,CAAC;MAC1C,MAAM,IAAIN,WAAW,CAAE,uCAAsCU,OAAQ,EAAC,CAAC;IACzE;EACF;EAEA,MAAMgC,sBAAsBA,CAC1BnB,KAA2B,EACI;IAC/B,IAAI,IAAI,CAACT,wBAAwB,EAAE;MACjC;MACA,OAAOS,KAAK;IACd,CAAC,MAAM;MACL,MAAME,QAAQ,GAAG,MAAM,IAAI,CAACH,YAAY,CAACC,KAAK,EAAE,cAAc,CAAC;MAE/D,IAAIE,QAAQ,CAACkB,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;QAClD,MAAMC,qBAAqB,GAAG3B,IAAI,CAACC,SAAS,CAACM,QAAQ,CAACkB,YAAY,CAAC;QACnE1C,GAAG,CAACe,KAAK,CAAE,iCAAgC6B,qBAAsB,EAAC,CAAC;QACnE,MAAM,IAAI9C,UAAU,CAClB,0DAA0D,GACxD,yBACJ,CAAC;MACH,CAAC,MAAM;QACL,IAAI,CAACe,wBAAwB,GAAG,IAAI;QACpC,OAAOS,KAAK;MACd;IACF;EACF;EAEA,MAAMuB,WAAWA,CAACT,OAAe,EAAiB;IAChD,MAAMd,KAAK,GAAG,MAAM,IAAI,CAACa,iBAAiB,CAACC,OAAO,CAAC;IACnD,MAAM,IAAI,CAACK,sBAAsB,CAACnB,KAAK,CAAC;IACxC,MAAM,IAAI,CAACD,YAAY,CAACC,KAAK,EAAE,QAAQ,CAAC;IACxCwB,OAAO,CAACC,MAAM,CAACC,KAAK,CACjB,4BAA2B,IAAIC,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CAAE,EACxD,CAAC;IACDlD,GAAG,CAACe,KAAK,CAAC,IAAI,CAAC;EACjB;AACF;;AAEA;;AAMA,OAAO,eAAeoC,OAAOA,CAC3BC,IAAY,EACZ;EAAE3D,gBAAgB,GAAGC;AAAwC,CAAC,GAAG,CAAC,CAAC,EAC3C;EACxBM,GAAG,CAACe,KAAK,CAAE,iCAAgCqC,IAAK,EAAC,CAAC;EAClD,MAAMxC,MAAM,GAAG,MAAMnB,gBAAgB,CAAC2D,IAAI,CAAC;EAC3CpD,GAAG,CAACe,KAAK,CAAE,oDAAmDqC,IAAK,EAAC,CAAC;EACrE,OAAO,IAAI1C,aAAa,CAACE,MAAM,CAAC;AAClC;;AAEA;;AAYA,OAAO,eAAeyC,qBAAqBA;AACzC;AACA;EAAEC,UAAU,GAAG,GAAG;EAAEC,aAAa,GAAG,GAAG;EAAEH;AAAkC,CAAC,EAC5E;EAAE3D,gBAAgB,GAAG0D;AAAmC,CAAC,GAAG,CAAC,CAAC,EACtC;EACxB,eAAeK,mBAAmBA,CAAA,EAAG;IACnC,IAAIC,SAAS;IAEb,KAAK,IAAIC,OAAO,GAAG,CAAC,EAAEA,OAAO,IAAIJ,UAAU,EAAEI,OAAO,EAAE,EAAE;MACtD,IAAI;QACF,OAAO,MAAMjE,gBAAgB,CAAC2D,IAAI,CAAC;MACrC,CAAC,CAAC,OAAO5C,KAAK,EAAE;QACd,IAAIZ,eAAe,CAAC,cAAc,EAAEY,KAAK,CAAC,EAAE;UAC1C;UACA,MAAM,IAAIsB,OAAO,CAAE6B,OAAO,IAAK;YAC7BC,UAAU,CAACD,OAAO,EAAEJ,aAAa,CAAC;UACpC,CAAC,CAAC;UAEFE,SAAS,GAAGjD,KAAK;UACjBR,GAAG,CAACe,KAAK,CACN,qBAAoB2C,OAAQ,wBAAuBlD,KAAM,EAC5D,CAAC;QACH,CAAC,MAAM;UACLR,GAAG,CAACQ,KAAK,CAACA,KAAK,CAACqD,KAAK,CAAC;UACtB,MAAMrD,KAAK;QACb;MACF;IACF;IAEAR,GAAG,CAACe,KAAK,CAAC,+CAA+C,CAAC;IAC1D,MAAM0C,SAAS;EACjB;EAEAzD,GAAG,CAACe,KAAK,CAAC,2CAA2C,CAAC;EACtD,OAAOyC,mBAAmB,CAAC,CAAC;AAC9B;AAEA,OAAO,SAASM,eAAeA,CAAA,EAAoB;EACjD,OAAO,IAAIhC,OAAO,CAAE6B,OAAO,IAAK;IAC9B,MAAMI,GAAG,GAAGxE,GAAG,CAACyE,YAAY,CAAC,CAAC;IAC9B;IACAD,GAAG,CAACE,MAAM,CAAC,CAAC,EAAE,WAAW,EAAE,MAAM;MAC/B,MAAMC,WAAW,GAAGH,GAAG,CAACI,OAAO,CAAC,CAAC,CAACf,IAAI;MACtCW,GAAG,CAACK,KAAK,CAAC,MAAMT,OAAO,CAACO,WAAW,CAAC,CAAC;IACvC,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ"}