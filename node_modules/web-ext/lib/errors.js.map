{"version": 3, "file": "errors.js", "names": ["ExtendableError", "WebExtError", "constructor", "message", "UsageError", "InvalidManifest", "RemoteTempInstallNotSupported", "MultiExtensionsReloadError", "errorsMap", "errors", "sourceDir", "error", "msg", "String", "errorsBySourceDir", "onlyInstancesOf", "predicate", "<PERSON><PERSON><PERSON><PERSON>", "onlyErrorsWithCode", "codeWanted", "throwError", "Array", "isArray", "indexOf", "code", "errno", "isErrorWithCode"], "sources": ["../src/errors.js"], "sourcesContent": ["/* @flow */\nimport ExtendableError from 'es6-error';\n\n/*\n * Base error for all custom web-ext errors.\n */\nexport class WebExtError extends ExtendableError {\n  constructor(message: string) {\n    super(message);\n  }\n}\n\n/*\n * The class for errors that can be fixed by the developer.\n */\nexport class UsageError extends WebExtError {\n  constructor(message: string) {\n    super(message);\n  }\n}\n\n/*\n * The manifest for the extension is invalid (or missing).\n */\nexport class InvalidManifest extends UsageError {\n  constructor(message: string) {\n    super(message);\n  }\n}\n\n/*\n * The remote Firefox does not support temporary add-on installation.\n */\nexport class RemoteTempInstallNotSupported extends WebExtError {\n  constructor(message: string) {\n    super(message);\n  }\n}\n\n/*\n * The errors collected when reloading all extensions at once\n * (initialized from a map of errors by extensionSourceDir string).\n */\nexport class MultiExtensionsReloadError extends WebExtError {\n  constructor(errorsMap: Map<string, Error>) {\n    let errors = '';\n    for (const [sourceDir, error] of errorsMap) {\n      const msg = String(error);\n      errors += `\\nError on extension loaded from ${sourceDir}: ${msg}\\n`;\n    }\n    const message = `Reload errors: ${errors}`;\n\n    super(message);\n    this.errorsBySourceDir = errorsMap;\n  }\n}\n\n/*\n * Sugar-y way to catch only instances of a certain error.\n *\n * Usage:\n *\n *  Promise.reject(SyntaxError)\n *    .catch(onlyInstancesOf(SyntaxError, (error) => {\n *      // error is guaranteed to be an instance of SyntaxError\n *    }))\n *\n * All other errors will be re-thrown.\n *\n */\nexport function onlyInstancesOf(\n  predicate: Function,\n  errorHandler: Function\n): Function {\n  return (error) => {\n    if (error instanceof predicate) {\n      return errorHandler(error);\n    } else {\n      throw error;\n    }\n  };\n}\n\n/*\n * Sugar-y way to catch only errors having certain code(s).\n *\n * Usage:\n *\n *  Promise.resolve()\n *    .catch(onlyErrorsWithCode('ENOENT', (error) => {\n *      // error.code is guaranteed to be ENOENT\n *    }))\n *\n *  or:\n *\n *  Promise.resolve()\n *    .catch(onlyErrorsWithCode(['ENOENT', 'ENOTDIR'], (error) => {\n *      // ...\n *    }))\n *\n * All other errors will be re-thrown.\n *\n */\nexport function onlyErrorsWithCode(\n  codeWanted: (string | number) | Array<string | number>,\n  errorHandler: Function\n): Function {\n  return (error) => {\n    let throwError = true;\n\n    if (Array.isArray(codeWanted)) {\n      if (\n        codeWanted.indexOf(error.code) !== -1 ||\n        codeWanted.indexOf(error.errno) !== -1\n      ) {\n        throwError = false;\n      }\n    } else if (error.code === codeWanted || error.errno === codeWanted) {\n      throwError = false;\n    }\n\n    if (throwError) {\n      throw error;\n    }\n\n    return errorHandler(error);\n  };\n}\n\nexport function isErrorWithCode(\n  codeWanted: string | Array<string>,\n  error: Object\n): boolean {\n  if (Array.isArray(codeWanted) && codeWanted.indexOf(error.code) !== -1) {\n    return true;\n  } else if (error.code === codeWanted) {\n    return true;\n  }\n\n  return false;\n}\n"], "mappings": "AACA,OAAOA,eAAe,MAAM,WAAW;;AAEvC;AACA;AACA;AACA,OAAO,MAAMC,WAAW,SAASD,eAAe,CAAC;EAC/CE,WAAWA,CAACC,OAAe,EAAE;IAC3B,KAAK,CAACA,OAAO,CAAC;EAChB;AACF;;AAEA;AACA;AACA;AACA,OAAO,MAAMC,UAAU,SAASH,WAAW,CAAC;EAC1CC,WAAWA,CAACC,OAAe,EAAE;IAC3B,KAAK,CAACA,OAAO,CAAC;EAChB;AACF;;AAEA;AACA;AACA;AACA,OAAO,MAAME,eAAe,SAASD,UAAU,CAAC;EAC9CF,WAAWA,CAACC,OAAe,EAAE;IAC3B,KAAK,CAACA,OAAO,CAAC;EAChB;AACF;;AAEA;AACA;AACA;AACA,OAAO,MAAMG,6BAA6B,SAASL,WAAW,CAAC;EAC7DC,WAAWA,CAACC,OAAe,EAAE;IAC3B,KAAK,CAACA,OAAO,CAAC;EAChB;AACF;;AAEA;AACA;AACA;AACA;AACA,OAAO,MAAMI,0BAA0B,SAASN,WAAW,CAAC;EAC1DC,WAAWA,CAACM,SAA6B,EAAE;IACzC,IAAIC,MAAM,GAAG,EAAE;IACf,KAAK,MAAM,CAACC,SAAS,EAAEC,KAAK,CAAC,IAAIH,SAAS,EAAE;MAC1C,MAAMI,GAAG,GAAGC,MAAM,CAACF,KAAK,CAAC;MACzBF,MAAM,IAAK,oCAAmCC,SAAU,KAAIE,GAAI,IAAG;IACrE;IACA,MAAMT,OAAO,GAAI,kBAAiBM,MAAO,EAAC;IAE1C,KAAK,CAACN,OAAO,CAAC;IACd,IAAI,CAACW,iBAAiB,GAAGN,SAAS;EACpC;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASO,eAAeA,CAC7BC,SAAmB,EACnBC,YAAsB,EACZ;EACV,OAAQN,KAAK,IAAK;IAChB,IAAIA,KAAK,YAAYK,SAAS,EAAE;MAC9B,OAAOC,YAAY,CAACN,KAAK,CAAC;IAC5B,CAAC,MAAM;MACL,MAAMA,KAAK;IACb;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASO,kBAAkBA,CAChCC,UAAsD,EACtDF,YAAsB,EACZ;EACV,OAAQN,KAAK,IAAK;IAChB,IAAIS,UAAU,GAAG,IAAI;IAErB,IAAIC,KAAK,CAACC,OAAO,CAACH,UAAU,CAAC,EAAE;MAC7B,IACEA,UAAU,CAACI,OAAO,CAACZ,KAAK,CAACa,IAAI,CAAC,KAAK,CAAC,CAAC,IACrCL,UAAU,CAACI,OAAO,CAACZ,KAAK,CAACc,KAAK,CAAC,KAAK,CAAC,CAAC,EACtC;QACAL,UAAU,GAAG,KAAK;MACpB;IACF,CAAC,MAAM,IAAIT,KAAK,CAACa,IAAI,KAAKL,UAAU,IAAIR,KAAK,CAACc,KAAK,KAAKN,UAAU,EAAE;MAClEC,UAAU,GAAG,KAAK;IACpB;IAEA,IAAIA,UAAU,EAAE;MACd,MAAMT,KAAK;IACb;IAEA,OAAOM,YAAY,CAACN,KAAK,CAAC;EAC5B,CAAC;AACH;AAEA,OAAO,SAASe,eAAeA,CAC7BP,UAAkC,EAClCR,KAAa,EACJ;EACT,IAAIU,KAAK,CAACC,OAAO,CAACH,UAAU,CAAC,IAAIA,UAAU,CAACI,OAAO,CAACZ,KAAK,CAACa,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;IACtE,OAAO,IAAI;EACb,CAAC,MAAM,IAAIb,KAAK,CAACa,IAAI,KAAKL,UAAU,EAAE;IACpC,OAAO,IAAI;EACb;EAEA,OAAO,KAAK;AACd"}