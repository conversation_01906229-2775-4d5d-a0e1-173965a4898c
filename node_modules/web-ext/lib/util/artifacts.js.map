{"version": 3, "file": "artifacts.js", "names": ["fs", "defaultAsyncMkdirp", "UsageError", "isErrorWithCode", "createLogger", "log", "import", "meta", "url", "defaultAsyncFsAccess", "access", "bind", "prepareArtifactsDir", "artifactsDir", "asyncMkdirp", "asyncFsAccess", "stats", "stat", "isDirectory", "W_OK", "accessErr", "error", "debug", "mkdirErr"], "sources": ["../../src/util/artifacts.js"], "sourcesContent": ["/* @flow */\nimport { fs } from 'mz';\nimport defaultAsyncMkdirp from 'mkdirp';\n\nimport { UsageError, isErrorWithCode } from '../errors.js';\nimport { createLogger } from './logger.js';\n\nconst log = createLogger(import.meta.url);\n\nconst defaultAsyncFsAccess: typeof fs.access = fs.access.bind(fs);\n\ntype PrepareArtifactsDirOptions = {\n  asyncMkdirp?: typeof defaultAsyncMkdirp,\n  asyncFsAccess?: typeof defaultAsyncFsAccess,\n};\n\nexport async function prepareArtifactsDir(\n  artifactsDir: string,\n  {\n    asyncMkdirp = defaultAsyncMkdirp,\n    asyncFsAccess = defaultAsyncFsAccess,\n  }: PrepareArtifactsDirOptions = {}\n): Promise<string> {\n  try {\n    const stats = await fs.stat(artifactsDir);\n    if (!stats.isDirectory()) {\n      throw new UsageError(\n        `--artifacts-dir=\"${artifactsDir}\" exists but it is not a directory.`\n      );\n    }\n    // If the artifactsDir already exists, check that we have the write permissions on it.\n    try {\n      await asyncFsAccess(artifactsDir, fs.W_OK);\n    } catch (accessErr) {\n      if (isErrorWithCode('EACCES', accessErr)) {\n        throw new UsageError(\n          `--artifacts-dir=\"${artifactsDir}\" exists but the user lacks ` +\n            'permissions on it.'\n        );\n      } else {\n        throw accessErr;\n      }\n    }\n  } catch (error) {\n    if (isErrorWithCode('EACCES', error)) {\n      // Handle errors when the artifactsDir cannot be accessed.\n      throw new UsageError(\n        `Cannot access --artifacts-dir=\"${artifactsDir}\" because the user ` +\n          `lacks permissions: ${error}`\n      );\n    } else if (isErrorWithCode('ENOENT', error)) {\n      // Create the artifact dir if it doesn't exist yet.\n      try {\n        log.debug(`Creating artifacts directory: ${artifactsDir}`);\n        await asyncMkdirp(artifactsDir);\n      } catch (mkdirErr) {\n        if (isErrorWithCode('EACCES', mkdirErr)) {\n          // Handle errors when the artifactsDir cannot be created for lack of permissions.\n          throw new UsageError(\n            `Cannot create --artifacts-dir=\"${artifactsDir}\" because the ` +\n              `user lacks permissions: ${mkdirErr}`\n          );\n        } else {\n          throw mkdirErr;\n        }\n      }\n    } else {\n      throw error;\n    }\n  }\n\n  return artifactsDir;\n}\n"], "mappings": "AACA,SAASA,EAAE,QAAQ,IAAI;AACvB,OAAOC,kBAAkB,MAAM,QAAQ;AAEvC,SAASC,UAAU,EAAEC,eAAe,QAAQ,cAAc;AAC1D,SAASC,YAAY,QAAQ,aAAa;AAE1C,MAAMC,GAAG,GAAGD,YAAY,CAACE,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC;AAEzC,MAAMC,oBAAsC,GAAGT,EAAE,CAACU,MAAM,CAACC,IAAI,CAACX,EAAE,CAAC;AAOjE,OAAO,eAAeY,mBAAmBA,CACvCC,YAAoB,EACpB;EACEC,WAAW,GAAGb,kBAAkB;EAChCc,aAAa,GAAGN;AACU,CAAC,GAAG,CAAC,CAAC,EACjB;EACjB,IAAI;IACF,MAAMO,KAAK,GAAG,MAAMhB,EAAE,CAACiB,IAAI,CAACJ,YAAY,CAAC;IACzC,IAAI,CAACG,KAAK,CAACE,WAAW,CAAC,CAAC,EAAE;MACxB,MAAM,IAAIhB,UAAU,CACjB,oBAAmBW,YAAa,qCACnC,CAAC;IACH;IACA;IACA,IAAI;MACF,MAAME,aAAa,CAACF,YAAY,EAAEb,EAAE,CAACmB,IAAI,CAAC;IAC5C,CAAC,CAAC,OAAOC,SAAS,EAAE;MAClB,IAAIjB,eAAe,CAAC,QAAQ,EAAEiB,SAAS,CAAC,EAAE;QACxC,MAAM,IAAIlB,UAAU,CACjB,oBAAmBW,YAAa,8BAA6B,GAC5D,oBACJ,CAAC;MACH,CAAC,MAAM;QACL,MAAMO,SAAS;MACjB;IACF;EACF,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,IAAIlB,eAAe,CAAC,QAAQ,EAAEkB,KAAK,CAAC,EAAE;MACpC;MACA,MAAM,IAAInB,UAAU,CACjB,kCAAiCW,YAAa,qBAAoB,GAChE,sBAAqBQ,KAAM,EAChC,CAAC;IACH,CAAC,MAAM,IAAIlB,eAAe,CAAC,QAAQ,EAAEkB,KAAK,CAAC,EAAE;MAC3C;MACA,IAAI;QACFhB,GAAG,CAACiB,KAAK,CAAE,iCAAgCT,YAAa,EAAC,CAAC;QAC1D,MAAMC,WAAW,CAACD,YAAY,CAAC;MACjC,CAAC,CAAC,OAAOU,QAAQ,EAAE;QACjB,IAAIpB,eAAe,CAAC,QAAQ,EAAEoB,QAAQ,CAAC,EAAE;UACvC;UACA,MAAM,IAAIrB,UAAU,CACjB,kCAAiCW,YAAa,gBAAe,GAC3D,2BAA0BU,QAAS,EACxC,CAAC;QACH,CAAC,MAAM;UACL,MAAMA,QAAQ;QAChB;MACF;IACF,CAAC,MAAM;MACL,MAAMF,KAAK;IACb;EACF;EAEA,OAAOR,YAAY;AACrB"}