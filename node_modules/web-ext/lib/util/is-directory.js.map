{"version": 3, "file": "is-directory.js", "names": ["fs", "onlyErrorsWithCode", "isDirectory", "path", "stat", "then", "stats", "catch"], "sources": ["../../src/util/is-directory.js"], "sourcesContent": ["/* @flow */\nimport { fs } from 'mz';\n\nimport { onlyErrorsWithCode } from '../errors.js';\n\n/*\n * Resolves true if the path is a readable directory.\n *\n * Usage:\n *\n * isDirectory('/some/path')\n *  .then((dirExists) => {\n *    // dirExists will be true or false.\n *  });\n *\n * */\nexport default function isDirectory(path: string): Promise<boolean> {\n  return fs\n    .stat(path)\n    .then((stats) => stats.isDirectory())\n    .catch(\n      onlyErrorsWithCode(['ENOENT', 'ENOTDIR'], () => {\n        return false;\n      })\n    );\n}\n"], "mappings": "AACA,SAASA,EAAE,QAAQ,IAAI;AAEvB,SAASC,kBAAkB,QAAQ,cAAc;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,WAAWA,CAACC,IAAY,EAAoB;EAClE,OAAOH,EAAE,CACNI,IAAI,CAACD,IAAI,CAAC,CACVE,IAAI,CAAEC,KAAK,IAAKA,KAAK,CAACJ,WAAW,CAAC,CAAC,CAAC,CACpCK,KAAK,CACJN,kBAAkB,CAAC,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAE,MAAM;IAC9C,OAAO,KAAK;EACd,CAAC,CACH,CAAC;AACL"}