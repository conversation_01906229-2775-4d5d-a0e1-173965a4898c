{"version": 3, "file": "logger.js", "names": ["fileURLToPath", "bunyan", "nameFromLevel", "createLogger", "defaultLogCreator", "ConsoleStream", "constructor", "verbose", "isCapturing", "capturedMessages", "format", "name", "msg", "level", "prefix", "makeVerbose", "write", "packet", "localProcess", "process", "thisLevel", "TRACE", "INFO", "push", "stdout", "startCapturing", "stopCapturing", "flushCapturedLogs", "consoleStream", "moduleURL", "createBunyanLog", "replace", "streams", "type", "stream"], "sources": ["../../src/util/logger.js"], "sourcesContent": ["/* @flow */\nimport { fileURLToPath } from 'url';\n\nimport bunyan, {\n  nameFromLevel,\n  createLogger as defaultLogCreator,\n} from 'bunyan';\n\n// Bunyan-related Flow types\n\nexport type TRACE = 10;\nexport type DEBUG = 20;\nexport type INFO = 30;\nexport type WARN = 40;\nexport type ERROR = 50;\nexport type FATAL = 60;\n\nexport type BunyanLogLevel = TRACE | DEBUG | INFO | WARN | ERROR | FATAL;\n\nexport type BunyanLogEntry = {|\n  name: string,\n  msg: string,\n  level: BunyanLogLevel,\n|};\n\nexport type Logger = {\n  debug: (msg: string, ...args: any) => void,\n  error: (msg: string, ...args: any) => void,\n  info: (msg: string, ...args: any) => void,\n  warn: (msg: string, ...args: any) => void,\n};\n\n// ConsoleStream types and implementation.\n\nexport type ConsoleStreamParams = {\n  verbose?: boolean,\n};\n\nexport type ConsoleOptions = {\n  localProcess?: typeof process,\n};\n\nexport class ConsoleStream {\n  verbose: boolean;\n  isCapturing: boolean;\n  capturedMessages: Array<string>;\n\n  constructor({ verbose = false }: ConsoleStreamParams = {}) {\n    this.verbose = verbose;\n    this.isCapturing = false;\n    this.capturedMessages = [];\n  }\n\n  format({ name, msg, level }: BunyanLogEntry): string {\n    const prefix = this.verbose ? `[${name}][${nameFromLevel[level]}] ` : '';\n    return `${prefix}${msg}\\n`;\n  }\n\n  makeVerbose() {\n    this.verbose = true;\n  }\n\n  write(\n    packet: BunyanLogEntry,\n    { localProcess = process }: ConsoleOptions = {}\n  ): void {\n    const thisLevel: BunyanLogLevel = this.verbose ? bunyan.TRACE : bunyan.INFO;\n    if (packet.level >= thisLevel) {\n      const msg = this.format(packet);\n      if (this.isCapturing) {\n        this.capturedMessages.push(msg);\n      } else {\n        localProcess.stdout.write(msg);\n      }\n    }\n  }\n\n  startCapturing() {\n    this.isCapturing = true;\n  }\n\n  stopCapturing() {\n    this.isCapturing = false;\n    this.capturedMessages = [];\n  }\n\n  flushCapturedLogs({ localProcess = process }: ConsoleOptions = {}) {\n    for (const msg of this.capturedMessages) {\n      localProcess.stdout.write(msg);\n    }\n    this.capturedMessages = [];\n  }\n}\n\nexport const consoleStream: ConsoleStream = new ConsoleStream();\n\n// createLogger types and implementation.\n\nexport type BunyanStreamConfig = {\n  type: string,\n  stream: ConsoleStream,\n};\n\nexport type CreateBunyanLogParams = {\n  name: string,\n  level: BunyanLogLevel,\n  streams: Array<BunyanStreamConfig>,\n};\n\nexport type CreateBunyanLogFn = (params: CreateBunyanLogParams) => Logger;\n\nexport type CreateLoggerOptions = {\n  createBunyanLog: CreateBunyanLogFn,\n};\n\nexport function createLogger(\n  moduleURL?: string,\n  { createBunyanLog = defaultLogCreator }: CreateLoggerOptions = {}\n): Logger {\n  return createBunyanLog({\n    // Strip the leading src/ from file names (which is in all file names) to\n    // make the name less redundant.\n    name: moduleURL\n      ? fileURLToPath(moduleURL).replace(/^src\\//, '')\n      : 'unknown-module',\n    // Capture all log levels and let the stream filter them.\n    level: bunyan.TRACE,\n    streams: [\n      {\n        type: 'raw',\n        stream: consoleStream,\n      },\n    ],\n  });\n}\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,KAAK;AAEnC,OAAOC,MAAM,IACXC,aAAa,EACbC,YAAY,IAAIC,iBAAiB,QAC5B,QAAQ;;AAEf;;AAwBA;;AAUA,OAAO,MAAMC,aAAa,CAAC;EAKzBC,WAAWA,CAAC;IAAEC,OAAO,GAAG;EAA2B,CAAC,GAAG,CAAC,CAAC,EAAE;IACzD,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,gBAAgB,GAAG,EAAE;EAC5B;EAEAC,MAAMA,CAAC;IAAEC,IAAI;IAAEC,GAAG;IAAEC;EAAsB,CAAC,EAAU;IACnD,MAAMC,MAAM,GAAG,IAAI,CAACP,OAAO,GAAI,IAAGI,IAAK,KAAIT,aAAa,CAACW,KAAK,CAAE,IAAG,GAAG,EAAE;IACxE,OAAQ,GAAEC,MAAO,GAAEF,GAAI,IAAG;EAC5B;EAEAG,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACR,OAAO,GAAG,IAAI;EACrB;EAEAS,KAAKA,CACHC,MAAsB,EACtB;IAAEC,YAAY,GAAGC;EAAwB,CAAC,GAAG,CAAC,CAAC,EACzC;IACN,MAAMC,SAAyB,GAAG,IAAI,CAACb,OAAO,GAAGN,MAAM,CAACoB,KAAK,GAAGpB,MAAM,CAACqB,IAAI;IAC3E,IAAIL,MAAM,CAACJ,KAAK,IAAIO,SAAS,EAAE;MAC7B,MAAMR,GAAG,GAAG,IAAI,CAACF,MAAM,CAACO,MAAM,CAAC;MAC/B,IAAI,IAAI,CAACT,WAAW,EAAE;QACpB,IAAI,CAACC,gBAAgB,CAACc,IAAI,CAACX,GAAG,CAAC;MACjC,CAAC,MAAM;QACLM,YAAY,CAACM,MAAM,CAACR,KAAK,CAACJ,GAAG,CAAC;MAChC;IACF;EACF;EAEAa,cAAcA,CAAA,EAAG;IACf,IAAI,CAACjB,WAAW,GAAG,IAAI;EACzB;EAEAkB,aAAaA,CAAA,EAAG;IACd,IAAI,CAAClB,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,gBAAgB,GAAG,EAAE;EAC5B;EAEAkB,iBAAiBA,CAAC;IAAET,YAAY,GAAGC;EAAwB,CAAC,GAAG,CAAC,CAAC,EAAE;IACjE,KAAK,MAAMP,GAAG,IAAI,IAAI,CAACH,gBAAgB,EAAE;MACvCS,YAAY,CAACM,MAAM,CAACR,KAAK,CAACJ,GAAG,CAAC;IAChC;IACA,IAAI,CAACH,gBAAgB,GAAG,EAAE;EAC5B;AACF;AAEA,OAAO,MAAMmB,aAA4B,GAAG,IAAIvB,aAAa,CAAC,CAAC;;AAE/D;;AAmBA,OAAO,SAASF,YAAYA,CAC1B0B,SAAkB,EAClB;EAAEC,eAAe,GAAG1B;AAAuC,CAAC,GAAG,CAAC,CAAC,EACzD;EACR,OAAO0B,eAAe,CAAC;IACrB;IACA;IACAnB,IAAI,EAAEkB,SAAS,GACX7B,aAAa,CAAC6B,SAAS,CAAC,CAACE,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,GAC9C,gBAAgB;IACpB;IACAlB,KAAK,EAAEZ,MAAM,CAACoB,KAAK;IACnBW,OAAO,EAAE,CACP;MACEC,IAAI,EAAE,KAAK;MACXC,MAAM,EAAEN;IACV,CAAC;EAEL,CAAC,CAAC;AACJ"}