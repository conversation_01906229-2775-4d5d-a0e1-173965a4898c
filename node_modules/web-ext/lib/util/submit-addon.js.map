{"version": 3, "file": "submit-addon.js", "names": ["createWriteStream", "promises", "fsPromises", "pipeline", "promisify", "fetch", "FormData", "fileFromSync", "Response", "SignJWT", "createLogger", "log", "import", "meta", "url", "JwtApiAuth", "<PERSON><PERSON><PERSON><PERSON>", "apiSecret", "apiJwtExpiresIn", "constructor", "signJWT", "iss", "setProtectedHeader", "alg", "setIssuedAt", "setExpirationTime", "sign", "Uint8Array", "from", "<PERSON><PERSON><PERSON>", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "authToken", "Client", "apiAuth", "baseUrl", "validationCheckInterval", "validationCheckTimeout", "approvalCheckInterval", "approvalCheckTimeout", "downloadDir", "process", "cwd", "userAgentString", "pathname", "endsWith", "URL", "href", "apiUrl", "path", "nodeFetch", "method", "headers", "body", "doUploadSubmit", "xpiPath", "channel", "formData", "set", "uuid", "<PERSON><PERSON><PERSON>", "waitForValidation", "waitRetry", "successFunc", "checkUrl", "checkInterval", "abortInterval", "context", "checkTimeout", "Promise", "resolve", "reject", "abortTimeout", "setTimeout", "clearTimeout", "Error", "pollStatus", "responseData", "undefined", "success", "err", "info", "detailResponseData", "processed", "validation", "valid", "doNewAddonSubmit", "metaDataJson", "jsonData", "version", "upload", "JSON", "stringify", "doNewAddonOrVersionSubmit", "addonId", "waitForApproval", "versionId", "file", "status", "errorMsg", "response", "statusText", "data", "json", "ok", "Authorization", "Accept", "downloadSignedFile", "fileUrl", "filename", "split", "pop", "dest", "saveToFile", "error", "id", "downloadedFiles", "contents", "destPath", "postNewAddon", "savedIdPath", "saveIdToFileFunc", "saveIdToFile", "uploadUuid", "versionObject", "guid", "newVersionId", "putVersion", "results", "signAddon", "amoBaseUrl", "timeout", "SubmitClient", "ApiAuthClass", "stats", "stat", "isFile", "statError", "client", "filePath", "writeFile", "toString", "join", "debug"], "sources": ["../../src/util/submit-addon.js"], "sourcesContent": ["/* @flow */\nimport { createWriteStream, promises as fsPromises } from 'fs';\nimport { pipeline } from 'stream';\nimport { promisify } from 'util';\n\n// eslint-disable-next-line no-shadow\nimport fetch, { FormData, fileFromSync, Response } from 'node-fetch';\nimport { SignJWT } from 'jose';\n\nimport { createLogger } from './../util/logger.js';\n\nconst log = createLogger(import.meta.url);\n\nexport type SignResult = {|\n  id: string,\n  downloadedFiles: Array<string>,\n|};\n\nexport interface ApiAuth {\n  getAuthHeader(): Promise<string>;\n}\n\nexport class JwtApiAuth {\n  #apiKey: string;\n  #apiSecret: string;\n  #apiJwtExpiresIn: number;\n\n  constructor({\n    apiKey,\n    apiSecret,\n    apiJwtExpiresIn = 60 * 5, // 5 minutes\n  }: {\n    apiKey: string,\n    apiSecret: string,\n    apiJwtExpiresIn?: number,\n  }) {\n    this.#apiKey = apiKey;\n    this.#apiSecret = apiSecret;\n    this.#apiJwtExpiresIn = apiJwtExpiresIn;\n  }\n\n  async signJWT(): Promise<string> {\n    return (\n      new SignJWT({ iss: this.#apiKey })\n        .setProtectedHeader({ alg: 'HS256' })\n        .setIssuedAt()\n        // jose expects either:\n        // a number, which is treated an absolute timestamp - so must be after now, or\n        // a string, which is parsed as a relative time from now.\n        .setExpirationTime(`${this.#apiJwtExpiresIn}seconds`)\n        .sign(Uint8Array.from(Buffer.from(this.#apiSecret, 'utf8')))\n    );\n  }\n\n  async getAuthHeader(): Promise<string> {\n    const authToken = await this.signJWT();\n    return `JWT ${authToken}`;\n  }\n}\n\ntype ClientConstructorParams = {|\n  apiAuth: ApiAuth,\n  baseUrl: URL,\n  validationCheckInterval?: number,\n  validationCheckTimeout?: number,\n  approvalCheckInterval?: number,\n  approvalCheckTimeout?: number,\n  downloadDir?: string,\n  userAgentString: string,\n|};\n\nexport default class Client {\n  apiAuth: ApiAuth;\n  apiUrl: URL;\n  validationCheckInterval: number;\n  validationCheckTimeout: number;\n  approvalCheckInterval: number;\n  approvalCheckTimeout: number;\n  downloadDir: string;\n  userAgentString: string;\n\n  constructor({\n    apiAuth,\n    baseUrl,\n    validationCheckInterval = 1000,\n    validationCheckTimeout = 300000, // 5 minutes.\n    approvalCheckInterval = 1000,\n    approvalCheckTimeout = 900000, // 15 minutes.\n    downloadDir = process.cwd(),\n    userAgentString,\n  }: ClientConstructorParams) {\n    this.apiAuth = apiAuth;\n    if (!baseUrl.pathname.endsWith('/')) {\n      baseUrl = new URL(baseUrl.href);\n      baseUrl.pathname += '/';\n    }\n    this.apiUrl = new URL('addons/', baseUrl);\n    this.validationCheckInterval = validationCheckInterval;\n    this.validationCheckTimeout = validationCheckTimeout;\n    this.approvalCheckInterval = approvalCheckInterval;\n    this.approvalCheckTimeout = approvalCheckTimeout;\n    this.downloadDir = downloadDir;\n    this.userAgentString = userAgentString;\n  }\n\n  fileFromSync(path: string): File {\n    return fileFromSync(path);\n  }\n\n  nodeFetch(\n    url: URL,\n    {\n      method,\n      headers,\n      body,\n    }: {\n      method: string,\n      headers: { [key: string]: string },\n      body?: typeof FormData | string,\n    }\n  ): Promise<typeof Response> {\n    return fetch(url, { method, headers, body });\n  }\n\n  async doUploadSubmit(xpiPath: string, channel: string): Promise<string> {\n    const url = new URL('upload/', this.apiUrl);\n    const formData = new FormData();\n    formData.set('channel', channel);\n    formData.set('upload', this.fileFromSync(xpiPath));\n    const { uuid } = await this.fetchJson(url, 'POST', formData);\n    return this.waitForValidation(uuid);\n  }\n\n  waitRetry(\n    successFunc: (detailResponseData: any) => string | null,\n    checkUrl: URL,\n    checkInterval: number,\n    abortInterval: number,\n    context: string\n  ): Promise<string> {\n    let checkTimeout;\n\n    return new Promise((resolve, reject) => {\n      const abortTimeout = setTimeout(() => {\n        clearTimeout(checkTimeout);\n        reject(new Error(`${context}: timeout.`));\n      }, abortInterval);\n\n      const pollStatus = async () => {\n        try {\n          const responseData = await this.fetchJson(\n            checkUrl,\n            'GET',\n            undefined,\n            'Getting details failed.'\n          );\n\n          const success = successFunc(responseData);\n          if (success) {\n            clearTimeout(abortTimeout);\n            resolve(success);\n          } else {\n            // Still in progress, so wait for a while and try again.\n            checkTimeout = setTimeout(pollStatus, checkInterval);\n          }\n        } catch (err) {\n          clearTimeout(abortTimeout);\n          reject(err);\n        }\n      };\n\n      pollStatus();\n    });\n  }\n\n  waitForValidation(uuid: string): Promise<string> {\n    log.info('Waiting for Validation...');\n    return this.waitRetry(\n      (detailResponseData): string | null => {\n        if (!detailResponseData.processed) {\n          return null;\n        }\n\n        log.info('Validation results:', detailResponseData.validation);\n        if (detailResponseData.valid) {\n          return detailResponseData.uuid;\n        }\n\n        log.info('Validation failed.');\n        throw new Error(\n          'Validation failed, open the following URL for more information: ' +\n            `${detailResponseData.url}`\n        );\n      },\n      new URL(`upload/${uuid}/`, this.apiUrl),\n      this.validationCheckInterval,\n      this.validationCheckTimeout,\n      'Validation'\n    );\n  }\n\n  async doNewAddonSubmit(uuid: string, metaDataJson: Object): Promise<any> {\n    const url = new URL('addon/', this.apiUrl);\n    const jsonData = {\n      ...metaDataJson,\n      version: { upload: uuid, ...metaDataJson.version },\n    };\n    return this.fetchJson(url, 'POST', JSON.stringify(jsonData));\n  }\n\n  doNewAddonOrVersionSubmit(\n    addonId: string,\n    uuid: string,\n    metaDataJson: Object\n  ): Promise<typeof Response> {\n    const url = new URL(`addon/${addonId}/`, this.apiUrl);\n    const jsonData = {\n      ...metaDataJson,\n      version: { upload: uuid, ...metaDataJson.version },\n    };\n    return this.fetch(url, 'PUT', JSON.stringify(jsonData));\n  }\n\n  waitForApproval(addonId: string, versionId: number): Promise<string> {\n    log.info('Waiting for Approval...');\n    return this.waitRetry(\n      (detailResponseData): string | null => {\n        const { file } = detailResponseData;\n        if (file && file.status === 'public') {\n          return file.url;\n        }\n\n        return null;\n      },\n      new URL(`addon/${addonId}/versions/${versionId}/`, this.apiUrl),\n      this.approvalCheckInterval,\n      this.approvalCheckTimeout,\n      'Approval'\n    );\n  }\n\n  async fetchJson(\n    url: URL,\n    method: string = 'GET',\n    body?: typeof FormData | string,\n    errorMsg: string = 'Bad Request'\n  ): Promise<any> {\n    const response = await this.fetch(url, method, body);\n    if (response.status < 200 || response.status >= 500) {\n      throw new Error(\n        `${errorMsg}: ${response.statusText || response.status}.`\n      );\n    }\n    const data = await response.json();\n\n    if (!response.ok) {\n      log.info('Server Response:', data);\n      throw new Error(\n        `${errorMsg}: ${response.statusText || response.status}.`\n      );\n    }\n    return data;\n  }\n\n  async fetch(\n    url: URL,\n    method: string = 'GET',\n    body?: typeof FormData | string\n  ): Promise<typeof Response> {\n    log.info(`Fetching URL: ${url.href}`);\n    let headers = {\n      Authorization: await this.apiAuth.getAuthHeader(),\n      Accept: 'application/json',\n      'User-Agent': this.userAgentString,\n    };\n    if (typeof body === 'string') {\n      headers = {\n        ...headers,\n        'Content-Type': 'application/json',\n      };\n    }\n    return this.nodeFetch(url, { method, body, headers });\n  }\n\n  async downloadSignedFile(fileUrl: URL, addonId: string): Promise<SignResult> {\n    const filename = fileUrl.pathname.split('/').pop(); // get the name from fileUrl\n    const dest = `${this.downloadDir}/${filename}`;\n    try {\n      const response = await this.fetch(fileUrl);\n      if (!response.ok || !response.body) {\n        throw new Error(`response status was ${response.status}`);\n      }\n      await this.saveToFile(response.body, dest);\n    } catch (error) {\n      log.info(`Download of signed xpi failed: ${error}.`);\n      throw new Error(`Downloading ${filename} failed`);\n    }\n    return {\n      id: addonId,\n      downloadedFiles: [filename],\n    };\n  }\n\n  async saveToFile(\n    contents: typeof Response.body,\n    destPath: string\n  ): Promise<any> {\n    return promisify(pipeline)(contents, createWriteStream(destPath));\n  }\n\n  async postNewAddon(\n    xpiPath: string,\n    channel: string,\n    savedIdPath: string,\n    metaDataJson: Object,\n    saveIdToFileFunc: (string, string) => Promise<void> = saveIdToFile\n  ): Promise<SignResult> {\n    const uploadUuid = await this.doUploadSubmit(xpiPath, channel);\n\n    const versionObject =\n      channel === 'listed' ? 'current_version' : 'latest_unlisted_version';\n    const {\n      guid: addonId,\n      [versionObject]: { id: newVersionId },\n    } = await this.doNewAddonSubmit(uploadUuid, metaDataJson);\n\n    await saveIdToFileFunc(savedIdPath, addonId);\n    log.info(`Generated extension ID: ${addonId}.`);\n    log.info('You must add the following to your manifest:');\n    log.info(`\"browser_specific_settings\": {\"gecko\": {\"id\": \"${addonId}\"}}`);\n\n    const fileUrl = new URL(await this.waitForApproval(addonId, newVersionId));\n\n    return this.downloadSignedFile(fileUrl, addonId);\n  }\n\n  async putVersion(\n    xpiPath: string,\n    channel: string,\n    addonId: string,\n    metaDataJson: Object\n  ): Promise<SignResult> {\n    const uploadUuid = await this.doUploadSubmit(xpiPath, channel);\n\n    await this.doNewAddonOrVersionSubmit(addonId, uploadUuid, metaDataJson);\n\n    const url = new URL(\n      `addon/${addonId}/versions/?filter=all_with_unlisted`,\n      this.apiUrl\n    );\n    const {\n      results: [{ id: newVersionId }],\n    } = await this.fetchJson(url);\n\n    const fileUrl = new URL(await this.waitForApproval(addonId, newVersionId));\n\n    return this.downloadSignedFile(fileUrl, addonId);\n  }\n}\n\ntype signAddonParams = {|\n  apiKey: string,\n  apiSecret: string,\n  amoBaseUrl: string,\n  timeout: number,\n  id?: string,\n  xpiPath: string,\n  downloadDir: string,\n  channel: string,\n  savedIdPath: string,\n  metaDataJson?: Object,\n  userAgentString: string,\n  SubmitClient?: typeof Client,\n  ApiAuthClass?: typeof JwtApiAuth,\n|};\n\nexport async function signAddon({\n  apiKey,\n  apiSecret,\n  amoBaseUrl,\n  timeout,\n  id,\n  xpiPath,\n  downloadDir,\n  channel,\n  savedIdPath,\n  metaDataJson = {},\n  userAgentString,\n  SubmitClient = Client,\n  ApiAuthClass = JwtApiAuth,\n}: signAddonParams): Promise<SignResult> {\n  try {\n    const stats = await fsPromises.stat(xpiPath);\n\n    if (!stats.isFile()) {\n      throw new Error(`not a file: ${xpiPath}`);\n    }\n  } catch (statError) {\n    throw new Error(`error with ${xpiPath}: ${statError}`);\n  }\n\n  let baseUrl;\n  try {\n    baseUrl = new URL(amoBaseUrl);\n  } catch (err) {\n    throw new Error(`Invalid AMO API base URL: ${amoBaseUrl}`);\n  }\n\n  const client = new SubmitClient({\n    apiAuth: new ApiAuthClass({ apiKey, apiSecret }),\n    baseUrl,\n    validationCheckTimeout: timeout,\n    approvalCheckTimeout: timeout,\n    downloadDir,\n    userAgentString,\n  });\n\n  // We specifically need to know if `id` has not been passed as a parameter because\n  // it's the indication that a new add-on should be created, rather than a new version.\n  if (id === undefined) {\n    return client.postNewAddon(xpiPath, channel, savedIdPath, metaDataJson);\n  }\n\n  return client.putVersion(xpiPath, channel, id, metaDataJson);\n}\n\nexport async function saveIdToFile(\n  filePath: string,\n  id: string\n): Promise<void> {\n  await fsPromises.writeFile(\n    filePath,\n    [\n      '# This file was created by https://github.com/mozilla/web-ext',\n      '# Your auto-generated extension ID for addons.mozilla.org is:',\n      id.toString(),\n    ].join('\\n')\n  );\n\n  log.debug(`Saved auto-generated ID ${id} to ${filePath}`);\n}\n"], "mappings": "AACA,SAASA,iBAAiB,EAAEC,QAAQ,IAAIC,UAAU,QAAQ,IAAI;AAC9D,SAASC,QAAQ,QAAQ,QAAQ;AACjC,SAASC,SAAS,QAAQ,MAAM;;AAEhC;AACA,OAAOC,KAAK,IAAIC,QAAQ,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,YAAY;AACpE,SAASC,OAAO,QAAQ,MAAM;AAE9B,SAASC,YAAY,QAAQ,qBAAqB;AAElD,MAAMC,GAAG,GAAGD,YAAY,CAACE,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC;AAWzC,OAAO,MAAMC,UAAU,CAAC;EACtB,CAACC,MAAM;EACP,CAACC,SAAS;EACV,CAACC,eAAe;EAEhBC,WAAWA,CAAC;IACVH,MAAM;IACNC,SAAS;IACTC,eAAe,GAAG,EAAE,GAAG,CAAC,CAAE;EAK5B,CAAC,EAAE;IACD,IAAI,CAAC,CAACF,MAAM,GAAGA,MAAM;IACrB,IAAI,CAAC,CAACC,SAAS,GAAGA,SAAS;IAC3B,IAAI,CAAC,CAACC,eAAe,GAAGA,eAAe;EACzC;EAEA,MAAME,OAAOA,CAAA,EAAoB;IAC/B,OACE,IAAIX,OAAO,CAAC;MAAEY,GAAG,EAAE,IAAI,CAAC,CAACL;IAAO,CAAC,CAAC,CAC/BM,kBAAkB,CAAC;MAAEC,GAAG,EAAE;IAAQ,CAAC,CAAC,CACpCC,WAAW,CAAC;IACb;IACA;IACA;IAAA,CACCC,iBAAiB,CAAE,GAAE,IAAI,CAAC,CAACP,eAAgB,SAAQ,CAAC,CACpDQ,IAAI,CAACC,UAAU,CAACC,IAAI,CAACC,MAAM,CAACD,IAAI,CAAC,IAAI,CAAC,CAACX,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC;EAElE;EAEA,MAAMa,aAAaA,CAAA,EAAoB;IACrC,MAAMC,SAAS,GAAG,MAAM,IAAI,CAACX,OAAO,CAAC,CAAC;IACtC,OAAQ,OAAMW,SAAU,EAAC;EAC3B;AACF;AAaA,eAAe,MAAMC,MAAM,CAAC;EAU1Bb,WAAWA,CAAC;IACVc,OAAO;IACPC,OAAO;IACPC,uBAAuB,GAAG,IAAI;IAC9BC,sBAAsB,GAAG,MAAM;IAAE;IACjCC,qBAAqB,GAAG,IAAI;IAC5BC,oBAAoB,GAAG,MAAM;IAAE;IAC/BC,WAAW,GAAGC,OAAO,CAACC,GAAG,CAAC,CAAC;IAC3BC;EACuB,CAAC,EAAE;IAC1B,IAAI,CAACT,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,OAAO,CAACS,QAAQ,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;MACnCV,OAAO,GAAG,IAAIW,GAAG,CAACX,OAAO,CAACY,IAAI,CAAC;MAC/BZ,OAAO,CAACS,QAAQ,IAAI,GAAG;IACzB;IACA,IAAI,CAACI,MAAM,GAAG,IAAIF,GAAG,CAAC,SAAS,EAAEX,OAAO,CAAC;IACzC,IAAI,CAACC,uBAAuB,GAAGA,uBAAuB;IACtD,IAAI,CAACC,sBAAsB,GAAGA,sBAAsB;IACpD,IAAI,CAACC,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACC,oBAAoB,GAAGA,oBAAoB;IAChD,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACG,eAAe,GAAGA,eAAe;EACxC;EAEAnC,YAAYA,CAACyC,IAAY,EAAQ;IAC/B,OAAOzC,YAAY,CAACyC,IAAI,CAAC;EAC3B;EAEAC,SAASA,CACPnC,GAAQ,EACR;IACEoC,MAAM;IACNC,OAAO;IACPC;EAKF,CAAC,EACyB;IAC1B,OAAO/C,KAAK,CAACS,GAAG,EAAE;MAAEoC,MAAM;MAAEC,OAAO;MAAEC;IAAK,CAAC,CAAC;EAC9C;EAEA,MAAMC,cAAcA,CAACC,OAAe,EAAEC,OAAe,EAAmB;IACtE,MAAMzC,GAAG,GAAG,IAAI+B,GAAG,CAAC,SAAS,EAAE,IAAI,CAACE,MAAM,CAAC;IAC3C,MAAMS,QAAQ,GAAG,IAAIlD,QAAQ,CAAC,CAAC;IAC/BkD,QAAQ,CAACC,GAAG,CAAC,SAAS,EAAEF,OAAO,CAAC;IAChCC,QAAQ,CAACC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAClD,YAAY,CAAC+C,OAAO,CAAC,CAAC;IAClD,MAAM;MAAEI;IAAK,CAAC,GAAG,MAAM,IAAI,CAACC,SAAS,CAAC7C,GAAG,EAAE,MAAM,EAAE0C,QAAQ,CAAC;IAC5D,OAAO,IAAI,CAACI,iBAAiB,CAACF,IAAI,CAAC;EACrC;EAEAG,SAASA,CACPC,WAAuD,EACvDC,QAAa,EACbC,aAAqB,EACrBC,aAAqB,EACrBC,OAAe,EACE;IACjB,IAAIC,YAAY;IAEhB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC,MAAMC,YAAY,GAAGC,UAAU,CAAC,MAAM;QACpCC,YAAY,CAACN,YAAY,CAAC;QAC1BG,MAAM,CAAC,IAAII,KAAK,CAAE,GAAER,OAAQ,YAAW,CAAC,CAAC;MAC3C,CAAC,EAAED,aAAa,CAAC;MAEjB,MAAMU,UAAU,GAAG,MAAAA,CAAA,KAAY;QAC7B,IAAI;UACF,MAAMC,YAAY,GAAG,MAAM,IAAI,CAACjB,SAAS,CACvCI,QAAQ,EACR,KAAK,EACLc,SAAS,EACT,yBACF,CAAC;UAED,MAAMC,OAAO,GAAGhB,WAAW,CAACc,YAAY,CAAC;UACzC,IAAIE,OAAO,EAAE;YACXL,YAAY,CAACF,YAAY,CAAC;YAC1BF,OAAO,CAACS,OAAO,CAAC;UAClB,CAAC,MAAM;YACL;YACAX,YAAY,GAAGK,UAAU,CAACG,UAAU,EAAEX,aAAa,CAAC;UACtD;QACF,CAAC,CAAC,OAAOe,GAAG,EAAE;UACZN,YAAY,CAACF,YAAY,CAAC;UAC1BD,MAAM,CAACS,GAAG,CAAC;QACb;MACF,CAAC;MAEDJ,UAAU,CAAC,CAAC;IACd,CAAC,CAAC;EACJ;EAEAf,iBAAiBA,CAACF,IAAY,EAAmB;IAC/C/C,GAAG,CAACqE,IAAI,CAAC,2BAA2B,CAAC;IACrC,OAAO,IAAI,CAACnB,SAAS,CAClBoB,kBAAkB,IAAoB;MACrC,IAAI,CAACA,kBAAkB,CAACC,SAAS,EAAE;QACjC,OAAO,IAAI;MACb;MAEAvE,GAAG,CAACqE,IAAI,CAAC,qBAAqB,EAAEC,kBAAkB,CAACE,UAAU,CAAC;MAC9D,IAAIF,kBAAkB,CAACG,KAAK,EAAE;QAC5B,OAAOH,kBAAkB,CAACvB,IAAI;MAChC;MAEA/C,GAAG,CAACqE,IAAI,CAAC,oBAAoB,CAAC;MAC9B,MAAM,IAAIN,KAAK,CACb,kEAAkE,GAC/D,GAAEO,kBAAkB,CAACnE,GAAI,EAC9B,CAAC;IACH,CAAC,EACD,IAAI+B,GAAG,CAAE,UAASa,IAAK,GAAE,EAAE,IAAI,CAACX,MAAM,CAAC,EACvC,IAAI,CAACZ,uBAAuB,EAC5B,IAAI,CAACC,sBAAsB,EAC3B,YACF,CAAC;EACH;EAEA,MAAMiD,gBAAgBA,CAAC3B,IAAY,EAAE4B,YAAoB,EAAgB;IACvE,MAAMxE,GAAG,GAAG,IAAI+B,GAAG,CAAC,QAAQ,EAAE,IAAI,CAACE,MAAM,CAAC;IAC1C,MAAMwC,QAAQ,GAAG;MACf,GAAGD,YAAY;MACfE,OAAO,EAAE;QAAEC,MAAM,EAAE/B,IAAI;QAAE,GAAG4B,YAAY,CAACE;MAAQ;IACnD,CAAC;IACD,OAAO,IAAI,CAAC7B,SAAS,CAAC7C,GAAG,EAAE,MAAM,EAAE4E,IAAI,CAACC,SAAS,CAACJ,QAAQ,CAAC,CAAC;EAC9D;EAEAK,yBAAyBA,CACvBC,OAAe,EACfnC,IAAY,EACZ4B,YAAoB,EACM;IAC1B,MAAMxE,GAAG,GAAG,IAAI+B,GAAG,CAAE,SAAQgD,OAAQ,GAAE,EAAE,IAAI,CAAC9C,MAAM,CAAC;IACrD,MAAMwC,QAAQ,GAAG;MACf,GAAGD,YAAY;MACfE,OAAO,EAAE;QAAEC,MAAM,EAAE/B,IAAI;QAAE,GAAG4B,YAAY,CAACE;MAAQ;IACnD,CAAC;IACD,OAAO,IAAI,CAACnF,KAAK,CAACS,GAAG,EAAE,KAAK,EAAE4E,IAAI,CAACC,SAAS,CAACJ,QAAQ,CAAC,CAAC;EACzD;EAEAO,eAAeA,CAACD,OAAe,EAAEE,SAAiB,EAAmB;IACnEpF,GAAG,CAACqE,IAAI,CAAC,yBAAyB,CAAC;IACnC,OAAO,IAAI,CAACnB,SAAS,CAClBoB,kBAAkB,IAAoB;MACrC,MAAM;QAAEe;MAAK,CAAC,GAAGf,kBAAkB;MACnC,IAAIe,IAAI,IAAIA,IAAI,CAACC,MAAM,KAAK,QAAQ,EAAE;QACpC,OAAOD,IAAI,CAAClF,GAAG;MACjB;MAEA,OAAO,IAAI;IACb,CAAC,EACD,IAAI+B,GAAG,CAAE,SAAQgD,OAAQ,aAAYE,SAAU,GAAE,EAAE,IAAI,CAAChD,MAAM,CAAC,EAC/D,IAAI,CAACV,qBAAqB,EAC1B,IAAI,CAACC,oBAAoB,EACzB,UACF,CAAC;EACH;EAEA,MAAMqB,SAASA,CACb7C,GAAQ,EACRoC,MAAc,GAAG,KAAK,EACtBE,IAA+B,EAC/B8C,QAAgB,GAAG,aAAa,EAClB;IACd,MAAMC,QAAQ,GAAG,MAAM,IAAI,CAAC9F,KAAK,CAACS,GAAG,EAAEoC,MAAM,EAAEE,IAAI,CAAC;IACpD,IAAI+C,QAAQ,CAACF,MAAM,GAAG,GAAG,IAAIE,QAAQ,CAACF,MAAM,IAAI,GAAG,EAAE;MACnD,MAAM,IAAIvB,KAAK,CACZ,GAAEwB,QAAS,KAAIC,QAAQ,CAACC,UAAU,IAAID,QAAQ,CAACF,MAAO,GACzD,CAAC;IACH;IACA,MAAMI,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;IAElC,IAAI,CAACH,QAAQ,CAACI,EAAE,EAAE;MAChB5F,GAAG,CAACqE,IAAI,CAAC,kBAAkB,EAAEqB,IAAI,CAAC;MAClC,MAAM,IAAI3B,KAAK,CACZ,GAAEwB,QAAS,KAAIC,QAAQ,CAACC,UAAU,IAAID,QAAQ,CAACF,MAAO,GACzD,CAAC;IACH;IACA,OAAOI,IAAI;EACb;EAEA,MAAMhG,KAAKA,CACTS,GAAQ,EACRoC,MAAc,GAAG,KAAK,EACtBE,IAA+B,EACL;IAC1BzC,GAAG,CAACqE,IAAI,CAAE,iBAAgBlE,GAAG,CAACgC,IAAK,EAAC,CAAC;IACrC,IAAIK,OAAO,GAAG;MACZqD,aAAa,EAAE,MAAM,IAAI,CAACvE,OAAO,CAACH,aAAa,CAAC,CAAC;MACjD2E,MAAM,EAAE,kBAAkB;MAC1B,YAAY,EAAE,IAAI,CAAC/D;IACrB,CAAC;IACD,IAAI,OAAOU,IAAI,KAAK,QAAQ,EAAE;MAC5BD,OAAO,GAAG;QACR,GAAGA,OAAO;QACV,cAAc,EAAE;MAClB,CAAC;IACH;IACA,OAAO,IAAI,CAACF,SAAS,CAACnC,GAAG,EAAE;MAAEoC,MAAM;MAAEE,IAAI;MAAED;IAAQ,CAAC,CAAC;EACvD;EAEA,MAAMuD,kBAAkBA,CAACC,OAAY,EAAEd,OAAe,EAAuB;IAC3E,MAAMe,QAAQ,GAAGD,OAAO,CAAChE,QAAQ,CAACkE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC;IACpD,MAAMC,IAAI,GAAI,GAAE,IAAI,CAACxE,WAAY,IAAGqE,QAAS,EAAC;IAC9C,IAAI;MACF,MAAMT,QAAQ,GAAG,MAAM,IAAI,CAAC9F,KAAK,CAACsG,OAAO,CAAC;MAC1C,IAAI,CAACR,QAAQ,CAACI,EAAE,IAAI,CAACJ,QAAQ,CAAC/C,IAAI,EAAE;QAClC,MAAM,IAAIsB,KAAK,CAAE,uBAAsByB,QAAQ,CAACF,MAAO,EAAC,CAAC;MAC3D;MACA,MAAM,IAAI,CAACe,UAAU,CAACb,QAAQ,CAAC/C,IAAI,EAAE2D,IAAI,CAAC;IAC5C,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdtG,GAAG,CAACqE,IAAI,CAAE,kCAAiCiC,KAAM,GAAE,CAAC;MACpD,MAAM,IAAIvC,KAAK,CAAE,eAAckC,QAAS,SAAQ,CAAC;IACnD;IACA,OAAO;MACLM,EAAE,EAAErB,OAAO;MACXsB,eAAe,EAAE,CAACP,QAAQ;IAC5B,CAAC;EACH;EAEA,MAAMI,UAAUA,CACdI,QAA8B,EAC9BC,QAAgB,EACF;IACd,OAAOjH,SAAS,CAACD,QAAQ,CAAC,CAACiH,QAAQ,EAAEpH,iBAAiB,CAACqH,QAAQ,CAAC,CAAC;EACnE;EAEA,MAAMC,YAAYA,CAChBhE,OAAe,EACfC,OAAe,EACfgE,WAAmB,EACnBjC,YAAoB,EACpBkC,gBAAmD,GAAGC,YAAY,EAC7C;IACrB,MAAMC,UAAU,GAAG,MAAM,IAAI,CAACrE,cAAc,CAACC,OAAO,EAAEC,OAAO,CAAC;IAE9D,MAAMoE,aAAa,GACjBpE,OAAO,KAAK,QAAQ,GAAG,iBAAiB,GAAG,yBAAyB;IACtE,MAAM;MACJqE,IAAI,EAAE/B,OAAO;MACb,CAAC8B,aAAa,GAAG;QAAET,EAAE,EAAEW;MAAa;IACtC,CAAC,GAAG,MAAM,IAAI,CAACxC,gBAAgB,CAACqC,UAAU,EAAEpC,YAAY,CAAC;IAEzD,MAAMkC,gBAAgB,CAACD,WAAW,EAAE1B,OAAO,CAAC;IAC5ClF,GAAG,CAACqE,IAAI,CAAE,2BAA0Ba,OAAQ,GAAE,CAAC;IAC/ClF,GAAG,CAACqE,IAAI,CAAC,8CAA8C,CAAC;IACxDrE,GAAG,CAACqE,IAAI,CAAE,kDAAiDa,OAAQ,KAAI,CAAC;IAExE,MAAMc,OAAO,GAAG,IAAI9D,GAAG,CAAC,MAAM,IAAI,CAACiD,eAAe,CAACD,OAAO,EAAEgC,YAAY,CAAC,CAAC;IAE1E,OAAO,IAAI,CAACnB,kBAAkB,CAACC,OAAO,EAAEd,OAAO,CAAC;EAClD;EAEA,MAAMiC,UAAUA,CACdxE,OAAe,EACfC,OAAe,EACfsC,OAAe,EACfP,YAAoB,EACC;IACrB,MAAMoC,UAAU,GAAG,MAAM,IAAI,CAACrE,cAAc,CAACC,OAAO,EAAEC,OAAO,CAAC;IAE9D,MAAM,IAAI,CAACqC,yBAAyB,CAACC,OAAO,EAAE6B,UAAU,EAAEpC,YAAY,CAAC;IAEvE,MAAMxE,GAAG,GAAG,IAAI+B,GAAG,CAChB,SAAQgD,OAAQ,qCAAoC,EACrD,IAAI,CAAC9C,MACP,CAAC;IACD,MAAM;MACJgF,OAAO,EAAE,CAAC;QAAEb,EAAE,EAAEW;MAAa,CAAC;IAChC,CAAC,GAAG,MAAM,IAAI,CAAClE,SAAS,CAAC7C,GAAG,CAAC;IAE7B,MAAM6F,OAAO,GAAG,IAAI9D,GAAG,CAAC,MAAM,IAAI,CAACiD,eAAe,CAACD,OAAO,EAAEgC,YAAY,CAAC,CAAC;IAE1E,OAAO,IAAI,CAACnB,kBAAkB,CAACC,OAAO,EAAEd,OAAO,CAAC;EAClD;AACF;AAkBA,OAAO,eAAemC,SAASA,CAAC;EAC9BhH,MAAM;EACNC,SAAS;EACTgH,UAAU;EACVC,OAAO;EACPhB,EAAE;EACF5D,OAAO;EACPf,WAAW;EACXgB,OAAO;EACPgE,WAAW;EACXjC,YAAY,GAAG,CAAC,CAAC;EACjB5C,eAAe;EACfyF,YAAY,GAAGnG,MAAM;EACrBoG,YAAY,GAAGrH;AACA,CAAC,EAAuB;EACvC,IAAI;IACF,MAAMsH,KAAK,GAAG,MAAMnI,UAAU,CAACoI,IAAI,CAAChF,OAAO,CAAC;IAE5C,IAAI,CAAC+E,KAAK,CAACE,MAAM,CAAC,CAAC,EAAE;MACnB,MAAM,IAAI7D,KAAK,CAAE,eAAcpB,OAAQ,EAAC,CAAC;IAC3C;EACF,CAAC,CAAC,OAAOkF,SAAS,EAAE;IAClB,MAAM,IAAI9D,KAAK,CAAE,cAAapB,OAAQ,KAAIkF,SAAU,EAAC,CAAC;EACxD;EAEA,IAAItG,OAAO;EACX,IAAI;IACFA,OAAO,GAAG,IAAIW,GAAG,CAACoF,UAAU,CAAC;EAC/B,CAAC,CAAC,OAAOlD,GAAG,EAAE;IACZ,MAAM,IAAIL,KAAK,CAAE,6BAA4BuD,UAAW,EAAC,CAAC;EAC5D;EAEA,MAAMQ,MAAM,GAAG,IAAIN,YAAY,CAAC;IAC9BlG,OAAO,EAAE,IAAImG,YAAY,CAAC;MAAEpH,MAAM;MAAEC;IAAU,CAAC,CAAC;IAChDiB,OAAO;IACPE,sBAAsB,EAAE8F,OAAO;IAC/B5F,oBAAoB,EAAE4F,OAAO;IAC7B3F,WAAW;IACXG;EACF,CAAC,CAAC;;EAEF;EACA;EACA,IAAIwE,EAAE,KAAKrC,SAAS,EAAE;IACpB,OAAO4D,MAAM,CAACnB,YAAY,CAAChE,OAAO,EAAEC,OAAO,EAAEgE,WAAW,EAAEjC,YAAY,CAAC;EACzE;EAEA,OAAOmD,MAAM,CAACX,UAAU,CAACxE,OAAO,EAAEC,OAAO,EAAE2D,EAAE,EAAE5B,YAAY,CAAC;AAC9D;AAEA,OAAO,eAAemC,YAAYA,CAChCiB,QAAgB,EAChBxB,EAAU,EACK;EACf,MAAMhH,UAAU,CAACyI,SAAS,CACxBD,QAAQ,EACR,CACE,+DAA+D,EAC/D,+DAA+D,EAC/DxB,EAAE,CAAC0B,QAAQ,CAAC,CAAC,CACd,CAACC,IAAI,CAAC,IAAI,CACb,CAAC;EAEDlI,GAAG,CAACmI,KAAK,CAAE,2BAA0B5B,EAAG,OAAMwB,QAAS,EAAC,CAAC;AAC3D"}