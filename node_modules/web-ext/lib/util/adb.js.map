{"version": 3, "file": "adb.js", "names": ["ADBKit", "isErrorWithCode", "UsageError", "WebExtError", "createLogger", "packageIdentifiers", "defaultApkComponents", "DEVICE_DIR_BASE", "ARTIFACTS_DIR_PREFIX", "defaultADB", "default", "log", "import", "meta", "url", "wrapADBCall", "asyncFn", "error", "message", "includes", "ADBUtils", "constructor", "params", "adb", "adbBin", "adbHost", "adbPort", "adbClient", "createClient", "bin", "host", "port", "artifactsDirMap", "Map", "userAbortDiscovery", "runShellCommand", "deviceId", "cmd", "debug", "JSON", "stringify", "getDevice", "shell", "then", "util", "readAll", "res", "toString", "discoverDevices", "devices", "listDevices", "map", "dev", "id", "getCurrentUser", "currentUser", "userId", "parseInt", "trim", "isNaN", "discoverInstalledFirefoxAPKs", "firefoxApk", "pmList", "split", "line", "replace", "filter", "browser", "startsWith", "amForceStopAPK", "apk", "getOrCreateArtifactsDir", "artifactsDir", "get", "Date", "now", "testDirOut", "set", "detectOrRemoveOldArtifacts", "removeArtifactDirs", "files", "readdir", "found", "file", "isDirectory", "name", "clearArtifactsDir", "delete", "pushFile", "localPath", "devicePath", "push", "transfer", "Promise", "resolve", "on", "startFirefoxAPK", "apkComponent", "deviceProfileDir", "extras", "key", "value", "component", "startActivity", "wait", "action", "category", "setUserAbortDiscovery", "discoverRDPUnixSocket", "maxDiscoveryTime", "retryInterval", "rdpUnixSockets", "discoveryStartedAt", "msg", "length", "info", "endsWith", "setTimeout", "pop", "setupForward", "remote", "local", "forward", "listADBDevices", "adbUtils", "listADBFirefoxAPKs"], "sources": ["../../src/util/adb.js"], "sourcesContent": ["/* @flow */\nimport ADBKit from '@devicefarmer/adbkit';\n\nimport { isErrorWithCode, UsageError, WebExtError } from '../errors.js';\nimport { createLogger } from '../util/logger.js';\nimport packageIdentifiers, {\n  defaultApkComponents,\n} from '../firefox/package-identifiers.js';\n\nexport const DEVICE_DIR_BASE = '/data/local/tmp/';\nexport const ARTIFACTS_DIR_PREFIX = 'web-ext-artifacts-';\n\nconst defaultADB = ADBKit.default;\n\nconst log = createLogger(import.meta.url);\n\nexport type ADBUtilsParams = {|\n  adb?: typeof defaultADB,\n  // ADB configs.\n  adbBin?: string,\n  adbHost?: string,\n  adbPort?: string,\n  adbDevice?: string,\n|};\n\nexport type DiscoveryParams = {\n  maxDiscoveryTime: number,\n  retryInterval: number,\n};\n\n// Helper function used to raise an UsageError when the adb binary has not been found.\nasync function wrapADBCall(asyncFn: (...any) => Promise<any>): Promise<any> {\n  try {\n    return await asyncFn();\n  } catch (error) {\n    if (\n      isErrorWithCode('ENOENT', error) &&\n      error.message.includes('spawn adb')\n    ) {\n      throw new UsageError(\n        'No adb executable has been found. ' +\n          'You can Use --adb-bin, --adb-host/--adb-port ' +\n          'to configure it manually if needed.'\n      );\n    }\n\n    throw error;\n  }\n}\n\nexport default class ADBUtils {\n  params: ADBUtilsParams;\n  adb: typeof defaultADB;\n  adbClient: any; // TODO: better flow typing here.\n\n  // Map<deviceId -> artifactsDir>\n  artifactsDirMap: Map<string, string>;\n  // Toggled when the user wants to abort the RDP Unix Socket discovery loop\n  // while it is still executing.\n  userAbortDiscovery: boolean;\n\n  constructor(params: ADBUtilsParams) {\n    this.params = params;\n\n    const { adb, adbBin, adbHost, adbPort } = params;\n\n    this.adb = adb || defaultADB;\n\n    this.adbClient = this.adb.createClient({\n      bin: adbBin,\n      host: adbHost,\n      port: adbPort,\n    });\n\n    this.artifactsDirMap = new Map();\n\n    this.userAbortDiscovery = false;\n  }\n\n  runShellCommand(\n    deviceId: string,\n    cmd: string | Array<string>\n  ): Promise<string> {\n    const { adb, adbClient } = this;\n\n    log.debug(`Run adb shell command on ${deviceId}: ${JSON.stringify(cmd)}`);\n\n    return wrapADBCall(async () => {\n      return await adbClient\n        .getDevice(deviceId)\n        .shell(cmd)\n        .then(adb.util.readAll);\n    }).then((res) => res.toString());\n  }\n\n  async discoverDevices(): Promise<Array<string>> {\n    const { adbClient } = this;\n\n    let devices = [];\n\n    log.debug('Listing android devices');\n    devices = await wrapADBCall(async () => adbClient.listDevices());\n\n    return devices.map((dev) => dev.id);\n  }\n\n  async getCurrentUser(deviceId: string): Promise<number> {\n    log.debug(`Retrieving  current user on ${deviceId}`);\n    const currentUser = await this.runShellCommand(deviceId, [\n      'am',\n      'get-current-user',\n    ]);\n\n    const userId = parseInt(currentUser.trim());\n    if (isNaN(userId)) {\n      throw new WebExtError(`Unable to retrieve current user on ${deviceId}`);\n    }\n\n    return userId;\n  }\n\n  async discoverInstalledFirefoxAPKs(\n    deviceId: string,\n    firefoxApk?: string\n  ): Promise<Array<string>> {\n    const userId = await this.getCurrentUser(deviceId);\n\n    log.debug(`Listing installed Firefox APKs on ${deviceId}`);\n    const pmList = await this.runShellCommand(deviceId, [\n      'pm',\n      'list',\n      'packages',\n      '--user',\n      `${userId}`,\n    ]);\n\n    return pmList\n      .split('\\n')\n      .map((line) => line.replace('package:', '').trim())\n      .filter((line) => {\n        // Look for an exact match if firefoxApk is defined.\n        if (firefoxApk) {\n          return line === firefoxApk;\n        }\n        // Match any package name that starts with the package name of a Firefox for Android browser.\n        for (const browser of packageIdentifiers) {\n          if (line.startsWith(browser)) {\n            return true;\n          }\n        }\n\n        return false;\n      });\n  }\n\n  async amForceStopAPK(deviceId: string, apk: string): Promise<void> {\n    await this.runShellCommand(deviceId, ['am', 'force-stop', apk]);\n  }\n\n  async getOrCreateArtifactsDir(deviceId: string): Promise<string> {\n    let artifactsDir = this.artifactsDirMap.get(deviceId);\n\n    if (artifactsDir) {\n      return artifactsDir;\n    }\n\n    artifactsDir = `${DEVICE_DIR_BASE}${ARTIFACTS_DIR_PREFIX}${Date.now()}`;\n\n    const testDirOut = (\n      await this.runShellCommand(deviceId, `test -d ${artifactsDir} ; echo $?`)\n    ).trim();\n\n    if (testDirOut !== '1') {\n      throw new WebExtError(\n        `Cannot create artifacts directory ${artifactsDir} ` +\n          `because it exists on ${deviceId}.`\n      );\n    }\n\n    await this.runShellCommand(deviceId, ['mkdir', '-p', artifactsDir]);\n\n    this.artifactsDirMap.set(deviceId, artifactsDir);\n\n    return artifactsDir;\n  }\n\n  async detectOrRemoveOldArtifacts(\n    deviceId: string,\n    removeArtifactDirs?: boolean = false\n  ): Promise<boolean> {\n    const { adbClient } = this;\n\n    log.debug('Checking adb device for existing web-ext artifacts dirs');\n\n    return wrapADBCall(async () => {\n      const files = await adbClient\n        .getDevice(deviceId)\n        .readdir(DEVICE_DIR_BASE);\n      let found = false;\n\n      for (const file of files) {\n        if (\n          !file.isDirectory() ||\n          !file.name.startsWith(ARTIFACTS_DIR_PREFIX)\n        ) {\n          continue;\n        }\n\n        // Return earlier if we only need to warn the user that some\n        // existing artifacts dirs have been found on the adb device.\n        if (!removeArtifactDirs) {\n          return true;\n        }\n\n        found = true;\n\n        const artifactsDir = `${DEVICE_DIR_BASE}${file.name}`;\n\n        log.debug(\n          `Removing artifacts directory ${artifactsDir} from device ${deviceId}`\n        );\n\n        await this.runShellCommand(deviceId, ['rm', '-rf', artifactsDir]);\n      }\n\n      return found;\n    });\n  }\n\n  async clearArtifactsDir(deviceId: string): Promise<void> {\n    const artifactsDir = this.artifactsDirMap.get(deviceId);\n\n    if (!artifactsDir) {\n      // nothing to do here.\n      return;\n    }\n\n    this.artifactsDirMap.delete(deviceId);\n\n    log.debug(\n      `Removing ${artifactsDir} artifacts directory on ${deviceId} device`\n    );\n\n    await this.runShellCommand(deviceId, ['rm', '-rf', artifactsDir]);\n  }\n\n  async pushFile(\n    deviceId: string,\n    localPath: string,\n    devicePath: string\n  ): Promise<void> {\n    const { adbClient } = this;\n\n    log.debug(`Pushing ${localPath} to ${devicePath} on ${deviceId}`);\n\n    await wrapADBCall(async () => {\n      await adbClient\n        .getDevice(deviceId)\n        .push(localPath, devicePath)\n        .then(function (transfer) {\n          return new Promise((resolve) => {\n            transfer.on('end', resolve);\n          });\n        });\n    });\n  }\n\n  async startFirefoxAPK(\n    deviceId: string,\n    apk: string,\n    apkComponent: ?string,\n    deviceProfileDir: string\n  ): Promise<void> {\n    const { adbClient } = this;\n\n    log.debug(`Starting ${apk} on ${deviceId}`);\n\n    // Fenix does ignore the -profile parameter, on the contrary Fennec\n    // would run using the given path as the profile to be used during\n    // this execution.\n    const extras = [\n      {\n        key: 'args',\n        value: `-profile ${deviceProfileDir}`,\n      },\n    ];\n\n    if (!apkComponent) {\n      apkComponent = '.App';\n      if (defaultApkComponents[apk]) {\n        apkComponent = defaultApkComponents[apk];\n      }\n    } else if (!apkComponent.includes('.')) {\n      apkComponent = `.${apkComponent}`;\n    }\n\n    // if `apk` is a browser package or the `apk` has a\n    // browser package prefix: prepend the package identifier\n    // before `apkComponent`\n    if (apkComponent.startsWith('.')) {\n      for (const browser of packageIdentifiers) {\n        if (apk === browser || apk.startsWith(`${browser}.`)) {\n          apkComponent = browser + apkComponent;\n        }\n      }\n    }\n\n    // if `apkComponent` starts with a '.', then adb will expand\n    // the following to: `${apk}/${apk}.${apkComponent}`\n    const component = `${apk}/${apkComponent}`;\n\n    await wrapADBCall(async () => {\n      try {\n        // TODO: once Fenix (release) uses Android 13, we can get rid of this\n        // call and only use the second call in the `catch` block.\n        await adbClient.getDevice(deviceId).startActivity({\n          wait: true,\n          action: 'android.activity.MAIN',\n          component,\n          extras,\n        });\n      } catch {\n        // Android 13+ requires a different action/category but we still need\n        // to support older Fenix builds.\n        await adbClient.getDevice(deviceId).startActivity({\n          wait: true,\n          action: 'android.intent.action.MAIN',\n          category: 'android.intent.category.LAUNCHER',\n          component,\n          extras,\n        });\n      }\n    });\n  }\n\n  setUserAbortDiscovery(value: boolean) {\n    this.userAbortDiscovery = value;\n  }\n\n  async discoverRDPUnixSocket(\n    deviceId: string,\n    apk: string,\n    { maxDiscoveryTime, retryInterval }: DiscoveryParams = {}\n  ): Promise<string> {\n    let rdpUnixSockets = [];\n\n    const discoveryStartedAt = Date.now();\n    const msg =\n      `Waiting for ${apk} Remote Debugging Server...` +\n      '\\nMake sure to enable \"Remote Debugging via USB\" ' +\n      'from Settings -> Developer Tools if it is not yet enabled.';\n\n    while (rdpUnixSockets.length === 0) {\n      log.info(msg);\n      if (this.userAbortDiscovery) {\n        throw new UsageError(\n          'Exiting Firefox Remote Debugging socket discovery on user request'\n        );\n      }\n\n      if (Date.now() - discoveryStartedAt > maxDiscoveryTime) {\n        throw new WebExtError(\n          'Timeout while waiting for the Android Firefox Debugger Socket'\n        );\n      }\n\n      rdpUnixSockets = (\n        await this.runShellCommand(deviceId, ['cat', '/proc/net/unix'])\n      )\n        .split('\\n')\n        .filter((line) => {\n          // The RDP unix socket is expected to be a path in the form:\n          //   /data/data/org.mozilla.fennec_rpl/firefox-debugger-socket\n          return line.trim().endsWith(`${apk}/firefox-debugger-socket`);\n        });\n\n      if (rdpUnixSockets.length === 0) {\n        await new Promise((resolve) => setTimeout(resolve, retryInterval));\n      }\n    }\n\n    // Convert into an array of unix socket filenames.\n    rdpUnixSockets = rdpUnixSockets.map((line) => {\n      return line.trim().split(/\\s/).pop();\n    });\n\n    if (rdpUnixSockets.length > 1) {\n      throw new WebExtError(\n        'Unexpected multiple RDP sockets: ' +\n          `${JSON.stringify(rdpUnixSockets)}`\n      );\n    }\n\n    return rdpUnixSockets[0];\n  }\n\n  async setupForward(deviceId: string, remote: string, local: string) {\n    const { adbClient } = this;\n\n    // TODO(rpl): we should use adb.listForwards and reuse the existing one if any (especially\n    // because adbkit doesn't seem to support `adb forward --remote` yet).\n    log.debug(`Configuring ADB forward for ${deviceId}: ${remote} -> ${local}`);\n\n    await wrapADBCall(async () => {\n      await adbClient.getDevice(deviceId).forward(local, remote);\n    });\n  }\n}\n\nexport async function listADBDevices(adbBin?: string): Promise<Array<string>> {\n  const adbUtils = new ADBUtils({ adbBin });\n  return adbUtils.discoverDevices();\n}\n\nexport async function listADBFirefoxAPKs(\n  deviceId: string,\n  adbBin?: string\n): Promise<Array<string>> {\n  const adbUtils = new ADBUtils({ adbBin });\n  return adbUtils.discoverInstalledFirefoxAPKs(deviceId);\n}\n"], "mappings": "AACA,OAAOA,MAAM,MAAM,sBAAsB;AAEzC,SAASC,eAAe,EAAEC,UAAU,EAAEC,WAAW,QAAQ,cAAc;AACvE,SAASC,YAAY,QAAQ,mBAAmB;AAChD,OAAOC,kBAAkB,IACvBC,oBAAoB,QACf,mCAAmC;AAE1C,OAAO,MAAMC,eAAe,GAAG,kBAAkB;AACjD,OAAO,MAAMC,oBAAoB,GAAG,oBAAoB;AAExD,MAAMC,UAAU,GAAGT,MAAM,CAACU,OAAO;AAEjC,MAAMC,GAAG,GAAGP,YAAY,CAACQ,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC;AAgBzC;AACA,eAAeC,WAAWA,CAACC,OAAiC,EAAgB;EAC1E,IAAI;IACF,OAAO,MAAMA,OAAO,CAAC,CAAC;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,IACEhB,eAAe,CAAC,QAAQ,EAAEgB,KAAK,CAAC,IAChCA,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,WAAW,CAAC,EACnC;MACA,MAAM,IAAIjB,UAAU,CAClB,oCAAoC,GAClC,+CAA+C,GAC/C,qCACJ,CAAC;IACH;IAEA,MAAMe,KAAK;EACb;AACF;AAEA,eAAe,MAAMG,QAAQ,CAAC;EAGZ;;EAEhB;;EAEA;EACA;;EAGAC,WAAWA,CAACC,MAAsB,EAAE;IAClC,IAAI,CAACA,MAAM,GAAGA,MAAM;IAEpB,MAAM;MAAEC,GAAG;MAAEC,MAAM;MAAEC,OAAO;MAAEC;IAAQ,CAAC,GAAGJ,MAAM;IAEhD,IAAI,CAACC,GAAG,GAAGA,GAAG,IAAId,UAAU;IAE5B,IAAI,CAACkB,SAAS,GAAG,IAAI,CAACJ,GAAG,CAACK,YAAY,CAAC;MACrCC,GAAG,EAAEL,MAAM;MACXM,IAAI,EAAEL,OAAO;MACbM,IAAI,EAAEL;IACR,CAAC,CAAC;IAEF,IAAI,CAACM,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;IAEhC,IAAI,CAACC,kBAAkB,GAAG,KAAK;EACjC;EAEAC,eAAeA,CACbC,QAAgB,EAChBC,GAA2B,EACV;IACjB,MAAM;MAAEd,GAAG;MAAEI;IAAU,CAAC,GAAG,IAAI;IAE/BhB,GAAG,CAAC2B,KAAK,CAAE,4BAA2BF,QAAS,KAAIG,IAAI,CAACC,SAAS,CAACH,GAAG,CAAE,EAAC,CAAC;IAEzE,OAAOtB,WAAW,CAAC,YAAY;MAC7B,OAAO,MAAMY,SAAS,CACnBc,SAAS,CAACL,QAAQ,CAAC,CACnBM,KAAK,CAACL,GAAG,CAAC,CACVM,IAAI,CAACpB,GAAG,CAACqB,IAAI,CAACC,OAAO,CAAC;IAC3B,CAAC,CAAC,CAACF,IAAI,CAAEG,GAAG,IAAKA,GAAG,CAACC,QAAQ,CAAC,CAAC,CAAC;EAClC;EAEA,MAAMC,eAAeA,CAAA,EAA2B;IAC9C,MAAM;MAAErB;IAAU,CAAC,GAAG,IAAI;IAE1B,IAAIsB,OAAO,GAAG,EAAE;IAEhBtC,GAAG,CAAC2B,KAAK,CAAC,yBAAyB,CAAC;IACpCW,OAAO,GAAG,MAAMlC,WAAW,CAAC,YAAYY,SAAS,CAACuB,WAAW,CAAC,CAAC,CAAC;IAEhE,OAAOD,OAAO,CAACE,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAACC,EAAE,CAAC;EACrC;EAEA,MAAMC,cAAcA,CAAClB,QAAgB,EAAmB;IACtDzB,GAAG,CAAC2B,KAAK,CAAE,+BAA8BF,QAAS,EAAC,CAAC;IACpD,MAAMmB,WAAW,GAAG,MAAM,IAAI,CAACpB,eAAe,CAACC,QAAQ,EAAE,CACvD,IAAI,EACJ,kBAAkB,CACnB,CAAC;IAEF,MAAMoB,MAAM,GAAGC,QAAQ,CAACF,WAAW,CAACG,IAAI,CAAC,CAAC,CAAC;IAC3C,IAAIC,KAAK,CAACH,MAAM,CAAC,EAAE;MACjB,MAAM,IAAIrD,WAAW,CAAE,sCAAqCiC,QAAS,EAAC,CAAC;IACzE;IAEA,OAAOoB,MAAM;EACf;EAEA,MAAMI,4BAA4BA,CAChCxB,QAAgB,EAChByB,UAAmB,EACK;IACxB,MAAML,MAAM,GAAG,MAAM,IAAI,CAACF,cAAc,CAAClB,QAAQ,CAAC;IAElDzB,GAAG,CAAC2B,KAAK,CAAE,qCAAoCF,QAAS,EAAC,CAAC;IAC1D,MAAM0B,MAAM,GAAG,MAAM,IAAI,CAAC3B,eAAe,CAACC,QAAQ,EAAE,CAClD,IAAI,EACJ,MAAM,EACN,UAAU,EACV,QAAQ,EACP,GAAEoB,MAAO,EAAC,CACZ,CAAC;IAEF,OAAOM,MAAM,CACVC,KAAK,CAAC,IAAI,CAAC,CACXZ,GAAG,CAAEa,IAAI,IAAKA,IAAI,CAACC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAACP,IAAI,CAAC,CAAC,CAAC,CAClDQ,MAAM,CAAEF,IAAI,IAAK;MAChB;MACA,IAAIH,UAAU,EAAE;QACd,OAAOG,IAAI,KAAKH,UAAU;MAC5B;MACA;MACA,KAAK,MAAMM,OAAO,IAAI9D,kBAAkB,EAAE;QACxC,IAAI2D,IAAI,CAACI,UAAU,CAACD,OAAO,CAAC,EAAE;UAC5B,OAAO,IAAI;QACb;MACF;MAEA,OAAO,KAAK;IACd,CAAC,CAAC;EACN;EAEA,MAAME,cAAcA,CAACjC,QAAgB,EAAEkC,GAAW,EAAiB;IACjE,MAAM,IAAI,CAACnC,eAAe,CAACC,QAAQ,EAAE,CAAC,IAAI,EAAE,YAAY,EAAEkC,GAAG,CAAC,CAAC;EACjE;EAEA,MAAMC,uBAAuBA,CAACnC,QAAgB,EAAmB;IAC/D,IAAIoC,YAAY,GAAG,IAAI,CAACxC,eAAe,CAACyC,GAAG,CAACrC,QAAQ,CAAC;IAErD,IAAIoC,YAAY,EAAE;MAChB,OAAOA,YAAY;IACrB;IAEAA,YAAY,GAAI,GAAEjE,eAAgB,GAAEC,oBAAqB,GAAEkE,IAAI,CAACC,GAAG,CAAC,CAAE,EAAC;IAEvE,MAAMC,UAAU,GAAG,CACjB,MAAM,IAAI,CAACzC,eAAe,CAACC,QAAQ,EAAG,WAAUoC,YAAa,YAAW,CAAC,EACzEd,IAAI,CAAC,CAAC;IAER,IAAIkB,UAAU,KAAK,GAAG,EAAE;MACtB,MAAM,IAAIzE,WAAW,CAClB,qCAAoCqE,YAAa,GAAE,GACjD,wBAAuBpC,QAAS,GACrC,CAAC;IACH;IAEA,MAAM,IAAI,CAACD,eAAe,CAACC,QAAQ,EAAE,CAAC,OAAO,EAAE,IAAI,EAAEoC,YAAY,CAAC,CAAC;IAEnE,IAAI,CAACxC,eAAe,CAAC6C,GAAG,CAACzC,QAAQ,EAAEoC,YAAY,CAAC;IAEhD,OAAOA,YAAY;EACrB;EAEA,MAAMM,0BAA0BA,CAC9B1C,QAAgB,EAChB2C,kBAA4B,GAAG,KAAK,EAClB;IAClB,MAAM;MAAEpD;IAAU,CAAC,GAAG,IAAI;IAE1BhB,GAAG,CAAC2B,KAAK,CAAC,yDAAyD,CAAC;IAEpE,OAAOvB,WAAW,CAAC,YAAY;MAC7B,MAAMiE,KAAK,GAAG,MAAMrD,SAAS,CAC1Bc,SAAS,CAACL,QAAQ,CAAC,CACnB6C,OAAO,CAAC1E,eAAe,CAAC;MAC3B,IAAI2E,KAAK,GAAG,KAAK;MAEjB,KAAK,MAAMC,IAAI,IAAIH,KAAK,EAAE;QACxB,IACE,CAACG,IAAI,CAACC,WAAW,CAAC,CAAC,IACnB,CAACD,IAAI,CAACE,IAAI,CAACjB,UAAU,CAAC5D,oBAAoB,CAAC,EAC3C;UACA;QACF;;QAEA;QACA;QACA,IAAI,CAACuE,kBAAkB,EAAE;UACvB,OAAO,IAAI;QACb;QAEAG,KAAK,GAAG,IAAI;QAEZ,MAAMV,YAAY,GAAI,GAAEjE,eAAgB,GAAE4E,IAAI,CAACE,IAAK,EAAC;QAErD1E,GAAG,CAAC2B,KAAK,CACN,gCAA+BkC,YAAa,gBAAepC,QAAS,EACvE,CAAC;QAED,MAAM,IAAI,CAACD,eAAe,CAACC,QAAQ,EAAE,CAAC,IAAI,EAAE,KAAK,EAAEoC,YAAY,CAAC,CAAC;MACnE;MAEA,OAAOU,KAAK;IACd,CAAC,CAAC;EACJ;EAEA,MAAMI,iBAAiBA,CAAClD,QAAgB,EAAiB;IACvD,MAAMoC,YAAY,GAAG,IAAI,CAACxC,eAAe,CAACyC,GAAG,CAACrC,QAAQ,CAAC;IAEvD,IAAI,CAACoC,YAAY,EAAE;MACjB;MACA;IACF;IAEA,IAAI,CAACxC,eAAe,CAACuD,MAAM,CAACnD,QAAQ,CAAC;IAErCzB,GAAG,CAAC2B,KAAK,CACN,YAAWkC,YAAa,2BAA0BpC,QAAS,SAC9D,CAAC;IAED,MAAM,IAAI,CAACD,eAAe,CAACC,QAAQ,EAAE,CAAC,IAAI,EAAE,KAAK,EAAEoC,YAAY,CAAC,CAAC;EACnE;EAEA,MAAMgB,QAAQA,CACZpD,QAAgB,EAChBqD,SAAiB,EACjBC,UAAkB,EACH;IACf,MAAM;MAAE/D;IAAU,CAAC,GAAG,IAAI;IAE1BhB,GAAG,CAAC2B,KAAK,CAAE,WAAUmD,SAAU,OAAMC,UAAW,OAAMtD,QAAS,EAAC,CAAC;IAEjE,MAAMrB,WAAW,CAAC,YAAY;MAC5B,MAAMY,SAAS,CACZc,SAAS,CAACL,QAAQ,CAAC,CACnBuD,IAAI,CAACF,SAAS,EAAEC,UAAU,CAAC,CAC3B/C,IAAI,CAAC,UAAUiD,QAAQ,EAAE;QACxB,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAK;UAC9BF,QAAQ,CAACG,EAAE,CAAC,KAAK,EAAED,OAAO,CAAC;QAC7B,CAAC,CAAC;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACJ;EAEA,MAAME,eAAeA,CACnB5D,QAAgB,EAChBkC,GAAW,EACX2B,YAAqB,EACrBC,gBAAwB,EACT;IACf,MAAM;MAAEvE;IAAU,CAAC,GAAG,IAAI;IAE1BhB,GAAG,CAAC2B,KAAK,CAAE,YAAWgC,GAAI,OAAMlC,QAAS,EAAC,CAAC;;IAE3C;IACA;IACA;IACA,MAAM+D,MAAM,GAAG,CACb;MACEC,GAAG,EAAE,MAAM;MACXC,KAAK,EAAG,YAAWH,gBAAiB;IACtC,CAAC,CACF;IAED,IAAI,CAACD,YAAY,EAAE;MACjBA,YAAY,GAAG,MAAM;MACrB,IAAI3F,oBAAoB,CAACgE,GAAG,CAAC,EAAE;QAC7B2B,YAAY,GAAG3F,oBAAoB,CAACgE,GAAG,CAAC;MAC1C;IACF,CAAC,MAAM,IAAI,CAAC2B,YAAY,CAAC9E,QAAQ,CAAC,GAAG,CAAC,EAAE;MACtC8E,YAAY,GAAI,IAAGA,YAAa,EAAC;IACnC;;IAEA;IACA;IACA;IACA,IAAIA,YAAY,CAAC7B,UAAU,CAAC,GAAG,CAAC,EAAE;MAChC,KAAK,MAAMD,OAAO,IAAI9D,kBAAkB,EAAE;QACxC,IAAIiE,GAAG,KAAKH,OAAO,IAAIG,GAAG,CAACF,UAAU,CAAE,GAAED,OAAQ,GAAE,CAAC,EAAE;UACpD8B,YAAY,GAAG9B,OAAO,GAAG8B,YAAY;QACvC;MACF;IACF;;IAEA;IACA;IACA,MAAMK,SAAS,GAAI,GAAEhC,GAAI,IAAG2B,YAAa,EAAC;IAE1C,MAAMlF,WAAW,CAAC,YAAY;MAC5B,IAAI;QACF;QACA;QACA,MAAMY,SAAS,CAACc,SAAS,CAACL,QAAQ,CAAC,CAACmE,aAAa,CAAC;UAChDC,IAAI,EAAE,IAAI;UACVC,MAAM,EAAE,uBAAuB;UAC/BH,SAAS;UACTH;QACF,CAAC,CAAC;MACJ,CAAC,CAAC,MAAM;QACN;QACA;QACA,MAAMxE,SAAS,CAACc,SAAS,CAACL,QAAQ,CAAC,CAACmE,aAAa,CAAC;UAChDC,IAAI,EAAE,IAAI;UACVC,MAAM,EAAE,4BAA4B;UACpCC,QAAQ,EAAE,kCAAkC;UAC5CJ,SAAS;UACTH;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;EAEAQ,qBAAqBA,CAACN,KAAc,EAAE;IACpC,IAAI,CAACnE,kBAAkB,GAAGmE,KAAK;EACjC;EAEA,MAAMO,qBAAqBA,CACzBxE,QAAgB,EAChBkC,GAAW,EACX;IAAEuC,gBAAgB;IAAEC;EAA+B,CAAC,GAAG,CAAC,CAAC,EACxC;IACjB,IAAIC,cAAc,GAAG,EAAE;IAEvB,MAAMC,kBAAkB,GAAGtC,IAAI,CAACC,GAAG,CAAC,CAAC;IACrC,MAAMsC,GAAG,GACN,eAAc3C,GAAI,6BAA4B,GAC/C,mDAAmD,GACnD,4DAA4D;IAE9D,OAAOyC,cAAc,CAACG,MAAM,KAAK,CAAC,EAAE;MAClCvG,GAAG,CAACwG,IAAI,CAACF,GAAG,CAAC;MACb,IAAI,IAAI,CAAC/E,kBAAkB,EAAE;QAC3B,MAAM,IAAIhC,UAAU,CAClB,mEACF,CAAC;MACH;MAEA,IAAIwE,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGqC,kBAAkB,GAAGH,gBAAgB,EAAE;QACtD,MAAM,IAAI1G,WAAW,CACnB,+DACF,CAAC;MACH;MAEA4G,cAAc,GAAG,CACf,MAAM,IAAI,CAAC5E,eAAe,CAACC,QAAQ,EAAE,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC,EAE9D2B,KAAK,CAAC,IAAI,CAAC,CACXG,MAAM,CAAEF,IAAI,IAAK;QAChB;QACA;QACA,OAAOA,IAAI,CAACN,IAAI,CAAC,CAAC,CAAC0D,QAAQ,CAAE,GAAE9C,GAAI,0BAAyB,CAAC;MAC/D,CAAC,CAAC;MAEJ,IAAIyC,cAAc,CAACG,MAAM,KAAK,CAAC,EAAE;QAC/B,MAAM,IAAIrB,OAAO,CAAEC,OAAO,IAAKuB,UAAU,CAACvB,OAAO,EAAEgB,aAAa,CAAC,CAAC;MACpE;IACF;;IAEA;IACAC,cAAc,GAAGA,cAAc,CAAC5D,GAAG,CAAEa,IAAI,IAAK;MAC5C,OAAOA,IAAI,CAACN,IAAI,CAAC,CAAC,CAACK,KAAK,CAAC,IAAI,CAAC,CAACuD,GAAG,CAAC,CAAC;IACtC,CAAC,CAAC;IAEF,IAAIP,cAAc,CAACG,MAAM,GAAG,CAAC,EAAE;MAC7B,MAAM,IAAI/G,WAAW,CACnB,mCAAmC,GAChC,GAAEoC,IAAI,CAACC,SAAS,CAACuE,cAAc,CAAE,EACtC,CAAC;IACH;IAEA,OAAOA,cAAc,CAAC,CAAC,CAAC;EAC1B;EAEA,MAAMQ,YAAYA,CAACnF,QAAgB,EAAEoF,MAAc,EAAEC,KAAa,EAAE;IAClE,MAAM;MAAE9F;IAAU,CAAC,GAAG,IAAI;;IAE1B;IACA;IACAhB,GAAG,CAAC2B,KAAK,CAAE,+BAA8BF,QAAS,KAAIoF,MAAO,OAAMC,KAAM,EAAC,CAAC;IAE3E,MAAM1G,WAAW,CAAC,YAAY;MAC5B,MAAMY,SAAS,CAACc,SAAS,CAACL,QAAQ,CAAC,CAACsF,OAAO,CAACD,KAAK,EAAED,MAAM,CAAC;IAC5D,CAAC,CAAC;EACJ;AACF;AAEA,OAAO,eAAeG,cAAcA,CAACnG,MAAe,EAA0B;EAC5E,MAAMoG,QAAQ,GAAG,IAAIxG,QAAQ,CAAC;IAAEI;EAAO,CAAC,CAAC;EACzC,OAAOoG,QAAQ,CAAC5E,eAAe,CAAC,CAAC;AACnC;AAEA,OAAO,eAAe6E,kBAAkBA,CACtCzF,QAAgB,EAChBZ,MAAe,EACS;EACxB,MAAMoG,QAAQ,GAAG,IAAIxG,QAAQ,CAAC;IAAEI;EAAO,CAAC,CAAC;EACzC,OAAOoG,QAAQ,CAAChE,4BAA4B,CAACxB,QAAQ,CAAC;AACxD"}