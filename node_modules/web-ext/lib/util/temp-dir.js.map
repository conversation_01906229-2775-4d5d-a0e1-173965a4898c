{"version": 3, "file": "temp-dir.js", "names": ["promisify", "tmp", "createLogger", "multiArgsPromisedFn", "promisifyCustom", "log", "import", "meta", "url", "dir", "createTempDir", "withTempDir", "makePromise", "tmpDir", "TempDir", "create", "then", "catch", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "_path", "undefined", "_removeTempDir", "prefix", "unsafeCleanup", "tmpPath", "removeTempDir", "Promise", "resolve", "reject", "next", "err", "debug", "path", "Error", "error", "remove", "promiseResult"], "sources": ["../../src/util/temp-dir.js"], "sourcesContent": ["/* @flow */\nimport { promisify } from 'util';\n\nimport tmp from 'tmp';\n\nimport { createLogger } from './logger.js';\nimport { multiArgsPromisedFn, promisifyCustom } from './promisify.js';\n\nconst log = createLogger(import.meta.url);\n\nexport type MakePromiseCallback = (tmpDir: TempDir) => any;\n\ntmp.dir[promisifyCustom] = multiArgsPromisedFn(tmp.dir);\n\nconst createTempDir = promisify(tmp.dir);\n\n/*\n * Work with a self-destructing temporary directory in a promise chain.\n *\n * The directory will be destroyed when the promise chain is finished\n * (whether there was an error or not).\n *\n * Usage:\n *\n * withTempDir(\n *   (tmpDir) =>\n *     doSomething(tmpDir.path())\n *     .then(...)\n * );\n *\n */\nexport function withTempDir(makePromise: MakePromiseCallback): Promise<any> {\n  const tmpDir = new TempDir();\n  return tmpDir\n    .create()\n    .then(() => {\n      return makePromise(tmpDir);\n    })\n    .catch(tmpDir.errorHandler())\n    .then(tmpDir.successHandler());\n}\n\n/*\n * Work with a self-destructing temporary directory object.\n *\n * It is safer to use withTempDir() instead but if you know\n * what you're doing you can use it directly like:\n *\n * let tmpDir = new TempDir();\n * tmpDir.create()\n *   .then(() => {\n *     // work with tmpDir.path()\n *   })\n *   .catch(tmpDir.errorHandler())\n *   .then(tmpDir.successHandler());\n *\n */\nexport class TempDir {\n  _path: string | void;\n  _removeTempDir: Function | void;\n\n  constructor() {\n    this._path = undefined;\n    this._removeTempDir = undefined;\n  }\n\n  /*\n   * Returns a promise that is fulfilled when the temp directory has\n   * been created.\n   */\n  create(): Promise<TempDir> {\n    return createTempDir({\n      prefix: 'tmp-web-ext-',\n      // This allows us to remove a non-empty tmp dir.\n      unsafeCleanup: true,\n    }).then(([tmpPath, removeTempDir]) => {\n      this._path = tmpPath;\n      this._removeTempDir = () =>\n        new Promise((resolve, reject) => {\n          // `removeTempDir` parameter is a `next` callback which\n          // is called once the dir has been removed.\n          const next = (err) => (err ? reject(err) : resolve());\n          removeTempDir(next);\n        });\n      log.debug(`Created temporary directory: ${this.path()}`);\n      return this;\n    });\n  }\n\n  /*\n   * Get the absolute path of the temp directory.\n   */\n  path(): string {\n    if (!this._path) {\n      throw new Error('You cannot access path() before calling create()');\n    }\n    return this._path;\n  }\n\n  /*\n   * Returns a callback that will catch an error, remove\n   * the temporary directory, and throw the error.\n   *\n   * This is intended for use in a promise like\n   * Promise().catch(tmp.errorHandler())\n   */\n  errorHandler(): Function {\n    return async (error) => {\n      await this.remove();\n      throw error;\n    };\n  }\n\n  /*\n   * Returns a callback that will remove the temporary direcotry.\n   *\n   * This is intended for use in a promise like\n   * Promise().then(tmp.successHandler())\n   */\n  successHandler(): Function {\n    return async (promiseResult) => {\n      await this.remove();\n      return promiseResult;\n    };\n  }\n\n  /*\n   * Remove the temp directory.\n   */\n  remove(): Promise<void> | void {\n    if (!this._removeTempDir) {\n      return;\n    }\n    log.debug(`Removing temporary directory: ${this.path()}`);\n    return this._removeTempDir && this._removeTempDir();\n  }\n}\n"], "mappings": "AACA,SAASA,SAAS,QAAQ,MAAM;AAEhC,OAAOC,GAAG,MAAM,KAAK;AAErB,SAASC,YAAY,QAAQ,aAAa;AAC1C,SAASC,mBAAmB,EAAEC,eAAe,QAAQ,gBAAgB;AAErE,MAAMC,GAAG,GAAGH,YAAY,CAACI,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC;AAIzCP,GAAG,CAACQ,GAAG,CAACL,eAAe,CAAC,GAAGD,mBAAmB,CAACF,GAAG,CAACQ,GAAG,CAAC;AAEvD,MAAMC,aAAa,GAAGV,SAAS,CAACC,GAAG,CAACQ,GAAG,CAAC;;AAExC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,WAAWA,CAACC,WAAgC,EAAgB;EAC1E,MAAMC,MAAM,GAAG,IAAIC,OAAO,CAAC,CAAC;EAC5B,OAAOD,MAAM,CACVE,MAAM,CAAC,CAAC,CACRC,IAAI,CAAC,MAAM;IACV,OAAOJ,WAAW,CAACC,MAAM,CAAC;EAC5B,CAAC,CAAC,CACDI,KAAK,CAACJ,MAAM,CAACK,YAAY,CAAC,CAAC,CAAC,CAC5BF,IAAI,CAACH,MAAM,CAACM,cAAc,CAAC,CAAC,CAAC;AAClC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAML,OAAO,CAAC;EAInBM,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,KAAK,GAAGC,SAAS;IACtB,IAAI,CAACC,cAAc,GAAGD,SAAS;EACjC;;EAEA;AACF;AACA;AACA;EACEP,MAAMA,CAAA,EAAqB;IACzB,OAAOL,aAAa,CAAC;MACnBc,MAAM,EAAE,cAAc;MACtB;MACAC,aAAa,EAAE;IACjB,CAAC,CAAC,CAACT,IAAI,CAAC,CAAC,CAACU,OAAO,EAAEC,aAAa,CAAC,KAAK;MACpC,IAAI,CAACN,KAAK,GAAGK,OAAO;MACpB,IAAI,CAACH,cAAc,GAAG,MACpB,IAAIK,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QAC/B;QACA;QACA,MAAMC,IAAI,GAAIC,GAAG,IAAMA,GAAG,GAAGF,MAAM,CAACE,GAAG,CAAC,GAAGH,OAAO,CAAC,CAAE;QACrDF,aAAa,CAACI,IAAI,CAAC;MACrB,CAAC,CAAC;MACJ1B,GAAG,CAAC4B,KAAK,CAAE,gCAA+B,IAAI,CAACC,IAAI,CAAC,CAAE,EAAC,CAAC;MACxD,OAAO,IAAI;IACb,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACEA,IAAIA,CAAA,EAAW;IACb,IAAI,CAAC,IAAI,CAACb,KAAK,EAAE;MACf,MAAM,IAAIc,KAAK,CAAC,kDAAkD,CAAC;IACrE;IACA,OAAO,IAAI,CAACd,KAAK;EACnB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEH,YAAYA,CAAA,EAAa;IACvB,OAAO,MAAOkB,KAAK,IAAK;MACtB,MAAM,IAAI,CAACC,MAAM,CAAC,CAAC;MACnB,MAAMD,KAAK;IACb,CAAC;EACH;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEjB,cAAcA,CAAA,EAAa;IACzB,OAAO,MAAOmB,aAAa,IAAK;MAC9B,MAAM,IAAI,CAACD,MAAM,CAAC,CAAC;MACnB,OAAOC,aAAa;IACtB,CAAC;EACH;;EAEA;AACF;AACA;EACED,MAAMA,CAAA,EAAyB;IAC7B,IAAI,CAAC,IAAI,CAACd,cAAc,EAAE;MACxB;IACF;IACAlB,GAAG,CAAC4B,KAAK,CAAE,iCAAgC,IAAI,CAACC,IAAI,CAAC,CAAE,EAAC,CAAC;IACzD,OAAO,IAAI,CAACX,cAAc,IAAI,IAAI,CAACA,cAAc,CAAC,CAAC;EACrD;AACF"}