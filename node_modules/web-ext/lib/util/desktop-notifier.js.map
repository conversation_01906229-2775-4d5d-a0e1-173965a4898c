{"version": 3, "file": "desktop-notifier.js", "names": ["defaultNotifier", "createLogger", "defaultLog", "import", "meta", "url", "showDesktopNotification", "title", "message", "icon", "notifier", "log", "Promise", "resolve", "reject", "notify", "err", "res", "debug"], "sources": ["../../src/util/desktop-notifier.js"], "sourcesContent": ["/* @flow */\nimport defaultNotifier from 'node-notifier';\n\nimport { createLogger } from './logger.js';\nimport type { Logger } from './logger';\n\nconst defaultLog = createLogger(import.meta.url);\n\nexport type DesktopNotificationsParams = {|\n  title: string,\n  message: string,\n  icon?: string,\n|};\n\nexport type DesktopNotificationsOptions = {\n  notifier?: typeof defaultNotifier,\n  log?: Logger,\n};\n\nexport function showDesktopNotification(\n  { title, message, icon }: DesktopNotificationsParams,\n  {\n    notifier = defaultNotifier,\n    log = defaultLog,\n  }: DesktopNotificationsOptions = {}\n): Promise<void> {\n  return new Promise((resolve, reject) => {\n    notifier.notify({ title, message, icon }, (err, res) => {\n      if (err) {\n        log.debug(\n          `Desktop notifier error: ${err.message},` + ` response: ${res}`\n        );\n        reject(err);\n      } else {\n        resolve();\n      }\n    });\n  });\n}\n"], "mappings": "AACA,OAAOA,eAAe,MAAM,eAAe;AAE3C,SAASC,YAAY,QAAQ,aAAa;AAG1C,MAAMC,UAAU,GAAGD,YAAY,CAACE,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC;AAahD,OAAO,SAASC,uBAAuBA,CACrC;EAAEC,KAAK;EAAEC,OAAO;EAAEC;AAAiC,CAAC,EACpD;EACEC,QAAQ,GAAGV,eAAe;EAC1BW,GAAG,GAAGT;AACqB,CAAC,GAAG,CAAC,CAAC,EACpB;EACf,OAAO,IAAIU,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtCJ,QAAQ,CAACK,MAAM,CAAC;MAAER,KAAK;MAAEC,OAAO;MAAEC;IAAK,CAAC,EAAE,CAACO,GAAG,EAAEC,GAAG,KAAK;MACtD,IAAID,GAAG,EAAE;QACPL,GAAG,CAACO,KAAK,CACN,2BAA0BF,GAAG,CAACR,OAAQ,GAAE,GAAI,cAAaS,GAAI,EAChE,CAAC;QACDH,MAAM,CAACE,GAAG,CAAC;MACb,CAAC,MAAM;QACLH,OAAO,CAAC,CAAC;MACX;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ"}