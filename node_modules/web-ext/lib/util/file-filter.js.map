{"version": 3, "file": "file-filter.js", "names": ["path", "multimatch", "createLogger", "log", "import", "meta", "url", "isSub<PERSON>ath", "src", "target", "relate", "relative", "startsWith", "sep", "FileFilter", "constructor", "baseIgnoredPatterns", "ignoreFiles", "sourceDir", "artifactsDir", "resolve", "filesToIgnore", "addToIgnoreList", "debug", "join", "resolveWithSourceDir", "file", "<PERSON><PERSON><PERSON>", "files", "char<PERSON>t", "resolvedFile", "substr", "push", "wantFile", "filePath", "matches", "length", "createFileFilter", "params"], "sources": ["../../src/util/file-filter.js"], "sourcesContent": ["/* @flow */\nimport path from 'path';\n\nimport multimatch from 'multimatch';\n\nimport { createLogger } from './logger.js';\n\nconst log = createLogger(import.meta.url);\n\n// check if target is a sub directory of src\nexport const isSubPath = (src: string, target: string): boolean => {\n  const relate = path.relative(src, target);\n  // same dir\n  if (!relate) {\n    return false;\n  }\n  if (relate === '..') {\n    return false;\n  }\n  return !relate.startsWith(`..${path.sep}`);\n};\n\n// FileFilter types and implementation.\n\nexport type FileFilterOptions = {\n  baseIgnoredPatterns?: Array<string>,\n  ignoreFiles?: Array<string>,\n  sourceDir: string,\n  artifactsDir?: string,\n};\n\n/*\n * Allows or ignores files.\n */\nexport class FileFilter {\n  filesToIgnore: Array<string>;\n  sourceDir: string;\n\n  constructor({\n    baseIgnoredPatterns = [\n      '**/*.xpi',\n      '**/*.zip',\n      '**/.*', // any hidden file and folder\n      '**/.*/**/*', // and the content inside hidden folder\n      '**/node_modules',\n      '**/node_modules/**/*',\n    ],\n    ignoreFiles = [],\n    sourceDir,\n    artifactsDir,\n  }: FileFilterOptions = {}) {\n    sourceDir = path.resolve(sourceDir);\n\n    this.filesToIgnore = [];\n    this.sourceDir = sourceDir;\n\n    this.addToIgnoreList(baseIgnoredPatterns);\n    if (ignoreFiles) {\n      this.addToIgnoreList(ignoreFiles);\n    }\n    if (artifactsDir && isSubPath(sourceDir, artifactsDir)) {\n      artifactsDir = path.resolve(artifactsDir);\n      log.debug(\n        `Ignoring artifacts directory \"${artifactsDir}\" ` +\n          'and all its subdirectories'\n      );\n      this.addToIgnoreList([artifactsDir, path.join(artifactsDir, '**', '*')]);\n    }\n  }\n\n  /**\n   *  Resolve relative path to absolute path with sourceDir.\n   */\n  resolveWithSourceDir(file: string): string {\n    const resolvedPath = path.resolve(this.sourceDir, file);\n    log.debug(\n      `Resolved path ${file} with sourceDir ${this.sourceDir} ` +\n        `to ${resolvedPath}`\n    );\n    return resolvedPath;\n  }\n\n  /**\n   *  Insert more files into filesToIgnore array.\n   */\n  addToIgnoreList(files: Array<string>) {\n    for (const file of files) {\n      if (file.charAt(0) === '!') {\n        const resolvedFile = this.resolveWithSourceDir(file.substr(1));\n        this.filesToIgnore.push(`!${resolvedFile}`);\n      } else {\n        this.filesToIgnore.push(this.resolveWithSourceDir(file));\n      }\n    }\n  }\n\n  /*\n   * Returns true if the file is wanted.\n   *\n   * If filePath does not start with a slash, it will be treated as a path\n   * relative to sourceDir when matching it against all configured\n   * ignore-patterns.\n   *\n   * Example: this is called by zipdir as wantFile(filePath) for each\n   * file in the folder that is being archived.\n   */\n  wantFile(filePath: string): boolean {\n    const resolvedPath = this.resolveWithSourceDir(filePath);\n    const matches = multimatch(resolvedPath, this.filesToIgnore);\n    if (matches.length > 0) {\n      log.debug(`FileFilter: ignoring file ${resolvedPath}`);\n      return false;\n    }\n    return true;\n  }\n}\n\n// a helper function to make mocking easier\n\nexport const createFileFilter = (params: FileFilterOptions): FileFilter =>\n  new FileFilter(params);\n\nexport type FileFilterCreatorFn = typeof createFileFilter;\n"], "mappings": "AACA,OAAOA,IAAI,MAAM,MAAM;AAEvB,OAAOC,UAAU,MAAM,YAAY;AAEnC,SAASC,YAAY,QAAQ,aAAa;AAE1C,MAAMC,GAAG,GAAGD,YAAY,CAACE,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC;;AAEzC;AACA,OAAO,MAAMC,SAAS,GAAGA,CAACC,GAAW,EAAEC,MAAc,KAAc;EACjE,MAAMC,MAAM,GAAGV,IAAI,CAACW,QAAQ,CAACH,GAAG,EAAEC,MAAM,CAAC;EACzC;EACA,IAAI,CAACC,MAAM,EAAE;IACX,OAAO,KAAK;EACd;EACA,IAAIA,MAAM,KAAK,IAAI,EAAE;IACnB,OAAO,KAAK;EACd;EACA,OAAO,CAACA,MAAM,CAACE,UAAU,CAAE,KAAIZ,IAAI,CAACa,GAAI,EAAC,CAAC;AAC5C,CAAC;;AAED;;AASA;AACA;AACA;AACA,OAAO,MAAMC,UAAU,CAAC;EAItBC,WAAWA,CAAC;IACVC,mBAAmB,GAAG,CACpB,UAAU,EACV,UAAU,EACV,OAAO;IAAE;IACT,YAAY;IAAE;IACd,iBAAiB,EACjB,sBAAsB,CACvB;IACDC,WAAW,GAAG,EAAE;IAChBC,SAAS;IACTC;EACiB,CAAC,GAAG,CAAC,CAAC,EAAE;IACzBD,SAAS,GAAGlB,IAAI,CAACoB,OAAO,CAACF,SAAS,CAAC;IAEnC,IAAI,CAACG,aAAa,GAAG,EAAE;IACvB,IAAI,CAACH,SAAS,GAAGA,SAAS;IAE1B,IAAI,CAACI,eAAe,CAACN,mBAAmB,CAAC;IACzC,IAAIC,WAAW,EAAE;MACf,IAAI,CAACK,eAAe,CAACL,WAAW,CAAC;IACnC;IACA,IAAIE,YAAY,IAAIZ,SAAS,CAACW,SAAS,EAAEC,YAAY,CAAC,EAAE;MACtDA,YAAY,GAAGnB,IAAI,CAACoB,OAAO,CAACD,YAAY,CAAC;MACzChB,GAAG,CAACoB,KAAK,CACN,iCAAgCJ,YAAa,IAAG,GAC/C,4BACJ,CAAC;MACD,IAAI,CAACG,eAAe,CAAC,CAACH,YAAY,EAAEnB,IAAI,CAACwB,IAAI,CAACL,YAAY,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;IAC1E;EACF;;EAEA;AACF;AACA;EACEM,oBAAoBA,CAACC,IAAY,EAAU;IACzC,MAAMC,YAAY,GAAG3B,IAAI,CAACoB,OAAO,CAAC,IAAI,CAACF,SAAS,EAAEQ,IAAI,CAAC;IACvDvB,GAAG,CAACoB,KAAK,CACN,iBAAgBG,IAAK,mBAAkB,IAAI,CAACR,SAAU,GAAE,GACtD,MAAKS,YAAa,EACvB,CAAC;IACD,OAAOA,YAAY;EACrB;;EAEA;AACF;AACA;EACEL,eAAeA,CAACM,KAAoB,EAAE;IACpC,KAAK,MAAMF,IAAI,IAAIE,KAAK,EAAE;MACxB,IAAIF,IAAI,CAACG,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAC1B,MAAMC,YAAY,GAAG,IAAI,CAACL,oBAAoB,CAACC,IAAI,CAACK,MAAM,CAAC,CAAC,CAAC,CAAC;QAC9D,IAAI,CAACV,aAAa,CAACW,IAAI,CAAE,IAAGF,YAAa,EAAC,CAAC;MAC7C,CAAC,MAAM;QACL,IAAI,CAACT,aAAa,CAACW,IAAI,CAAC,IAAI,CAACP,oBAAoB,CAACC,IAAI,CAAC,CAAC;MAC1D;IACF;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEO,QAAQA,CAACC,QAAgB,EAAW;IAClC,MAAMP,YAAY,GAAG,IAAI,CAACF,oBAAoB,CAACS,QAAQ,CAAC;IACxD,MAAMC,OAAO,GAAGlC,UAAU,CAAC0B,YAAY,EAAE,IAAI,CAACN,aAAa,CAAC;IAC5D,IAAIc,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;MACtBjC,GAAG,CAACoB,KAAK,CAAE,6BAA4BI,YAAa,EAAC,CAAC;MACtD,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb;AACF;;AAEA;;AAEA,OAAO,MAAMU,gBAAgB,GAAIC,MAAyB,IACxD,IAAIxB,UAAU,CAACwB,MAAM,CAAC"}