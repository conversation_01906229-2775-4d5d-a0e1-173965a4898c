{"version": 3, "file": "promisify.js", "names": ["promisify", "promisifyCustom", "custom", "multiArgsPromisedFn", "fn", "callerArgs", "Promise", "resolve", "reject", "err", "rest"], "sources": ["../../src/util/promisify.js"], "sourcesContent": ["/* @flow */\n\nimport { promisify } from 'util';\n\n// promisify.custom is missing from the node types know to flow,\n// and it triggers flow-check errors if used directly.\n// By using the value exported here, flow-check passes successfully\n// using a single FLOW_IGNORE suppress comment.\n\n// $FlowIgnore: promisify.custom is missing in flow type signatures.\nexport const promisifyCustom = promisify.custom;\n\n/*\n * A small promisify helper to make it easier to customize a\n * function promisified (using the 'util' module available in\n * nodejs >= 8) to resolve to an array of results:\n *\n *    import {promisify} from 'util';\n *    import {multiArgsPromisedFn} from '../util/promisify';\n *\n *    aCallbackBasedFn[promisify.custom] = multiArgsPromisedFn(tmp.dir);\n *    ...\n */\nexport function multiArgsPromisedFn(fn: Function): Function {\n  return (...callerArgs: Array<any>): Promise<any> => {\n    return new Promise((resolve, reject) => {\n      fn(...callerArgs, (err, ...rest) => {\n        if (err) {\n          reject(err);\n        } else {\n          resolve(rest);\n        }\n      });\n    });\n  };\n}\n"], "mappings": "AAEA,SAASA,SAAS,QAAQ,MAAM;;AAEhC;AACA;AACA;AACA;;AAEA;AACA,OAAO,MAAMC,eAAe,GAAGD,SAAS,CAACE,MAAM;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,mBAAmBA,CAACC,EAAY,EAAY;EAC1D,OAAO,CAAC,GAAGC,UAAsB,KAAmB;IAClD,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtCJ,EAAE,CAAC,GAAGC,UAAU,EAAE,CAACI,GAAG,EAAE,GAAGC,IAAI,KAAK;QAClC,IAAID,GAAG,EAAE;UACPD,MAAM,CAACC,GAAG,CAAC;QACb,CAAC,MAAM;UACLF,OAAO,CAACG,IAAI,CAAC;QACf;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;AACH"}