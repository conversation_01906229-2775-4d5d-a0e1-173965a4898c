{"version": 3, "file": "stdin.js", "names": ["isTTY", "stream", "setRawMode", "rawMode"], "sources": ["../../src/util/stdin.js"], "sourcesContent": ["/* @flow */\n\nimport type { Readable } from 'stream';\n\nexport function isTTY(stream: Readable): boolean {\n  // $FlowFixMe: flow complains that stream may not provide isTTY as a property.\n  return stream.isTTY;\n}\n\nexport function setRawMode(stream: Readable, rawMode: boolean) {\n  // $FlowFixMe: flow complains that stdin may not provide setRawMode.\n  stream.setRawMode(rawMode);\n}\n"], "mappings": "AAIA,OAAO,SAASA,KAAKA,CAACC,MAAgB,EAAW;EAC/C;EACA,OAAOA,MAAM,CAACD,KAAK;AACrB;AAEA,OAAO,SAASE,UAAUA,CAACD,MAAgB,EAAEE,OAAgB,EAAE;EAC7D;EACAF,MAAM,CAACC,UAAU,CAACC,OAAO,CAAC;AAC5B"}