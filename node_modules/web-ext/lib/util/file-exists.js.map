{"version": 3, "file": "file-exists.js", "names": ["fs", "isErrorWithCode", "fileExists", "path", "fileIsReadable", "f", "access", "constants", "<PERSON>_<PERSON>", "stat", "isFile", "error"], "sources": ["../../src/util/file-exists.js"], "sourcesContent": ["/* @flow */\nimport { fs } from 'mz';\n\nimport { isErrorWithCode } from '../errors.js';\n\ntype FileExistsOptions = {\n  fileIsReadable: (filePath: string) => Promise<boolean>,\n};\n\n/*\n * Resolves true if the path is a readable file.\n *\n * Usage:\n *\n * const exists = await fileExists(filePath);\n * if (exists) {\n *   // ...\n * }\n *\n * */\nexport default async function fileExists(\n  path: string,\n  {\n    fileIsReadable = (f) => fs.access(f, fs.constants.R_OK),\n  }: FileExistsOptions = {}\n): Promise<boolean> {\n  try {\n    await fileIsReadable(path);\n    const stat = await fs.stat(path);\n    return stat.isFile();\n  } catch (error) {\n    if (isErrorWithCode(['EACCES', 'ENOENT'], error)) {\n      return false;\n    }\n    throw error;\n  }\n}\n"], "mappings": "AACA,SAASA,EAAE,QAAQ,IAAI;AAEvB,SAASC,eAAe,QAAQ,cAAc;AAM9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,eAAeC,UAAUA,CACtCC,IAAY,EACZ;EACEC,cAAc,GAAIC,CAAC,IAAKL,EAAE,CAACM,MAAM,CAACD,CAAC,EAAEL,EAAE,CAACO,SAAS,CAACC,IAAI;AACrC,CAAC,GAAG,CAAC,CAAC,EACP;EAClB,IAAI;IACF,MAAMJ,cAAc,CAACD,IAAI,CAAC;IAC1B,MAAMM,IAAI,GAAG,MAAMT,EAAE,CAACS,IAAI,CAACN,IAAI,CAAC;IAChC,OAAOM,IAAI,CAACC,MAAM,CAAC,CAAC;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,IAAIV,eAAe,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAEU,KAAK,CAAC,EAAE;MAChD,OAAO,KAAK;IACd;IACA,MAAMA,KAAK;EACb;AACF"}