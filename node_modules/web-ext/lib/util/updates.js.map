{"version": 3, "file": "updates.js", "names": ["defaultUpdateNotifier", "checkForUpdates", "version", "updateNotifier", "pkg", "name", "updateCheckInterval", "notify"], "sources": ["../../src/util/updates.js"], "sourcesContent": ["/* @flow */\nimport defaultUpdateNotifier from 'update-notifier';\n\ntype CheckForUpdatesParams = {|\n  version: string,\n  updateNotifier?: typeof defaultUpdateNotifier,\n|};\n\nexport function checkForUpdates({\n  version,\n  updateNotifier = defaultUpdateNotifier,\n}: CheckForUpdatesParams) {\n  const pkg = { name: 'web-ext', version };\n\n  updateNotifier({\n    pkg,\n    updateCheckInterval: 1000 * 60 * 60 * 24 * 3, // 3 days,\n  }).notify();\n}\n"], "mappings": "AACA,OAAOA,qBAAqB,MAAM,iBAAiB;AAOnD,OAAO,SAASC,eAAeA,CAAC;EAC9BC,OAAO;EACPC,cAAc,GAAGH;AACI,CAAC,EAAE;EACxB,MAAMI,GAAG,GAAG;IAAEC,IAAI,EAAE,SAAS;IAAEH;EAAQ,CAAC;EAExCC,cAAc,CAAC;IACbC,GAAG;IACHE,mBAAmB,EAAE,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAE;EAChD,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;AACb"}