{"version": 3, "file": "manifest.js", "names": ["path", "fs", "parseJSON", "stripBom", "stripJsonComments", "InvalidManifest", "createLogger", "log", "import", "meta", "url", "getValidatedManifest", "sourceDir", "manifestFile", "join", "debug", "manifestContents", "readFile", "encoding", "error", "manifestData", "errors", "name", "push", "version", "applications", "gecko", "length", "getManifestId", "manifestApps", "browser_specific_settings", "apps", "id", "undefined"], "sources": ["../../src/util/manifest.js"], "sourcesContent": ["/* @flow */\nimport path from 'path';\n\nimport { fs } from 'mz';\nimport parseJSON from 'parse-json';\nimport stripBom from 'strip-bom';\nimport stripJsonComments from 'strip-json-comments';\n\nimport { InvalidManifest } from '../errors.js';\nimport { createLogger } from './logger.js';\n\nconst log = createLogger(import.meta.url);\n\n// getValidatedManifest helper types and implementation\n\nexport type ExtensionManifestApplications = {|\n  gecko?: {|\n    id?: string,\n    strict_min_version?: string,\n    strict_max_version?: string,\n    update_url?: string,\n  |},\n|};\n\nexport type ExtensionManifest = {|\n  name: string,\n  version: string,\n  default_locale?: string,\n  applications?: ExtensionManifestApplications,\n  browser_specific_settings?: ExtensionManifestApplications,\n  permissions?: Array<string>,\n|};\n\nexport default async function getValidatedManifest(\n  sourceDir: string\n): Promise<ExtensionManifest> {\n  const manifestFile = path.join(sourceDir, 'manifest.json');\n  log.debug(`Validating manifest at ${manifestFile}`);\n\n  let manifestContents;\n\n  try {\n    manifestContents = await fs.readFile(manifestFile, { encoding: 'utf-8' });\n  } catch (error) {\n    throw new InvalidManifest(\n      `Could not read manifest.json file at ${manifestFile}: ${error}`\n    );\n  }\n\n  manifestContents = stripBom(manifestContents);\n\n  let manifestData;\n\n  try {\n    manifestData = parseJSON(stripJsonComments(manifestContents));\n  } catch (error) {\n    throw new InvalidManifest(\n      `Error parsing manifest.json file at ${manifestFile}: ${error}`\n    );\n  }\n\n  const errors = [];\n  // This is just some basic validation of what web-ext needs, not\n  // what Firefox will need to run the extension.\n  // TODO: integrate with the addons-linter for actual validation.\n  if (!manifestData.name) {\n    errors.push('missing \"name\" property');\n  }\n  if (!manifestData.version) {\n    errors.push('missing \"version\" property');\n  }\n\n  if (manifestData.applications && !manifestData.applications.gecko) {\n    // Since the applications property only applies to gecko, make\n    // sure 'gecko' exists when 'applications' is defined. This should\n    // make introspection of gecko properties easier.\n    errors.push('missing \"applications.gecko\" property');\n  }\n\n  if (errors.length) {\n    throw new InvalidManifest(\n      `Manifest at ${manifestFile} is invalid: ${errors.join('; ')}`\n    );\n  }\n\n  return manifestData;\n}\n\nexport function getManifestId(manifestData: ExtensionManifest): string | void {\n  const manifestApps = [\n    manifestData.browser_specific_settings,\n    manifestData.applications,\n  ];\n  for (const apps of manifestApps) {\n    // If both bss and applicants contains a defined gecko property,\n    // we prefer bss even if the id property isn't available.\n    // This match what Firefox does in this particular scenario, see\n    // https://searchfox.org/mozilla-central/rev/828f2319c0195d7f561ed35533aef6fe183e68e3/toolkit/mozapps/extensions/internal/XPIInstall.jsm#470-474,488\n    if (apps?.gecko) {\n      return apps.gecko.id;\n    }\n  }\n\n  return undefined;\n}\n"], "mappings": "AACA,OAAOA,IAAI,MAAM,MAAM;AAEvB,SAASC,EAAE,QAAQ,IAAI;AACvB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,QAAQ,MAAM,WAAW;AAChC,OAAOC,iBAAiB,MAAM,qBAAqB;AAEnD,SAASC,eAAe,QAAQ,cAAc;AAC9C,SAASC,YAAY,QAAQ,aAAa;AAE1C,MAAMC,GAAG,GAAGD,YAAY,CAACE,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC;;AAEzC;;AAoBA,eAAe,eAAeC,oBAAoBA,CAChDC,SAAiB,EACW;EAC5B,MAAMC,YAAY,GAAGb,IAAI,CAACc,IAAI,CAACF,SAAS,EAAE,eAAe,CAAC;EAC1DL,GAAG,CAACQ,KAAK,CAAE,0BAAyBF,YAAa,EAAC,CAAC;EAEnD,IAAIG,gBAAgB;EAEpB,IAAI;IACFA,gBAAgB,GAAG,MAAMf,EAAE,CAACgB,QAAQ,CAACJ,YAAY,EAAE;MAAEK,QAAQ,EAAE;IAAQ,CAAC,CAAC;EAC3E,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,MAAM,IAAId,eAAe,CACtB,wCAAuCQ,YAAa,KAAIM,KAAM,EACjE,CAAC;EACH;EAEAH,gBAAgB,GAAGb,QAAQ,CAACa,gBAAgB,CAAC;EAE7C,IAAII,YAAY;EAEhB,IAAI;IACFA,YAAY,GAAGlB,SAAS,CAACE,iBAAiB,CAACY,gBAAgB,CAAC,CAAC;EAC/D,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,MAAM,IAAId,eAAe,CACtB,uCAAsCQ,YAAa,KAAIM,KAAM,EAChE,CAAC;EACH;EAEA,MAAME,MAAM,GAAG,EAAE;EACjB;EACA;EACA;EACA,IAAI,CAACD,YAAY,CAACE,IAAI,EAAE;IACtBD,MAAM,CAACE,IAAI,CAAC,yBAAyB,CAAC;EACxC;EACA,IAAI,CAACH,YAAY,CAACI,OAAO,EAAE;IACzBH,MAAM,CAACE,IAAI,CAAC,4BAA4B,CAAC;EAC3C;EAEA,IAAIH,YAAY,CAACK,YAAY,IAAI,CAACL,YAAY,CAACK,YAAY,CAACC,KAAK,EAAE;IACjE;IACA;IACA;IACAL,MAAM,CAACE,IAAI,CAAC,uCAAuC,CAAC;EACtD;EAEA,IAAIF,MAAM,CAACM,MAAM,EAAE;IACjB,MAAM,IAAItB,eAAe,CACtB,eAAcQ,YAAa,gBAAeQ,MAAM,CAACP,IAAI,CAAC,IAAI,CAAE,EAC/D,CAAC;EACH;EAEA,OAAOM,YAAY;AACrB;AAEA,OAAO,SAASQ,aAAaA,CAACR,YAA+B,EAAiB;EAC5E,MAAMS,YAAY,GAAG,CACnBT,YAAY,CAACU,yBAAyB,EACtCV,YAAY,CAACK,YAAY,CAC1B;EACD,KAAK,MAAMM,IAAI,IAAIF,YAAY,EAAE;IAC/B;IACA;IACA;IACA;IACA,IAAIE,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEL,KAAK,EAAE;MACf,OAAOK,IAAI,CAACL,KAAK,CAACM,EAAE;IACtB;EACF;EAEA,OAAOC,SAAS;AAClB"}