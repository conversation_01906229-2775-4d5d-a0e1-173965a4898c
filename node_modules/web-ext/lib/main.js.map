{"version": 3, "file": "main.js", "names": ["main", "cmd"], "sources": ["../src/main.js"], "sourcesContent": ["/* @flow */\nimport { main } from './program.js';\nimport cmd from './cmd/index.js';\n\n// This only exposes main and cmd, while util/logger and util/adb are defined as\n// separate additional exports in the package.json.\nexport default { main, cmd };\n"], "mappings": "AACA,SAASA,IAAI,QAAQ,cAAc;AACnC,OAAOC,GAAG,MAAM,gBAAgB;;AAEhC;AACA;AACA,eAAe;EAAED,IAAI;EAAEC;AAAI,CAAC"}