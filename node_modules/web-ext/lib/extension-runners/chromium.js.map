{"version": 3, "file": "chromium.js", "names": ["path", "fs", "asyncMkdirp", "Launcher", "ChromeLauncher", "launch", "defaultChromiumLaunch", "WebSocket", "WebSocketServer", "createLogger", "TempDir", "isDirectory", "fileExists", "log", "import", "meta", "url", "EXCLUDED_CHROME_FLAGS", "DEFAULT_CHROME_FLAGS", "defaultFlags", "filter", "flag", "includes", "ChromiumExtensionRunner", "constructor", "params", "chromiumLaunch", "cleanupCallbacks", "Set", "getName", "run", "_promiseSetupDone", "setupInstance", "isUserDataDir", "<PERSON><PERSON><PERSON>", "localStatePath", "join", "defaultPath", "isProfileDir", "securePreferencesPath", "getProfilePaths", "chromiumProfile", "userDataDir", "profileDirName", "isProfileDirAndNotUserData", "dir", "base", "parse", "wss", "Promise", "resolve", "server", "port", "host", "clientTracking", "on", "socket", "err", "debug", "reloadManagerExtension", "createReloadManagerExtension", "extensions", "concat", "map", "sourceDir", "chromiumBinary", "chromeFlags", "push", "args", "keepProfileChanges", "Error", "tmpDir", "create", "tmpDir<PERSON>ath", "copy", "startingUrl", "startUrl", "startingUrls", "Array", "isArray", "shift", "chromiumInstance", "enableExtensions", "chromePath", "ignoreDefaultFlags", "process", "once", "exiting", "info", "exit", "wssBroadcast", "data", "clients", "cleanWebExtReloadComplete", "client", "removeEventListener", "webExtReloadComplete", "delete", "message", "msg", "JSON", "type", "call", "readyState", "OPEN", "addEventListener", "send", "stringify", "size", "registerCleanup", "remove", "extPath", "Date", "now", "writeFile", "manifest_version", "name", "version", "permissions", "background", "scripts", "wssInfo", "address", "bgPage", "reloadAllExtensions", "<PERSON><PERSON><PERSON>", "stdout", "write", "toTimeString", "reloadExtensionBySourceDir", "extensionSourceDir", "fn", "add", "catch", "kill", "wssClient", "_this$wss", "terminate", "close", "error"], "sources": ["../../src/extension-runners/chromium.js"], "sourcesContent": ["/* @flow */\n\n/**\n * This module provide an ExtensionRunner subclass that manage an extension executed\n * in a Chromium-based browser instance.\n */\n\nimport path from 'path';\n\nimport fs from 'fs-extra';\nimport asyncMkdirp from 'mkdirp';\nimport {\n  Launcher as ChromeLauncher,\n  launch as defaultChromiumLaunch,\n} from 'chrome-launcher';\nimport WebSocket, { WebSocketServer } from 'ws';\n\nimport { createLogger } from '../util/logger.js';\nimport { TempDir } from '../util/temp-dir.js';\nimport type {\n  ExtensionRunnerParams,\n  ExtensionRunnerReloadResult,\n} from './base';\nimport isDirectory from '../util/is-directory.js';\nimport fileExists from '../util/file-exists.js';\n\ntype ChromiumSpecificRunnerParams = {|\n  chromiumBinary?: string,\n  chromiumProfile?: string,\n  chromiumLaunch?: typeof defaultChromiumLaunch,\n|};\n\nexport type ChromiumExtensionRunnerParams = {|\n  ...ExtensionRunnerParams,\n  // Chromium desktop CLI params.\n  ...ChromiumSpecificRunnerParams,\n|};\n\nconst log = createLogger(import.meta.url);\n\nconst EXCLUDED_CHROME_FLAGS = ['--disable-extensions', '--mute-audio'];\n\nexport const DEFAULT_CHROME_FLAGS: Array<string> =\n  ChromeLauncher.defaultFlags().filter(\n    (flag) => !EXCLUDED_CHROME_FLAGS.includes(flag)\n  );\n\n/**\n * Implements an IExtensionRunner which manages a Chromium instance.\n */\nexport class ChromiumExtensionRunner {\n  cleanupCallbacks: Set<Function>;\n  params: ChromiumExtensionRunnerParams;\n  chromiumInstance: ?ChromeLauncher;\n  chromiumLaunch: typeof defaultChromiumLaunch;\n  reloadManagerExtension: string;\n  wss: ?WebSocketServer;\n  exiting: boolean;\n  _promiseSetupDone: ?Promise<void>;\n\n  constructor(params: ChromiumExtensionRunnerParams) {\n    const { chromiumLaunch = defaultChromiumLaunch } = params;\n    this.params = params;\n    this.chromiumLaunch = chromiumLaunch;\n    this.cleanupCallbacks = new Set();\n  }\n\n  // Method exported from the IExtensionRunner interface.\n\n  /**\n   * Returns the runner name.\n   */\n  getName(): string {\n    return 'Chromium';\n  }\n\n  async run(): Promise<void> {\n    // Run should never be called more than once.\n    this._promiseSetupDone = this.setupInstance();\n    await this._promiseSetupDone;\n  }\n\n  static async isUserDataDir(dirPath: string): Promise<boolean> {\n    const localStatePath = path.join(dirPath, 'Local State');\n    const defaultPath = path.join(dirPath, 'Default');\n    // Local State and Default are typical for the user-data-dir\n    return (\n      (await fileExists(localStatePath)) && (await isDirectory(defaultPath))\n    );\n  }\n\n  static async isProfileDir(dirPath: string): Promise<boolean> {\n    const securePreferencesPath = path.join(dirPath, 'Secure Preferences');\n    //Secure Preferences is typical for a profile dir inside a user data dir\n    return await fileExists(securePreferencesPath);\n  }\n\n  static async getProfilePaths(chromiumProfile: ?string): Promise<{\n    userDataDir: ?string,\n    profileDirName: ?string,\n  }> {\n    if (!chromiumProfile) {\n      return {\n        userDataDir: null,\n        profileDirName: null,\n      };\n    }\n\n    const isProfileDirAndNotUserData =\n      (await ChromiumExtensionRunner.isProfileDir(chromiumProfile)) &&\n      !(await ChromiumExtensionRunner.isUserDataDir(chromiumProfile));\n\n    if (isProfileDirAndNotUserData) {\n      const { dir: userDataDir, base: profileDirName } =\n        path.parse(chromiumProfile);\n      return {\n        userDataDir,\n        profileDirName,\n      };\n    }\n\n    return {\n      userDataDir: chromiumProfile,\n      profileDirName: null,\n    };\n  }\n\n  /**\n   * Setup the Chromium Profile and run a Chromium instance.\n   */\n  async setupInstance(): Promise<void> {\n    // Start a websocket server on a free localhost TCP port.\n    this.wss = await new Promise((resolve) => {\n      const server = new WebSocketServer(\n        // Use a ipv4 host so we don't need to escape ipv6 address\n        // https://github.com/mozilla/web-ext/issues/2331\n        { port: 0, host: '127.0.0.1', clientTracking: true },\n        // Wait the server to be listening (so that the extension\n        // runner can successfully retrieve server address and port).\n        () => resolve(server)\n      );\n    });\n\n    // Prevent unhandled socket error (e.g. when chrome\n    // is exiting, See https://github.com/websockets/ws/issues/1256).\n    this.wss.on('connection', function (socket) {\n      socket.on('error', (err) => {\n        log.debug(`websocket connection error: ${err}`);\n      });\n    });\n\n    // Create the extension that will manage the addon reloads\n    this.reloadManagerExtension = await this.createReloadManagerExtension();\n\n    // Start chrome pointing it to a given profile dir\n    const extensions = [this.reloadManagerExtension]\n      .concat(this.params.extensions.map(({ sourceDir }) => sourceDir))\n      .join(',');\n\n    const { chromiumBinary } = this.params;\n\n    log.debug('Starting Chromium instance...');\n\n    if (chromiumBinary) {\n      log.debug(`(chromiumBinary: ${chromiumBinary})`);\n    }\n\n    const chromeFlags = [...DEFAULT_CHROME_FLAGS];\n\n    chromeFlags.push(`--load-extension=${extensions}`);\n\n    if (this.params.args) {\n      chromeFlags.push(...this.params.args);\n    }\n\n    // eslint-disable-next-line prefer-const\n    let { userDataDir, profileDirName } =\n      await ChromiumExtensionRunner.getProfilePaths(\n        this.params.chromiumProfile\n      );\n\n    if (userDataDir && this.params.keepProfileChanges) {\n      if (\n        profileDirName &&\n        !(await ChromiumExtensionRunner.isUserDataDir(userDataDir))\n      ) {\n        throw new Error(\n          'The profile you provided is not in a ' +\n            'user-data-dir. The changes cannot be kept. Please either ' +\n            'remove --keep-profile-changes or use a profile in a ' +\n            'user-data-dir directory'\n        );\n      }\n    } else if (!this.params.keepProfileChanges) {\n      // the user provided an existing profile directory but doesn't want\n      // the changes to be kept. we copy this directory to a temporary\n      // user data dir.\n      const tmpDir = new TempDir();\n      await tmpDir.create();\n      const tmpDirPath = tmpDir.path();\n\n      if (userDataDir && profileDirName) {\n        // copy profile dir to this temp user data dir.\n        await fs.copy(\n          path.join(userDataDir, profileDirName),\n          path.join(tmpDirPath, profileDirName)\n        );\n      } else if (userDataDir) {\n        await fs.copy(userDataDir, tmpDirPath);\n      }\n      userDataDir = tmpDirPath;\n    }\n\n    if (profileDirName) {\n      chromeFlags.push(`--profile-directory=${profileDirName}`);\n    }\n\n    let startingUrl;\n    if (this.params.startUrl) {\n      const startingUrls = Array.isArray(this.params.startUrl)\n        ? this.params.startUrl\n        : [this.params.startUrl];\n      startingUrl = startingUrls.shift();\n      chromeFlags.push(...startingUrls);\n    }\n\n    this.chromiumInstance = await this.chromiumLaunch({\n      enableExtensions: true,\n      chromePath: chromiumBinary,\n      chromeFlags,\n      startingUrl,\n      userDataDir,\n      // Ignore default flags to keep the extension enabled.\n      ignoreDefaultFlags: true,\n    });\n\n    this.chromiumInstance.process.once('close', () => {\n      this.chromiumInstance = null;\n\n      if (!this.exiting) {\n        log.info('Exiting on Chromium instance disconnected.');\n        this.exit();\n      }\n    });\n  }\n\n  async wssBroadcast(data: Object): Promise<void> {\n    return new Promise((resolve) => {\n      const clients = this.wss ? new Set(this.wss.clients) : new Set();\n\n      function cleanWebExtReloadComplete() {\n        const client = this;\n        client.removeEventListener('message', webExtReloadComplete);\n        client.removeEventListener('close', cleanWebExtReloadComplete);\n        clients.delete(client);\n      }\n\n      const webExtReloadComplete = async (message) => {\n        const msg = JSON.parse(message.data);\n\n        if (msg.type === 'webExtReloadExtensionComplete') {\n          for (const client of clients) {\n            cleanWebExtReloadComplete.call(client);\n          }\n          resolve();\n        }\n      };\n\n      for (const client of clients) {\n        if (client.readyState === WebSocket.OPEN) {\n          client.addEventListener('message', webExtReloadComplete);\n          client.addEventListener('close', cleanWebExtReloadComplete);\n\n          client.send(JSON.stringify(data));\n        } else {\n          clients.delete(client);\n        }\n      }\n\n      if (clients.size === 0) {\n        resolve();\n      }\n    });\n  }\n\n  async createReloadManagerExtension(): Promise<string> {\n    const tmpDir = new TempDir();\n    await tmpDir.create();\n    this.registerCleanup(() => tmpDir.remove());\n\n    const extPath = path.join(\n      tmpDir.path(),\n      `reload-manager-extension-${Date.now()}`\n    );\n\n    log.debug(`Creating reload-manager-extension in ${extPath}`);\n\n    await asyncMkdirp(extPath);\n\n    await fs.writeFile(\n      path.join(extPath, 'manifest.json'),\n      JSON.stringify({\n        manifest_version: 2,\n        name: 'web-ext Reload Manager Extension',\n        version: '1.0',\n        permissions: ['management', 'tabs'],\n        background: {\n          scripts: ['bg.js'],\n        },\n      })\n    );\n\n    // $FlowIgnore: this method is only called right after creating the server and so wss should be defined.\n    const wssInfo = this.wss.address();\n\n    const bgPage = `(function bgPage() {\n      async function getAllDevExtensions() {\n        const allExtensions = await new Promise(\n          r => chrome.management.getAll(r));\n\n        return allExtensions.filter((extension) => {\n          return extension.enabled &&\n            extension.installType === \"development\" &&\n            extension.id !== chrome.runtime.id;\n        });\n      }\n\n      const setEnabled = (extensionId, value) =>\n        chrome.runtime.id == extensionId ?\n        new Promise.resolve() :\n        new Promise(r => chrome.management.setEnabled(extensionId, value, r));\n\n      async function reloadExtension(extensionId) {\n        await setEnabled(extensionId, false);\n        await setEnabled(extensionId, true);\n      }\n\n      const ws = new window.WebSocket(\n        \"ws://${wssInfo.address}:${wssInfo.port}\");\n\n      ws.onmessage = async (evt) => {\n        const msg = JSON.parse(evt.data);\n        if (msg.type === 'webExtReloadAllExtensions') {\n          const devExtensions = await getAllDevExtensions();\n          await Promise.all(devExtensions.map(ext => reloadExtension(ext.id)));\n          ws.send(JSON.stringify({ type: 'webExtReloadExtensionComplete' }));\n        }\n      };\n    })()`;\n\n    await fs.writeFile(path.join(extPath, 'bg.js'), bgPage);\n    return extPath;\n  }\n\n  /**\n   * Reloads all the extensions, collect any reload error and resolves to\n   * an array composed by a single ExtensionRunnerReloadResult object.\n   */\n  async reloadAllExtensions(): Promise<Array<ExtensionRunnerReloadResult>> {\n    const runnerName = this.getName();\n\n    await this.wssBroadcast({\n      type: 'webExtReloadAllExtensions',\n    });\n\n    process.stdout.write(\n      `\\rLast extension reload: ${new Date().toTimeString()}`\n    );\n    log.debug('\\n');\n\n    return [{ runnerName }];\n  }\n\n  /**\n   * Reloads a single extension, collect any reload error and resolves to\n   * an array composed by a single ExtensionRunnerReloadResult object.\n   */\n  async reloadExtensionBySourceDir(\n    extensionSourceDir: string // eslint-disable-line no-unused-vars\n  ): Promise<Array<ExtensionRunnerReloadResult>> {\n    // TODO(rpl): detect the extension ids assigned to the\n    // target extensions and map it to the extensions source dir\n    // (https://github.com/mozilla/web-ext/issues/1687).\n    return this.reloadAllExtensions();\n  }\n\n  /**\n   * Register a callback to be called when the runner has been exited\n   * (e.g. the Chromium instance exits or the user has requested web-ext\n   * to exit).\n   */\n  registerCleanup(fn: Function): void {\n    this.cleanupCallbacks.add(fn);\n  }\n\n  /**\n   * Exits the runner, by closing the managed Chromium instance.\n   */\n  async exit(): Promise<void> {\n    this.exiting = true;\n\n    // Wait for the setup to complete if the extension runner is already\n    // being started.\n    if (this._promiseSetupDone) {\n      // Ignore initialization errors if any.\n      await this._promiseSetupDone.catch((err) => {\n        log.debug(`ignored setup error on chromium runner shutdown: ${err}`);\n      });\n    }\n\n    if (this.chromiumInstance) {\n      await this.chromiumInstance.kill();\n      this.chromiumInstance = null;\n    }\n\n    if (this.wss) {\n      // Close all websocket clients, closing the WebSocketServer\n      // does not terminate the existing connection and it wouldn't\n      // resolve until all of the existing connections are closed.\n      for (const wssClient of this.wss?.clients || []) {\n        if (wssClient.readyState === WebSocket.OPEN) {\n          wssClient.terminate();\n        }\n      }\n      await new Promise((resolve) =>\n        this.wss ? this.wss.close(resolve) : resolve()\n      );\n      this.wss = null;\n    }\n\n    // Call all the registered cleanup callbacks.\n    for (const fn of this.cleanupCallbacks) {\n      try {\n        fn();\n      } catch (error) {\n        log.error(error);\n      }\n    }\n  }\n}\n"], "mappings": "AAEA;AACA;AACA;AACA;AAEA,OAAOA,IAAI,MAAM,MAAM;AAEvB,OAAOC,EAAE,MAAM,UAAU;AACzB,OAAOC,WAAW,MAAM,QAAQ;AAChC,SACEC,QAAQ,IAAIC,cAAc,EAC1BC,MAAM,IAAIC,qBAAqB,QAC1B,iBAAiB;AACxB,OAAOC,SAAS,IAAIC,eAAe,QAAQ,IAAI;AAE/C,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,OAAO,QAAQ,qBAAqB;AAK7C,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,UAAU,MAAM,wBAAwB;AAc/C,MAAMC,GAAG,GAAGJ,YAAY,CAACK,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC;AAEzC,MAAMC,qBAAqB,GAAG,CAAC,sBAAsB,EAAE,cAAc,CAAC;AAEtE,OAAO,MAAMC,oBAAmC,GAC9Cd,cAAc,CAACe,YAAY,CAAC,CAAC,CAACC,MAAM,CACjCC,IAAI,IAAK,CAACJ,qBAAqB,CAACK,QAAQ,CAACD,IAAI,CAChD,CAAC;;AAEH;AACA;AACA;AACA,OAAO,MAAME,uBAAuB,CAAC;EAUnCC,WAAWA,CAACC,MAAqC,EAAE;IACjD,MAAM;MAAEC,cAAc,GAAGpB;IAAsB,CAAC,GAAGmB,MAAM;IACzD,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC;EACnC;;EAEA;;EAEA;AACF;AACA;EACEC,OAAOA,CAAA,EAAW;IAChB,OAAO,UAAU;EACnB;EAEA,MAAMC,GAAGA,CAAA,EAAkB;IACzB;IACA,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;IAC7C,MAAM,IAAI,CAACD,iBAAiB;EAC9B;EAEA,aAAaE,aAAaA,CAACC,OAAe,EAAoB;IAC5D,MAAMC,cAAc,GAAGnC,IAAI,CAACoC,IAAI,CAACF,OAAO,EAAE,aAAa,CAAC;IACxD,MAAMG,WAAW,GAAGrC,IAAI,CAACoC,IAAI,CAACF,OAAO,EAAE,SAAS,CAAC;IACjD;IACA,OACE,CAAC,MAAMtB,UAAU,CAACuB,cAAc,CAAC,MAAM,MAAMxB,WAAW,CAAC0B,WAAW,CAAC,CAAC;EAE1E;EAEA,aAAaC,YAAYA,CAACJ,OAAe,EAAoB;IAC3D,MAAMK,qBAAqB,GAAGvC,IAAI,CAACoC,IAAI,CAACF,OAAO,EAAE,oBAAoB,CAAC;IACtE;IACA,OAAO,MAAMtB,UAAU,CAAC2B,qBAAqB,CAAC;EAChD;EAEA,aAAaC,eAAeA,CAACC,eAAwB,EAGlD;IACD,IAAI,CAACA,eAAe,EAAE;MACpB,OAAO;QACLC,WAAW,EAAE,IAAI;QACjBC,cAAc,EAAE;MAClB,CAAC;IACH;IAEA,MAAMC,0BAA0B,GAC9B,CAAC,MAAMrB,uBAAuB,CAACe,YAAY,CAACG,eAAe,CAAC,KAC5D,EAAE,MAAMlB,uBAAuB,CAACU,aAAa,CAACQ,eAAe,CAAC,CAAC;IAEjE,IAAIG,0BAA0B,EAAE;MAC9B,MAAM;QAAEC,GAAG,EAAEH,WAAW;QAAEI,IAAI,EAAEH;MAAe,CAAC,GAC9C3C,IAAI,CAAC+C,KAAK,CAACN,eAAe,CAAC;MAC7B,OAAO;QACLC,WAAW;QACXC;MACF,CAAC;IACH;IAEA,OAAO;MACLD,WAAW,EAAED,eAAe;MAC5BE,cAAc,EAAE;IAClB,CAAC;EACH;;EAEA;AACF;AACA;EACE,MAAMX,aAAaA,CAAA,EAAkB;IACnC;IACA,IAAI,CAACgB,GAAG,GAAG,MAAM,IAAIC,OAAO,CAAEC,OAAO,IAAK;MACxC,MAAMC,MAAM,GAAG,IAAI3C,eAAe;MAChC;MACA;MACA;QAAE4C,IAAI,EAAE,CAAC;QAAEC,IAAI,EAAE,WAAW;QAAEC,cAAc,EAAE;MAAK,CAAC;MACpD;MACA;MACA,MAAMJ,OAAO,CAACC,MAAM,CACtB,CAAC;IACH,CAAC,CAAC;;IAEF;IACA;IACA,IAAI,CAACH,GAAG,CAACO,EAAE,CAAC,YAAY,EAAE,UAAUC,MAAM,EAAE;MAC1CA,MAAM,CAACD,EAAE,CAAC,OAAO,EAAGE,GAAG,IAAK;QAC1B5C,GAAG,CAAC6C,KAAK,CAAE,+BAA8BD,GAAI,EAAC,CAAC;MACjD,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACA,IAAI,CAACE,sBAAsB,GAAG,MAAM,IAAI,CAACC,4BAA4B,CAAC,CAAC;;IAEvE;IACA,MAAMC,UAAU,GAAG,CAAC,IAAI,CAACF,sBAAsB,CAAC,CAC7CG,MAAM,CAAC,IAAI,CAACrC,MAAM,CAACoC,UAAU,CAACE,GAAG,CAAC,CAAC;MAAEC;IAAU,CAAC,KAAKA,SAAS,CAAC,CAAC,CAChE5B,IAAI,CAAC,GAAG,CAAC;IAEZ,MAAM;MAAE6B;IAAe,CAAC,GAAG,IAAI,CAACxC,MAAM;IAEtCZ,GAAG,CAAC6C,KAAK,CAAC,+BAA+B,CAAC;IAE1C,IAAIO,cAAc,EAAE;MAClBpD,GAAG,CAAC6C,KAAK,CAAE,oBAAmBO,cAAe,GAAE,CAAC;IAClD;IAEA,MAAMC,WAAW,GAAG,CAAC,GAAGhD,oBAAoB,CAAC;IAE7CgD,WAAW,CAACC,IAAI,CAAE,oBAAmBN,UAAW,EAAC,CAAC;IAElD,IAAI,IAAI,CAACpC,MAAM,CAAC2C,IAAI,EAAE;MACpBF,WAAW,CAACC,IAAI,CAAC,GAAG,IAAI,CAAC1C,MAAM,CAAC2C,IAAI,CAAC;IACvC;;IAEA;IACA,IAAI;MAAE1B,WAAW;MAAEC;IAAe,CAAC,GACjC,MAAMpB,uBAAuB,CAACiB,eAAe,CAC3C,IAAI,CAACf,MAAM,CAACgB,eACd,CAAC;IAEH,IAAIC,WAAW,IAAI,IAAI,CAACjB,MAAM,CAAC4C,kBAAkB,EAAE;MACjD,IACE1B,cAAc,IACd,EAAE,MAAMpB,uBAAuB,CAACU,aAAa,CAACS,WAAW,CAAC,CAAC,EAC3D;QACA,MAAM,IAAI4B,KAAK,CACb,uCAAuC,GACrC,2DAA2D,GAC3D,sDAAsD,GACtD,yBACJ,CAAC;MACH;IACF,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC7C,MAAM,CAAC4C,kBAAkB,EAAE;MAC1C;MACA;MACA;MACA,MAAME,MAAM,GAAG,IAAI7D,OAAO,CAAC,CAAC;MAC5B,MAAM6D,MAAM,CAACC,MAAM,CAAC,CAAC;MACrB,MAAMC,UAAU,GAAGF,MAAM,CAACvE,IAAI,CAAC,CAAC;MAEhC,IAAI0C,WAAW,IAAIC,cAAc,EAAE;QACjC;QACA,MAAM1C,EAAE,CAACyE,IAAI,CACX1E,IAAI,CAACoC,IAAI,CAACM,WAAW,EAAEC,cAAc,CAAC,EACtC3C,IAAI,CAACoC,IAAI,CAACqC,UAAU,EAAE9B,cAAc,CACtC,CAAC;MACH,CAAC,MAAM,IAAID,WAAW,EAAE;QACtB,MAAMzC,EAAE,CAACyE,IAAI,CAAChC,WAAW,EAAE+B,UAAU,CAAC;MACxC;MACA/B,WAAW,GAAG+B,UAAU;IAC1B;IAEA,IAAI9B,cAAc,EAAE;MAClBuB,WAAW,CAACC,IAAI,CAAE,uBAAsBxB,cAAe,EAAC,CAAC;IAC3D;IAEA,IAAIgC,WAAW;IACf,IAAI,IAAI,CAAClD,MAAM,CAACmD,QAAQ,EAAE;MACxB,MAAMC,YAAY,GAAGC,KAAK,CAACC,OAAO,CAAC,IAAI,CAACtD,MAAM,CAACmD,QAAQ,CAAC,GACpD,IAAI,CAACnD,MAAM,CAACmD,QAAQ,GACpB,CAAC,IAAI,CAACnD,MAAM,CAACmD,QAAQ,CAAC;MAC1BD,WAAW,GAAGE,YAAY,CAACG,KAAK,CAAC,CAAC;MAClCd,WAAW,CAACC,IAAI,CAAC,GAAGU,YAAY,CAAC;IACnC;IAEA,IAAI,CAACI,gBAAgB,GAAG,MAAM,IAAI,CAACvD,cAAc,CAAC;MAChDwD,gBAAgB,EAAE,IAAI;MACtBC,UAAU,EAAElB,cAAc;MAC1BC,WAAW;MACXS,WAAW;MACXjC,WAAW;MACX;MACA0C,kBAAkB,EAAE;IACtB,CAAC,CAAC;IAEF,IAAI,CAACH,gBAAgB,CAACI,OAAO,CAACC,IAAI,CAAC,OAAO,EAAE,MAAM;MAChD,IAAI,CAACL,gBAAgB,GAAG,IAAI;MAE5B,IAAI,CAAC,IAAI,CAACM,OAAO,EAAE;QACjB1E,GAAG,CAAC2E,IAAI,CAAC,4CAA4C,CAAC;QACtD,IAAI,CAACC,IAAI,CAAC,CAAC;MACb;IACF,CAAC,CAAC;EACJ;EAEA,MAAMC,YAAYA,CAACC,IAAY,EAAiB;IAC9C,OAAO,IAAI1C,OAAO,CAAEC,OAAO,IAAK;MAC9B,MAAM0C,OAAO,GAAG,IAAI,CAAC5C,GAAG,GAAG,IAAIpB,GAAG,CAAC,IAAI,CAACoB,GAAG,CAAC4C,OAAO,CAAC,GAAG,IAAIhE,GAAG,CAAC,CAAC;MAEhE,SAASiE,yBAAyBA,CAAA,EAAG;QACnC,MAAMC,MAAM,GAAG,IAAI;QACnBA,MAAM,CAACC,mBAAmB,CAAC,SAAS,EAAEC,oBAAoB,CAAC;QAC3DF,MAAM,CAACC,mBAAmB,CAAC,OAAO,EAAEF,yBAAyB,CAAC;QAC9DD,OAAO,CAACK,MAAM,CAACH,MAAM,CAAC;MACxB;MAEA,MAAME,oBAAoB,GAAG,MAAOE,OAAO,IAAK;QAC9C,MAAMC,GAAG,GAAGC,IAAI,CAACrD,KAAK,CAACmD,OAAO,CAACP,IAAI,CAAC;QAEpC,IAAIQ,GAAG,CAACE,IAAI,KAAK,+BAA+B,EAAE;UAChD,KAAK,MAAMP,MAAM,IAAIF,OAAO,EAAE;YAC5BC,yBAAyB,CAACS,IAAI,CAACR,MAAM,CAAC;UACxC;UACA5C,OAAO,CAAC,CAAC;QACX;MACF,CAAC;MAED,KAAK,MAAM4C,MAAM,IAAIF,OAAO,EAAE;QAC5B,IAAIE,MAAM,CAACS,UAAU,KAAKhG,SAAS,CAACiG,IAAI,EAAE;UACxCV,MAAM,CAACW,gBAAgB,CAAC,SAAS,EAAET,oBAAoB,CAAC;UACxDF,MAAM,CAACW,gBAAgB,CAAC,OAAO,EAAEZ,yBAAyB,CAAC;UAE3DC,MAAM,CAACY,IAAI,CAACN,IAAI,CAACO,SAAS,CAAChB,IAAI,CAAC,CAAC;QACnC,CAAC,MAAM;UACLC,OAAO,CAACK,MAAM,CAACH,MAAM,CAAC;QACxB;MACF;MAEA,IAAIF,OAAO,CAACgB,IAAI,KAAK,CAAC,EAAE;QACtB1D,OAAO,CAAC,CAAC;MACX;IACF,CAAC,CAAC;EACJ;EAEA,MAAMU,4BAA4BA,CAAA,EAAoB;IACpD,MAAMW,MAAM,GAAG,IAAI7D,OAAO,CAAC,CAAC;IAC5B,MAAM6D,MAAM,CAACC,MAAM,CAAC,CAAC;IACrB,IAAI,CAACqC,eAAe,CAAC,MAAMtC,MAAM,CAACuC,MAAM,CAAC,CAAC,CAAC;IAE3C,MAAMC,OAAO,GAAG/G,IAAI,CAACoC,IAAI,CACvBmC,MAAM,CAACvE,IAAI,CAAC,CAAC,EACZ,4BAA2BgH,IAAI,CAACC,GAAG,CAAC,CAAE,EACzC,CAAC;IAEDpG,GAAG,CAAC6C,KAAK,CAAE,wCAAuCqD,OAAQ,EAAC,CAAC;IAE5D,MAAM7G,WAAW,CAAC6G,OAAO,CAAC;IAE1B,MAAM9G,EAAE,CAACiH,SAAS,CAChBlH,IAAI,CAACoC,IAAI,CAAC2E,OAAO,EAAE,eAAe,CAAC,EACnCX,IAAI,CAACO,SAAS,CAAC;MACbQ,gBAAgB,EAAE,CAAC;MACnBC,IAAI,EAAE,kCAAkC;MACxCC,OAAO,EAAE,KAAK;MACdC,WAAW,EAAE,CAAC,YAAY,EAAE,MAAM,CAAC;MACnCC,UAAU,EAAE;QACVC,OAAO,EAAE,CAAC,OAAO;MACnB;IACF,CAAC,CACH,CAAC;;IAED;IACA,MAAMC,OAAO,GAAG,IAAI,CAACzE,GAAG,CAAC0E,OAAO,CAAC,CAAC;IAElC,MAAMC,MAAM,GAAI;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgBF,OAAO,CAACC,OAAQ,IAAGD,OAAO,CAACrE,IAAK;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;IAEL,MAAMnD,EAAE,CAACiH,SAAS,CAAClH,IAAI,CAACoC,IAAI,CAAC2E,OAAO,EAAE,OAAO,CAAC,EAAEY,MAAM,CAAC;IACvD,OAAOZ,OAAO;EAChB;;EAEA;AACF;AACA;AACA;EACE,MAAMa,mBAAmBA,CAAA,EAAgD;IACvE,MAAMC,UAAU,GAAG,IAAI,CAAChG,OAAO,CAAC,CAAC;IAEjC,MAAM,IAAI,CAAC6D,YAAY,CAAC;MACtBW,IAAI,EAAE;IACR,CAAC,CAAC;IAEFhB,OAAO,CAACyC,MAAM,CAACC,KAAK,CACjB,4BAA2B,IAAIf,IAAI,CAAC,CAAC,CAACgB,YAAY,CAAC,CAAE,EACxD,CAAC;IACDnH,GAAG,CAAC6C,KAAK,CAAC,IAAI,CAAC;IAEf,OAAO,CAAC;MAAEmE;IAAW,CAAC,CAAC;EACzB;;EAEA;AACF;AACA;AACA;EACE,MAAMI,0BAA0BA,CAC9BC,kBAA0B,EACmB;IAC7C;IACA;IACA;IACA,OAAO,IAAI,CAACN,mBAAmB,CAAC,CAAC;EACnC;;EAEA;AACF;AACA;AACA;AACA;EACEf,eAAeA,CAACsB,EAAY,EAAQ;IAClC,IAAI,CAACxG,gBAAgB,CAACyG,GAAG,CAACD,EAAE,CAAC;EAC/B;;EAEA;AACF;AACA;EACE,MAAM1C,IAAIA,CAAA,EAAkB;IAC1B,IAAI,CAACF,OAAO,GAAG,IAAI;;IAEnB;IACA;IACA,IAAI,IAAI,CAACxD,iBAAiB,EAAE;MAC1B;MACA,MAAM,IAAI,CAACA,iBAAiB,CAACsG,KAAK,CAAE5E,GAAG,IAAK;QAC1C5C,GAAG,CAAC6C,KAAK,CAAE,oDAAmDD,GAAI,EAAC,CAAC;MACtE,CAAC,CAAC;IACJ;IAEA,IAAI,IAAI,CAACwB,gBAAgB,EAAE;MACzB,MAAM,IAAI,CAACA,gBAAgB,CAACqD,IAAI,CAAC,CAAC;MAClC,IAAI,CAACrD,gBAAgB,GAAG,IAAI;IAC9B;IAEA,IAAI,IAAI,CAACjC,GAAG,EAAE;MACZ;MACA;MACA;MACA,KAAK,MAAMuF,SAAS,IAAI,EAAAC,SAAA,OAAI,CAACxF,GAAG,cAAAwF,SAAA,uBAARA,SAAA,CAAU5C,OAAO,KAAI,EAAE,EAAE;QAAA,IAAA4C,SAAA;QAC/C,IAAID,SAAS,CAAChC,UAAU,KAAKhG,SAAS,CAACiG,IAAI,EAAE;UAC3C+B,SAAS,CAACE,SAAS,CAAC,CAAC;QACvB;MACF;MACA,MAAM,IAAIxF,OAAO,CAAEC,OAAO,IACxB,IAAI,CAACF,GAAG,GAAG,IAAI,CAACA,GAAG,CAAC0F,KAAK,CAACxF,OAAO,CAAC,GAAGA,OAAO,CAAC,CAC/C,CAAC;MACD,IAAI,CAACF,GAAG,GAAG,IAAI;IACjB;;IAEA;IACA,KAAK,MAAMmF,EAAE,IAAI,IAAI,CAACxG,gBAAgB,EAAE;MACtC,IAAI;QACFwG,EAAE,CAAC,CAAC;MACN,CAAC,CAAC,OAAOQ,KAAK,EAAE;QACd9H,GAAG,CAAC8H,KAAK,CAACA,KAAK,CAAC;MAClB;IACF;EACF;AACF"}