{"version": 3, "file": "index.js", "names": ["readline", "WebExtError", "showDesktopNotification", "defaultDesktopNotifications", "createLogger", "createFileFilter", "defaultFileFilterCreator", "isTTY", "setRawMode", "defaultSourceWatcher", "log", "import", "meta", "url", "createExtensionRunner", "config", "target", "FirefoxDesktopExtensionRunner", "params", "FirefoxAndroidExtensionRunner", "ChromiumExtensionRunner", "MultiExtensionRunner", "constructor", "extensionRunners", "runners", "desktopNotifications", "getName", "run", "promises", "runner", "push", "Promise", "all", "reloadAllExtensions", "debug", "reloadPromise", "then", "<PERSON><PERSON><PERSON>", "error", "reloadError", "results", "handleReloadResults", "reloadExtensionBySourceDir", "sourceDir", "registerCleanup", "cleanup<PERSON><PERSON>back", "resolve", "exit", "Error", "message", "stack", "title", "defaultWatcherCreator", "reloadExtension", "watchFile", "watchIgnored", "artifactsDir", "ignoreFiles", "onSourceChange", "fileFilter", "onChange", "shouldWatchFile", "file", "wantFile", "defaultReloadStrategy", "<PERSON><PERSON><PERSON>ner", "noInput", "createWatcher", "stdin", "process", "kill", "allowInput", "watcher", "watchedSourceDir", "close", "pause", "emitKeypressEvents", "keypressUsageInfo", "info", "userExit", "keyPressed", "once", "str", "key", "ctrl", "name", "pid", "catch", "err", "warn"], "sources": ["../../src/extension-runners/index.js"], "sourcesContent": ["/* @flow */\n\nimport readline from 'readline';\n\nimport type Watchpack from 'watchpack';\n\nimport type {\n  IExtensionRunner, // eslint-disable-line import/named\n  ExtensionRunnerReloadResult,\n} from './base';\nimport { WebExtError } from '../errors.js';\nimport { showDesktopNotification as defaultDesktopNotifications } from '../util/desktop-notifier.js';\nimport type { FirefoxAndroidExtensionRunnerParams } from './firefox-android.js';\nimport type { FirefoxDesktopExtensionRunnerParams } from './firefox-desktop.js';\nimport type { ChromiumExtensionRunnerParams } from './chromium.js';\nimport { createLogger } from '../util/logger.js';\nimport type { FileFilterCreatorFn } from '../util/file-filter.js';\nimport { createFileFilter as defaultFileFilterCreator } from '../util/file-filter.js';\nimport { isTTY, setRawMode } from '../util/stdin.js';\nimport defaultSourceWatcher from '../watcher.js';\nimport type { OnSourceChangeFn } from '../watcher';\n\nconst log = createLogger(import.meta.url);\n\nexport type ExtensionRunnerConfig =\n  | {|\n      target: 'firefox-desktop',\n      params: FirefoxDesktopExtensionRunnerParams,\n    |}\n  | {|\n      target: 'firefox-android',\n      params: FirefoxAndroidExtensionRunnerParams,\n    |}\n  | {|\n      target: 'chromium',\n      params: ChromiumExtensionRunnerParams,\n    |};\n\nexport type MultiExtensionRunnerParams = {|\n  runners: Array<IExtensionRunner>,\n  desktopNotifications: typeof defaultDesktopNotifications,\n|};\n\nexport async function createExtensionRunner(\n  config: ExtensionRunnerConfig\n): Promise<IExtensionRunner> {\n  switch (config.target) {\n    case 'firefox-desktop': {\n      const { FirefoxDesktopExtensionRunner } = await import(\n        './firefox-desktop.js'\n      );\n      return new FirefoxDesktopExtensionRunner(config.params);\n    }\n    case 'firefox-android': {\n      const { FirefoxAndroidExtensionRunner } = await import(\n        './firefox-android.js'\n      );\n      return new FirefoxAndroidExtensionRunner(config.params);\n    }\n    case 'chromium': {\n      const { ChromiumExtensionRunner } = await import('./chromium.js');\n      return new ChromiumExtensionRunner(config.params);\n    }\n    default:\n      throw new WebExtError(`Unknown target: \"${config.target}\"`);\n  }\n}\n\n/**\n * Implements an IExtensionRunner which allow the caller to\n * manage multiple extension runners at the same time (e.g. by running\n * a Firefox Desktop instance alongside to a Firefox for Android instance).\n */\nexport class MultiExtensionRunner {\n  extensionRunners: Array<IExtensionRunner>;\n  desktopNotifications: typeof defaultDesktopNotifications;\n\n  constructor(params: MultiExtensionRunnerParams) {\n    this.extensionRunners = params.runners;\n    this.desktopNotifications = params.desktopNotifications;\n  }\n\n  // Method exported from the IExtensionRunner interface.\n\n  /**\n   * Returns the runner name.\n   */\n  getName(): string {\n    return 'Multi Extension Runner';\n  }\n\n  /**\n   * Call the `run` method on all the managed extension runners,\n   * and awaits that all the runners has been successfully started.\n   */\n  async run(): Promise<void> {\n    const promises = [];\n    for (const runner of this.extensionRunners) {\n      promises.push(runner.run());\n    }\n\n    await Promise.all(promises);\n  }\n\n  /**\n   * Reloads all the extensions on all the managed extension runners,\n   * collect any reload error, and resolves to an array composed by\n   * a ExtensionRunnerReloadResult object per managed runner.\n   *\n   * Any detected reload error is also logged on the terminal and shows as a\n   * desktop notification.\n   */\n  async reloadAllExtensions(): Promise<Array<ExtensionRunnerReloadResult>> {\n    log.debug('Reloading all reloadable add-ons');\n\n    const promises = [];\n    for (const runner of this.extensionRunners) {\n      const reloadPromise = runner.reloadAllExtensions().then(\n        () => {\n          return { runnerName: runner.getName() };\n        },\n        (error) => {\n          return {\n            runnerName: runner.getName(),\n            reloadError: error,\n          };\n        }\n      );\n\n      promises.push(reloadPromise);\n    }\n\n    return await Promise.all(promises).then((results) => {\n      this.handleReloadResults(results);\n      return results;\n    });\n  }\n\n  /**\n   * Reloads a single extension on all the managed extension runners,\n   * collect any reload error and resolves to an array composed by\n   * a ExtensionRunnerReloadResult object per managed runner.\n   *\n   * Any detected reload error is also logged on the terminal and shows as a\n   * desktop notification.\n   */\n  async reloadExtensionBySourceDir(\n    sourceDir: string\n  ): Promise<Array<ExtensionRunnerReloadResult>> {\n    log.debug(`Reloading add-on at ${sourceDir}`);\n\n    const promises: Array<Promise<ExtensionRunnerReloadResult>> = [];\n    for (const runner of this.extensionRunners) {\n      const reloadPromise = runner.reloadExtensionBySourceDir(sourceDir).then(\n        () => {\n          return { runnerName: runner.getName(), sourceDir };\n        },\n        (error) => {\n          return {\n            runnerName: runner.getName(),\n            reloadError: error,\n            sourceDir,\n          };\n        }\n      );\n\n      promises.push(reloadPromise);\n    }\n\n    return await Promise.all(promises).then((results) => {\n      this.handleReloadResults(results);\n      return results;\n    });\n  }\n\n  /**\n   * Register a callback to be called when all the managed runners has been exited.\n   */\n  registerCleanup(cleanupCallback: Function): void {\n    const promises = [];\n\n    // Create a promise for every extension runner managed by this instance,\n    // the promise will be resolved when the particular runner calls its\n    // registered cleanup callbacks.\n    for (const runner of this.extensionRunners) {\n      promises.push(\n        new Promise((resolve) => {\n          runner.registerCleanup(resolve);\n        })\n      );\n    }\n\n    // Wait for all the created promises to be resolved or rejected\n    // (once each one of the runners has cleaned up) and then call\n    // the cleanup callback registered to this runner.\n    Promise.all(promises).then(cleanupCallback, cleanupCallback);\n  }\n\n  /**\n   * Exits all the managed runner has been exited.\n   */\n  async exit(): Promise<void> {\n    const promises = [];\n    for (const runner of this.extensionRunners) {\n      promises.push(runner.exit());\n    }\n\n    await Promise.all(promises);\n  }\n\n  // Private helper methods.\n\n  handleReloadResults(results: Array<ExtensionRunnerReloadResult>): void {\n    for (const { runnerName, reloadError, sourceDir } of results) {\n      if (reloadError instanceof Error) {\n        let message = 'Error occurred while reloading';\n        if (sourceDir) {\n          message += ` \"${sourceDir}\" `;\n        }\n\n        message += `on \"${runnerName}\" - ${reloadError.message}`;\n\n        log.error(`\\n${message}`);\n        log.debug(reloadError.stack);\n\n        this.desktopNotifications({\n          title: 'web-ext run: extension reload error',\n          message,\n        });\n      }\n    }\n  }\n}\n\n// defaultWatcherCreator types and implementation.\n\nexport type WatcherCreatorParams = {|\n  reloadExtension: (string) => void,\n  sourceDir: string,\n  watchFile?: Array<string>,\n  watchIgnored?: Array<string>,\n  artifactsDir: string,\n  onSourceChange?: OnSourceChangeFn,\n  ignoreFiles?: Array<string>,\n  createFileFilter?: FileFilterCreatorFn,\n|};\n\nexport type WatcherCreatorFn = (params: WatcherCreatorParams) => Watchpack;\n\nexport function defaultWatcherCreator({\n  reloadExtension,\n  sourceDir,\n  watchFile,\n  watchIgnored,\n  artifactsDir,\n  ignoreFiles,\n  onSourceChange = defaultSourceWatcher,\n  createFileFilter = defaultFileFilterCreator,\n}: WatcherCreatorParams): Watchpack {\n  const fileFilter = createFileFilter({ sourceDir, artifactsDir, ignoreFiles });\n  return onSourceChange({\n    sourceDir,\n    watchFile,\n    watchIgnored,\n    artifactsDir,\n    onChange: () => reloadExtension(sourceDir),\n    shouldWatchFile: (file) => fileFilter.wantFile(file),\n  });\n}\n\n// defaultReloadStrategy types and implementation.\n\nexport type ReloadStrategyParams = {|\n  extensionRunner: IExtensionRunner,\n  sourceDir: string,\n  watchFile?: Array<string>,\n  watchIgnored?: Array<string>,\n  artifactsDir: string,\n  ignoreFiles?: Array<string>,\n  noInput?: boolean,\n|};\n\nexport type ReloadStrategyOptions = {\n  createWatcher?: WatcherCreatorFn,\n  stdin?: stream$Readable,\n  kill?: (pid: number, signal?: string | number) => void,\n};\n\nexport function defaultReloadStrategy(\n  {\n    artifactsDir,\n    extensionRunner,\n    ignoreFiles,\n    noInput = false,\n    sourceDir,\n    watchFile,\n    watchIgnored,\n  }: ReloadStrategyParams,\n  {\n    createWatcher = defaultWatcherCreator,\n    stdin = process.stdin,\n    // $FlowIgnore: ignore method-unbinding.\n    kill = process.kill,\n  }: ReloadStrategyOptions = {}\n): void {\n  const allowInput = !noInput;\n  if (!allowInput) {\n    log.debug('Input has been disabled because of noInput==true');\n  }\n\n  const watcher: Watchpack = createWatcher({\n    reloadExtension: (watchedSourceDir) => {\n      extensionRunner.reloadExtensionBySourceDir(watchedSourceDir);\n    },\n    sourceDir,\n    watchFile,\n    watchIgnored,\n    artifactsDir,\n    ignoreFiles,\n  });\n\n  extensionRunner.registerCleanup(() => {\n    watcher.close();\n    if (allowInput) {\n      stdin.pause();\n    }\n  });\n\n  if (allowInput && isTTY(stdin)) {\n    readline.emitKeypressEvents(stdin);\n    setRawMode(stdin, true);\n\n    const keypressUsageInfo = 'Press R to reload (and Ctrl-C to quit)';\n\n    // NOTE: this `Promise.resolve().then(...)` is basically used to spawn a \"co-routine\"\n    // that is executed before the callback attached to the Promise returned by this function\n    // (and it allows the `run` function to not be stuck in the while loop).\n    Promise.resolve().then(async function () {\n      log.info(keypressUsageInfo);\n\n      let userExit = false;\n\n      while (!userExit) {\n        const keyPressed = await new Promise((resolve) => {\n          stdin.once('keypress', (str, key) => resolve(key));\n        });\n\n        if (keyPressed.ctrl && keyPressed.name === 'c') {\n          userExit = true;\n        } else if (keyPressed.name === 'z') {\n          // Prepare to suspend.\n\n          // NOTE: Switch the raw mode off before suspending (needed to make the keypress event\n          // to work correctly when the nodejs process is resumed).\n          setRawMode(stdin, false);\n\n          log.info('\\nweb-ext has been suspended on user request');\n          kill(process.pid, 'SIGTSTP');\n\n          // Prepare to resume.\n\n          log.info(`\\nweb-ext has been resumed. ${keypressUsageInfo}`);\n\n          // Switch the raw mode on on resume.\n          setRawMode(stdin, true);\n        } else if (keyPressed.name === 'r') {\n          log.debug('Reloading installed extensions on user request');\n          await extensionRunner.reloadAllExtensions().catch((err) => {\n            log.warn(`\\nError reloading extension: ${err}`);\n            log.debug(`Reloading extension error stack: ${err.stack}`);\n          });\n        }\n      }\n\n      log.info('\\nExiting web-ext on user request');\n      extensionRunner.exit();\n    });\n  }\n}\n"], "mappings": "AAEA,OAAOA,QAAQ,MAAM,UAAU;AAQ/B,SAASC,WAAW,QAAQ,cAAc;AAC1C,SAASC,uBAAuB,IAAIC,2BAA2B,QAAQ,6BAA6B;AAIpG,SAASC,YAAY,QAAQ,mBAAmB;AAEhD,SAASC,gBAAgB,IAAIC,wBAAwB,QAAQ,wBAAwB;AACrF,SAASC,KAAK,EAAEC,UAAU,QAAQ,kBAAkB;AACpD,OAAOC,oBAAoB,MAAM,eAAe;AAGhD,MAAMC,GAAG,GAAGN,YAAY,CAACO,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC;AAqBzC,OAAO,eAAeC,qBAAqBA,CACzCC,MAA6B,EACF;EAC3B,QAAQA,MAAM,CAACC,MAAM;IACnB,KAAK,iBAAiB;MAAE;QACtB,MAAM;UAAEC;QAA8B,CAAC,GAAG,MAAM,MAAM,CACpD,sBACF,CAAC;QACD,OAAO,IAAIA,6BAA6B,CAACF,MAAM,CAACG,MAAM,CAAC;MACzD;IACA,KAAK,iBAAiB;MAAE;QACtB,MAAM;UAAEC;QAA8B,CAAC,GAAG,MAAM,MAAM,CACpD,sBACF,CAAC;QACD,OAAO,IAAIA,6BAA6B,CAACJ,MAAM,CAACG,MAAM,CAAC;MACzD;IACA,KAAK,UAAU;MAAE;QACf,MAAM;UAAEE;QAAwB,CAAC,GAAG,MAAM,MAAM,CAAC,eAAe,CAAC;QACjE,OAAO,IAAIA,uBAAuB,CAACL,MAAM,CAACG,MAAM,CAAC;MACnD;IACA;MACE,MAAM,IAAIjB,WAAW,CAAE,oBAAmBc,MAAM,CAACC,MAAO,GAAE,CAAC;EAC/D;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMK,oBAAoB,CAAC;EAIhCC,WAAWA,CAACJ,MAAkC,EAAE;IAC9C,IAAI,CAACK,gBAAgB,GAAGL,MAAM,CAACM,OAAO;IACtC,IAAI,CAACC,oBAAoB,GAAGP,MAAM,CAACO,oBAAoB;EACzD;;EAEA;;EAEA;AACF;AACA;EACEC,OAAOA,CAAA,EAAW;IAChB,OAAO,wBAAwB;EACjC;;EAEA;AACF;AACA;AACA;EACE,MAAMC,GAAGA,CAAA,EAAkB;IACzB,MAAMC,QAAQ,GAAG,EAAE;IACnB,KAAK,MAAMC,MAAM,IAAI,IAAI,CAACN,gBAAgB,EAAE;MAC1CK,QAAQ,CAACE,IAAI,CAACD,MAAM,CAACF,GAAG,CAAC,CAAC,CAAC;IAC7B;IAEA,MAAMI,OAAO,CAACC,GAAG,CAACJ,QAAQ,CAAC;EAC7B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMK,mBAAmBA,CAAA,EAAgD;IACvEvB,GAAG,CAACwB,KAAK,CAAC,kCAAkC,CAAC;IAE7C,MAAMN,QAAQ,GAAG,EAAE;IACnB,KAAK,MAAMC,MAAM,IAAI,IAAI,CAACN,gBAAgB,EAAE;MAC1C,MAAMY,aAAa,GAAGN,MAAM,CAACI,mBAAmB,CAAC,CAAC,CAACG,IAAI,CACrD,MAAM;QACJ,OAAO;UAAEC,UAAU,EAAER,MAAM,CAACH,OAAO,CAAC;QAAE,CAAC;MACzC,CAAC,EACAY,KAAK,IAAK;QACT,OAAO;UACLD,UAAU,EAAER,MAAM,CAACH,OAAO,CAAC,CAAC;UAC5Ba,WAAW,EAAED;QACf,CAAC;MACH,CACF,CAAC;MAEDV,QAAQ,CAACE,IAAI,CAACK,aAAa,CAAC;IAC9B;IAEA,OAAO,MAAMJ,OAAO,CAACC,GAAG,CAACJ,QAAQ,CAAC,CAACQ,IAAI,CAAEI,OAAO,IAAK;MACnD,IAAI,CAACC,mBAAmB,CAACD,OAAO,CAAC;MACjC,OAAOA,OAAO;IAChB,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAME,0BAA0BA,CAC9BC,SAAiB,EAC4B;IAC7CjC,GAAG,CAACwB,KAAK,CAAE,uBAAsBS,SAAU,EAAC,CAAC;IAE7C,MAAMf,QAAqD,GAAG,EAAE;IAChE,KAAK,MAAMC,MAAM,IAAI,IAAI,CAACN,gBAAgB,EAAE;MAC1C,MAAMY,aAAa,GAAGN,MAAM,CAACa,0BAA0B,CAACC,SAAS,CAAC,CAACP,IAAI,CACrE,MAAM;QACJ,OAAO;UAAEC,UAAU,EAAER,MAAM,CAACH,OAAO,CAAC,CAAC;UAAEiB;QAAU,CAAC;MACpD,CAAC,EACAL,KAAK,IAAK;QACT,OAAO;UACLD,UAAU,EAAER,MAAM,CAACH,OAAO,CAAC,CAAC;UAC5Ba,WAAW,EAAED,KAAK;UAClBK;QACF,CAAC;MACH,CACF,CAAC;MAEDf,QAAQ,CAACE,IAAI,CAACK,aAAa,CAAC;IAC9B;IAEA,OAAO,MAAMJ,OAAO,CAACC,GAAG,CAACJ,QAAQ,CAAC,CAACQ,IAAI,CAAEI,OAAO,IAAK;MACnD,IAAI,CAACC,mBAAmB,CAACD,OAAO,CAAC;MACjC,OAAOA,OAAO;IAChB,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACEI,eAAeA,CAACC,eAAyB,EAAQ;IAC/C,MAAMjB,QAAQ,GAAG,EAAE;;IAEnB;IACA;IACA;IACA,KAAK,MAAMC,MAAM,IAAI,IAAI,CAACN,gBAAgB,EAAE;MAC1CK,QAAQ,CAACE,IAAI,CACX,IAAIC,OAAO,CAAEe,OAAO,IAAK;QACvBjB,MAAM,CAACe,eAAe,CAACE,OAAO,CAAC;MACjC,CAAC,CACH,CAAC;IACH;;IAEA;IACA;IACA;IACAf,OAAO,CAACC,GAAG,CAACJ,QAAQ,CAAC,CAACQ,IAAI,CAACS,eAAe,EAAEA,eAAe,CAAC;EAC9D;;EAEA;AACF;AACA;EACE,MAAME,IAAIA,CAAA,EAAkB;IAC1B,MAAMnB,QAAQ,GAAG,EAAE;IACnB,KAAK,MAAMC,MAAM,IAAI,IAAI,CAACN,gBAAgB,EAAE;MAC1CK,QAAQ,CAACE,IAAI,CAACD,MAAM,CAACkB,IAAI,CAAC,CAAC,CAAC;IAC9B;IAEA,MAAMhB,OAAO,CAACC,GAAG,CAACJ,QAAQ,CAAC;EAC7B;;EAEA;;EAEAa,mBAAmBA,CAACD,OAA2C,EAAQ;IACrE,KAAK,MAAM;MAAEH,UAAU;MAAEE,WAAW;MAAEI;IAAU,CAAC,IAAIH,OAAO,EAAE;MAC5D,IAAID,WAAW,YAAYS,KAAK,EAAE;QAChC,IAAIC,OAAO,GAAG,gCAAgC;QAC9C,IAAIN,SAAS,EAAE;UACbM,OAAO,IAAK,KAAIN,SAAU,IAAG;QAC/B;QAEAM,OAAO,IAAK,OAAMZ,UAAW,OAAME,WAAW,CAACU,OAAQ,EAAC;QAExDvC,GAAG,CAAC4B,KAAK,CAAE,KAAIW,OAAQ,EAAC,CAAC;QACzBvC,GAAG,CAACwB,KAAK,CAACK,WAAW,CAACW,KAAK,CAAC;QAE5B,IAAI,CAACzB,oBAAoB,CAAC;UACxB0B,KAAK,EAAE,qCAAqC;UAC5CF;QACF,CAAC,CAAC;MACJ;IACF;EACF;AACF;;AAEA;;AAeA,OAAO,SAASG,qBAAqBA,CAAC;EACpCC,eAAe;EACfV,SAAS;EACTW,SAAS;EACTC,YAAY;EACZC,YAAY;EACZC,WAAW;EACXC,cAAc,GAAGjD,oBAAoB;EACrCJ,gBAAgB,GAAGC;AACC,CAAC,EAAa;EAClC,MAAMqD,UAAU,GAAGtD,gBAAgB,CAAC;IAAEsC,SAAS;IAAEa,YAAY;IAAEC;EAAY,CAAC,CAAC;EAC7E,OAAOC,cAAc,CAAC;IACpBf,SAAS;IACTW,SAAS;IACTC,YAAY;IACZC,YAAY;IACZI,QAAQ,EAAEA,CAAA,KAAMP,eAAe,CAACV,SAAS,CAAC;IAC1CkB,eAAe,EAAGC,IAAI,IAAKH,UAAU,CAACI,QAAQ,CAACD,IAAI;EACrD,CAAC,CAAC;AACJ;;AAEA;;AAkBA,OAAO,SAASE,qBAAqBA,CACnC;EACER,YAAY;EACZS,eAAe;EACfR,WAAW;EACXS,OAAO,GAAG,KAAK;EACfvB,SAAS;EACTW,SAAS;EACTC;AACoB,CAAC,EACvB;EACEY,aAAa,GAAGf,qBAAqB;EACrCgB,KAAK,GAAGC,OAAO,CAACD,KAAK;EACrB;EACAE,IAAI,GAAGD,OAAO,CAACC;AACM,CAAC,GAAG,CAAC,CAAC,EACvB;EACN,MAAMC,UAAU,GAAG,CAACL,OAAO;EAC3B,IAAI,CAACK,UAAU,EAAE;IACf7D,GAAG,CAACwB,KAAK,CAAC,kDAAkD,CAAC;EAC/D;EAEA,MAAMsC,OAAkB,GAAGL,aAAa,CAAC;IACvCd,eAAe,EAAGoB,gBAAgB,IAAK;MACrCR,eAAe,CAACvB,0BAA0B,CAAC+B,gBAAgB,CAAC;IAC9D,CAAC;IACD9B,SAAS;IACTW,SAAS;IACTC,YAAY;IACZC,YAAY;IACZC;EACF,CAAC,CAAC;EAEFQ,eAAe,CAACrB,eAAe,CAAC,MAAM;IACpC4B,OAAO,CAACE,KAAK,CAAC,CAAC;IACf,IAAIH,UAAU,EAAE;MACdH,KAAK,CAACO,KAAK,CAAC,CAAC;IACf;EACF,CAAC,CAAC;EAEF,IAAIJ,UAAU,IAAIhE,KAAK,CAAC6D,KAAK,CAAC,EAAE;IAC9BpE,QAAQ,CAAC4E,kBAAkB,CAACR,KAAK,CAAC;IAClC5D,UAAU,CAAC4D,KAAK,EAAE,IAAI,CAAC;IAEvB,MAAMS,iBAAiB,GAAG,wCAAwC;;IAElE;IACA;IACA;IACA9C,OAAO,CAACe,OAAO,CAAC,CAAC,CAACV,IAAI,CAAC,kBAAkB;MACvC1B,GAAG,CAACoE,IAAI,CAACD,iBAAiB,CAAC;MAE3B,IAAIE,QAAQ,GAAG,KAAK;MAEpB,OAAO,CAACA,QAAQ,EAAE;QAChB,MAAMC,UAAU,GAAG,MAAM,IAAIjD,OAAO,CAAEe,OAAO,IAAK;UAChDsB,KAAK,CAACa,IAAI,CAAC,UAAU,EAAE,CAACC,GAAG,EAAEC,GAAG,KAAKrC,OAAO,CAACqC,GAAG,CAAC,CAAC;QACpD,CAAC,CAAC;QAEF,IAAIH,UAAU,CAACI,IAAI,IAAIJ,UAAU,CAACK,IAAI,KAAK,GAAG,EAAE;UAC9CN,QAAQ,GAAG,IAAI;QACjB,CAAC,MAAM,IAAIC,UAAU,CAACK,IAAI,KAAK,GAAG,EAAE;UAClC;;UAEA;UACA;UACA7E,UAAU,CAAC4D,KAAK,EAAE,KAAK,CAAC;UAExB1D,GAAG,CAACoE,IAAI,CAAC,8CAA8C,CAAC;UACxDR,IAAI,CAACD,OAAO,CAACiB,GAAG,EAAE,SAAS,CAAC;;UAE5B;;UAEA5E,GAAG,CAACoE,IAAI,CAAE,+BAA8BD,iBAAkB,EAAC,CAAC;;UAE5D;UACArE,UAAU,CAAC4D,KAAK,EAAE,IAAI,CAAC;QACzB,CAAC,MAAM,IAAIY,UAAU,CAACK,IAAI,KAAK,GAAG,EAAE;UAClC3E,GAAG,CAACwB,KAAK,CAAC,gDAAgD,CAAC;UAC3D,MAAM+B,eAAe,CAAChC,mBAAmB,CAAC,CAAC,CAACsD,KAAK,CAAEC,GAAG,IAAK;YACzD9E,GAAG,CAAC+E,IAAI,CAAE,gCAA+BD,GAAI,EAAC,CAAC;YAC/C9E,GAAG,CAACwB,KAAK,CAAE,oCAAmCsD,GAAG,CAACtC,KAAM,EAAC,CAAC;UAC5D,CAAC,CAAC;QACJ;MACF;MAEAxC,GAAG,CAACoE,IAAI,CAAC,mCAAmC,CAAC;MAC7Cb,eAAe,CAAClB,IAAI,CAAC,CAAC;IACxB,CAAC,CAAC;EACJ;AACF"}