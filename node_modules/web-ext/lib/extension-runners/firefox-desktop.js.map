{"version": 3, "file": "firefox-desktop.js", "names": ["MultiExtensionsReloadError", "RemoteTempInstallNotSupported", "WebExtError", "defaultFirefoxApp", "connectWithMaxRetries", "defaultFirefoxConnector", "createLogger", "log", "import", "meta", "url", "FirefoxDesktopExtensionRunner", "constructor", "params", "reloadableExtensions", "Map", "cleanupCallbacks", "Set", "getName", "run", "setupProfileDir", "startFirefoxInstance", "reloadAllExtensions", "<PERSON><PERSON><PERSON>", "reloadErrors", "sourceDir", "extensions", "res", "reloadExtensionBySourceDir", "reloadError", "Error", "set", "size", "extensionSourceDir", "addonId", "get", "remoteFirefox", "reloadAddon", "error", "registerCleanup", "fn", "add", "exit", "runningInfo", "firefox", "kill", "customPrefs", "keepProfileChanges", "preInstall", "profilePath", "firefoxApp", "debug", "profile", "useProfile", "copyProfile", "createProfile", "extension", "installExtension", "asProxy", "extensionPath", "manifestData", "browserConsole", "devtools", "firefoxBinary", "startUrl", "firefoxClient", "args", "binaryArgs", "push", "urls", "Array", "isArray", "on", "cleanupCb", "port", "debuggerPort", "installTemporaryAddon", "then", "installResult", "addon", "id", "String"], "sources": ["../../src/extension-runners/firefox-desktop.js"], "sourcesContent": ["/* @flow */\n\n/**\n * This module provide an ExtensionRunner subclass that manage an extension executed\n * in a Firefox for Desktop instance.\n */\n\n// Import flow types from npm dependencies.\nimport type FirefoxProfile from 'firefox-profile';\n\nimport {\n  MultiExtensionsReloadError,\n  RemoteTempInstallNotSupported,\n  WebExtError,\n} from '../errors.js';\nimport * as defaultFirefoxApp from '../firefox/index.js';\nimport { connectWithMaxRetries as defaultFirefoxConnector } from '../firefox/remote.js';\nimport { createLogger } from '../util/logger.js';\n// Import flow types from project files.\nimport type { FirefoxRDPResponseAddon, RemoteFirefox } from '../firefox/remote';\nimport type {\n  ExtensionRunnerParams,\n  ExtensionRunnerReloadResult,\n} from './base';\nimport type { FirefoxPreferences } from '../firefox/preferences';\nimport type { FirefoxInfo } from '../firefox/index'; // eslint-disable-line import/named\n\ntype FirefoxDesktopSpecificRunnerParams = {|\n  customPrefs?: FirefoxPreferences,\n  browserConsole: boolean,\n  devtools: boolean,\n  firefoxBinary: string,\n  preInstall: boolean,\n\n  // Firefox desktop injected dependencies.\n  firefoxApp: typeof defaultFirefoxApp,\n  firefoxClient: typeof defaultFirefoxConnector,\n|};\n\nexport type FirefoxDesktopExtensionRunnerParams = {|\n  ...ExtensionRunnerParams,\n  // Firefox desktop CLI params.\n  ...FirefoxDesktopSpecificRunnerParams,\n|};\n\nconst log = createLogger(import.meta.url);\n\n/**\n * Implements an IExtensionRunner which manages a Firefox Desktop instance.\n */\nexport class FirefoxDesktopExtensionRunner {\n  cleanupCallbacks: Set<Function>;\n  params: FirefoxDesktopExtensionRunnerParams;\n  profile: FirefoxProfile;\n  // Map extensions sourceDir to their related addon ids.\n  reloadableExtensions: Map<string, string>;\n  remoteFirefox: RemoteFirefox;\n  runningInfo: FirefoxInfo;\n\n  constructor(params: FirefoxDesktopExtensionRunnerParams) {\n    this.params = params;\n\n    this.reloadableExtensions = new Map();\n    this.cleanupCallbacks = new Set();\n  }\n\n  // Method exported from the IExtensionRunner interface.\n\n  /**\n   * Returns the runner name.\n   */\n  getName(): string {\n    return 'Firefox Desktop';\n  }\n\n  /**\n   * Setup the Firefox Profile and run a Firefox Desktop instance.\n   */\n  async run(): Promise<void> {\n    // Get a firefox profile with the custom Prefs set (a new or a cloned one).\n    // Pre-install extensions as proxy if needed (and disable auto-reload if you do)\n    await this.setupProfileDir();\n\n    // (if reload is enabled):\n    // - Connect to the firefox instance on RDP\n    // - Install any extension if needed (if not installed as proxy)\n    // - Keep track of the extension id assigned in a map with the sourceDir as a key\n    await this.startFirefoxInstance();\n  }\n\n  /**\n   * Reloads all the extensions, collect any reload error and resolves to\n   * an array composed by a single ExtensionRunnerReloadResult object.\n   */\n  async reloadAllExtensions(): Promise<Array<ExtensionRunnerReloadResult>> {\n    const runnerName = this.getName();\n    const reloadErrors = new Map();\n    for (const { sourceDir } of this.params.extensions) {\n      const [res] = await this.reloadExtensionBySourceDir(sourceDir);\n      if (res.reloadError instanceof Error) {\n        reloadErrors.set(sourceDir, res.reloadError);\n      }\n    }\n\n    if (reloadErrors.size > 0) {\n      return [\n        {\n          runnerName,\n          reloadError: new MultiExtensionsReloadError(reloadErrors),\n        },\n      ];\n    }\n\n    return [{ runnerName }];\n  }\n\n  /**\n   * Reloads a single extension, collect any reload error and resolves to\n   * an array composed by a single ExtensionRunnerReloadResult object.\n   */\n  async reloadExtensionBySourceDir(\n    extensionSourceDir: string\n  ): Promise<Array<ExtensionRunnerReloadResult>> {\n    const runnerName = this.getName();\n    const addonId = this.reloadableExtensions.get(extensionSourceDir);\n\n    if (!addonId) {\n      return [\n        {\n          sourceDir: extensionSourceDir,\n          reloadError: new WebExtError(\n            'Extension not reloadable: ' +\n              `no addonId has been mapped to \"${extensionSourceDir}\"`\n          ),\n          runnerName,\n        },\n      ];\n    }\n\n    try {\n      await this.remoteFirefox.reloadAddon(addonId);\n    } catch (error) {\n      return [\n        {\n          sourceDir: extensionSourceDir,\n          reloadError: error,\n          runnerName,\n        },\n      ];\n    }\n\n    return [{ runnerName, sourceDir: extensionSourceDir }];\n  }\n\n  /**\n   * Register a callback to be called when the runner has been exited\n   * (e.g. the Firefox instance exits or the user has requested web-ext\n   * to exit).\n   */\n  registerCleanup(fn: Function): void {\n    this.cleanupCallbacks.add(fn);\n  }\n\n  /**\n   * Exits the runner, by closing the managed Firefox instance.\n   */\n  async exit(): Promise<void> {\n    if (!this.runningInfo || !this.runningInfo.firefox) {\n      throw new WebExtError('No firefox instance is currently running');\n    }\n\n    this.runningInfo.firefox.kill();\n  }\n\n  // Private helper methods.\n\n  async setupProfileDir() {\n    const {\n      customPrefs,\n      extensions,\n      keepProfileChanges,\n      preInstall,\n      profilePath,\n      firefoxApp,\n    } = this.params;\n\n    if (profilePath) {\n      if (keepProfileChanges) {\n        log.debug(`Using Firefox profile from ${profilePath}`);\n        this.profile = await firefoxApp.useProfile(profilePath, {\n          customPrefs,\n        });\n      } else {\n        log.debug(`Copying Firefox profile from ${profilePath}`);\n        this.profile = await firefoxApp.copyProfile(profilePath, {\n          customPrefs,\n        });\n      }\n    } else {\n      log.debug('Creating new Firefox profile');\n      this.profile = await firefoxApp.createProfile({ customPrefs });\n    }\n\n    // preInstall the extensions if needed.\n    if (preInstall) {\n      for (const extension of extensions) {\n        await firefoxApp.installExtension({\n          asProxy: true,\n          extensionPath: extension.sourceDir,\n          manifestData: extension.manifestData,\n          profile: this.profile,\n        });\n      }\n    }\n  }\n\n  async startFirefoxInstance() {\n    const {\n      browserConsole,\n      devtools,\n      extensions,\n      firefoxBinary,\n      preInstall,\n      startUrl,\n      firefoxApp,\n      firefoxClient,\n      args,\n    } = this.params;\n\n    const binaryArgs = [];\n\n    if (browserConsole) {\n      binaryArgs.push('-jsconsole');\n    }\n    if (startUrl) {\n      const urls = Array.isArray(startUrl) ? startUrl : [startUrl];\n      for (const url of urls) {\n        binaryArgs.push('--url', url);\n      }\n    }\n\n    if (args) {\n      binaryArgs.push(...args);\n    }\n\n    this.runningInfo = await firefoxApp.run(this.profile, {\n      firefoxBinary,\n      binaryArgs,\n      extensions,\n      devtools,\n    });\n\n    this.runningInfo.firefox.on('close', () => {\n      for (const cleanupCb of this.cleanupCallbacks) {\n        try {\n          cleanupCb();\n        } catch (error) {\n          log.error(`Exception on executing cleanup callback: ${error}`);\n        }\n      }\n    });\n\n    if (!preInstall) {\n      const remoteFirefox = (this.remoteFirefox = await firefoxClient({\n        port: this.runningInfo.debuggerPort,\n      }));\n\n      // Install all the temporary addons.\n      for (const extension of extensions) {\n        try {\n          const addonId = await remoteFirefox\n            .installTemporaryAddon(extension.sourceDir, devtools)\n            .then((installResult: FirefoxRDPResponseAddon) => {\n              return installResult.addon.id;\n            });\n\n          if (!addonId) {\n            throw new WebExtError(\n              'Unexpected missing addonId in the installAsTemporaryAddon result'\n            );\n          }\n\n          this.reloadableExtensions.set(extension.sourceDir, addonId);\n        } catch (error) {\n          if (error instanceof RemoteTempInstallNotSupported) {\n            log.debug(`Caught: ${String(error)}`);\n            throw new WebExtError(\n              'Temporary add-on installation is not supported in this version' +\n                ' of Firefox (you need Firefox 49 or higher). For older Firefox' +\n                ' versions, use --pre-install'\n            );\n          } else {\n            throw error;\n          }\n        }\n      }\n    }\n  }\n}\n"], "mappings": "AAEA;AACA;AACA;AACA;AAEA;AAGA,SACEA,0BAA0B,EAC1BC,6BAA6B,EAC7BC,WAAW,QACN,cAAc;AACrB,OAAO,KAAKC,iBAAiB,MAAM,qBAAqB;AACxD,SAASC,qBAAqB,IAAIC,uBAAuB,QAAQ,sBAAsB;AACvF,SAASC,YAAY,QAAQ,mBAAmB;AAChD;;AAOqD;;AAoBrD,MAAMC,GAAG,GAAGD,YAAY,CAACE,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC;;AAEzC;AACA;AACA;AACA,OAAO,MAAMC,6BAA6B,CAAC;EAIzC;;EAKAC,WAAWA,CAACC,MAA2C,EAAE;IACvD,IAAI,CAACA,MAAM,GAAGA,MAAM;IAEpB,IAAI,CAACC,oBAAoB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACrC,IAAI,CAACC,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC;EACnC;;EAEA;;EAEA;AACF;AACA;EACEC,OAAOA,CAAA,EAAW;IAChB,OAAO,iBAAiB;EAC1B;;EAEA;AACF;AACA;EACE,MAAMC,GAAGA,CAAA,EAAkB;IACzB;IACA;IACA,MAAM,IAAI,CAACC,eAAe,CAAC,CAAC;;IAE5B;IACA;IACA;IACA;IACA,MAAM,IAAI,CAACC,oBAAoB,CAAC,CAAC;EACnC;;EAEA;AACF;AACA;AACA;EACE,MAAMC,mBAAmBA,CAAA,EAAgD;IACvE,MAAMC,UAAU,GAAG,IAAI,CAACL,OAAO,CAAC,CAAC;IACjC,MAAMM,YAAY,GAAG,IAAIT,GAAG,CAAC,CAAC;IAC9B,KAAK,MAAM;MAAEU;IAAU,CAAC,IAAI,IAAI,CAACZ,MAAM,CAACa,UAAU,EAAE;MAClD,MAAM,CAACC,GAAG,CAAC,GAAG,MAAM,IAAI,CAACC,0BAA0B,CAACH,SAAS,CAAC;MAC9D,IAAIE,GAAG,CAACE,WAAW,YAAYC,KAAK,EAAE;QACpCN,YAAY,CAACO,GAAG,CAACN,SAAS,EAAEE,GAAG,CAACE,WAAW,CAAC;MAC9C;IACF;IAEA,IAAIL,YAAY,CAACQ,IAAI,GAAG,CAAC,EAAE;MACzB,OAAO,CACL;QACET,UAAU;QACVM,WAAW,EAAE,IAAI7B,0BAA0B,CAACwB,YAAY;MAC1D,CAAC,CACF;IACH;IAEA,OAAO,CAAC;MAAED;IAAW,CAAC,CAAC;EACzB;;EAEA;AACF;AACA;AACA;EACE,MAAMK,0BAA0BA,CAC9BK,kBAA0B,EACmB;IAC7C,MAAMV,UAAU,GAAG,IAAI,CAACL,OAAO,CAAC,CAAC;IACjC,MAAMgB,OAAO,GAAG,IAAI,CAACpB,oBAAoB,CAACqB,GAAG,CAACF,kBAAkB,CAAC;IAEjE,IAAI,CAACC,OAAO,EAAE;MACZ,OAAO,CACL;QACET,SAAS,EAAEQ,kBAAkB;QAC7BJ,WAAW,EAAE,IAAI3B,WAAW,CAC1B,4BAA4B,GACzB,kCAAiC+B,kBAAmB,GACzD,CAAC;QACDV;MACF,CAAC,CACF;IACH;IAEA,IAAI;MACF,MAAM,IAAI,CAACa,aAAa,CAACC,WAAW,CAACH,OAAO,CAAC;IAC/C,CAAC,CAAC,OAAOI,KAAK,EAAE;MACd,OAAO,CACL;QACEb,SAAS,EAAEQ,kBAAkB;QAC7BJ,WAAW,EAAES,KAAK;QAClBf;MACF,CAAC,CACF;IACH;IAEA,OAAO,CAAC;MAAEA,UAAU;MAAEE,SAAS,EAAEQ;IAAmB,CAAC,CAAC;EACxD;;EAEA;AACF;AACA;AACA;AACA;EACEM,eAAeA,CAACC,EAAY,EAAQ;IAClC,IAAI,CAACxB,gBAAgB,CAACyB,GAAG,CAACD,EAAE,CAAC;EAC/B;;EAEA;AACF;AACA;EACE,MAAME,IAAIA,CAAA,EAAkB;IAC1B,IAAI,CAAC,IAAI,CAACC,WAAW,IAAI,CAAC,IAAI,CAACA,WAAW,CAACC,OAAO,EAAE;MAClD,MAAM,IAAI1C,WAAW,CAAC,0CAA0C,CAAC;IACnE;IAEA,IAAI,CAACyC,WAAW,CAACC,OAAO,CAACC,IAAI,CAAC,CAAC;EACjC;;EAEA;;EAEA,MAAMzB,eAAeA,CAAA,EAAG;IACtB,MAAM;MACJ0B,WAAW;MACXpB,UAAU;MACVqB,kBAAkB;MAClBC,UAAU;MACVC,WAAW;MACXC;IACF,CAAC,GAAG,IAAI,CAACrC,MAAM;IAEf,IAAIoC,WAAW,EAAE;MACf,IAAIF,kBAAkB,EAAE;QACtBxC,GAAG,CAAC4C,KAAK,CAAE,8BAA6BF,WAAY,EAAC,CAAC;QACtD,IAAI,CAACG,OAAO,GAAG,MAAMF,UAAU,CAACG,UAAU,CAACJ,WAAW,EAAE;UACtDH;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACLvC,GAAG,CAAC4C,KAAK,CAAE,gCAA+BF,WAAY,EAAC,CAAC;QACxD,IAAI,CAACG,OAAO,GAAG,MAAMF,UAAU,CAACI,WAAW,CAACL,WAAW,EAAE;UACvDH;QACF,CAAC,CAAC;MACJ;IACF,CAAC,MAAM;MACLvC,GAAG,CAAC4C,KAAK,CAAC,8BAA8B,CAAC;MACzC,IAAI,CAACC,OAAO,GAAG,MAAMF,UAAU,CAACK,aAAa,CAAC;QAAET;MAAY,CAAC,CAAC;IAChE;;IAEA;IACA,IAAIE,UAAU,EAAE;MACd,KAAK,MAAMQ,SAAS,IAAI9B,UAAU,EAAE;QAClC,MAAMwB,UAAU,CAACO,gBAAgB,CAAC;UAChCC,OAAO,EAAE,IAAI;UACbC,aAAa,EAAEH,SAAS,CAAC/B,SAAS;UAClCmC,YAAY,EAAEJ,SAAS,CAACI,YAAY;UACpCR,OAAO,EAAE,IAAI,CAACA;QAChB,CAAC,CAAC;MACJ;IACF;EACF;EAEA,MAAM/B,oBAAoBA,CAAA,EAAG;IAC3B,MAAM;MACJwC,cAAc;MACdC,QAAQ;MACRpC,UAAU;MACVqC,aAAa;MACbf,UAAU;MACVgB,QAAQ;MACRd,UAAU;MACVe,aAAa;MACbC;IACF,CAAC,GAAG,IAAI,CAACrD,MAAM;IAEf,MAAMsD,UAAU,GAAG,EAAE;IAErB,IAAIN,cAAc,EAAE;MAClBM,UAAU,CAACC,IAAI,CAAC,YAAY,CAAC;IAC/B;IACA,IAAIJ,QAAQ,EAAE;MACZ,MAAMK,IAAI,GAAGC,KAAK,CAACC,OAAO,CAACP,QAAQ,CAAC,GAAGA,QAAQ,GAAG,CAACA,QAAQ,CAAC;MAC5D,KAAK,MAAMtD,GAAG,IAAI2D,IAAI,EAAE;QACtBF,UAAU,CAACC,IAAI,CAAC,OAAO,EAAE1D,GAAG,CAAC;MAC/B;IACF;IAEA,IAAIwD,IAAI,EAAE;MACRC,UAAU,CAACC,IAAI,CAAC,GAAGF,IAAI,CAAC;IAC1B;IAEA,IAAI,CAACvB,WAAW,GAAG,MAAMO,UAAU,CAAC/B,GAAG,CAAC,IAAI,CAACiC,OAAO,EAAE;MACpDW,aAAa;MACbI,UAAU;MACVzC,UAAU;MACVoC;IACF,CAAC,CAAC;IAEF,IAAI,CAACnB,WAAW,CAACC,OAAO,CAAC4B,EAAE,CAAC,OAAO,EAAE,MAAM;MACzC,KAAK,MAAMC,SAAS,IAAI,IAAI,CAACzD,gBAAgB,EAAE;QAC7C,IAAI;UACFyD,SAAS,CAAC,CAAC;QACb,CAAC,CAAC,OAAOnC,KAAK,EAAE;UACd/B,GAAG,CAAC+B,KAAK,CAAE,4CAA2CA,KAAM,EAAC,CAAC;QAChE;MACF;IACF,CAAC,CAAC;IAEF,IAAI,CAACU,UAAU,EAAE;MACf,MAAMZ,aAAa,GAAI,IAAI,CAACA,aAAa,GAAG,MAAM6B,aAAa,CAAC;QAC9DS,IAAI,EAAE,IAAI,CAAC/B,WAAW,CAACgC;MACzB,CAAC,CAAE;;MAEH;MACA,KAAK,MAAMnB,SAAS,IAAI9B,UAAU,EAAE;QAClC,IAAI;UACF,MAAMQ,OAAO,GAAG,MAAME,aAAa,CAChCwC,qBAAqB,CAACpB,SAAS,CAAC/B,SAAS,EAAEqC,QAAQ,CAAC,CACpDe,IAAI,CAAEC,aAAsC,IAAK;YAChD,OAAOA,aAAa,CAACC,KAAK,CAACC,EAAE;UAC/B,CAAC,CAAC;UAEJ,IAAI,CAAC9C,OAAO,EAAE;YACZ,MAAM,IAAIhC,WAAW,CACnB,kEACF,CAAC;UACH;UAEA,IAAI,CAACY,oBAAoB,CAACiB,GAAG,CAACyB,SAAS,CAAC/B,SAAS,EAAES,OAAO,CAAC;QAC7D,CAAC,CAAC,OAAOI,KAAK,EAAE;UACd,IAAIA,KAAK,YAAYrC,6BAA6B,EAAE;YAClDM,GAAG,CAAC4C,KAAK,CAAE,WAAU8B,MAAM,CAAC3C,KAAK,CAAE,EAAC,CAAC;YACrC,MAAM,IAAIpC,WAAW,CACnB,gEAAgE,GAC9D,gEAAgE,GAChE,8BACJ,CAAC;UACH,CAAC,MAAM;YACL,MAAMoC,KAAK;UACb;QACF;MACF;IACF;EACF;AACF"}