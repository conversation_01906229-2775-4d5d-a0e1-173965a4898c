{"version": 3, "file": "base.js", "names": ["showDesktopNotification", "defaultDesktopNotifications"], "sources": ["../../src/extension-runners/base.js"], "sourcesContent": ["/* @flow */\n\nimport { showDesktopNotification as defaultDesktopNotifications } from '../util/desktop-notifier';\nimport type { ExtensionManifest } from '../util/manifest';\n\nexport type Extension = {|\n  sourceDir: string,\n  manifestData: ExtensionManifest,\n|};\n\nexport type ExtensionRunnerParams = {|\n  // Common cli params.\n  extensions: Array<Extension>,\n  profilePath?: string,\n  keepProfileChanges: boolean,\n  startUrl: ?string | ?Array<string>,\n  args?: Array<string>,\n\n  // Common injected dependencies.\n  desktopNotifications: typeof defaultDesktopNotifications,\n|};\n\nexport type ExtensionRunnerReloadResult = {|\n  runnerName: string,\n  reloadError?: Error,\n  sourceDir?: string,\n|};\n\nexport interface IExtensionRunner {\n  getName(): string;\n  run(): Promise<void>;\n  reloadAllExtensions(): Promise<Array<ExtensionRunnerReloadResult>>;\n  reloadExtensionBySourceDir(\n    extensionSourceDir: string\n  ): Promise<Array<ExtensionRunnerReloadResult>>;\n  registerCleanup(fn: Function): void;\n  exit(): Promise<void>;\n}\n"], "mappings": "AAEA,SAASA,uBAAuB,IAAIC,2BAA2B,QAAQ,0BAA0B"}