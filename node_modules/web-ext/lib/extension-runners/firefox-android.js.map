{"version": 3, "file": "firefox-android.js", "names": ["path", "readline", "withTempDir", "DefaultADBUtils", "showDesktopNotification", "defaultDesktopNotifications", "MultiExtensionsReloadError", "UsageError", "WebExtError", "defaultFirefoxApp", "connectWithMaxRetries", "defaultFirefoxConnector", "findFreeTcpPort", "createLogger", "isTTY", "setRawMode", "log", "import", "meta", "url", "ignoredParams", "profilePath", "keepProfileChanges", "browserConsole", "preInstall", "startUrl", "args", "DEFAULT_ADB_HOST", "getIgnoredParamsWarningsMessage", "optionName", "FirefoxAndroidExtensionRunner", "unixSocketDiscoveryRetryInterval", "unixSocketDiscoveryMaxTime", "constructor", "params", "cleanupCallbacks", "Set", "adbExtensionsPathBySourceDir", "Map", "reloadableExtensions", "printIgnoredParamsWarnings", "run", "adbBin", "adbHost", "adbPort", "ADBUtils", "adbUtils", "adbDevicesDiscoveryAndSelect", "apkPackagesDiscoveryAndSelect", "adbForceStopSelectedPackage", "adbPrepareProfileDir", "Promise", "all", "adbStartSelectedPackage", "buildAndPushExtensions", "adbDiscoveryAndForwardRDPUnixSocket", "rdpInstallExtensions", "getName", "reloadAllExtensions", "<PERSON><PERSON><PERSON>", "reloadErrors", "sourceDir", "extensions", "res", "reloadExtensionBySourceDir", "reloadError", "Error", "set", "size", "extensionSourceDir", "addonId", "get", "buildAndPushExtension", "remoteFirefox", "reloadAddon", "error", "registerCleanup", "fn", "add", "exit", "selectedAdbDevice", "selectedArtifactsDir", "exiting", "debug", "clearArtifactsDir", "getDeviceProfileDir", "Object", "keys", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON>", "warn", "adbDevice", "devices", "discoverDevices", "length", "devicesMsg", "map", "dev", "join", "info", "foundDevices", "filter", "device", "JSON", "stringify", "firefoxApk", "packages", "discoverInstalledFirefoxAPKs", "pkgsListMsg", "pkgs", "pkg", "selectedFirefoxApk", "filteredPackages", "line", "pkgsList", "amForceStopAPK", "customPrefs", "firefoxApp", "adbRemoveOldArtifacts", "profile", "createProfile", "app", "foundOldArtifacts", "detectOrRemoveOldArtifacts", "getOrCreateArtifactsDir", "deviceProfileDir", "runShellCommand", "pushFile", "profileDir", "firefoxApkComponent", "startFirefoxAPK", "buildSourceDir", "tmpDir", "extensionPath", "extFileName", "basename", "adbExtensionPath", "adbDiscoveryTimeout", "stdin", "process", "handleCtrlC", "str", "key", "ctrl", "name", "setUserAbortDiscovery", "emitKeypressEvents", "on", "selectedRDPSocketFile", "discoverRDPUnixSocket", "maxDiscoveryTime", "retryInterval", "removeListener", "tcpPort", "forwardSocketSpec", "startsWith", "substr", "setupForward", "selectedTCPPort", "firefoxClient", "port", "client", "extension", "installTemporaryAddon", "then", "installResult", "addon", "id"], "sources": ["../../src/extension-runners/firefox-android.js"], "sourcesContent": ["/* @flow */\n\n/**\n * This module provide an ExtensionRunner subclass that manage an extension executed\n * in a Firefox for Android instance.\n */\n\nimport path from 'path';\nimport readline from 'readline';\n\nimport { withTempDir } from '../util/temp-dir.js';\nimport DefaultADBUtils from '../util/adb.js';\nimport { showDesktopNotification as defaultDesktopNotifications } from '../util/desktop-notifier.js';\nimport {\n  MultiExtensionsReloadError,\n  UsageError,\n  WebExtError,\n} from '../errors.js';\nimport * as defaultFirefoxApp from '../firefox/index.js';\nimport {\n  connectWithMaxRetries as defaultFirefoxConnector,\n  findFreeTcpPort,\n} from '../firefox/remote.js';\nimport { createLogger } from '../util/logger.js';\nimport { isTTY, setRawMode } from '../util/stdin.js';\nimport type {\n  ExtensionRunnerParams,\n  ExtensionRunnerReloadResult,\n} from './base';\nimport type { FirefoxPreferences } from '../firefox/preferences';\nimport type { FirefoxRDPResponseAddon, RemoteFirefox } from '../firefox/remote';\nimport type { ExtensionBuildResult } from '../cmd/build';\n\nconst log = createLogger(import.meta.url);\n\nconst ignoredParams = {\n  profilePath: '--profile-path',\n  keepProfileChanges: '--keep-profile-changes',\n  browserConsole: '--browser-console',\n  preInstall: '--pre-install',\n  startUrl: '--start-url',\n  args: '--args',\n};\n\n// Default adbHost to 127.0.0.1 to prevent issues with nodejs 17\n// (because if not specified adbkit may default to ipv6 while\n// adb may still only be listening on the ipv4 address),\n// see https://github.com/mozilla/web-ext/issues/2337.\nconst DEFAULT_ADB_HOST = '127.0.0.1';\n\nconst getIgnoredParamsWarningsMessage = (optionName) => {\n  return `The Firefox for Android target does not support ${optionName}`;\n};\n\nexport type FirefoxAndroidExtensionRunnerParams = {|\n  ...ExtensionRunnerParams,\n\n  // Firefox specific.\n  customPrefs?: FirefoxPreferences,\n\n  // Not supported (currently ignored with logged warning).\n  preInstall?: boolean,\n  browserConsole?: boolean,\n\n  // Firefox android injected dependencies.\n  adbBin?: string,\n  adbHost?: string,\n  adbPort?: string,\n  adbDevice?: string,\n  adbDiscoveryTimeout?: number,\n  adbRemoveOldArtifacts?: boolean,\n  firefoxApk?: string,\n  firefoxApkComponent?: string,\n\n  // Injected Dependencies.\n  firefoxApp: typeof defaultFirefoxApp,\n  firefoxClient: typeof defaultFirefoxConnector,\n  ADBUtils?: typeof DefaultADBUtils,\n  buildSourceDir: (string, string) => Promise<ExtensionBuildResult>,\n  desktopNotifications: typeof defaultDesktopNotifications,\n  stdin?: stream$Readable,\n|};\n\n/**\n * Implements an IExtensionRunner which manages a Firefox for Android instance.\n */\nexport class FirefoxAndroidExtensionRunner {\n  // Wait 3s before the next unix socket discovery loop.\n  static unixSocketDiscoveryRetryInterval: number = 3 * 1000;\n  // Wait for at most 3 minutes before giving up.\n  static unixSocketDiscoveryMaxTime: number = 3 * 60 * 1000;\n\n  params: FirefoxAndroidExtensionRunnerParams;\n  adbUtils: DefaultADBUtils;\n  exiting: boolean;\n  selectedAdbDevice: string;\n  selectedFirefoxApk: string;\n  selectedArtifactsDir: string;\n  selectedRDPSocketFile: string;\n  selectedTCPPort: number;\n  cleanupCallbacks: Set<Function>;\n  adbExtensionsPathBySourceDir: Map<string, string>;\n  reloadableExtensions: Map<string, string>;\n  remoteFirefox: RemoteFirefox;\n\n  constructor(params: FirefoxAndroidExtensionRunnerParams) {\n    this.params = params;\n    this.cleanupCallbacks = new Set();\n    this.adbExtensionsPathBySourceDir = new Map();\n    this.reloadableExtensions = new Map();\n\n    // Print warning for not currently supported options (e.g. preInstall,\n    // cloned profiles, browser console).\n    this.printIgnoredParamsWarnings();\n  }\n\n  async run(): Promise<void> {\n    const {\n      adbBin,\n      adbHost = DEFAULT_ADB_HOST,\n      adbPort,\n      ADBUtils = DefaultADBUtils,\n    } = this.params;\n\n    this.adbUtils = new ADBUtils({\n      adbBin,\n      adbHost,\n      adbPort,\n    });\n\n    await this.adbDevicesDiscoveryAndSelect();\n    await this.apkPackagesDiscoveryAndSelect();\n    await this.adbForceStopSelectedPackage();\n\n    // Create profile prefs (with enabled remote RDP server), prepare the\n    // artifacts and temporary directory on the selected device, and\n    // push the profile preferences to the remote profile dir.\n    await this.adbPrepareProfileDir();\n\n    // NOTE: running Firefox for Android on the Android Emulator can be\n    // pretty slow, we can run the following 3 steps in parallel to speed up\n    // it a bit.\n    await Promise.all([\n      // Start Firefox for Android instance if not started yet.\n      // (Fennec would run in an temporary profile and so it is explicitly\n      // stopped, Fenix runs on its usual profile and so it may be already\n      // running).\n      this.adbStartSelectedPackage(),\n\n      // Build and push to devices all the extension xpis\n      // and keep track of the xpi built and uploaded by extension sourceDir.\n      this.buildAndPushExtensions(),\n\n      // Wait for RDP unix socket file created and\n      // Create an ADB forward connection on a free tcp port\n      this.adbDiscoveryAndForwardRDPUnixSocket(),\n    ]);\n\n    // Connect to RDP socket on the local tcp server, install all the pushed extension\n    // and keep track of the built and installed extension by extension sourceDir.\n    await this.rdpInstallExtensions();\n  }\n\n  // Method exported from the IExtensionRunner interface.\n\n  /**\n   * Returns the runner name.\n   */\n  getName(): string {\n    return 'Firefox Android';\n  }\n\n  /**\n   * Reloads all the extensions, collect any reload error and resolves to\n   * an array composed by a single ExtensionRunnerReloadResult object.\n   */\n  async reloadAllExtensions(): Promise<Array<ExtensionRunnerReloadResult>> {\n    const runnerName = this.getName();\n    const reloadErrors = new Map();\n\n    for (const { sourceDir } of this.params.extensions) {\n      const [res] = await this.reloadExtensionBySourceDir(sourceDir);\n      if (res.reloadError instanceof Error) {\n        reloadErrors.set(sourceDir, res.reloadError);\n      }\n    }\n\n    if (reloadErrors.size > 0) {\n      return [\n        {\n          runnerName,\n          reloadError: new MultiExtensionsReloadError(reloadErrors),\n        },\n      ];\n    }\n\n    return [{ runnerName }];\n  }\n\n  /**\n   * Reloads a single extension, collect any reload error and resolves to\n   * an array composed by a single ExtensionRunnerReloadResult object.\n   */\n  async reloadExtensionBySourceDir(\n    extensionSourceDir: string\n  ): Promise<Array<ExtensionRunnerReloadResult>> {\n    const runnerName = this.getName();\n    const addonId = this.reloadableExtensions.get(extensionSourceDir);\n\n    if (!addonId) {\n      return [\n        {\n          sourceDir: extensionSourceDir,\n          reloadError: new WebExtError(\n            'Extension not reloadable: ' +\n              `no addonId has been mapped to \"${extensionSourceDir}\"`\n          ),\n          runnerName,\n        },\n      ];\n    }\n\n    try {\n      await this.buildAndPushExtension(extensionSourceDir);\n      await this.remoteFirefox.reloadAddon(addonId);\n    } catch (error) {\n      return [\n        {\n          sourceDir: extensionSourceDir,\n          reloadError: error,\n          runnerName,\n        },\n      ];\n    }\n\n    return [{ runnerName, sourceDir: extensionSourceDir }];\n  }\n\n  /**\n   * Register a callback to be called when the runner has been exited\n   * (e.g. the Firefox instance exits or the user has requested web-ext\n   * to exit).\n   */\n  registerCleanup(fn: Function): void {\n    this.cleanupCallbacks.add(fn);\n  }\n\n  /**\n   * Exits the runner, by closing the managed Firefox instance.\n   */\n  async exit(): Promise<void> {\n    const { adbUtils, selectedAdbDevice, selectedArtifactsDir } = this;\n\n    this.exiting = true;\n\n    // If a Firefox for Android instance has been started,\n    // we should ensure that it has been stopped when we exit.\n    await this.adbForceStopSelectedPackage();\n\n    if (selectedArtifactsDir) {\n      log.debug('Cleaning up artifacts directory on the Android device...');\n      await adbUtils.clearArtifactsDir(selectedAdbDevice);\n    }\n\n    // Call all the registered cleanup callbacks.\n    for (const fn of this.cleanupCallbacks) {\n      try {\n        fn();\n      } catch (error) {\n        log.error(error);\n      }\n    }\n  }\n\n  // Private helper methods.\n\n  getDeviceProfileDir(): string {\n    return `${this.selectedArtifactsDir}/profile`;\n  }\n\n  printIgnoredParamsWarnings() {\n    Object.keys(ignoredParams).forEach((ignoredParam) => {\n      if (this.params[ignoredParam]) {\n        log.warn(getIgnoredParamsWarningsMessage(ignoredParams[ignoredParam]));\n      }\n    });\n  }\n\n  async adbDevicesDiscoveryAndSelect() {\n    const { adbUtils } = this;\n    const { adbDevice } = this.params;\n    let devices = [];\n\n    log.debug('Listing android devices');\n    devices = await adbUtils.discoverDevices();\n\n    if (devices.length === 0) {\n      throw new UsageError(\n        'No Android device found through ADB. ' +\n          'Make sure the device is connected and USB debugging is enabled.'\n      );\n    }\n\n    if (!adbDevice) {\n      const devicesMsg = devices.map((dev) => ` - ${dev}`).join('\\n');\n      log.info(`\\nAndroid devices found:\\n${devicesMsg}`);\n      throw new UsageError(\n        'Select an android device using --android-device=<name>'\n      );\n    }\n\n    const foundDevices = devices.filter((device) => {\n      return device === adbDevice;\n    });\n\n    if (foundDevices.length === 0) {\n      const devicesMsg = JSON.stringify(devices);\n      throw new UsageError(\n        `Android device ${adbDevice} was not found in list: ${devicesMsg}`\n      );\n    }\n\n    this.selectedAdbDevice = foundDevices[0];\n    log.info(`Selected ADB device: ${this.selectedAdbDevice}`);\n  }\n\n  async apkPackagesDiscoveryAndSelect() {\n    const {\n      adbUtils,\n      selectedAdbDevice,\n      params: { firefoxApk },\n    } = this;\n    // Discovery and select a Firefox for Android version.\n    const packages = await adbUtils.discoverInstalledFirefoxAPKs(\n      selectedAdbDevice,\n      firefoxApk\n    );\n\n    if (packages.length === 0) {\n      throw new UsageError(\n        'No Firefox packages were found on the selected Android device'\n      );\n    }\n\n    const pkgsListMsg = (pkgs) => {\n      return pkgs.map((pkg) => ` - ${pkg}`).join('\\n');\n    };\n\n    if (!firefoxApk) {\n      log.info(`\\nPackages found:\\n${pkgsListMsg(packages)}`);\n\n      if (packages.length > 1) {\n        throw new UsageError('Select one of the packages using --firefox-apk');\n      }\n\n      // If only one APK has been found, select it even if it has not been\n      // specified explicitly on the comment line.\n      this.selectedFirefoxApk = packages[0];\n      log.info(`Selected Firefox for Android APK: ${this.selectedFirefoxApk}`);\n      return;\n    }\n\n    const filteredPackages = packages.filter((line) => line === firefoxApk);\n\n    if (filteredPackages.length === 0) {\n      const pkgsList = pkgsListMsg(filteredPackages);\n      throw new UsageError(\n        `Package ${firefoxApk} was not found in list: ${pkgsList}`\n      );\n    }\n\n    this.selectedFirefoxApk = filteredPackages[0];\n    log.debug(`Selected Firefox for Android APK: ${this.selectedFirefoxApk}`);\n  }\n\n  async adbForceStopSelectedPackage() {\n    const { adbUtils, selectedAdbDevice, selectedFirefoxApk } = this;\n\n    log.info(`Stopping existing instances of ${selectedFirefoxApk}...`);\n    await adbUtils.amForceStopAPK(selectedAdbDevice, selectedFirefoxApk);\n  }\n\n  async adbPrepareProfileDir() {\n    const {\n      adbUtils,\n      selectedAdbDevice,\n      selectedFirefoxApk,\n      params: { customPrefs, firefoxApp, adbRemoveOldArtifacts },\n    } = this;\n    // Create the preferences file and the Fennec temporary profile.\n    log.debug(`Preparing a temporary profile for ${selectedFirefoxApk}...`);\n\n    const profile = await firefoxApp.createProfile({\n      app: 'fennec',\n      customPrefs,\n    });\n\n    // Check if there are any artifacts dirs from previous runs and\n    // automatically remove them if adbRemoteOldArtifacts is true.\n    const foundOldArtifacts = await adbUtils.detectOrRemoveOldArtifacts(\n      selectedAdbDevice,\n      adbRemoveOldArtifacts\n    );\n\n    if (foundOldArtifacts) {\n      if (adbRemoveOldArtifacts) {\n        log.info(\n          'Old web-ext artifacts have been found and removed ' +\n            `from ${selectedAdbDevice} device`\n        );\n      } else {\n        log.warn(\n          `Old artifacts directories have been found on ${selectedAdbDevice} ` +\n            'device. Use --adb-remove-old-artifacts to remove them automatically.'\n        );\n      }\n    }\n\n    // Choose a artifacts dir name for the assets pushed to the\n    // Android device.\n    this.selectedArtifactsDir = await adbUtils.getOrCreateArtifactsDir(\n      selectedAdbDevice\n    );\n\n    const deviceProfileDir = this.getDeviceProfileDir();\n\n    await adbUtils.runShellCommand(selectedAdbDevice, [\n      'mkdir',\n      '-p',\n      deviceProfileDir,\n    ]);\n    await adbUtils.pushFile(\n      selectedAdbDevice,\n      path.join(profile.profileDir, 'user.js'),\n      `${deviceProfileDir}/user.js`\n    );\n\n    log.debug(`Created temporary profile at ${deviceProfileDir}.`);\n  }\n\n  async adbStartSelectedPackage() {\n    const {\n      adbUtils,\n      selectedFirefoxApk,\n      selectedAdbDevice,\n      params: { firefoxApkComponent },\n    } = this;\n\n    const deviceProfileDir = this.getDeviceProfileDir();\n\n    log.info(`Starting ${selectedFirefoxApk}...`);\n\n    log.debug(`Using profile ${deviceProfileDir} (ignored by Fenix)`);\n\n    await adbUtils.startFirefoxAPK(\n      selectedAdbDevice,\n      selectedFirefoxApk,\n      firefoxApkComponent,\n      deviceProfileDir\n    );\n  }\n\n  async buildAndPushExtension(sourceDir: string) {\n    const {\n      adbUtils,\n      selectedAdbDevice,\n      selectedArtifactsDir,\n      params: { buildSourceDir },\n    } = this;\n\n    await withTempDir(async (tmpDir) => {\n      const { extensionPath } = await buildSourceDir(sourceDir, tmpDir.path());\n\n      const extFileName = path.basename(extensionPath, '.zip');\n\n      let adbExtensionPath = this.adbExtensionsPathBySourceDir.get(sourceDir);\n\n      if (!adbExtensionPath) {\n        adbExtensionPath = `${selectedArtifactsDir}/${extFileName}.xpi`;\n      }\n\n      log.debug(`Uploading ${extFileName} on the android device`);\n\n      await adbUtils.pushFile(\n        selectedAdbDevice,\n        extensionPath,\n        adbExtensionPath\n      );\n\n      log.debug(`Upload completed: ${adbExtensionPath}`);\n\n      this.adbExtensionsPathBySourceDir.set(sourceDir, adbExtensionPath);\n    });\n  }\n\n  async buildAndPushExtensions() {\n    for (const { sourceDir } of this.params.extensions) {\n      await this.buildAndPushExtension(sourceDir);\n    }\n  }\n\n  async adbDiscoveryAndForwardRDPUnixSocket() {\n    const {\n      adbUtils,\n      selectedAdbDevice,\n      selectedFirefoxApk,\n      params: { adbDiscoveryTimeout },\n    } = this;\n\n    const stdin = this.params.stdin || process.stdin;\n\n    const { unixSocketDiscoveryRetryInterval } = FirefoxAndroidExtensionRunner;\n\n    let { unixSocketDiscoveryMaxTime } = FirefoxAndroidExtensionRunner;\n\n    if (typeof adbDiscoveryTimeout === 'number') {\n      unixSocketDiscoveryMaxTime = adbDiscoveryTimeout;\n    }\n\n    const handleCtrlC = (str, key) => {\n      if (key.ctrl && key.name === 'c') {\n        adbUtils.setUserAbortDiscovery(true);\n      }\n    };\n\n    // TODO: use noInput property to decide if we should\n    // disable direct keypress handling.\n    if (isTTY(stdin)) {\n      readline.emitKeypressEvents(stdin);\n      setRawMode(stdin, true);\n\n      stdin.on('keypress', handleCtrlC);\n    }\n\n    try {\n      // Got a debugger socket file to connect.\n      this.selectedRDPSocketFile = await adbUtils.discoverRDPUnixSocket(\n        selectedAdbDevice,\n        selectedFirefoxApk,\n        {\n          maxDiscoveryTime: unixSocketDiscoveryMaxTime,\n          retryInterval: unixSocketDiscoveryRetryInterval,\n        }\n      );\n    } finally {\n      if (isTTY(stdin)) {\n        stdin.removeListener('keypress', handleCtrlC);\n      }\n    }\n\n    log.debug(`RDP Socket File selected: ${this.selectedRDPSocketFile}`);\n\n    const tcpPort = await findFreeTcpPort();\n\n    // Log the choosen tcp port at info level (useful to the user to be able\n    // to connect the Firefox DevTools to the Firefox for Android instance).\n    log.info(`You can connect to this Android device on TCP port ${tcpPort}`);\n\n    const forwardSocketSpec = this.selectedRDPSocketFile.startsWith('@')\n      ? `localabstract:${this.selectedRDPSocketFile.substr(1)}`\n      : `localfilesystem:${this.selectedRDPSocketFile}`;\n\n    await adbUtils.setupForward(\n      selectedAdbDevice,\n      forwardSocketSpec,\n      `tcp:${tcpPort}`\n    );\n\n    this.selectedTCPPort = tcpPort;\n  }\n\n  async rdpInstallExtensions() {\n    const {\n      selectedTCPPort,\n      params: { extensions, firefoxClient },\n    } = this;\n\n    const remoteFirefox = (this.remoteFirefox = await firefoxClient({\n      port: selectedTCPPort,\n    }));\n\n    // Exit and cleanup the extension runner if the connection to the\n    // remote Firefox for Android instance has been closed.\n    remoteFirefox.client.on('end', () => {\n      if (!this.exiting) {\n        log.info('Exiting the device because Firefox for Android disconnected');\n        this.exit();\n      }\n    });\n\n    // Install all the temporary addons.\n    for (const extension of extensions) {\n      const { sourceDir } = extension;\n      const adbExtensionPath = this.adbExtensionsPathBySourceDir.get(sourceDir);\n\n      if (!adbExtensionPath) {\n        throw new WebExtError(\n          `ADB extension path for \"${sourceDir}\" was unexpectedly empty`\n        );\n      }\n\n      const addonId = await remoteFirefox\n        .installTemporaryAddon(adbExtensionPath)\n        .then((installResult: FirefoxRDPResponseAddon) => {\n          return installResult.addon.id;\n        });\n\n      if (!addonId) {\n        throw new WebExtError(\n          'Received an empty addonId from ' +\n            `remoteFirefox.installTemporaryAddon(\"${adbExtensionPath}\")`\n        );\n      }\n\n      this.reloadableExtensions.set(extension.sourceDir, addonId);\n    }\n  }\n}\n"], "mappings": "AAEA;AACA;AACA;AACA;AAEA,OAAOA,IAAI,MAAM,MAAM;AACvB,OAAOC,QAAQ,MAAM,UAAU;AAE/B,SAASC,WAAW,QAAQ,qBAAqB;AACjD,OAAOC,eAAe,MAAM,gBAAgB;AAC5C,SAASC,uBAAuB,IAAIC,2BAA2B,QAAQ,6BAA6B;AACpG,SACEC,0BAA0B,EAC1BC,UAAU,EACVC,WAAW,QACN,cAAc;AACrB,OAAO,KAAKC,iBAAiB,MAAM,qBAAqB;AACxD,SACEC,qBAAqB,IAAIC,uBAAuB,EAChDC,eAAe,QACV,sBAAsB;AAC7B,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,KAAK,EAAEC,UAAU,QAAQ,kBAAkB;AASpD,MAAMC,GAAG,GAAGH,YAAY,CAACI,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC;AAEzC,MAAMC,aAAa,GAAG;EACpBC,WAAW,EAAE,gBAAgB;EAC7BC,kBAAkB,EAAE,wBAAwB;EAC5CC,cAAc,EAAE,mBAAmB;EACnCC,UAAU,EAAE,eAAe;EAC3BC,QAAQ,EAAE,aAAa;EACvBC,IAAI,EAAE;AACR,CAAC;;AAED;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,GAAG,WAAW;AAEpC,MAAMC,+BAA+B,GAAIC,UAAU,IAAK;EACtD,OAAQ,mDAAkDA,UAAW,EAAC;AACxE,CAAC;AA+BD;AACA;AACA;AACA,OAAO,MAAMC,6BAA6B,CAAC;EACzC;EACA,OAAOC,gCAAgC,GAAW,CAAC,GAAG,IAAI;EAC1D;EACA,OAAOC,0BAA0B,GAAW,CAAC,GAAG,EAAE,GAAG,IAAI;EAezDC,WAAWA,CAACC,MAA2C,EAAE;IACvD,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACjC,IAAI,CAACC,4BAA4B,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC7C,IAAI,CAACC,oBAAoB,GAAG,IAAID,GAAG,CAAC,CAAC;;IAErC;IACA;IACA,IAAI,CAACE,0BAA0B,CAAC,CAAC;EACnC;EAEA,MAAMC,GAAGA,CAAA,EAAkB;IACzB,MAAM;MACJC,MAAM;MACNC,OAAO,GAAGhB,gBAAgB;MAC1BiB,OAAO;MACPC,QAAQ,GAAG1C;IACb,CAAC,GAAG,IAAI,CAAC+B,MAAM;IAEf,IAAI,CAACY,QAAQ,GAAG,IAAID,QAAQ,CAAC;MAC3BH,MAAM;MACNC,OAAO;MACPC;IACF,CAAC,CAAC;IAEF,MAAM,IAAI,CAACG,4BAA4B,CAAC,CAAC;IACzC,MAAM,IAAI,CAACC,6BAA6B,CAAC,CAAC;IAC1C,MAAM,IAAI,CAACC,2BAA2B,CAAC,CAAC;;IAExC;IACA;IACA;IACA,MAAM,IAAI,CAACC,oBAAoB,CAAC,CAAC;;IAEjC;IACA;IACA;IACA,MAAMC,OAAO,CAACC,GAAG,CAAC;IAChB;IACA;IACA;IACA;IACA,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAE9B;IACA;IACA,IAAI,CAACC,sBAAsB,CAAC,CAAC;IAE7B;IACA;IACA,IAAI,CAACC,mCAAmC,CAAC,CAAC,CAC3C,CAAC;;IAEF;IACA;IACA,MAAM,IAAI,CAACC,oBAAoB,CAAC,CAAC;EACnC;;EAEA;;EAEA;AACF;AACA;EACEC,OAAOA,CAAA,EAAW;IAChB,OAAO,iBAAiB;EAC1B;;EAEA;AACF;AACA;AACA;EACE,MAAMC,mBAAmBA,CAAA,EAAgD;IACvE,MAAMC,UAAU,GAAG,IAAI,CAACF,OAAO,CAAC,CAAC;IACjC,MAAMG,YAAY,GAAG,IAAItB,GAAG,CAAC,CAAC;IAE9B,KAAK,MAAM;MAAEuB;IAAU,CAAC,IAAI,IAAI,CAAC3B,MAAM,CAAC4B,UAAU,EAAE;MAClD,MAAM,CAACC,GAAG,CAAC,GAAG,MAAM,IAAI,CAACC,0BAA0B,CAACH,SAAS,CAAC;MAC9D,IAAIE,GAAG,CAACE,WAAW,YAAYC,KAAK,EAAE;QACpCN,YAAY,CAACO,GAAG,CAACN,SAAS,EAAEE,GAAG,CAACE,WAAW,CAAC;MAC9C;IACF;IAEA,IAAIL,YAAY,CAACQ,IAAI,GAAG,CAAC,EAAE;MACzB,OAAO,CACL;QACET,UAAU;QACVM,WAAW,EAAE,IAAI3D,0BAA0B,CAACsD,YAAY;MAC1D,CAAC,CACF;IACH;IAEA,OAAO,CAAC;MAAED;IAAW,CAAC,CAAC;EACzB;;EAEA;AACF;AACA;AACA;EACE,MAAMK,0BAA0BA,CAC9BK,kBAA0B,EACmB;IAC7C,MAAMV,UAAU,GAAG,IAAI,CAACF,OAAO,CAAC,CAAC;IACjC,MAAMa,OAAO,GAAG,IAAI,CAAC/B,oBAAoB,CAACgC,GAAG,CAACF,kBAAkB,CAAC;IAEjE,IAAI,CAACC,OAAO,EAAE;MACZ,OAAO,CACL;QACET,SAAS,EAAEQ,kBAAkB;QAC7BJ,WAAW,EAAE,IAAIzD,WAAW,CAC1B,4BAA4B,GACzB,kCAAiC6D,kBAAmB,GACzD,CAAC;QACDV;MACF,CAAC,CACF;IACH;IAEA,IAAI;MACF,MAAM,IAAI,CAACa,qBAAqB,CAACH,kBAAkB,CAAC;MACpD,MAAM,IAAI,CAACI,aAAa,CAACC,WAAW,CAACJ,OAAO,CAAC;IAC/C,CAAC,CAAC,OAAOK,KAAK,EAAE;MACd,OAAO,CACL;QACEd,SAAS,EAAEQ,kBAAkB;QAC7BJ,WAAW,EAAEU,KAAK;QAClBhB;MACF,CAAC,CACF;IACH;IAEA,OAAO,CAAC;MAAEA,UAAU;MAAEE,SAAS,EAAEQ;IAAmB,CAAC,CAAC;EACxD;;EAEA;AACF;AACA;AACA;AACA;EACEO,eAAeA,CAACC,EAAY,EAAQ;IAClC,IAAI,CAAC1C,gBAAgB,CAAC2C,GAAG,CAACD,EAAE,CAAC;EAC/B;;EAEA;AACF;AACA;EACE,MAAME,IAAIA,CAAA,EAAkB;IAC1B,MAAM;MAAEjC,QAAQ;MAAEkC,iBAAiB;MAAEC;IAAqB,CAAC,GAAG,IAAI;IAElE,IAAI,CAACC,OAAO,GAAG,IAAI;;IAEnB;IACA;IACA,MAAM,IAAI,CAACjC,2BAA2B,CAAC,CAAC;IAExC,IAAIgC,oBAAoB,EAAE;MACxBjE,GAAG,CAACmE,KAAK,CAAC,0DAA0D,CAAC;MACrE,MAAMrC,QAAQ,CAACsC,iBAAiB,CAACJ,iBAAiB,CAAC;IACrD;;IAEA;IACA,KAAK,MAAMH,EAAE,IAAI,IAAI,CAAC1C,gBAAgB,EAAE;MACtC,IAAI;QACF0C,EAAE,CAAC,CAAC;MACN,CAAC,CAAC,OAAOF,KAAK,EAAE;QACd3D,GAAG,CAAC2D,KAAK,CAACA,KAAK,CAAC;MAClB;IACF;EACF;;EAEA;;EAEAU,mBAAmBA,CAAA,EAAW;IAC5B,OAAQ,GAAE,IAAI,CAACJ,oBAAqB,UAAS;EAC/C;EAEAzC,0BAA0BA,CAAA,EAAG;IAC3B8C,MAAM,CAACC,IAAI,CAACnE,aAAa,CAAC,CAACoE,OAAO,CAAEC,YAAY,IAAK;MACnD,IAAI,IAAI,CAACvD,MAAM,CAACuD,YAAY,CAAC,EAAE;QAC7BzE,GAAG,CAAC0E,IAAI,CAAC9D,+BAA+B,CAACR,aAAa,CAACqE,YAAY,CAAC,CAAC,CAAC;MACxE;IACF,CAAC,CAAC;EACJ;EAEA,MAAM1C,4BAA4BA,CAAA,EAAG;IACnC,MAAM;MAAED;IAAS,CAAC,GAAG,IAAI;IACzB,MAAM;MAAE6C;IAAU,CAAC,GAAG,IAAI,CAACzD,MAAM;IACjC,IAAI0D,OAAO,GAAG,EAAE;IAEhB5E,GAAG,CAACmE,KAAK,CAAC,yBAAyB,CAAC;IACpCS,OAAO,GAAG,MAAM9C,QAAQ,CAAC+C,eAAe,CAAC,CAAC;IAE1C,IAAID,OAAO,CAACE,MAAM,KAAK,CAAC,EAAE;MACxB,MAAM,IAAIvF,UAAU,CAClB,uCAAuC,GACrC,iEACJ,CAAC;IACH;IAEA,IAAI,CAACoF,SAAS,EAAE;MACd,MAAMI,UAAU,GAAGH,OAAO,CAACI,GAAG,CAAEC,GAAG,IAAM,MAAKA,GAAI,EAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;MAC/DlF,GAAG,CAACmF,IAAI,CAAE,6BAA4BJ,UAAW,EAAC,CAAC;MACnD,MAAM,IAAIxF,UAAU,CAClB,wDACF,CAAC;IACH;IAEA,MAAM6F,YAAY,GAAGR,OAAO,CAACS,MAAM,CAAEC,MAAM,IAAK;MAC9C,OAAOA,MAAM,KAAKX,SAAS;IAC7B,CAAC,CAAC;IAEF,IAAIS,YAAY,CAACN,MAAM,KAAK,CAAC,EAAE;MAC7B,MAAMC,UAAU,GAAGQ,IAAI,CAACC,SAAS,CAACZ,OAAO,CAAC;MAC1C,MAAM,IAAIrF,UAAU,CACjB,kBAAiBoF,SAAU,2BAA0BI,UAAW,EACnE,CAAC;IACH;IAEA,IAAI,CAACf,iBAAiB,GAAGoB,YAAY,CAAC,CAAC,CAAC;IACxCpF,GAAG,CAACmF,IAAI,CAAE,wBAAuB,IAAI,CAACnB,iBAAkB,EAAC,CAAC;EAC5D;EAEA,MAAMhC,6BAA6BA,CAAA,EAAG;IACpC,MAAM;MACJF,QAAQ;MACRkC,iBAAiB;MACjB9C,MAAM,EAAE;QAAEuE;MAAW;IACvB,CAAC,GAAG,IAAI;IACR;IACA,MAAMC,QAAQ,GAAG,MAAM5D,QAAQ,CAAC6D,4BAA4B,CAC1D3B,iBAAiB,EACjByB,UACF,CAAC;IAED,IAAIC,QAAQ,CAACZ,MAAM,KAAK,CAAC,EAAE;MACzB,MAAM,IAAIvF,UAAU,CAClB,+DACF,CAAC;IACH;IAEA,MAAMqG,WAAW,GAAIC,IAAI,IAAK;MAC5B,OAAOA,IAAI,CAACb,GAAG,CAAEc,GAAG,IAAM,MAAKA,GAAI,EAAC,CAAC,CAACZ,IAAI,CAAC,IAAI,CAAC;IAClD,CAAC;IAED,IAAI,CAACO,UAAU,EAAE;MACfzF,GAAG,CAACmF,IAAI,CAAE,sBAAqBS,WAAW,CAACF,QAAQ,CAAE,EAAC,CAAC;MAEvD,IAAIA,QAAQ,CAACZ,MAAM,GAAG,CAAC,EAAE;QACvB,MAAM,IAAIvF,UAAU,CAAC,gDAAgD,CAAC;MACxE;;MAEA;MACA;MACA,IAAI,CAACwG,kBAAkB,GAAGL,QAAQ,CAAC,CAAC,CAAC;MACrC1F,GAAG,CAACmF,IAAI,CAAE,qCAAoC,IAAI,CAACY,kBAAmB,EAAC,CAAC;MACxE;IACF;IAEA,MAAMC,gBAAgB,GAAGN,QAAQ,CAACL,MAAM,CAAEY,IAAI,IAAKA,IAAI,KAAKR,UAAU,CAAC;IAEvE,IAAIO,gBAAgB,CAAClB,MAAM,KAAK,CAAC,EAAE;MACjC,MAAMoB,QAAQ,GAAGN,WAAW,CAACI,gBAAgB,CAAC;MAC9C,MAAM,IAAIzG,UAAU,CACjB,WAAUkG,UAAW,2BAA0BS,QAAS,EAC3D,CAAC;IACH;IAEA,IAAI,CAACH,kBAAkB,GAAGC,gBAAgB,CAAC,CAAC,CAAC;IAC7ChG,GAAG,CAACmE,KAAK,CAAE,qCAAoC,IAAI,CAAC4B,kBAAmB,EAAC,CAAC;EAC3E;EAEA,MAAM9D,2BAA2BA,CAAA,EAAG;IAClC,MAAM;MAAEH,QAAQ;MAAEkC,iBAAiB;MAAE+B;IAAmB,CAAC,GAAG,IAAI;IAEhE/F,GAAG,CAACmF,IAAI,CAAE,kCAAiCY,kBAAmB,KAAI,CAAC;IACnE,MAAMjE,QAAQ,CAACqE,cAAc,CAACnC,iBAAiB,EAAE+B,kBAAkB,CAAC;EACtE;EAEA,MAAM7D,oBAAoBA,CAAA,EAAG;IAC3B,MAAM;MACJJ,QAAQ;MACRkC,iBAAiB;MACjB+B,kBAAkB;MAClB7E,MAAM,EAAE;QAAEkF,WAAW;QAAEC,UAAU;QAAEC;MAAsB;IAC3D,CAAC,GAAG,IAAI;IACR;IACAtG,GAAG,CAACmE,KAAK,CAAE,qCAAoC4B,kBAAmB,KAAI,CAAC;IAEvE,MAAMQ,OAAO,GAAG,MAAMF,UAAU,CAACG,aAAa,CAAC;MAC7CC,GAAG,EAAE,QAAQ;MACbL;IACF,CAAC,CAAC;;IAEF;IACA;IACA,MAAMM,iBAAiB,GAAG,MAAM5E,QAAQ,CAAC6E,0BAA0B,CACjE3C,iBAAiB,EACjBsC,qBACF,CAAC;IAED,IAAII,iBAAiB,EAAE;MACrB,IAAIJ,qBAAqB,EAAE;QACzBtG,GAAG,CAACmF,IAAI,CACN,oDAAoD,GACjD,QAAOnB,iBAAkB,SAC9B,CAAC;MACH,CAAC,MAAM;QACLhE,GAAG,CAAC0E,IAAI,CACL,gDAA+CV,iBAAkB,GAAE,GAClE,sEACJ,CAAC;MACH;IACF;;IAEA;IACA;IACA,IAAI,CAACC,oBAAoB,GAAG,MAAMnC,QAAQ,CAAC8E,uBAAuB,CAChE5C,iBACF,CAAC;IAED,MAAM6C,gBAAgB,GAAG,IAAI,CAACxC,mBAAmB,CAAC,CAAC;IAEnD,MAAMvC,QAAQ,CAACgF,eAAe,CAAC9C,iBAAiB,EAAE,CAChD,OAAO,EACP,IAAI,EACJ6C,gBAAgB,CACjB,CAAC;IACF,MAAM/E,QAAQ,CAACiF,QAAQ,CACrB/C,iBAAiB,EACjBhF,IAAI,CAACkG,IAAI,CAACqB,OAAO,CAACS,UAAU,EAAE,SAAS,CAAC,EACvC,GAAEH,gBAAiB,UACtB,CAAC;IAED7G,GAAG,CAACmE,KAAK,CAAE,gCAA+B0C,gBAAiB,GAAE,CAAC;EAChE;EAEA,MAAMxE,uBAAuBA,CAAA,EAAG;IAC9B,MAAM;MACJP,QAAQ;MACRiE,kBAAkB;MAClB/B,iBAAiB;MACjB9C,MAAM,EAAE;QAAE+F;MAAoB;IAChC,CAAC,GAAG,IAAI;IAER,MAAMJ,gBAAgB,GAAG,IAAI,CAACxC,mBAAmB,CAAC,CAAC;IAEnDrE,GAAG,CAACmF,IAAI,CAAE,YAAWY,kBAAmB,KAAI,CAAC;IAE7C/F,GAAG,CAACmE,KAAK,CAAE,iBAAgB0C,gBAAiB,qBAAoB,CAAC;IAEjE,MAAM/E,QAAQ,CAACoF,eAAe,CAC5BlD,iBAAiB,EACjB+B,kBAAkB,EAClBkB,mBAAmB,EACnBJ,gBACF,CAAC;EACH;EAEA,MAAMrD,qBAAqBA,CAACX,SAAiB,EAAE;IAC7C,MAAM;MACJf,QAAQ;MACRkC,iBAAiB;MACjBC,oBAAoB;MACpB/C,MAAM,EAAE;QAAEiG;MAAe;IAC3B,CAAC,GAAG,IAAI;IAER,MAAMjI,WAAW,CAAC,MAAOkI,MAAM,IAAK;MAClC,MAAM;QAAEC;MAAc,CAAC,GAAG,MAAMF,cAAc,CAACtE,SAAS,EAAEuE,MAAM,CAACpI,IAAI,CAAC,CAAC,CAAC;MAExE,MAAMsI,WAAW,GAAGtI,IAAI,CAACuI,QAAQ,CAACF,aAAa,EAAE,MAAM,CAAC;MAExD,IAAIG,gBAAgB,GAAG,IAAI,CAACnG,4BAA4B,CAACkC,GAAG,CAACV,SAAS,CAAC;MAEvE,IAAI,CAAC2E,gBAAgB,EAAE;QACrBA,gBAAgB,GAAI,GAAEvD,oBAAqB,IAAGqD,WAAY,MAAK;MACjE;MAEAtH,GAAG,CAACmE,KAAK,CAAE,aAAYmD,WAAY,wBAAuB,CAAC;MAE3D,MAAMxF,QAAQ,CAACiF,QAAQ,CACrB/C,iBAAiB,EACjBqD,aAAa,EACbG,gBACF,CAAC;MAEDxH,GAAG,CAACmE,KAAK,CAAE,qBAAoBqD,gBAAiB,EAAC,CAAC;MAElD,IAAI,CAACnG,4BAA4B,CAAC8B,GAAG,CAACN,SAAS,EAAE2E,gBAAgB,CAAC;IACpE,CAAC,CAAC;EACJ;EAEA,MAAMlF,sBAAsBA,CAAA,EAAG;IAC7B,KAAK,MAAM;MAAEO;IAAU,CAAC,IAAI,IAAI,CAAC3B,MAAM,CAAC4B,UAAU,EAAE;MAClD,MAAM,IAAI,CAACU,qBAAqB,CAACX,SAAS,CAAC;IAC7C;EACF;EAEA,MAAMN,mCAAmCA,CAAA,EAAG;IAC1C,MAAM;MACJT,QAAQ;MACRkC,iBAAiB;MACjB+B,kBAAkB;MAClB7E,MAAM,EAAE;QAAEuG;MAAoB;IAChC,CAAC,GAAG,IAAI;IAER,MAAMC,KAAK,GAAG,IAAI,CAACxG,MAAM,CAACwG,KAAK,IAAIC,OAAO,CAACD,KAAK;IAEhD,MAAM;MAAE3G;IAAiC,CAAC,GAAGD,6BAA6B;IAE1E,IAAI;MAAEE;IAA2B,CAAC,GAAGF,6BAA6B;IAElE,IAAI,OAAO2G,mBAAmB,KAAK,QAAQ,EAAE;MAC3CzG,0BAA0B,GAAGyG,mBAAmB;IAClD;IAEA,MAAMG,WAAW,GAAGA,CAACC,GAAG,EAAEC,GAAG,KAAK;MAChC,IAAIA,GAAG,CAACC,IAAI,IAAID,GAAG,CAACE,IAAI,KAAK,GAAG,EAAE;QAChClG,QAAQ,CAACmG,qBAAqB,CAAC,IAAI,CAAC;MACtC;IACF,CAAC;;IAED;IACA;IACA,IAAInI,KAAK,CAAC4H,KAAK,CAAC,EAAE;MAChBzI,QAAQ,CAACiJ,kBAAkB,CAACR,KAAK,CAAC;MAClC3H,UAAU,CAAC2H,KAAK,EAAE,IAAI,CAAC;MAEvBA,KAAK,CAACS,EAAE,CAAC,UAAU,EAAEP,WAAW,CAAC;IACnC;IAEA,IAAI;MACF;MACA,IAAI,CAACQ,qBAAqB,GAAG,MAAMtG,QAAQ,CAACuG,qBAAqB,CAC/DrE,iBAAiB,EACjB+B,kBAAkB,EAClB;QACEuC,gBAAgB,EAAEtH,0BAA0B;QAC5CuH,aAAa,EAAExH;MACjB,CACF,CAAC;IACH,CAAC,SAAS;MACR,IAAIjB,KAAK,CAAC4H,KAAK,CAAC,EAAE;QAChBA,KAAK,CAACc,cAAc,CAAC,UAAU,EAAEZ,WAAW,CAAC;MAC/C;IACF;IAEA5H,GAAG,CAACmE,KAAK,CAAE,6BAA4B,IAAI,CAACiE,qBAAsB,EAAC,CAAC;IAEpE,MAAMK,OAAO,GAAG,MAAM7I,eAAe,CAAC,CAAC;;IAEvC;IACA;IACAI,GAAG,CAACmF,IAAI,CAAE,sDAAqDsD,OAAQ,EAAC,CAAC;IAEzE,MAAMC,iBAAiB,GAAG,IAAI,CAACN,qBAAqB,CAACO,UAAU,CAAC,GAAG,CAAC,GAC/D,iBAAgB,IAAI,CAACP,qBAAqB,CAACQ,MAAM,CAAC,CAAC,CAAE,EAAC,GACtD,mBAAkB,IAAI,CAACR,qBAAsB,EAAC;IAEnD,MAAMtG,QAAQ,CAAC+G,YAAY,CACzB7E,iBAAiB,EACjB0E,iBAAiB,EAChB,OAAMD,OAAQ,EACjB,CAAC;IAED,IAAI,CAACK,eAAe,GAAGL,OAAO;EAChC;EAEA,MAAMjG,oBAAoBA,CAAA,EAAG;IAC3B,MAAM;MACJsG,eAAe;MACf5H,MAAM,EAAE;QAAE4B,UAAU;QAAEiG;MAAc;IACtC,CAAC,GAAG,IAAI;IAER,MAAMtF,aAAa,GAAI,IAAI,CAACA,aAAa,GAAG,MAAMsF,aAAa,CAAC;MAC9DC,IAAI,EAAEF;IACR,CAAC,CAAE;;IAEH;IACA;IACArF,aAAa,CAACwF,MAAM,CAACd,EAAE,CAAC,KAAK,EAAE,MAAM;MACnC,IAAI,CAAC,IAAI,CAACjE,OAAO,EAAE;QACjBlE,GAAG,CAACmF,IAAI,CAAC,6DAA6D,CAAC;QACvE,IAAI,CAACpB,IAAI,CAAC,CAAC;MACb;IACF,CAAC,CAAC;;IAEF;IACA,KAAK,MAAMmF,SAAS,IAAIpG,UAAU,EAAE;MAClC,MAAM;QAAED;MAAU,CAAC,GAAGqG,SAAS;MAC/B,MAAM1B,gBAAgB,GAAG,IAAI,CAACnG,4BAA4B,CAACkC,GAAG,CAACV,SAAS,CAAC;MAEzE,IAAI,CAAC2E,gBAAgB,EAAE;QACrB,MAAM,IAAIhI,WAAW,CAClB,2BAA0BqD,SAAU,0BACvC,CAAC;MACH;MAEA,MAAMS,OAAO,GAAG,MAAMG,aAAa,CAChC0F,qBAAqB,CAAC3B,gBAAgB,CAAC,CACvC4B,IAAI,CAAEC,aAAsC,IAAK;QAChD,OAAOA,aAAa,CAACC,KAAK,CAACC,EAAE;MAC/B,CAAC,CAAC;MAEJ,IAAI,CAACjG,OAAO,EAAE;QACZ,MAAM,IAAI9D,WAAW,CACnB,iCAAiC,GAC9B,wCAAuCgI,gBAAiB,IAC7D,CAAC;MACH;MAEA,IAAI,CAACjG,oBAAoB,CAAC4B,GAAG,CAAC+F,SAAS,CAACrG,SAAS,EAAES,OAAO,CAAC;IAC7D;EACF;AACF"}