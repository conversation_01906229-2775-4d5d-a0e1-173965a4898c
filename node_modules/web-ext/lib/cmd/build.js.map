{"version": 3, "file": "build.js", "names": ["path", "createWriteStream", "fs", "parseJSON", "stripBom", "defaultFromEvent", "zipDir", "defaultSourceWatcher", "getValidatedManifest", "getManifestId", "prepareArtifactsDir", "createLogger", "UsageError", "isErrorWithCode", "createFileFilter", "defaultFileFilterCreator", "FileFilter", "log", "import", "meta", "url", "DEFAULT_FILENAME_TEMPLATE", "safeFileName", "name", "toLowerCase", "replace", "getDefaultLocalizedName", "messageFile", "manifestData", "messageData", "messageContents", "extensionName", "readFile", "encoding", "error", "default", "stripJsonComments", "match", "messageName", "message", "Promise", "resolve", "getStringPropertyValue", "prop", "obj", "properties", "split", "value", "reduce", "prev", "curr", "includes", "stringValue", "length", "getPackageNameFromTemplate", "filenameTemplate", "packageName", "manifestProperty", "parsed", "parse", "dir", "ext", "defaultPackageCreator", "sourceDir", "fileFilter", "artifactsDir", "overwriteDest", "showReadyMessage", "filename", "fromEvent", "id", "debug", "buffer", "filter", "args", "wantFile", "default_locale", "join", "extensionPath", "stream", "flags", "write", "end", "info", "overwriteStream", "build", "asNeeded", "ignoreFiles", "onSourceChange", "packageCreator", "rebuildAsNeeded", "createPackage", "result", "onChange", "catch", "stack", "shouldWatchFile"], "sources": ["../../src/cmd/build.js"], "sourcesContent": ["/* @flow */\nimport path from 'path';\nimport { createWriteStream } from 'fs';\n\nimport { fs } from 'mz';\nimport parseJSON from 'parse-json';\nimport stripBom from 'strip-bom';\nimport defaultFromEvent from 'promise-toolbox/fromEvent';\nimport zipDir from 'zip-dir';\n\nimport defaultSourceWatcher from '../watcher.js';\nimport getValidatedManifest, { getManifestId } from '../util/manifest.js';\nimport { prepareArtifactsDir } from '../util/artifacts.js';\nimport { createLogger } from '../util/logger.js';\nimport { UsageError, isErrorWithCode } from '../errors.js';\nimport {\n  createFileFilter as defaultFileFilterCreator,\n  FileFilter,\n} from '../util/file-filter.js';\n// Import flow types.\nimport type { OnSourceChangeFn } from '../watcher.js';\nimport type { ExtensionManifest } from '../util/manifest.js';\nimport type { FileFilterCreatorFn } from '../util/file-filter.js';\n\nconst log = createLogger(import.meta.url);\nconst DEFAULT_FILENAME_TEMPLATE = '{name}-{version}.zip';\n\nexport function safeFileName(name: string): string {\n  return name.toLowerCase().replace(/[^a-z0-9.-]+/g, '_');\n}\n\n// defaultPackageCreator types and implementation.\n\nexport type ExtensionBuildResult = {|\n  extensionPath: string,\n|};\n\nexport type PackageCreatorParams = {|\n  manifestData?: ExtensionManifest,\n  sourceDir: string,\n  fileFilter: FileFilter,\n  artifactsDir: string,\n  overwriteDest: boolean,\n  showReadyMessage: boolean,\n  filename?: string,\n|};\n\nexport type LocalizedNameParams = {|\n  messageFile: string,\n  manifestData: ExtensionManifest,\n|};\n\nexport type PackageCreatorOptions = {\n  fromEvent: typeof defaultFromEvent,\n};\n\n// This defines the _locales/messages.json type. See:\n// https://developer.mozilla.org/en-US/Add-ons/WebExtensions/Internationalization#Providing_localized_strings_in__locales\ntype LocalizedMessageData = {|\n  [messageName: string]: {|\n    description: string,\n    message: string,\n  |},\n|};\n\nexport async function getDefaultLocalizedName({\n  messageFile,\n  manifestData,\n}: LocalizedNameParams): Promise<string> {\n  let messageData: LocalizedMessageData;\n  let messageContents: string | Buffer;\n  let extensionName: string = manifestData.name;\n\n  try {\n    messageContents = await fs.readFile(messageFile, { encoding: 'utf-8' });\n  } catch (error) {\n    throw new UsageError(\n      `Error reading messages.json file at ${messageFile}: ${error}`\n    );\n  }\n\n  messageContents = stripBom(messageContents);\n\n  const { default: stripJsonComments } = await import('strip-json-comments');\n  try {\n    messageData = parseJSON(stripJsonComments(messageContents));\n  } catch (error) {\n    throw new UsageError(\n      `Error parsing messages.json file at ${messageFile}: ${error}`\n    );\n  }\n\n  extensionName = manifestData.name.replace(\n    /__MSG_([A-Za-z0-9@_]+?)__/g,\n    (match, messageName) => {\n      if (!(messageData[messageName] && messageData[messageName].message)) {\n        const error = new UsageError(\n          `The locale file ${messageFile} ` + `is missing key: ${messageName}`\n        );\n        throw error;\n      } else {\n        return messageData[messageName].message;\n      }\n    }\n  );\n  return Promise.resolve(extensionName);\n}\n\n// https://stackoverflow.com/a/22129960\nexport function getStringPropertyValue(prop: string, obj: Object): string {\n  const properties = prop.split('.');\n  const value = properties.reduce((prev, curr) => prev && prev[curr], obj);\n  if (!['string', 'number'].includes(typeof value)) {\n    throw new UsageError(\n      `Manifest key \"${prop}\" is missing or has an invalid type: ${value}`\n    );\n  }\n  const stringValue = `${value}`;\n  if (!stringValue.length) {\n    throw new UsageError(`Manifest key \"${prop}\" value is an empty string`);\n  }\n  return stringValue;\n}\n\nfunction getPackageNameFromTemplate(\n  filenameTemplate: string,\n  manifestData: ExtensionManifest\n): string {\n  const packageName = filenameTemplate.replace(\n    /{([A-Za-z0-9._]+?)}/g,\n    (match, manifestProperty) => {\n      return safeFileName(\n        getStringPropertyValue(manifestProperty, manifestData)\n      );\n    }\n  );\n\n  // Validate the resulting packageName string, after interpolating the manifest property\n  // specified in the template string.\n  const parsed = path.parse(packageName);\n  if (parsed.dir) {\n    throw new UsageError(\n      `Invalid filename template \"${filenameTemplate}\". ` +\n        `Filename \"${packageName}\" should not contain a path`\n    );\n  }\n  if (!['.zip', '.xpi'].includes(parsed.ext)) {\n    throw new UsageError(\n      `Invalid filename template \"${filenameTemplate}\". ` +\n        `Filename \"${packageName}\" should have a zip or xpi extension`\n    );\n  }\n\n  return packageName;\n}\n\nexport type PackageCreatorFn = (\n  params: PackageCreatorParams\n) => Promise<ExtensionBuildResult>;\n\nexport async function defaultPackageCreator(\n  {\n    manifestData,\n    sourceDir,\n    fileFilter,\n    artifactsDir,\n    overwriteDest,\n    showReadyMessage,\n    filename = DEFAULT_FILENAME_TEMPLATE,\n  }: PackageCreatorParams,\n  { fromEvent = defaultFromEvent }: PackageCreatorOptions = {}\n): Promise<ExtensionBuildResult> {\n  let id;\n  if (manifestData) {\n    id = getManifestId(manifestData);\n    log.debug(`Using manifest id=${id || '[not specified]'}`);\n  } else {\n    manifestData = await getValidatedManifest(sourceDir);\n  }\n\n  const buffer = await zipDir(sourceDir, {\n    filter: (...args) => fileFilter.wantFile(...args),\n  });\n\n  let filenameTemplate = filename;\n\n  let { default_locale } = manifestData;\n  if (default_locale) {\n    default_locale = default_locale.replace(/-/g, '_');\n    const messageFile = path.join(\n      sourceDir,\n      '_locales',\n      default_locale,\n      'messages.json'\n    );\n    log.debug('Manifest declared default_locale, localizing extension name');\n    const extensionName = await getDefaultLocalizedName({\n      messageFile,\n      manifestData,\n    });\n    // allow for a localized `{name}`, without mutating `manifestData`\n    filenameTemplate = filenameTemplate.replace(/{name}/g, extensionName);\n  }\n\n  const packageName = safeFileName(\n    getPackageNameFromTemplate(filenameTemplate, manifestData)\n  );\n  const extensionPath = path.join(artifactsDir, packageName);\n\n  // Added 'wx' flags to avoid overwriting of existing package.\n  const stream = createWriteStream(extensionPath, { flags: 'wx' });\n\n  stream.write(buffer, () => {\n    stream.end();\n  });\n\n  try {\n    await fromEvent(stream, 'close');\n  } catch (error) {\n    if (!isErrorWithCode('EEXIST', error)) {\n      throw error;\n    }\n    if (!overwriteDest) {\n      throw new UsageError(\n        `Extension exists at the destination path: ${extensionPath}\\n` +\n          'Use --overwrite-dest to enable overwriting.'\n      );\n    }\n    log.info(`Destination exists, overwriting: ${extensionPath}`);\n    const overwriteStream = createWriteStream(extensionPath);\n    overwriteStream.write(buffer, () => {\n      overwriteStream.end();\n    });\n    await fromEvent(overwriteStream, 'close');\n  }\n\n  if (showReadyMessage) {\n    log.info(`Your web extension is ready: ${extensionPath}`);\n  }\n  return { extensionPath };\n}\n\n// Build command types and implementation.\n\nexport type BuildCmdParams = {|\n  sourceDir: string,\n  artifactsDir: string,\n  asNeeded?: boolean,\n  overwriteDest?: boolean,\n  ignoreFiles?: Array<string>,\n  filename?: string,\n|};\n\nexport type BuildCmdOptions = {\n  manifestData?: ExtensionManifest,\n  fileFilter?: FileFilter,\n  onSourceChange?: OnSourceChangeFn,\n  packageCreator?: PackageCreatorFn,\n  showReadyMessage?: boolean,\n  createFileFilter?: FileFilterCreatorFn,\n  shouldExitProgram?: boolean,\n};\n\nexport default async function build(\n  {\n    sourceDir,\n    artifactsDir,\n    asNeeded = false,\n    overwriteDest = false,\n    ignoreFiles = [],\n    filename = DEFAULT_FILENAME_TEMPLATE,\n  }: BuildCmdParams,\n  {\n    manifestData,\n    createFileFilter = defaultFileFilterCreator,\n    fileFilter = createFileFilter({\n      sourceDir,\n      artifactsDir,\n      ignoreFiles,\n    }),\n    onSourceChange = defaultSourceWatcher,\n    packageCreator = defaultPackageCreator,\n    showReadyMessage = true,\n  }: BuildCmdOptions = {}\n): Promise<ExtensionBuildResult> {\n  const rebuildAsNeeded = asNeeded; // alias for `build --as-needed`\n  log.info(`Building web extension from ${sourceDir}`);\n\n  const createPackage = () =>\n    packageCreator({\n      manifestData,\n      sourceDir,\n      fileFilter,\n      artifactsDir,\n      overwriteDest,\n      showReadyMessage,\n      filename,\n    });\n\n  await prepareArtifactsDir(artifactsDir);\n  const result = await createPackage();\n\n  if (rebuildAsNeeded) {\n    log.info('Rebuilding when files change...');\n    onSourceChange({\n      sourceDir,\n      artifactsDir,\n      onChange: () => {\n        return createPackage().catch((error) => {\n          log.error(error.stack);\n          throw error;\n        });\n      },\n      shouldWatchFile: (...args) => fileFilter.wantFile(...args),\n    });\n  }\n\n  return result;\n}\n"], "mappings": "AACA,OAAOA,IAAI,MAAM,MAAM;AACvB,SAASC,iBAAiB,QAAQ,IAAI;AAEtC,SAASC,EAAE,QAAQ,IAAI;AACvB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,QAAQ,MAAM,WAAW;AAChC,OAAOC,gBAAgB,MAAM,2BAA2B;AACxD,OAAOC,MAAM,MAAM,SAAS;AAE5B,OAAOC,oBAAoB,MAAM,eAAe;AAChD,OAAOC,oBAAoB,IAAIC,aAAa,QAAQ,qBAAqB;AACzE,SAASC,mBAAmB,QAAQ,sBAAsB;AAC1D,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,UAAU,EAAEC,eAAe,QAAQ,cAAc;AAC1D,SACEC,gBAAgB,IAAIC,wBAAwB,EAC5CC,UAAU,QACL,wBAAwB;AAC/B;;AAKA,MAAMC,GAAG,GAAGN,YAAY,CAACO,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC;AACzC,MAAMC,yBAAyB,GAAG,sBAAsB;AAExD,OAAO,SAASC,YAAYA,CAACC,IAAY,EAAU;EACjD,OAAOA,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC;AACzD;;AAEA;;AAyBA;AACA;;AAQA,OAAO,eAAeC,uBAAuBA,CAAC;EAC5CC,WAAW;EACXC;AACmB,CAAC,EAAmB;EACvC,IAAIC,WAAiC;EACrC,IAAIC,eAAgC;EACpC,IAAIC,aAAqB,GAAGH,YAAY,CAACL,IAAI;EAE7C,IAAI;IACFO,eAAe,GAAG,MAAM5B,EAAE,CAAC8B,QAAQ,CAACL,WAAW,EAAE;MAAEM,QAAQ,EAAE;IAAQ,CAAC,CAAC;EACzE,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,MAAM,IAAItB,UAAU,CACjB,uCAAsCe,WAAY,KAAIO,KAAM,EAC/D,CAAC;EACH;EAEAJ,eAAe,GAAG1B,QAAQ,CAAC0B,eAAe,CAAC;EAE3C,MAAM;IAAEK,OAAO,EAAEC;EAAkB,CAAC,GAAG,MAAM,MAAM,CAAC,qBAAqB,CAAC;EAC1E,IAAI;IACFP,WAAW,GAAG1B,SAAS,CAACiC,iBAAiB,CAACN,eAAe,CAAC,CAAC;EAC7D,CAAC,CAAC,OAAOI,KAAK,EAAE;IACd,MAAM,IAAItB,UAAU,CACjB,uCAAsCe,WAAY,KAAIO,KAAM,EAC/D,CAAC;EACH;EAEAH,aAAa,GAAGH,YAAY,CAACL,IAAI,CAACE,OAAO,CACvC,4BAA4B,EAC5B,CAACY,KAAK,EAAEC,WAAW,KAAK;IACtB,IAAI,EAAET,WAAW,CAACS,WAAW,CAAC,IAAIT,WAAW,CAACS,WAAW,CAAC,CAACC,OAAO,CAAC,EAAE;MACnE,MAAML,KAAK,GAAG,IAAItB,UAAU,CACzB,mBAAkBe,WAAY,GAAE,GAAI,mBAAkBW,WAAY,EACrE,CAAC;MACD,MAAMJ,KAAK;IACb,CAAC,MAAM;MACL,OAAOL,WAAW,CAACS,WAAW,CAAC,CAACC,OAAO;IACzC;EACF,CACF,CAAC;EACD,OAAOC,OAAO,CAACC,OAAO,CAACV,aAAa,CAAC;AACvC;;AAEA;AACA,OAAO,SAASW,sBAAsBA,CAACC,IAAY,EAAEC,GAAW,EAAU;EACxE,MAAMC,UAAU,GAAGF,IAAI,CAACG,KAAK,CAAC,GAAG,CAAC;EAClC,MAAMC,KAAK,GAAGF,UAAU,CAACG,MAAM,CAAC,CAACC,IAAI,EAAEC,IAAI,KAAKD,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,EAAEN,GAAG,CAAC;EACxE,IAAI,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAACO,QAAQ,CAAC,OAAOJ,KAAK,CAAC,EAAE;IAChD,MAAM,IAAInC,UAAU,CACjB,iBAAgB+B,IAAK,wCAAuCI,KAAM,EACrE,CAAC;EACH;EACA,MAAMK,WAAW,GAAI,GAAEL,KAAM,EAAC;EAC9B,IAAI,CAACK,WAAW,CAACC,MAAM,EAAE;IACvB,MAAM,IAAIzC,UAAU,CAAE,iBAAgB+B,IAAK,4BAA2B,CAAC;EACzE;EACA,OAAOS,WAAW;AACpB;AAEA,SAASE,0BAA0BA,CACjCC,gBAAwB,EACxB3B,YAA+B,EACvB;EACR,MAAM4B,WAAW,GAAGD,gBAAgB,CAAC9B,OAAO,CAC1C,sBAAsB,EACtB,CAACY,KAAK,EAAEoB,gBAAgB,KAAK;IAC3B,OAAOnC,YAAY,CACjBoB,sBAAsB,CAACe,gBAAgB,EAAE7B,YAAY,CACvD,CAAC;EACH,CACF,CAAC;;EAED;EACA;EACA,MAAM8B,MAAM,GAAG1D,IAAI,CAAC2D,KAAK,CAACH,WAAW,CAAC;EACtC,IAAIE,MAAM,CAACE,GAAG,EAAE;IACd,MAAM,IAAIhD,UAAU,CACjB,8BAA6B2C,gBAAiB,KAAI,GAChD,aAAYC,WAAY,6BAC7B,CAAC;EACH;EACA,IAAI,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAACL,QAAQ,CAACO,MAAM,CAACG,GAAG,CAAC,EAAE;IAC1C,MAAM,IAAIjD,UAAU,CACjB,8BAA6B2C,gBAAiB,KAAI,GAChD,aAAYC,WAAY,sCAC7B,CAAC;EACH;EAEA,OAAOA,WAAW;AACpB;AAMA,OAAO,eAAeM,qBAAqBA,CACzC;EACElC,YAAY;EACZmC,SAAS;EACTC,UAAU;EACVC,YAAY;EACZC,aAAa;EACbC,gBAAgB;EAChBC,QAAQ,GAAG/C;AACS,CAAC,EACvB;EAAEgD,SAAS,GAAGhE;AAAwC,CAAC,GAAG,CAAC,CAAC,EAC7B;EAC/B,IAAIiE,EAAE;EACN,IAAI1C,YAAY,EAAE;IAChB0C,EAAE,GAAG7D,aAAa,CAACmB,YAAY,CAAC;IAChCX,GAAG,CAACsD,KAAK,CAAE,qBAAoBD,EAAE,IAAI,iBAAkB,EAAC,CAAC;EAC3D,CAAC,MAAM;IACL1C,YAAY,GAAG,MAAMpB,oBAAoB,CAACuD,SAAS,CAAC;EACtD;EAEA,MAAMS,MAAM,GAAG,MAAMlE,MAAM,CAACyD,SAAS,EAAE;IACrCU,MAAM,EAAEA,CAAC,GAAGC,IAAI,KAAKV,UAAU,CAACW,QAAQ,CAAC,GAAGD,IAAI;EAClD,CAAC,CAAC;EAEF,IAAInB,gBAAgB,GAAGa,QAAQ;EAE/B,IAAI;IAAEQ;EAAe,CAAC,GAAGhD,YAAY;EACrC,IAAIgD,cAAc,EAAE;IAClBA,cAAc,GAAGA,cAAc,CAACnD,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;IAClD,MAAME,WAAW,GAAG3B,IAAI,CAAC6E,IAAI,CAC3Bd,SAAS,EACT,UAAU,EACVa,cAAc,EACd,eACF,CAAC;IACD3D,GAAG,CAACsD,KAAK,CAAC,6DAA6D,CAAC;IACxE,MAAMxC,aAAa,GAAG,MAAML,uBAAuB,CAAC;MAClDC,WAAW;MACXC;IACF,CAAC,CAAC;IACF;IACA2B,gBAAgB,GAAGA,gBAAgB,CAAC9B,OAAO,CAAC,SAAS,EAAEM,aAAa,CAAC;EACvE;EAEA,MAAMyB,WAAW,GAAGlC,YAAY,CAC9BgC,0BAA0B,CAACC,gBAAgB,EAAE3B,YAAY,CAC3D,CAAC;EACD,MAAMkD,aAAa,GAAG9E,IAAI,CAAC6E,IAAI,CAACZ,YAAY,EAAET,WAAW,CAAC;;EAE1D;EACA,MAAMuB,MAAM,GAAG9E,iBAAiB,CAAC6E,aAAa,EAAE;IAAEE,KAAK,EAAE;EAAK,CAAC,CAAC;EAEhED,MAAM,CAACE,KAAK,CAACT,MAAM,EAAE,MAAM;IACzBO,MAAM,CAACG,GAAG,CAAC,CAAC;EACd,CAAC,CAAC;EAEF,IAAI;IACF,MAAMb,SAAS,CAACU,MAAM,EAAE,OAAO,CAAC;EAClC,CAAC,CAAC,OAAO7C,KAAK,EAAE;IACd,IAAI,CAACrB,eAAe,CAAC,QAAQ,EAAEqB,KAAK,CAAC,EAAE;MACrC,MAAMA,KAAK;IACb;IACA,IAAI,CAACgC,aAAa,EAAE;MAClB,MAAM,IAAItD,UAAU,CACjB,6CAA4CkE,aAAc,IAAG,GAC5D,6CACJ,CAAC;IACH;IACA7D,GAAG,CAACkE,IAAI,CAAE,oCAAmCL,aAAc,EAAC,CAAC;IAC7D,MAAMM,eAAe,GAAGnF,iBAAiB,CAAC6E,aAAa,CAAC;IACxDM,eAAe,CAACH,KAAK,CAACT,MAAM,EAAE,MAAM;MAClCY,eAAe,CAACF,GAAG,CAAC,CAAC;IACvB,CAAC,CAAC;IACF,MAAMb,SAAS,CAACe,eAAe,EAAE,OAAO,CAAC;EAC3C;EAEA,IAAIjB,gBAAgB,EAAE;IACpBlD,GAAG,CAACkE,IAAI,CAAE,gCAA+BL,aAAc,EAAC,CAAC;EAC3D;EACA,OAAO;IAAEA;EAAc,CAAC;AAC1B;;AAEA;;AAqBA,eAAe,eAAeO,KAAKA,CACjC;EACEtB,SAAS;EACTE,YAAY;EACZqB,QAAQ,GAAG,KAAK;EAChBpB,aAAa,GAAG,KAAK;EACrBqB,WAAW,GAAG,EAAE;EAChBnB,QAAQ,GAAG/C;AACG,CAAC,EACjB;EACEO,YAAY;EACZd,gBAAgB,GAAGC,wBAAwB;EAC3CiD,UAAU,GAAGlD,gBAAgB,CAAC;IAC5BiD,SAAS;IACTE,YAAY;IACZsB;EACF,CAAC,CAAC;EACFC,cAAc,GAAGjF,oBAAoB;EACrCkF,cAAc,GAAG3B,qBAAqB;EACtCK,gBAAgB,GAAG;AACJ,CAAC,GAAG,CAAC,CAAC,EACQ;EAC/B,MAAMuB,eAAe,GAAGJ,QAAQ,CAAC,CAAC;EAClCrE,GAAG,CAACkE,IAAI,CAAE,+BAA8BpB,SAAU,EAAC,CAAC;EAEpD,MAAM4B,aAAa,GAAGA,CAAA,KACpBF,cAAc,CAAC;IACb7D,YAAY;IACZmC,SAAS;IACTC,UAAU;IACVC,YAAY;IACZC,aAAa;IACbC,gBAAgB;IAChBC;EACF,CAAC,CAAC;EAEJ,MAAM1D,mBAAmB,CAACuD,YAAY,CAAC;EACvC,MAAM2B,MAAM,GAAG,MAAMD,aAAa,CAAC,CAAC;EAEpC,IAAID,eAAe,EAAE;IACnBzE,GAAG,CAACkE,IAAI,CAAC,iCAAiC,CAAC;IAC3CK,cAAc,CAAC;MACbzB,SAAS;MACTE,YAAY;MACZ4B,QAAQ,EAAEA,CAAA,KAAM;QACd,OAAOF,aAAa,CAAC,CAAC,CAACG,KAAK,CAAE5D,KAAK,IAAK;UACtCjB,GAAG,CAACiB,KAAK,CAACA,KAAK,CAAC6D,KAAK,CAAC;UACtB,MAAM7D,KAAK;QACb,CAAC,CAAC;MACJ,CAAC;MACD8D,eAAe,EAAEA,CAAC,GAAGtB,IAAI,KAAKV,UAAU,CAACW,QAAQ,CAAC,GAAGD,IAAI;IAC3D,CAAC,CAAC;EACJ;EAEA,OAAOkB,MAAM;AACf"}