{"version": 3, "file": "lint.js", "names": ["createInstance", "defaultLinterCreator", "createLogger", "createFileFilter", "defaultFileFilterCreator", "log", "import", "meta", "url", "lint", "artifactsDir", "boring", "firefoxPreview", "ignoreFiles", "metadata", "output", "pretty", "privileged", "sourceDir", "selfHosted", "verbose", "warningsAsErrors", "createLinter", "shouldExitProgram", "fileFilter", "config", "logLevel", "stack", "Boolean", "shouldScanFile", "fileName", "wantFile", "minManifestVersion", "maxManifestVersion", "_", "includes", "warn", "join", "debug", "linter", "runAsBinary", "run"], "sources": ["../../src/cmd/lint.js"], "sourcesContent": ["/* @flow */\nimport { createInstance as defaultLinterCreator } from 'addons-linter';\n\nimport { createLogger } from '../util/logger.js';\nimport { createFileFilter as defaultFileFilterCreator } from '../util/file-filter.js';\n// import flow types\nimport type { FileFilterCreatorFn } from '../util/file-filter.js';\n\nconst log = createLogger(import.meta.url);\n\n// Define the needed 'addons-linter' module flow types.\n\nexport type LinterOutputType = 'text' | 'json';\n\nexport type LinterCreatorParams = {|\n  config: {|\n    logLevel: 'debug' | 'fatal',\n    stack: boolean,\n    pretty?: boolean,\n    warningsAsErrors?: boolean,\n    metadata?: boolean,\n    minManifestVersion?: number,\n    maxManifestVersion?: number,\n    output?: LinterOutputType,\n    privileged?: boolean,\n    boring?: boolean,\n    selfHosted?: boolean,\n    shouldScanFile: (fileName: string) => boolean,\n    _: Array<string>,\n  |},\n  runAsBinary: boolean,\n|};\n\nexport type Linter = {|\n  run: () => Promise<void>,\n|};\n\nexport type LinterCreatorFn = (params: LinterCreatorParams) => Linter;\n\n// Lint command types and implementation.\n\nexport type LintCmdParams = {|\n  artifactsDir?: string,\n  boring?: boolean,\n  firefoxPreview: Array<string>,\n  ignoreFiles?: Array<string>,\n  metadata?: boolean,\n  output?: LinterOutputType,\n  pretty?: boolean,\n  privileged?: boolean,\n  selfHosted?: boolean,\n  sourceDir: string,\n  verbose?: boolean,\n  warningsAsErrors?: boolean,\n|};\n\nexport type LintCmdOptions = {\n  createLinter?: LinterCreatorFn,\n  createFileFilter?: FileFilterCreatorFn,\n  shouldExitProgram?: boolean,\n};\n\nexport default function lint(\n  {\n    artifactsDir,\n    boring,\n    firefoxPreview = [],\n    ignoreFiles,\n    metadata,\n    output,\n    pretty,\n    privileged,\n    sourceDir,\n    selfHosted,\n    verbose,\n    warningsAsErrors,\n  }: LintCmdParams,\n  {\n    createLinter = defaultLinterCreator,\n    createFileFilter = defaultFileFilterCreator,\n    shouldExitProgram = true,\n  }: LintCmdOptions = {}\n): Promise<void> {\n  const fileFilter = createFileFilter({ sourceDir, ignoreFiles, artifactsDir });\n\n  const config = {\n    logLevel: verbose ? 'debug' : 'fatal',\n    stack: Boolean(verbose),\n    pretty,\n    privileged,\n    warningsAsErrors,\n    metadata,\n    output,\n    boring,\n    selfHosted,\n    shouldScanFile: (fileName) => fileFilter.wantFile(fileName),\n    minManifestVersion: 2,\n    maxManifestVersion: 3,\n    // This mimics the first command line argument from yargs, which should be\n    // the directory to the extension.\n    _: [sourceDir],\n  };\n\n  if (firefoxPreview.includes('mv3')) {\n    log.warn(\n      [\n        'Manifest Version 3 is now officially supported and',\n        '\"--firefox-preview=mv3\" is no longer needed.',\n        'In addition, the \"mv3\" value will be removed in the future.',\n      ].join(' ')\n    );\n  }\n\n  log.debug(`Running addons-linter on ${sourceDir}`);\n  const linter = createLinter({ config, runAsBinary: shouldExitProgram });\n  return linter.run();\n}\n"], "mappings": "AACA,SAASA,cAAc,IAAIC,oBAAoB,QAAQ,eAAe;AAEtE,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,gBAAgB,IAAIC,wBAAwB,QAAQ,wBAAwB;AACrF;;AAGA,MAAMC,GAAG,GAAGH,YAAY,CAACI,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC;;AAEzC;;AA6BA;;AAuBA,eAAe,SAASC,IAAIA,CAC1B;EACEC,YAAY;EACZC,MAAM;EACNC,cAAc,GAAG,EAAE;EACnBC,WAAW;EACXC,QAAQ;EACRC,MAAM;EACNC,MAAM;EACNC,UAAU;EACVC,SAAS;EACTC,UAAU;EACVC,OAAO;EACPC;AACa,CAAC,EAChB;EACEC,YAAY,GAAGrB,oBAAoB;EACnCE,gBAAgB,GAAGC,wBAAwB;EAC3CmB,iBAAiB,GAAG;AACN,CAAC,GAAG,CAAC,CAAC,EACP;EACf,MAAMC,UAAU,GAAGrB,gBAAgB,CAAC;IAAEe,SAAS;IAAEL,WAAW;IAAEH;EAAa,CAAC,CAAC;EAE7E,MAAMe,MAAM,GAAG;IACbC,QAAQ,EAAEN,OAAO,GAAG,OAAO,GAAG,OAAO;IACrCO,KAAK,EAAEC,OAAO,CAACR,OAAO,CAAC;IACvBJ,MAAM;IACNC,UAAU;IACVI,gBAAgB;IAChBP,QAAQ;IACRC,MAAM;IACNJ,MAAM;IACNQ,UAAU;IACVU,cAAc,EAAGC,QAAQ,IAAKN,UAAU,CAACO,QAAQ,CAACD,QAAQ,CAAC;IAC3DE,kBAAkB,EAAE,CAAC;IACrBC,kBAAkB,EAAE,CAAC;IACrB;IACA;IACAC,CAAC,EAAE,CAAChB,SAAS;EACf,CAAC;EAED,IAAIN,cAAc,CAACuB,QAAQ,CAAC,KAAK,CAAC,EAAE;IAClC9B,GAAG,CAAC+B,IAAI,CACN,CACE,oDAAoD,EACpD,8CAA8C,EAC9C,6DAA6D,CAC9D,CAACC,IAAI,CAAC,GAAG,CACZ,CAAC;EACH;EAEAhC,GAAG,CAACiC,KAAK,CAAE,4BAA2BpB,SAAU,EAAC,CAAC;EAClD,MAAMqB,MAAM,GAAGjB,YAAY,CAAC;IAAEG,MAAM;IAAEe,WAAW,EAAEjB;EAAkB,CAAC,CAAC;EACvE,OAAOgB,MAAM,CAACE,GAAG,CAAC,CAAC;AACrB"}