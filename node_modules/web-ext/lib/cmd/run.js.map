{"version": 3, "file": "run.js", "names": ["fs", "defaultBuildExtension", "showDesktopNotification", "defaultDesktopNotifications", "defaultFirefoxApp", "connectWithMaxRetries", "defaultFirefoxClient", "createLogger", "defaultGetValidatedManifest", "UsageError", "createExtensionRunner", "defaultReloadStrategy", "MultiExtensionRunner", "DefaultMultiExtensionRunner", "log", "import", "meta", "url", "run", "artifactsDir", "browserConsole", "devtools", "pref", "firefox", "firefoxProfile", "profileCreateIfMissing", "keepProfileChanges", "ignoreFiles", "noInput", "noReload", "preInstall", "sourceDir", "watchFile", "watchIgnored", "startUrl", "target", "args", "firefoxPreview", "adbBin", "adbHost", "adbPort", "adbDevice", "adbDiscoveryTimeout", "adbRemoveOldArtifacts", "firefoxApk", "firefoxApkComponent", "chromiumBinary", "chromiumProfile", "buildExtension", "desktopNotifications", "firefoxApp", "firefoxClient", "reloadStrategy", "getValidatedManifest", "info", "Array", "isArray", "every", "el", "customPrefs", "includes", "manifestData", "profileDir", "isDir", "existsSync", "mkdir", "runners", "commonRunnerParams", "extensions", "length", "firefoxDesktopRunnerParams", "firefoxBinary", "profilePath", "firefoxDesktopRunner", "params", "push", "firefoxAndroidRunnerParams", "buildSourceDir", "extensionSourceDir", "tmpArtifactsDir", "asNeeded", "showReadyMessage", "firefoxAndroidRunner", "chromiumRunnerParams", "chromiumRunner", "<PERSON><PERSON><PERSON>ner"], "sources": ["../../src/cmd/run.js"], "sourcesContent": ["/* @flow */\nimport { fs } from 'mz';\n\nimport defaultBuildExtension from './build.js';\nimport { showDesktopNotification as defaultDesktopNotifications } from '../util/desktop-notifier.js';\nimport * as defaultFirefoxApp from '../firefox/index.js';\nimport { connectWithMaxRetries as defaultFirefoxClient } from '../firefox/remote.js';\nimport { createLogger } from '../util/logger.js';\nimport defaultGetValidatedManifest from '../util/manifest.js';\nimport { UsageError } from '../errors.js';\nimport {\n  createExtensionRunner,\n  defaultReloadStrategy,\n  MultiExtensionRunner as DefaultMultiExtensionRunner,\n} from '../extension-runners/index.js';\n// Import objects that are only used as Flow types.\nimport type { FirefoxPreferences } from '../firefox/preferences.js';\n\nconst log = createLogger(import.meta.url);\n\n// Run command types and implementation.\n\nexport type CmdRunParams = {|\n  artifactsDir: string,\n  browserConsole: boolean,\n  devtools: boolean,\n  pref?: FirefoxPreferences,\n  firefox: string,\n  firefoxProfile?: string,\n  profileCreateIfMissing?: boolean,\n  ignoreFiles?: Array<string>,\n  keepProfileChanges: boolean,\n  noInput?: boolean,\n  noReload: boolean,\n  preInstall: boolean,\n  sourceDir: string,\n  watchFile?: Array<string>,\n  watchIgnored?: Array<string>,\n  startUrl?: Array<string>,\n  target?: Array<string>,\n  args?: Array<string>,\n  firefoxPreview: Array<string>,\n\n  // Android CLI options.\n  adbBin?: string,\n  adbHost?: string,\n  adbPort?: string,\n  adbDevice?: string,\n  adbDiscoveryTimeout?: number,\n  adbRemoveOldArtifacts?: boolean,\n  firefoxApk?: string,\n  firefoxApkComponent?: string,\n\n  // Chromium Desktop CLI options.\n  chromiumBinary?: string,\n  chromiumProfile?: string,\n|};\n\nexport type CmdRunOptions = {\n  buildExtension: typeof defaultBuildExtension,\n  desktopNotifications: typeof defaultDesktopNotifications,\n  firefoxApp: typeof defaultFirefoxApp,\n  firefoxClient: typeof defaultFirefoxClient,\n  reloadStrategy: typeof defaultReloadStrategy,\n  shouldExitProgram?: boolean,\n  MultiExtensionRunner?: typeof DefaultMultiExtensionRunner,\n  getValidatedManifest?: typeof defaultGetValidatedManifest,\n};\n\nexport default async function run(\n  {\n    artifactsDir,\n    browserConsole = false,\n    devtools = false,\n    pref,\n    firefox,\n    firefoxProfile,\n    profileCreateIfMissing,\n    keepProfileChanges = false,\n    ignoreFiles,\n    noInput = false,\n    noReload = false,\n    preInstall = false,\n    sourceDir,\n    watchFile,\n    watchIgnored,\n    startUrl,\n    target,\n    args,\n    firefoxPreview = [],\n    // Android CLI options.\n    adbBin,\n    adbHost,\n    adbPort,\n    adbDevice,\n    adbDiscoveryTimeout,\n    adbRemoveOldArtifacts,\n    firefoxApk,\n    firefoxApkComponent,\n    // Chromium CLI options.\n    chromiumBinary,\n    chromiumProfile,\n  }: CmdRunParams,\n  {\n    buildExtension = defaultBuildExtension,\n    desktopNotifications = defaultDesktopNotifications,\n    firefoxApp = defaultFirefoxApp,\n    firefoxClient = defaultFirefoxClient,\n    reloadStrategy = defaultReloadStrategy,\n    MultiExtensionRunner = DefaultMultiExtensionRunner,\n    getValidatedManifest = defaultGetValidatedManifest,\n  }: CmdRunOptions = {}\n): Promise<DefaultMultiExtensionRunner> {\n  log.info(`Running web extension from ${sourceDir}`);\n  if (preInstall) {\n    log.info(\n      \"Disabled auto-reloading because it's not possible with \" +\n        '--pre-install'\n    );\n    noReload = true;\n  }\n\n  if (\n    watchFile != null &&\n    (!Array.isArray(watchFile) ||\n      !watchFile.every((el) => typeof el === 'string'))\n  ) {\n    throw new UsageError('Unexpected watchFile type');\n  }\n\n  // Create an alias for --pref since it has been transformed into an\n  // object containing one or more preferences.\n  const customPrefs: FirefoxPreferences = { ...pref };\n  if (firefoxPreview.includes('mv3')) {\n    log.info('Configuring Firefox preferences for Manifest V3');\n    customPrefs['extensions.manifestV3.enabled'] = true;\n  }\n\n  const manifestData = await getValidatedManifest(sourceDir);\n\n  const profileDir = firefoxProfile || chromiumProfile;\n\n  if (profileCreateIfMissing) {\n    if (!profileDir) {\n      throw new UsageError(\n        '--profile-create-if-missing requires ' +\n          '--firefox-profile or --chromium-profile'\n      );\n    }\n    const isDir = fs.existsSync(profileDir);\n    if (isDir) {\n      log.info(`Profile directory ${profileDir} already exists`);\n    } else {\n      log.info(`Profile directory not found. Creating directory ${profileDir}`);\n      await fs.mkdir(profileDir);\n    }\n  }\n\n  const runners = [];\n\n  const commonRunnerParams = {\n    // Common options.\n    extensions: [{ sourceDir, manifestData }],\n    keepProfileChanges,\n    startUrl,\n    args,\n    desktopNotifications,\n  };\n\n  if (!target || target.length === 0 || target.includes('firefox-desktop')) {\n    const firefoxDesktopRunnerParams = {\n      ...commonRunnerParams,\n\n      // Firefox specific CLI options.\n      firefoxBinary: firefox,\n      profilePath: firefoxProfile,\n      customPrefs,\n      browserConsole,\n      devtools,\n      preInstall,\n\n      // Firefox runner injected dependencies.\n      firefoxApp,\n      firefoxClient,\n    };\n\n    const firefoxDesktopRunner = await createExtensionRunner({\n      target: 'firefox-desktop',\n      params: firefoxDesktopRunnerParams,\n    });\n    runners.push(firefoxDesktopRunner);\n  }\n\n  if (target && target.includes('firefox-android')) {\n    const firefoxAndroidRunnerParams = {\n      ...commonRunnerParams,\n\n      // Firefox specific CLI options.\n      profilePath: firefoxProfile,\n      customPrefs,\n      browserConsole,\n      preInstall,\n      firefoxApk,\n      firefoxApkComponent,\n      adbDevice,\n      adbHost,\n      adbPort,\n      adbBin,\n      adbDiscoveryTimeout,\n      adbRemoveOldArtifacts,\n\n      // Injected dependencies.\n      firefoxApp,\n      firefoxClient,\n      desktopNotifications: defaultDesktopNotifications,\n      buildSourceDir: (extensionSourceDir: string, tmpArtifactsDir: string) => {\n        return buildExtension(\n          {\n            sourceDir: extensionSourceDir,\n            ignoreFiles,\n            asNeeded: false,\n            // Use a separate temporary directory for building the extension zip file\n            // that we are going to upload on the android device.\n            artifactsDir: tmpArtifactsDir,\n          },\n          {\n            // Suppress the message usually logged by web-ext build.\n            showReadyMessage: false,\n          }\n        );\n      },\n    };\n\n    const firefoxAndroidRunner = await createExtensionRunner({\n      target: 'firefox-android',\n      params: firefoxAndroidRunnerParams,\n    });\n    runners.push(firefoxAndroidRunner);\n  }\n\n  if (target && target.includes('chromium')) {\n    const chromiumRunnerParams = {\n      ...commonRunnerParams,\n      chromiumBinary,\n      chromiumProfile,\n    };\n\n    const chromiumRunner = await createExtensionRunner({\n      target: 'chromium',\n      params: chromiumRunnerParams,\n    });\n    runners.push(chromiumRunner);\n  }\n\n  const extensionRunner = new MultiExtensionRunner({\n    desktopNotifications,\n    runners,\n  });\n\n  await extensionRunner.run();\n\n  if (noReload) {\n    log.info('Automatic extension reloading has been disabled');\n  } else {\n    log.info('The extension will reload if any source file changes');\n\n    reloadStrategy({\n      extensionRunner,\n      sourceDir,\n      watchFile,\n      watchIgnored,\n      artifactsDir,\n      ignoreFiles,\n      noInput,\n    });\n  }\n\n  return extensionRunner;\n}\n"], "mappings": "AACA,SAASA,EAAE,QAAQ,IAAI;AAEvB,OAAOC,qBAAqB,MAAM,YAAY;AAC9C,SAASC,uBAAuB,IAAIC,2BAA2B,QAAQ,6BAA6B;AACpG,OAAO,KAAKC,iBAAiB,MAAM,qBAAqB;AACxD,SAASC,qBAAqB,IAAIC,oBAAoB,QAAQ,sBAAsB;AACpF,SAASC,YAAY,QAAQ,mBAAmB;AAChD,OAAOC,2BAA2B,MAAM,qBAAqB;AAC7D,SAASC,UAAU,QAAQ,cAAc;AACzC,SACEC,qBAAqB,EACrBC,qBAAqB,EACrBC,oBAAoB,IAAIC,2BAA2B,QAC9C,+BAA+B;AACtC;;AAGA,MAAMC,GAAG,GAAGP,YAAY,CAACQ,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC;;AAEzC;;AAiDA,eAAe,eAAeC,GAAGA,CAC/B;EACEC,YAAY;EACZC,cAAc,GAAG,KAAK;EACtBC,QAAQ,GAAG,KAAK;EAChBC,IAAI;EACJC,OAAO;EACPC,cAAc;EACdC,sBAAsB;EACtBC,kBAAkB,GAAG,KAAK;EAC1BC,WAAW;EACXC,OAAO,GAAG,KAAK;EACfC,QAAQ,GAAG,KAAK;EAChBC,UAAU,GAAG,KAAK;EAClBC,SAAS;EACTC,SAAS;EACTC,YAAY;EACZC,QAAQ;EACRC,MAAM;EACNC,IAAI;EACJC,cAAc,GAAG,EAAE;EACnB;EACAC,MAAM;EACNC,OAAO;EACPC,OAAO;EACPC,SAAS;EACTC,mBAAmB;EACnBC,qBAAqB;EACrBC,UAAU;EACVC,mBAAmB;EACnB;EACAC,cAAc;EACdC;AACY,CAAC,EACf;EACEC,cAAc,GAAG/C,qBAAqB;EACtCgD,oBAAoB,GAAG9C,2BAA2B;EAClD+C,UAAU,GAAG9C,iBAAiB;EAC9B+C,aAAa,GAAG7C,oBAAoB;EACpC8C,cAAc,GAAGzC,qBAAqB;EACtCC,oBAAoB,GAAGC,2BAA2B;EAClDwC,oBAAoB,GAAG7C;AACV,CAAC,GAAG,CAAC,CAAC,EACiB;EACtCM,GAAG,CAACwC,IAAI,CAAE,8BAA6BvB,SAAU,EAAC,CAAC;EACnD,IAAID,UAAU,EAAE;IACdhB,GAAG,CAACwC,IAAI,CACN,yDAAyD,GACvD,eACJ,CAAC;IACDzB,QAAQ,GAAG,IAAI;EACjB;EAEA,IACEG,SAAS,IAAI,IAAI,KAChB,CAACuB,KAAK,CAACC,OAAO,CAACxB,SAAS,CAAC,IACxB,CAACA,SAAS,CAACyB,KAAK,CAAEC,EAAE,IAAK,OAAOA,EAAE,KAAK,QAAQ,CAAC,CAAC,EACnD;IACA,MAAM,IAAIjD,UAAU,CAAC,2BAA2B,CAAC;EACnD;;EAEA;EACA;EACA,MAAMkD,WAA+B,GAAG;IAAE,GAAGrC;EAAK,CAAC;EACnD,IAAIe,cAAc,CAACuB,QAAQ,CAAC,KAAK,CAAC,EAAE;IAClC9C,GAAG,CAACwC,IAAI,CAAC,iDAAiD,CAAC;IAC3DK,WAAW,CAAC,+BAA+B,CAAC,GAAG,IAAI;EACrD;EAEA,MAAME,YAAY,GAAG,MAAMR,oBAAoB,CAACtB,SAAS,CAAC;EAE1D,MAAM+B,UAAU,GAAGtC,cAAc,IAAIuB,eAAe;EAEpD,IAAItB,sBAAsB,EAAE;IAC1B,IAAI,CAACqC,UAAU,EAAE;MACf,MAAM,IAAIrD,UAAU,CAClB,uCAAuC,GACrC,yCACJ,CAAC;IACH;IACA,MAAMsD,KAAK,GAAG/D,EAAE,CAACgE,UAAU,CAACF,UAAU,CAAC;IACvC,IAAIC,KAAK,EAAE;MACTjD,GAAG,CAACwC,IAAI,CAAE,qBAAoBQ,UAAW,iBAAgB,CAAC;IAC5D,CAAC,MAAM;MACLhD,GAAG,CAACwC,IAAI,CAAE,mDAAkDQ,UAAW,EAAC,CAAC;MACzE,MAAM9D,EAAE,CAACiE,KAAK,CAACH,UAAU,CAAC;IAC5B;EACF;EAEA,MAAMI,OAAO,GAAG,EAAE;EAElB,MAAMC,kBAAkB,GAAG;IACzB;IACAC,UAAU,EAAE,CAAC;MAAErC,SAAS;MAAE8B;IAAa,CAAC,CAAC;IACzCnC,kBAAkB;IAClBQ,QAAQ;IACRE,IAAI;IACJa;EACF,CAAC;EAED,IAAI,CAACd,MAAM,IAAIA,MAAM,CAACkC,MAAM,KAAK,CAAC,IAAIlC,MAAM,CAACyB,QAAQ,CAAC,iBAAiB,CAAC,EAAE;IACxE,MAAMU,0BAA0B,GAAG;MACjC,GAAGH,kBAAkB;MAErB;MACAI,aAAa,EAAEhD,OAAO;MACtBiD,WAAW,EAAEhD,cAAc;MAC3BmC,WAAW;MACXvC,cAAc;MACdC,QAAQ;MACRS,UAAU;MAEV;MACAoB,UAAU;MACVC;IACF,CAAC;IAED,MAAMsB,oBAAoB,GAAG,MAAM/D,qBAAqB,CAAC;MACvDyB,MAAM,EAAE,iBAAiB;MACzBuC,MAAM,EAAEJ;IACV,CAAC,CAAC;IACFJ,OAAO,CAACS,IAAI,CAACF,oBAAoB,CAAC;EACpC;EAEA,IAAItC,MAAM,IAAIA,MAAM,CAACyB,QAAQ,CAAC,iBAAiB,CAAC,EAAE;IAChD,MAAMgB,0BAA0B,GAAG;MACjC,GAAGT,kBAAkB;MAErB;MACAK,WAAW,EAAEhD,cAAc;MAC3BmC,WAAW;MACXvC,cAAc;MACdU,UAAU;MACVc,UAAU;MACVC,mBAAmB;MACnBJ,SAAS;MACTF,OAAO;MACPC,OAAO;MACPF,MAAM;MACNI,mBAAmB;MACnBC,qBAAqB;MAErB;MACAO,UAAU;MACVC,aAAa;MACbF,oBAAoB,EAAE9C,2BAA2B;MACjD0E,cAAc,EAAEA,CAACC,kBAA0B,EAAEC,eAAuB,KAAK;QACvE,OAAO/B,cAAc,CACnB;UACEjB,SAAS,EAAE+C,kBAAkB;UAC7BnD,WAAW;UACXqD,QAAQ,EAAE,KAAK;UACf;UACA;UACA7D,YAAY,EAAE4D;QAChB,CAAC,EACD;UACE;UACAE,gBAAgB,EAAE;QACpB,CACF,CAAC;MACH;IACF,CAAC;IAED,MAAMC,oBAAoB,GAAG,MAAMxE,qBAAqB,CAAC;MACvDyB,MAAM,EAAE,iBAAiB;MACzBuC,MAAM,EAAEE;IACV,CAAC,CAAC;IACFV,OAAO,CAACS,IAAI,CAACO,oBAAoB,CAAC;EACpC;EAEA,IAAI/C,MAAM,IAAIA,MAAM,CAACyB,QAAQ,CAAC,UAAU,CAAC,EAAE;IACzC,MAAMuB,oBAAoB,GAAG;MAC3B,GAAGhB,kBAAkB;MACrBrB,cAAc;MACdC;IACF,CAAC;IAED,MAAMqC,cAAc,GAAG,MAAM1E,qBAAqB,CAAC;MACjDyB,MAAM,EAAE,UAAU;MAClBuC,MAAM,EAAES;IACV,CAAC,CAAC;IACFjB,OAAO,CAACS,IAAI,CAACS,cAAc,CAAC;EAC9B;EAEA,MAAMC,eAAe,GAAG,IAAIzE,oBAAoB,CAAC;IAC/CqC,oBAAoB;IACpBiB;EACF,CAAC,CAAC;EAEF,MAAMmB,eAAe,CAACnE,GAAG,CAAC,CAAC;EAE3B,IAAIW,QAAQ,EAAE;IACZf,GAAG,CAACwC,IAAI,CAAC,iDAAiD,CAAC;EAC7D,CAAC,MAAM;IACLxC,GAAG,CAACwC,IAAI,CAAC,sDAAsD,CAAC;IAEhEF,cAAc,CAAC;MACbiC,eAAe;MACftD,SAAS;MACTC,SAAS;MACTC,YAAY;MACZd,YAAY;MACZQ,WAAW;MACXC;IACF,CAAC,CAAC;EACJ;EAEA,OAAOyD,eAAe;AACxB"}