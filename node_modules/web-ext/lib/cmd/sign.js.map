{"version": 3, "file": "sign.js", "names": ["path", "fs", "signAddon", "defaultAddonSigner", "defaultBuilder", "isErrorWithCode", "UsageError", "WebExtError", "prepareArtifactsDir", "createLogger", "getValidatedManifest", "getManifestId", "defaultSubmitAddonSigner", "saveIdToFile", "withTempDir", "isTTY", "log", "import", "meta", "url", "defaultAsyncFsReadFile", "readFile", "bind", "extensionIdFile", "sign", "amoBaseUrl", "<PERSON><PERSON><PERSON><PERSON>", "apiProxy", "apiSecret", "apiUrlPrefix", "useSubmissionApi", "artifactsDir", "id", "ignoreFiles", "sourceDir", "timeout", "disableP<PERSON>ress<PERSON>ar", "process", "stdin", "verbose", "channel", "amoMetadata", "webextVersion", "build", "preValidatedManifest", "submitAddon", "asyncFsReadFile", "warn", "tmpDir", "manifestData", "savedIdPath", "join", "buildResult", "idFromSourceDir", "Promise", "all", "showReadyMessage", "getIdFromFile", "manifestId", "debug", "info", "metaDataJson", "metadataFileBuffer", "JSON", "parse", "toString", "err", "userAgentString", "signSubmitArgs", "xpiPath", "extensionPath", "downloadDir", "result", "success", "newId", "downloadedFiles", "version", "apiRequestConfig", "headers", "Error", "clientError", "message", "filePath", "content", "error", "lines", "split", "filter", "line", "trim", "startsWith"], "sources": ["../../src/cmd/sign.js"], "sourcesContent": ["/* @flow */\nimport path from 'path';\n\nimport { fs } from 'mz';\nimport { signAddon as defaultAddonSigner } from 'sign-addon';\n\nimport defaultBuilder from './build.js';\nimport { isErrorWithCode, UsageError, WebExtError } from '../errors.js';\nimport { prepareArtifactsDir } from '../util/artifacts.js';\nimport { createLogger } from '../util/logger.js';\nimport getValidatedManifest, { getManifestId } from '../util/manifest.js';\nimport type { ExtensionManifest } from '../util/manifest.js';\nimport {\n  signAddon as defaultSubmitAddonSigner,\n  saveIdToFile,\n} from '../util/submit-addon.js';\nimport type { SignResult } from '../util/submit-addon.js';\nimport { withTempDir } from '../util/temp-dir.js';\nimport { isTTY } from '../util/stdin.js';\n\nexport type { SignResult };\n\nconst log = createLogger(import.meta.url);\n\nconst defaultAsyncFsReadFile: (string) => Promise<Buffer> =\n  fs.readFile.bind(fs);\n\nexport const extensionIdFile = '.web-extension-id';\n\n// Sign command types and implementation.\n\nexport type SignParams = {|\n  amoBaseUrl: string,\n  apiKey: string,\n  apiProxy?: string,\n  apiSecret: string,\n  apiUrlPrefix: string,\n  useSubmissionApi?: boolean,\n  artifactsDir: string,\n  id?: string,\n  ignoreFiles?: Array<string>,\n  sourceDir: string,\n  timeout: number,\n  disableProgressBar?: boolean,\n  verbose?: boolean,\n  channel?: string,\n  amoMetadata?: string,\n  webextVersion: string,\n|};\n\nexport type SignOptions = {\n  build?: typeof defaultBuilder,\n  signAddon?: typeof defaultAddonSigner,\n  submitAddon?: typeof defaultSubmitAddonSigner,\n  preValidatedManifest?: ExtensionManifest,\n  shouldExitProgram?: boolean,\n  asyncFsReadFile?: typeof defaultAsyncFsReadFile,\n};\n\nexport default function sign(\n  {\n    amoBaseUrl,\n    apiKey,\n    apiProxy,\n    apiSecret,\n    apiUrlPrefix,\n    useSubmissionApi = false,\n    artifactsDir,\n    id,\n    ignoreFiles = [],\n    sourceDir,\n    timeout,\n    disableProgressBar = !isTTY(process.stdin),\n    verbose,\n    channel,\n    amoMetadata,\n    webextVersion,\n  }: SignParams,\n  {\n    build = defaultBuilder,\n    preValidatedManifest,\n    signAddon = defaultAddonSigner,\n    submitAddon = defaultSubmitAddonSigner,\n    asyncFsReadFile = defaultAsyncFsReadFile,\n  }: SignOptions = {}\n): Promise<SignResult> {\n  if (!useSubmissionApi) {\n    log.warn(\n      'IMPORTANT: web-ext v8 will introduce a new AMO submission API for signing and you might need to take actions when upgrading from v7 to v8. You can test this upcoming change now by specifying `--use-submission-api` to the `sign` command. If you want to keep using the API you are using now (via \"sign-addon\"), you must stay on v7.'\n    );\n  }\n\n  return withTempDir(async function (tmpDir) {\n    await prepareArtifactsDir(artifactsDir);\n\n    let manifestData;\n    const savedIdPath = path.join(sourceDir, extensionIdFile);\n\n    if (preValidatedManifest) {\n      manifestData = preValidatedManifest;\n    } else {\n      manifestData = await getValidatedManifest(sourceDir);\n    }\n\n    const [buildResult, idFromSourceDir] = await Promise.all([\n      build(\n        { sourceDir, ignoreFiles, artifactsDir: tmpDir.path() },\n        { manifestData, showReadyMessage: false }\n      ),\n      getIdFromFile(savedIdPath),\n    ]);\n\n    const manifestId = getManifestId(manifestData);\n\n    if (useSubmissionApi && id && !manifestId) {\n      throw new UsageError(\n        `Cannot set custom ID ${id} - addon submission API requires a ` +\n          'custom ID be specified in the manifest'\n      );\n    }\n    if (useSubmissionApi && idFromSourceDir && !manifestId) {\n      throw new UsageError(\n        'Cannot use previously auto-generated extension ID ' +\n          `${idFromSourceDir} - addon submission API ` +\n          'requires a custom ID be specified in the manifest'\n      );\n    }\n    if (id && manifestId) {\n      throw new UsageError(\n        `Cannot set custom ID ${id} because manifest.json ` +\n          `declares ID ${manifestId}`\n      );\n    }\n    if (id) {\n      log.debug(`Using custom ID declared as --id=${id}`);\n    }\n\n    if (manifestId) {\n      id = manifestId;\n    }\n\n    if (!id && idFromSourceDir) {\n      log.info(\n        `Using previously auto-generated extension ID: ${idFromSourceDir}`\n      );\n      id = idFromSourceDir;\n    }\n\n    if (!id) {\n      log.warn('No extension ID specified (it will be auto-generated)');\n    }\n\n    if (useSubmissionApi && !channel) {\n      throw new UsageError(\n        'channel is a required parameter for the addon submission API'\n      );\n    }\n\n    if (useSubmissionApi && apiProxy) {\n      // https://github.com/mozilla/web-ext/issues/2472\n      throw new UsageError(\n        \"apiProxy isn't yet supported for the addon submission API. \" +\n          'See https://github.com/mozilla/web-ext/issues/2472'\n      );\n    }\n\n    let metaDataJson;\n    if (amoMetadata) {\n      const metadataFileBuffer = await asyncFsReadFile(amoMetadata);\n      try {\n        metaDataJson = JSON.parse(metadataFileBuffer.toString());\n      } catch (err) {\n        throw new UsageError('Invalid JSON in listing metadata');\n      }\n    }\n    const userAgentString = `web-ext/${webextVersion}`;\n\n    const signSubmitArgs = {\n      apiKey,\n      apiSecret,\n      timeout,\n      id,\n      xpiPath: buildResult.extensionPath,\n      downloadDir: artifactsDir,\n      channel,\n    };\n\n    let result;\n    try {\n      if (useSubmissionApi) {\n        result = await submitAddon({\n          ...signSubmitArgs,\n          amoBaseUrl,\n          // $FlowIgnore: we verify 'channel' is set above\n          channel,\n          savedIdPath,\n          metaDataJson,\n          userAgentString,\n        });\n      } else {\n        const {\n          success,\n          id: newId,\n          downloadedFiles,\n        } = await signAddon({\n          ...signSubmitArgs,\n          apiUrlPrefix,\n          apiProxy,\n          disableProgressBar,\n          verbose,\n          version: manifestData.version,\n          apiRequestConfig: { headers: { 'User-Agent': userAgentString } },\n        });\n        if (!success) {\n          throw new Error('The extension could not be signed');\n        }\n        result = { id: newId, downloadedFiles };\n        // All information about the downloaded files would have already been\n        // logged by signAddon(). submitAddon() calls saveIdToFile itself.\n        await saveIdToFile(savedIdPath, newId);\n        log.info(`Extension ID: ${newId}`);\n        log.info('SUCCESS');\n      }\n    } catch (clientError) {\n      log.info('FAIL');\n      throw new WebExtError(clientError.message);\n    }\n\n    return result;\n  });\n}\n\nexport async function getIdFromFile(\n  filePath: string,\n  asyncFsReadFile: typeof defaultAsyncFsReadFile = defaultAsyncFsReadFile\n): Promise<string | void> {\n  let content;\n\n  try {\n    content = await asyncFsReadFile(filePath);\n  } catch (error) {\n    if (isErrorWithCode('ENOENT', error)) {\n      log.debug(`No ID file found at: ${filePath}`);\n      return;\n    }\n    throw error;\n  }\n\n  let lines = content.toString().split('\\n');\n  lines = lines.filter((line) => {\n    line = line.trim();\n    if (line && !line.startsWith('#')) {\n      return line;\n    }\n  });\n\n  const id = lines[0];\n  log.debug(`Found extension ID ${id} in ${filePath}`);\n\n  if (!id) {\n    throw new UsageError(`No ID found in extension ID file ${filePath}`);\n  }\n\n  return id;\n}\n"], "mappings": "AACA,OAAOA,IAAI,MAAM,MAAM;AAEvB,SAASC,EAAE,QAAQ,IAAI;AACvB,SAASC,SAAS,IAAIC,kBAAkB,QAAQ,YAAY;AAE5D,OAAOC,cAAc,MAAM,YAAY;AACvC,SAASC,eAAe,EAAEC,UAAU,EAAEC,WAAW,QAAQ,cAAc;AACvE,SAASC,mBAAmB,QAAQ,sBAAsB;AAC1D,SAASC,YAAY,QAAQ,mBAAmB;AAChD,OAAOC,oBAAoB,IAAIC,aAAa,QAAQ,qBAAqB;AAEzE,SACET,SAAS,IAAIU,wBAAwB,EACrCC,YAAY,QACP,yBAAyB;AAEhC,SAASC,WAAW,QAAQ,qBAAqB;AACjD,SAASC,KAAK,QAAQ,kBAAkB;AAIxC,MAAMC,GAAG,GAAGP,YAAY,CAACQ,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC;AAEzC,MAAMC,sBAAmD,GACvDnB,EAAE,CAACoB,QAAQ,CAACC,IAAI,CAACrB,EAAE,CAAC;AAEtB,OAAO,MAAMsB,eAAe,GAAG,mBAAmB;;AAElD;;AA8BA,eAAe,SAASC,IAAIA,CAC1B;EACEC,UAAU;EACVC,MAAM;EACNC,QAAQ;EACRC,SAAS;EACTC,YAAY;EACZC,gBAAgB,GAAG,KAAK;EACxBC,YAAY;EACZC,EAAE;EACFC,WAAW,GAAG,EAAE;EAChBC,SAAS;EACTC,OAAO;EACPC,kBAAkB,GAAG,CAACrB,KAAK,CAACsB,OAAO,CAACC,KAAK,CAAC;EAC1CC,OAAO;EACPC,OAAO;EACPC,WAAW;EACXC;AACU,CAAC,EACb;EACEC,KAAK,GAAGvC,cAAc;EACtBwC,oBAAoB;EACpB1C,SAAS,GAAGC,kBAAkB;EAC9B0C,WAAW,GAAGjC,wBAAwB;EACtCkC,eAAe,GAAG1B;AACP,CAAC,GAAG,CAAC,CAAC,EACE;EACrB,IAAI,CAACU,gBAAgB,EAAE;IACrBd,GAAG,CAAC+B,IAAI,CACN,2UACF,CAAC;EACH;EAEA,OAAOjC,WAAW,CAAC,gBAAgBkC,MAAM,EAAE;IACzC,MAAMxC,mBAAmB,CAACuB,YAAY,CAAC;IAEvC,IAAIkB,YAAY;IAChB,MAAMC,WAAW,GAAGlD,IAAI,CAACmD,IAAI,CAACjB,SAAS,EAAEX,eAAe,CAAC;IAEzD,IAAIqB,oBAAoB,EAAE;MACxBK,YAAY,GAAGL,oBAAoB;IACrC,CAAC,MAAM;MACLK,YAAY,GAAG,MAAMvC,oBAAoB,CAACwB,SAAS,CAAC;IACtD;IAEA,MAAM,CAACkB,WAAW,EAAEC,eAAe,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACvDZ,KAAK,CACH;MAAET,SAAS;MAAED,WAAW;MAAEF,YAAY,EAAEiB,MAAM,CAAChD,IAAI,CAAC;IAAE,CAAC,EACvD;MAAEiD,YAAY;MAAEO,gBAAgB,EAAE;IAAM,CAC1C,CAAC,EACDC,aAAa,CAACP,WAAW,CAAC,CAC3B,CAAC;IAEF,MAAMQ,UAAU,GAAG/C,aAAa,CAACsC,YAAY,CAAC;IAE9C,IAAInB,gBAAgB,IAAIE,EAAE,IAAI,CAAC0B,UAAU,EAAE;MACzC,MAAM,IAAIpD,UAAU,CACjB,wBAAuB0B,EAAG,qCAAoC,GAC7D,wCACJ,CAAC;IACH;IACA,IAAIF,gBAAgB,IAAIuB,eAAe,IAAI,CAACK,UAAU,EAAE;MACtD,MAAM,IAAIpD,UAAU,CAClB,oDAAoD,GACjD,GAAE+C,eAAgB,0BAAyB,GAC5C,mDACJ,CAAC;IACH;IACA,IAAIrB,EAAE,IAAI0B,UAAU,EAAE;MACpB,MAAM,IAAIpD,UAAU,CACjB,wBAAuB0B,EAAG,yBAAwB,GAChD,eAAc0B,UAAW,EAC9B,CAAC;IACH;IACA,IAAI1B,EAAE,EAAE;MACNhB,GAAG,CAAC2C,KAAK,CAAE,oCAAmC3B,EAAG,EAAC,CAAC;IACrD;IAEA,IAAI0B,UAAU,EAAE;MACd1B,EAAE,GAAG0B,UAAU;IACjB;IAEA,IAAI,CAAC1B,EAAE,IAAIqB,eAAe,EAAE;MAC1BrC,GAAG,CAAC4C,IAAI,CACL,iDAAgDP,eAAgB,EACnE,CAAC;MACDrB,EAAE,GAAGqB,eAAe;IACtB;IAEA,IAAI,CAACrB,EAAE,EAAE;MACPhB,GAAG,CAAC+B,IAAI,CAAC,uDAAuD,CAAC;IACnE;IAEA,IAAIjB,gBAAgB,IAAI,CAACU,OAAO,EAAE;MAChC,MAAM,IAAIlC,UAAU,CAClB,8DACF,CAAC;IACH;IAEA,IAAIwB,gBAAgB,IAAIH,QAAQ,EAAE;MAChC;MACA,MAAM,IAAIrB,UAAU,CAClB,6DAA6D,GAC3D,oDACJ,CAAC;IACH;IAEA,IAAIuD,YAAY;IAChB,IAAIpB,WAAW,EAAE;MACf,MAAMqB,kBAAkB,GAAG,MAAMhB,eAAe,CAACL,WAAW,CAAC;MAC7D,IAAI;QACFoB,YAAY,GAAGE,IAAI,CAACC,KAAK,CAACF,kBAAkB,CAACG,QAAQ,CAAC,CAAC,CAAC;MAC1D,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZ,MAAM,IAAI5D,UAAU,CAAC,kCAAkC,CAAC;MAC1D;IACF;IACA,MAAM6D,eAAe,GAAI,WAAUzB,aAAc,EAAC;IAElD,MAAM0B,cAAc,GAAG;MACrB1C,MAAM;MACNE,SAAS;MACTO,OAAO;MACPH,EAAE;MACFqC,OAAO,EAAEjB,WAAW,CAACkB,aAAa;MAClCC,WAAW,EAAExC,YAAY;MACzBS;IACF,CAAC;IAED,IAAIgC,MAAM;IACV,IAAI;MACF,IAAI1C,gBAAgB,EAAE;QACpB0C,MAAM,GAAG,MAAM3B,WAAW,CAAC;UACzB,GAAGuB,cAAc;UACjB3C,UAAU;UACV;UACAe,OAAO;UACPU,WAAW;UACXW,YAAY;UACZM;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAM;UACJM,OAAO;UACPzC,EAAE,EAAE0C,KAAK;UACTC;QACF,CAAC,GAAG,MAAMzE,SAAS,CAAC;UAClB,GAAGkE,cAAc;UACjBvC,YAAY;UACZF,QAAQ;UACRS,kBAAkB;UAClBG,OAAO;UACPqC,OAAO,EAAE3B,YAAY,CAAC2B,OAAO;UAC7BC,gBAAgB,EAAE;YAAEC,OAAO,EAAE;cAAE,YAAY,EAAEX;YAAgB;UAAE;QACjE,CAAC,CAAC;QACF,IAAI,CAACM,OAAO,EAAE;UACZ,MAAM,IAAIM,KAAK,CAAC,mCAAmC,CAAC;QACtD;QACAP,MAAM,GAAG;UAAExC,EAAE,EAAE0C,KAAK;UAAEC;QAAgB,CAAC;QACvC;QACA;QACA,MAAM9D,YAAY,CAACqC,WAAW,EAAEwB,KAAK,CAAC;QACtC1D,GAAG,CAAC4C,IAAI,CAAE,iBAAgBc,KAAM,EAAC,CAAC;QAClC1D,GAAG,CAAC4C,IAAI,CAAC,SAAS,CAAC;MACrB;IACF,CAAC,CAAC,OAAOoB,WAAW,EAAE;MACpBhE,GAAG,CAAC4C,IAAI,CAAC,MAAM,CAAC;MAChB,MAAM,IAAIrD,WAAW,CAACyE,WAAW,CAACC,OAAO,CAAC;IAC5C;IAEA,OAAOT,MAAM;EACf,CAAC,CAAC;AACJ;AAEA,OAAO,eAAef,aAAaA,CACjCyB,QAAgB,EAChBpC,eAA8C,GAAG1B,sBAAsB,EAC/C;EACxB,IAAI+D,OAAO;EAEX,IAAI;IACFA,OAAO,GAAG,MAAMrC,eAAe,CAACoC,QAAQ,CAAC;EAC3C,CAAC,CAAC,OAAOE,KAAK,EAAE;IACd,IAAI/E,eAAe,CAAC,QAAQ,EAAE+E,KAAK,CAAC,EAAE;MACpCpE,GAAG,CAAC2C,KAAK,CAAE,wBAAuBuB,QAAS,EAAC,CAAC;MAC7C;IACF;IACA,MAAME,KAAK;EACb;EAEA,IAAIC,KAAK,GAAGF,OAAO,CAAClB,QAAQ,CAAC,CAAC,CAACqB,KAAK,CAAC,IAAI,CAAC;EAC1CD,KAAK,GAAGA,KAAK,CAACE,MAAM,CAAEC,IAAI,IAAK;IAC7BA,IAAI,GAAGA,IAAI,CAACC,IAAI,CAAC,CAAC;IAClB,IAAID,IAAI,IAAI,CAACA,IAAI,CAACE,UAAU,CAAC,GAAG,CAAC,EAAE;MACjC,OAAOF,IAAI;IACb;EACF,CAAC,CAAC;EAEF,MAAMxD,EAAE,GAAGqD,KAAK,CAAC,CAAC,CAAC;EACnBrE,GAAG,CAAC2C,KAAK,CAAE,sBAAqB3B,EAAG,OAAMkD,QAAS,EAAC,CAAC;EAEpD,IAAI,CAAClD,EAAE,EAAE;IACP,MAAM,IAAI1B,UAAU,CAAE,oCAAmC4E,QAAS,EAAC,CAAC;EACtE;EAEA,OAAOlD,EAAE;AACX"}