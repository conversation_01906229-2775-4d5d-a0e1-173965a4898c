{"version": 3, "file": "index.js", "names": ["build", "params", "options", "default", "runCommand", "lint", "run", "sign", "docs"], "sources": ["../../src/cmd/index.js"], "sourcesContent": ["/* @flow */\n\nimport type {\n  BuildCmdParams,\n  BuildCmdOptions,\n  ExtensionBuildResult,\n} from './build.js';\nimport type { LintCmdParams, LintCmdOptions } from './lint.js';\nimport type { CmdRunParams, CmdRunOptions } from './run.js';\nimport type { MultiExtensionRunner } from '../extension-runners/index.js';\nimport type { SignParams, SignOptions, SignResult } from './sign.js';\nimport type { DocsParams, DocsOptions } from './docs.js';\n\n// This module exports entry points for all supported commands. For performance\n// reasons (faster start-up), the implementations are not statically imported\n// at the top of the file, but lazily loaded in the (exported) functions.\n// The latter would slow down start-up by several seconds, as seen in #1302 .\n\nasync function build(\n  params: BuildCmdParams,\n  options: BuildCmdOptions\n): Promise<ExtensionBuildResult> {\n  const { default: runCommand } = await import('./build.js');\n  return runCommand(params, options);\n}\n\nasync function lint(\n  params: LintCmdParams,\n  options: LintCmdOptions\n): Promise<void> {\n  const { default: runCommand } = await import('./lint.js');\n  return runCommand(params, options);\n}\n\nasync function run(\n  params: CmdRunParams,\n  options: CmdRunOptions\n): Promise<MultiExtensionRunner> {\n  const { default: runCommand } = await import('./run.js');\n  return runCommand(params, options);\n}\n\nasync function sign(\n  params: SignParams,\n  options: SignOptions\n): Promise<SignResult> {\n  const { default: runCommand } = await import('./sign.js');\n  return runCommand(params, options);\n}\n\nasync function docs(params: DocsParams, options: DocsOptions): Promise<void> {\n  const { default: runCommand } = await import('./docs.js');\n  return runCommand(params, options);\n}\n\nexport default { build, lint, run, sign, docs };\n"], "mappings": "AAaA;AACA;AACA;AACA;AAEA,eAAeA,KAAKA,CAClBC,MAAsB,EACtBC,OAAwB,EACO;EAC/B,MAAM;IAAEC,OAAO,EAAEC;EAAW,CAAC,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC;EAC1D,OAAOA,UAAU,CAACH,MAAM,EAAEC,OAAO,CAAC;AACpC;AAEA,eAAeG,IAAIA,CACjBJ,MAAqB,EACrBC,OAAuB,EACR;EACf,MAAM;IAAEC,OAAO,EAAEC;EAAW,CAAC,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC;EACzD,OAAOA,UAAU,CAACH,MAAM,EAAEC,OAAO,CAAC;AACpC;AAEA,eAAeI,GAAGA,CAChBL,MAAoB,EACpBC,OAAsB,EACS;EAC/B,MAAM;IAAEC,OAAO,EAAEC;EAAW,CAAC,GAAG,MAAM,MAAM,CAAC,UAAU,CAAC;EACxD,OAAOA,UAAU,CAACH,MAAM,EAAEC,OAAO,CAAC;AACpC;AAEA,eAAeK,IAAIA,CACjBN,MAAkB,EAClBC,OAAoB,EACC;EACrB,MAAM;IAAEC,OAAO,EAAEC;EAAW,CAAC,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC;EACzD,OAAOA,UAAU,CAACH,MAAM,EAAEC,OAAO,CAAC;AACpC;AAEA,eAAeM,IAAIA,CAACP,MAAkB,EAAEC,OAAoB,EAAiB;EAC3E,MAAM;IAAEC,OAAO,EAAEC;EAAW,CAAC,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC;EACzD,OAAOA,UAAU,CAACH,MAAM,EAAEC,OAAO,CAAC;AACpC;AAEA,eAAe;EAAEF,KAAK;EAAEK,IAAI;EAAEC,GAAG;EAAEC,IAAI;EAAEC;AAAK,CAAC"}