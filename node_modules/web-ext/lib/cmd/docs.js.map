{"version": 3, "file": "docs.js", "names": ["open", "createLogger", "log", "import", "meta", "url", "docs", "params", "openUrl", "error", "debug"], "sources": ["../../src/cmd/docs.js"], "sourcesContent": ["/* @flow */\nimport open from 'open';\n\nimport { createLogger } from '../util/logger.js';\n\nconst log = createLogger(import.meta.url);\n\nexport type DocsParams = {\n  noInput?: boolean,\n  shouldExitProgram?: boolean,\n};\n\nexport type DocsOptions = {\n  openUrl?: typeof open,\n};\n\n// eslint-disable-next-line max-len\nexport const url =\n  'https://extensionworkshop.com/documentation/develop/getting-started-with-web-ext/';\n\nexport default async function docs(\n  params: DocsParams,\n  { openUrl = open }: DocsOptions = {}\n): Promise<void> {\n  try {\n    await openUrl(url);\n  } catch (error) {\n    log.debug(`Encountered an error while opening URL ${url}`, error);\n    throw error;\n  }\n}\n"], "mappings": "AACA,OAAOA,IAAI,MAAM,MAAM;AAEvB,SAASC,YAAY,QAAQ,mBAAmB;AAEhD,MAAMC,GAAG,GAAGD,YAAY,CAACE,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC;AAWzC;AACA,OAAO,MAAMA,GAAG,GACd,mFAAmF;AAErF,eAAe,eAAeC,IAAIA,CAChCC,MAAkB,EAClB;EAAEC,OAAO,GAAGR;AAAkB,CAAC,GAAG,CAAC,CAAC,EACrB;EACf,IAAI;IACF,MAAMQ,OAAO,CAACH,GAAG,CAAC;EACpB,CAAC,CAAC,OAAOI,KAAK,EAAE;IACdP,GAAG,CAACQ,KAAK,CAAE,0CAAyCL,GAAI,EAAC,EAAEI,KAAK,CAAC;IACjE,MAAMA,KAAK;EACb;AACF"}