{"version": 3, "file": "config.js", "names": ["os", "path", "importFresh", "camelCase", "decamelize", "fileExists", "createLogger", "UsageError", "WebExtError", "log", "import", "meta", "url", "applyConfigToArgv", "argv", "argvFromCLI", "configObject", "options", "configFileName", "newArgv", "option", "Object", "keys", "Array", "isArray", "decamelizedOptName", "separator", "type", "undefined", "expectedType", "optionType", "defaultValue", "default", "wasValueSetOnCLI", "debug", "coerce", "loadJSConfigFile", "filePath", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "resolve", "error", "message", "endsWith", "webExt", "length", "discoverConfigFiles", "getHomeDir", "homedir", "magicConfigName", "possibleConfigs", "join", "process", "cwd", "configs", "Promise", "all", "map", "fileName", "resolvedFileName", "existingConfigs", "for<PERSON>ach", "f", "push"], "sources": ["../src/config.js"], "sourcesContent": ["/* @flow */\nimport os from 'os';\nimport path from 'path';\n\nimport importFresh from 'import-fresh';\nimport camelCase from 'camelcase';\nimport decamelize from 'decamelize';\n\nimport fileExists from './util/file-exists.js';\nimport { createLogger } from './util/logger.js';\nimport { UsageError, WebExtError } from './errors.js';\n\nconst log = createLogger(import.meta.url);\n\ntype ApplyConfigToArgvParams = {|\n  // This is the argv object which will get updated by each\n  // config applied.\n  argv: Object,\n  // This is the argv that only has CLI values applied to it.\n  argvFromCLI: Object,\n  configObject: Object,\n  options: Object,\n  configFileName: string,\n|};\n\nexport function applyConfigToArgv({\n  argv,\n  argvFromCLI,\n  configObject,\n  options,\n  configFileName,\n}: ApplyConfigToArgvParams): Object {\n  let newArgv = { ...argv };\n\n  for (const option of Object.keys(configObject)) {\n    if (camelCase(option) !== option) {\n      throw new UsageError(\n        `The config option \"${option}\" must be ` +\n          `specified in camel case: \"${camelCase(option)}\"`\n      );\n    }\n\n    // A config option cannot be a sub-command config\n    // object if it is an array.\n    if (\n      !Array.isArray(configObject[option]) &&\n      typeof options[option] === 'object' &&\n      typeof configObject[option] === 'object'\n    ) {\n      // Descend into the nested configuration for a sub-command.\n      newArgv = applyConfigToArgv({\n        argv: newArgv,\n        argvFromCLI,\n        configObject: configObject[option],\n        options: options[option],\n        configFileName,\n      });\n      continue;\n    }\n\n    const decamelizedOptName = decamelize(option, { separator: '-' });\n\n    if (typeof options[decamelizedOptName] !== 'object') {\n      throw new UsageError(\n        `The config file at ${configFileName} specified ` +\n          `an unknown option: \"${option}\"`\n      );\n    }\n    if (options[decamelizedOptName].type === undefined) {\n      // This means yargs option type wasn't not defined correctly\n      throw new WebExtError(`Option: ${option} was defined without a type.`);\n    }\n\n    const expectedType =\n      options[decamelizedOptName].type === 'count'\n        ? 'number'\n        : options[decamelizedOptName].type;\n\n    const optionType = Array.isArray(configObject[option])\n      ? 'array'\n      : typeof configObject[option];\n\n    if (optionType !== expectedType) {\n      throw new UsageError(\n        `The config file at ${configFileName} specified ` +\n          `the type of \"${option}\" incorrectly as \"${optionType}\"` +\n          ` (expected type \"${expectedType}\")`\n      );\n    }\n\n    let defaultValue;\n    if (options[decamelizedOptName]) {\n      if (options[decamelizedOptName].default !== undefined) {\n        defaultValue = options[decamelizedOptName].default;\n      } else if (expectedType === 'boolean') {\n        defaultValue = false;\n      }\n    }\n\n    // This is our best effort (without patching yargs) to detect\n    // if a value was set on the CLI instead of in the config.\n    // It looks for a default value and if the argv value is\n    // different, it assumes that the value was configured on the CLI.\n\n    const wasValueSetOnCLI =\n      typeof argvFromCLI[option] !== 'undefined' &&\n      argvFromCLI[option] !== defaultValue;\n    if (wasValueSetOnCLI) {\n      log.debug(\n        `Favoring CLI: ${option}=${argvFromCLI[option]} over ` +\n          `configuration: ${option}=${configObject[option]}`\n      );\n      newArgv[option] = argvFromCLI[option];\n      continue;\n    }\n\n    newArgv[option] = configObject[option];\n\n    const coerce = options[decamelizedOptName].coerce;\n    if (coerce) {\n      log.debug(`Calling coerce() on configured value for ${option}`);\n      newArgv[option] = coerce(newArgv[option]);\n    }\n\n    newArgv[decamelizedOptName] = newArgv[option];\n  }\n  return newArgv;\n}\n\nexport function loadJSConfigFile(filePath: string): Object {\n  const resolvedFilePath = path.resolve(filePath);\n  log.debug(\n    `Loading JS config file: \"${filePath}\" ` +\n      `(resolved to \"${resolvedFilePath}\")`\n  );\n  let configObject;\n  try {\n    configObject = importFresh(resolvedFilePath);\n  } catch (error) {\n    log.debug('Handling error:', error);\n    throw new UsageError(\n      `Cannot read config file: ${resolvedFilePath}\\n` +\n        `Error: ${error.message}`\n    );\n  }\n  if (filePath.endsWith('package.json')) {\n    log.debug('Looking for webExt key inside package.json file');\n    configObject = configObject.webExt || {};\n  }\n  if (Object.keys(configObject).length === 0) {\n    log.debug(\n      `Config file ${resolvedFilePath} did not define any options. ` +\n        'Did you set module.exports = {...}?'\n    );\n  }\n  return configObject;\n}\n\ntype DiscoverConfigFilesParams = {\n  getHomeDir: () => string,\n};\n\nexport async function discoverConfigFiles({\n  getHomeDir = os.homedir,\n}: DiscoverConfigFilesParams = {}): Promise<Array<string>> {\n  const magicConfigName = 'web-ext-config.js';\n\n  // Config files will be loaded in this order.\n  const possibleConfigs = [\n    // Look for a magic hidden config (preceded by dot) in home dir.\n    path.join(getHomeDir(), `.${magicConfigName}`),\n    // Look for webExt key inside package.json file\n    path.join(process.cwd(), 'package.json'),\n    // Look for a magic config in the current working directory.\n    path.join(process.cwd(), magicConfigName),\n  ];\n\n  const configs = await Promise.all(\n    possibleConfigs.map(async (fileName) => {\n      const resolvedFileName = path.resolve(fileName);\n      if (await fileExists(resolvedFileName)) {\n        return resolvedFileName;\n      } else {\n        log.debug(\n          `Discovered config \"${resolvedFileName}\" does not ` +\n            'exist or is not readable'\n        );\n        return undefined;\n      }\n    })\n  );\n\n  const existingConfigs = [];\n  configs.forEach((f) => {\n    if (typeof f === 'string') {\n      existingConfigs.push(f);\n    }\n  });\n  return existingConfigs;\n}\n"], "mappings": "AACA,OAAOA,EAAE,MAAM,IAAI;AACnB,OAAOC,IAAI,MAAM,MAAM;AAEvB,OAAOC,WAAW,MAAM,cAAc;AACtC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,UAAU,MAAM,YAAY;AAEnC,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,SAASC,YAAY,QAAQ,kBAAkB;AAC/C,SAASC,UAAU,EAAEC,WAAW,QAAQ,aAAa;AAErD,MAAMC,GAAG,GAAGH,YAAY,CAACI,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC;AAazC,OAAO,SAASC,iBAAiBA,CAAC;EAChCC,IAAI;EACJC,WAAW;EACXC,YAAY;EACZC,OAAO;EACPC;AACuB,CAAC,EAAU;EAClC,IAAIC,OAAO,GAAG;IAAE,GAAGL;EAAK,CAAC;EAEzB,KAAK,MAAMM,MAAM,IAAIC,MAAM,CAACC,IAAI,CAACN,YAAY,CAAC,EAAE;IAC9C,IAAIb,SAAS,CAACiB,MAAM,CAAC,KAAKA,MAAM,EAAE;MAChC,MAAM,IAAIb,UAAU,CACjB,sBAAqBa,MAAO,YAAW,GACrC,6BAA4BjB,SAAS,CAACiB,MAAM,CAAE,GACnD,CAAC;IACH;;IAEA;IACA;IACA,IACE,CAACG,KAAK,CAACC,OAAO,CAACR,YAAY,CAACI,MAAM,CAAC,CAAC,IACpC,OAAOH,OAAO,CAACG,MAAM,CAAC,KAAK,QAAQ,IACnC,OAAOJ,YAAY,CAACI,MAAM,CAAC,KAAK,QAAQ,EACxC;MACA;MACAD,OAAO,GAAGN,iBAAiB,CAAC;QAC1BC,IAAI,EAAEK,OAAO;QACbJ,WAAW;QACXC,YAAY,EAAEA,YAAY,CAACI,MAAM,CAAC;QAClCH,OAAO,EAAEA,OAAO,CAACG,MAAM,CAAC;QACxBF;MACF,CAAC,CAAC;MACF;IACF;IAEA,MAAMO,kBAAkB,GAAGrB,UAAU,CAACgB,MAAM,EAAE;MAAEM,SAAS,EAAE;IAAI,CAAC,CAAC;IAEjE,IAAI,OAAOT,OAAO,CAACQ,kBAAkB,CAAC,KAAK,QAAQ,EAAE;MACnD,MAAM,IAAIlB,UAAU,CACjB,sBAAqBW,cAAe,aAAY,GAC9C,uBAAsBE,MAAO,GAClC,CAAC;IACH;IACA,IAAIH,OAAO,CAACQ,kBAAkB,CAAC,CAACE,IAAI,KAAKC,SAAS,EAAE;MAClD;MACA,MAAM,IAAIpB,WAAW,CAAE,WAAUY,MAAO,8BAA6B,CAAC;IACxE;IAEA,MAAMS,YAAY,GAChBZ,OAAO,CAACQ,kBAAkB,CAAC,CAACE,IAAI,KAAK,OAAO,GACxC,QAAQ,GACRV,OAAO,CAACQ,kBAAkB,CAAC,CAACE,IAAI;IAEtC,MAAMG,UAAU,GAAGP,KAAK,CAACC,OAAO,CAACR,YAAY,CAACI,MAAM,CAAC,CAAC,GAClD,OAAO,GACP,OAAOJ,YAAY,CAACI,MAAM,CAAC;IAE/B,IAAIU,UAAU,KAAKD,YAAY,EAAE;MAC/B,MAAM,IAAItB,UAAU,CACjB,sBAAqBW,cAAe,aAAY,GAC9C,gBAAeE,MAAO,qBAAoBU,UAAW,GAAE,GACvD,oBAAmBD,YAAa,IACrC,CAAC;IACH;IAEA,IAAIE,YAAY;IAChB,IAAId,OAAO,CAACQ,kBAAkB,CAAC,EAAE;MAC/B,IAAIR,OAAO,CAACQ,kBAAkB,CAAC,CAACO,OAAO,KAAKJ,SAAS,EAAE;QACrDG,YAAY,GAAGd,OAAO,CAACQ,kBAAkB,CAAC,CAACO,OAAO;MACpD,CAAC,MAAM,IAAIH,YAAY,KAAK,SAAS,EAAE;QACrCE,YAAY,GAAG,KAAK;MACtB;IACF;;IAEA;IACA;IACA;IACA;;IAEA,MAAME,gBAAgB,GACpB,OAAOlB,WAAW,CAACK,MAAM,CAAC,KAAK,WAAW,IAC1CL,WAAW,CAACK,MAAM,CAAC,KAAKW,YAAY;IACtC,IAAIE,gBAAgB,EAAE;MACpBxB,GAAG,CAACyB,KAAK,CACN,iBAAgBd,MAAO,IAAGL,WAAW,CAACK,MAAM,CAAE,QAAO,GACnD,kBAAiBA,MAAO,IAAGJ,YAAY,CAACI,MAAM,CAAE,EACrD,CAAC;MACDD,OAAO,CAACC,MAAM,CAAC,GAAGL,WAAW,CAACK,MAAM,CAAC;MACrC;IACF;IAEAD,OAAO,CAACC,MAAM,CAAC,GAAGJ,YAAY,CAACI,MAAM,CAAC;IAEtC,MAAMe,MAAM,GAAGlB,OAAO,CAACQ,kBAAkB,CAAC,CAACU,MAAM;IACjD,IAAIA,MAAM,EAAE;MACV1B,GAAG,CAACyB,KAAK,CAAE,4CAA2Cd,MAAO,EAAC,CAAC;MAC/DD,OAAO,CAACC,MAAM,CAAC,GAAGe,MAAM,CAAChB,OAAO,CAACC,MAAM,CAAC,CAAC;IAC3C;IAEAD,OAAO,CAACM,kBAAkB,CAAC,GAAGN,OAAO,CAACC,MAAM,CAAC;EAC/C;EACA,OAAOD,OAAO;AAChB;AAEA,OAAO,SAASiB,gBAAgBA,CAACC,QAAgB,EAAU;EACzD,MAAMC,gBAAgB,GAAGrC,IAAI,CAACsC,OAAO,CAACF,QAAQ,CAAC;EAC/C5B,GAAG,CAACyB,KAAK,CACN,4BAA2BG,QAAS,IAAG,GACrC,iBAAgBC,gBAAiB,IACtC,CAAC;EACD,IAAItB,YAAY;EAChB,IAAI;IACFA,YAAY,GAAGd,WAAW,CAACoC,gBAAgB,CAAC;EAC9C,CAAC,CAAC,OAAOE,KAAK,EAAE;IACd/B,GAAG,CAACyB,KAAK,CAAC,iBAAiB,EAAEM,KAAK,CAAC;IACnC,MAAM,IAAIjC,UAAU,CACjB,4BAA2B+B,gBAAiB,IAAG,GAC7C,UAASE,KAAK,CAACC,OAAQ,EAC5B,CAAC;EACH;EACA,IAAIJ,QAAQ,CAACK,QAAQ,CAAC,cAAc,CAAC,EAAE;IACrCjC,GAAG,CAACyB,KAAK,CAAC,iDAAiD,CAAC;IAC5DlB,YAAY,GAAGA,YAAY,CAAC2B,MAAM,IAAI,CAAC,CAAC;EAC1C;EACA,IAAItB,MAAM,CAACC,IAAI,CAACN,YAAY,CAAC,CAAC4B,MAAM,KAAK,CAAC,EAAE;IAC1CnC,GAAG,CAACyB,KAAK,CACN,eAAcI,gBAAiB,+BAA8B,GAC5D,qCACJ,CAAC;EACH;EACA,OAAOtB,YAAY;AACrB;AAMA,OAAO,eAAe6B,mBAAmBA,CAAC;EACxCC,UAAU,GAAG9C,EAAE,CAAC+C;AACS,CAAC,GAAG,CAAC,CAAC,EAA0B;EACzD,MAAMC,eAAe,GAAG,mBAAmB;;EAE3C;EACA,MAAMC,eAAe,GAAG;EACtB;EACAhD,IAAI,CAACiD,IAAI,CAACJ,UAAU,CAAC,CAAC,EAAG,IAAGE,eAAgB,EAAC,CAAC;EAC9C;EACA/C,IAAI,CAACiD,IAAI,CAACC,OAAO,CAACC,GAAG,CAAC,CAAC,EAAE,cAAc,CAAC;EACxC;EACAnD,IAAI,CAACiD,IAAI,CAACC,OAAO,CAACC,GAAG,CAAC,CAAC,EAAEJ,eAAe,CAAC,CAC1C;EAED,MAAMK,OAAO,GAAG,MAAMC,OAAO,CAACC,GAAG,CAC/BN,eAAe,CAACO,GAAG,CAAC,MAAOC,QAAQ,IAAK;IACtC,MAAMC,gBAAgB,GAAGzD,IAAI,CAACsC,OAAO,CAACkB,QAAQ,CAAC;IAC/C,IAAI,MAAMpD,UAAU,CAACqD,gBAAgB,CAAC,EAAE;MACtC,OAAOA,gBAAgB;IACzB,CAAC,MAAM;MACLjD,GAAG,CAACyB,KAAK,CACN,sBAAqBwB,gBAAiB,aAAY,GACjD,0BACJ,CAAC;MACD,OAAO9B,SAAS;IAClB;EACF,CAAC,CACH,CAAC;EAED,MAAM+B,eAAe,GAAG,EAAE;EAC1BN,OAAO,CAACO,OAAO,CAAEC,CAAC,IAAK;IACrB,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;MACzBF,eAAe,CAACG,IAAI,CAACD,CAAC,CAAC;IACzB;EACF,CAAC,CAAC;EACF,OAAOF,eAAe;AACxB"}