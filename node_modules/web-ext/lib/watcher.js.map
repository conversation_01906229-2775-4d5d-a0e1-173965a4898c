{"version": 3, "file": "watcher.js", "names": ["fs", "Watchpack", "debounce", "UsageError", "createLogger", "log", "import", "meta", "url", "onSourceChange", "sourceDir", "watchFile", "watchIgnored", "artifactsDir", "onChange", "shouldWatchFile", "debounceTime", "ignored", "process", "platform", "map", "it", "replace", "watcher", "executeImmediately", "on", "filePath", "proxyFileChanges", "debug", "join", "watchedDirs", "watchedFiles", "existsSync", "lstatSync", "isFile", "push", "watch", "files", "directories", "missing", "startTime", "Date", "now", "close", "indexOf", "toTimeString"], "sources": ["../src/watcher.js"], "sourcesContent": ["/* @flow */\nimport { fs } from 'mz';\nimport Watchpack from 'watchpack';\nimport debounce from 'debounce';\n\nimport { UsageError } from './errors.js';\nimport { createLogger } from './util/logger.js';\n\nconst log = createLogger(import.meta.url);\n\n// onSourceChange types and implementation\n\nexport type ShouldWatchFn = (filePath: string) => boolean;\n\nexport type OnChangeFn = () => any;\n\nexport type OnSourceChangeParams = {|\n  sourceDir: string,\n  watchFile?: Array<string>,\n  watchIgnored?: Array<string>,\n  artifactsDir: string,\n  onChange: OnChangeFn,\n  shouldWatchFile: ShouldWatchFn,\n  debounceTime?: number,\n|};\n\nexport type OnSourceChangeFn = (params: OnSourceChangeParams) => Watchpack;\n\nexport default function onSourceChange({\n  sourceDir,\n  watchFile,\n  watchIgnored,\n  artifactsDir,\n  onChange,\n  shouldWatchFile,\n  debounceTime = 500,\n}: OnSourceChangeParams): Watchpack {\n  // When running on Windows, transform the ignored paths and globs\n  // as Watchpack does translate the changed files path internally\n  // (See https://github.com/webpack/watchpack/blob/v2.1.1/lib/DirectoryWatcher.js#L99-L103).\n  const ignored =\n    watchIgnored && process.platform === 'win32'\n      ? watchIgnored.map((it) => it.replace(/\\\\/g, '/'))\n      : watchIgnored;\n\n  // TODO: For network disks, we would need to add {poll: true}.\n  const watcher = ignored ? new Watchpack({ ignored }) : new Watchpack();\n\n  // Allow multiple files to be changed before reloading the extension\n  const executeImmediately = false;\n  onChange = debounce(onChange, debounceTime, executeImmediately);\n\n  watcher.on('change', (filePath) => {\n    proxyFileChanges({ artifactsDir, onChange, filePath, shouldWatchFile });\n  });\n\n  log.debug(\n    `Watching ${watchFile ? watchFile.join(',') : sourceDir} for changes`\n  );\n\n  const watchedDirs = [];\n  const watchedFiles = [];\n\n  if (watchFile) {\n    for (const filePath of watchFile) {\n      if (fs.existsSync(filePath) && !fs.lstatSync(filePath).isFile()) {\n        throw new UsageError(\n          'Invalid --watch-file value: ' + `\"${filePath}\" is not a file.`\n        );\n      }\n\n      watchedFiles.push(filePath);\n    }\n  } else {\n    watchedDirs.push(sourceDir);\n  }\n\n  watcher.watch({\n    files: watchedFiles,\n    directories: watchedDirs,\n    missing: [],\n    startTime: Date.now(),\n  });\n\n  // TODO: support interrupting the watcher on Windows.\n  // https://github.com/mozilla/web-ext/issues/225\n  process.on('SIGINT', () => watcher.close());\n  return watcher;\n}\n\n// proxyFileChanges types and implementation.\n\nexport type ProxyFileChangesParams = {|\n  artifactsDir: string,\n  onChange: OnChangeFn,\n  filePath: string,\n  shouldWatchFile: ShouldWatchFn,\n|};\n\nexport function proxyFileChanges({\n  artifactsDir,\n  onChange,\n  filePath,\n  shouldWatchFile,\n}: ProxyFileChangesParams): void {\n  if (filePath.indexOf(artifactsDir) === 0 || !shouldWatchFile(filePath)) {\n    log.debug(`Ignoring change to: ${filePath}`);\n  } else {\n    log.debug(`Changed: ${filePath}`);\n    log.debug(`Last change detection: ${new Date().toTimeString()}`);\n    onChange();\n  }\n}\n"], "mappings": "AACA,SAASA,EAAE,QAAQ,IAAI;AACvB,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,QAAQ,MAAM,UAAU;AAE/B,SAASC,UAAU,QAAQ,aAAa;AACxC,SAASC,YAAY,QAAQ,kBAAkB;AAE/C,MAAMC,GAAG,GAAGD,YAAY,CAACE,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC;;AAEzC;;AAkBA,eAAe,SAASC,cAAcA,CAAC;EACrCC,SAAS;EACTC,SAAS;EACTC,YAAY;EACZC,YAAY;EACZC,QAAQ;EACRC,eAAe;EACfC,YAAY,GAAG;AACK,CAAC,EAAa;EAClC;EACA;EACA;EACA,MAAMC,OAAO,GACXL,YAAY,IAAIM,OAAO,CAACC,QAAQ,KAAK,OAAO,GACxCP,YAAY,CAACQ,GAAG,CAAEC,EAAE,IAAKA,EAAE,CAACC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,GAChDV,YAAY;;EAElB;EACA,MAAMW,OAAO,GAAGN,OAAO,GAAG,IAAIhB,SAAS,CAAC;IAAEgB;EAAQ,CAAC,CAAC,GAAG,IAAIhB,SAAS,CAAC,CAAC;;EAEtE;EACA,MAAMuB,kBAAkB,GAAG,KAAK;EAChCV,QAAQ,GAAGZ,QAAQ,CAACY,QAAQ,EAAEE,YAAY,EAAEQ,kBAAkB,CAAC;EAE/DD,OAAO,CAACE,EAAE,CAAC,QAAQ,EAAGC,QAAQ,IAAK;IACjCC,gBAAgB,CAAC;MAAEd,YAAY;MAAEC,QAAQ;MAAEY,QAAQ;MAAEX;IAAgB,CAAC,CAAC;EACzE,CAAC,CAAC;EAEFV,GAAG,CAACuB,KAAK,CACN,YAAWjB,SAAS,GAAGA,SAAS,CAACkB,IAAI,CAAC,GAAG,CAAC,GAAGnB,SAAU,cAC1D,CAAC;EAED,MAAMoB,WAAW,GAAG,EAAE;EACtB,MAAMC,YAAY,GAAG,EAAE;EAEvB,IAAIpB,SAAS,EAAE;IACb,KAAK,MAAMe,QAAQ,IAAIf,SAAS,EAAE;MAChC,IAAIX,EAAE,CAACgC,UAAU,CAACN,QAAQ,CAAC,IAAI,CAAC1B,EAAE,CAACiC,SAAS,CAACP,QAAQ,CAAC,CAACQ,MAAM,CAAC,CAAC,EAAE;QAC/D,MAAM,IAAI/B,UAAU,CAClB,8BAA8B,GAAI,IAAGuB,QAAS,kBAChD,CAAC;MACH;MAEAK,YAAY,CAACI,IAAI,CAACT,QAAQ,CAAC;IAC7B;EACF,CAAC,MAAM;IACLI,WAAW,CAACK,IAAI,CAACzB,SAAS,CAAC;EAC7B;EAEAa,OAAO,CAACa,KAAK,CAAC;IACZC,KAAK,EAAEN,YAAY;IACnBO,WAAW,EAAER,WAAW;IACxBS,OAAO,EAAE,EAAE;IACXC,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;EACtB,CAAC,CAAC;;EAEF;EACA;EACAxB,OAAO,CAACO,EAAE,CAAC,QAAQ,EAAE,MAAMF,OAAO,CAACoB,KAAK,CAAC,CAAC,CAAC;EAC3C,OAAOpB,OAAO;AAChB;;AAEA;;AASA,OAAO,SAASI,gBAAgBA,CAAC;EAC/Bd,YAAY;EACZC,QAAQ;EACRY,QAAQ;EACRX;AACsB,CAAC,EAAQ;EAC/B,IAAIW,QAAQ,CAACkB,OAAO,CAAC/B,YAAY,CAAC,KAAK,CAAC,IAAI,CAACE,eAAe,CAACW,QAAQ,CAAC,EAAE;IACtErB,GAAG,CAACuB,KAAK,CAAE,uBAAsBF,QAAS,EAAC,CAAC;EAC9C,CAAC,MAAM;IACLrB,GAAG,CAACuB,KAAK,CAAE,YAAWF,QAAS,EAAC,CAAC;IACjCrB,GAAG,CAACuB,KAAK,CAAE,0BAAyB,IAAIa,IAAI,CAAC,CAAC,CAACI,YAAY,CAAC,CAAE,EAAC,CAAC;IAChE/B,QAAQ,CAAC,CAAC;EACZ;AACF"}