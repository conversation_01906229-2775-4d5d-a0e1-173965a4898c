{"version": 3, "file": "program.js", "names": ["os", "path", "readFileSync", "camelCase", "decamelize", "yargs", "<PERSON><PERSON><PERSON>", "ya<PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultCommands", "UsageError", "createLogger", "consoleStream", "defaultLogStream", "coerceCLICustomPreference", "checkForUpdates", "defaultUpdateChecker", "discoverConfigFiles", "defaultConfigDiscovery", "loadJSConfigFile", "defaultLoadJSConfigFile", "applyConfigToArgv", "defaultApplyConfigToArgv", "log", "import", "meta", "url", "envPrefix", "defaultGlobalEnv", "AMO_BASE_URL", "Program", "constructor", "argv", "absolutePackageDir", "process", "cwd", "slice", "programArgv", "yargsInstance", "verboseEnabled", "shouldExitProgram", "parserConfiguration", "strict", "wrap", "terminalWidth", "commands", "options", "command", "name", "description", "executor", "commandOptions", "yargsForCmd", "demandCommand", "undefined", "exitProcess", "env", "setGlobalOptions", "Object", "keys", "for<PERSON>ach", "key", "global", "demandOption", "enableVerboseMode", "logStream", "version", "makeVerbose", "info", "getArguments", "validationInstance", "getInternalMethods", "getValidationInstance", "requiredArguments", "demandedOptions", "getDemandedOptions", "args", "err", "message", "startsWith", "configDiscovery", "noConfigDiscovery", "reload", "noReload", "input", "noInput", "ignoreFiles", "length", "startUrl", "Array", "isArray", "firefoxPreview", "checkRequiredArguments", "adjustedArgv", "cleanupProcessEnvConfigs", "systemProcess", "cmd", "_", "toOptionKey", "k", "replace", "separator", "filter", "optKey", "globalOpt", "cmdOpt", "debug", "execute", "getVersion", "defaultVersionGetter", "globalEnv", "runCommand", "verbose", "webextVersion", "configFiles", "discoveredConfigs", "push", "config", "resolve", "niceFileList", "map", "f", "homedir", "join", "configFileName", "configObject", "argvFromCLI", "error", "stack", "String", "code", "exit", "packageData", "JSON", "parse", "git", "branch", "long", "throwUsageErrorIfArray", "errorMessage", "value", "main", "runOptions", "program", "firefoxPreviewOption", "describe", "type", "usage", "help", "alias", "recommendCommands", "default", "requiresArg", "coerce", "arg", "normalize", "hidden", "build", "filename", "sign", "id", "timeout", "channel", "run", "target", "choices", "firefox", "pref", "devtools", "lint", "output", "metadata", "pretty", "privileged", "boring", "docs"], "sources": ["../src/program.js"], "sourcesContent": ["/* @flow */\nimport os from 'os';\nimport path from 'path';\nimport { readFileSync } from 'fs';\n\nimport camelCase from 'camelcase';\nimport decamelize from 'decamelize';\nimport yargs from 'yargs';\nimport { Parser as yargsParser } from 'yargs/helpers';\n\nimport defaultCommands from './cmd/index.js';\nimport { UsageError } from './errors.js';\nimport {\n  createLogger,\n  consoleStream as defaultLogStream,\n} from './util/logger.js';\nimport { coerceCLICustomPreference } from './firefox/preferences.js';\nimport { checkForUpdates as defaultUpdateChecker } from './util/updates.js';\nimport {\n  discoverConfigFiles as defaultConfigDiscovery,\n  loadJSConfigFile as defaultLoadJSConfigFile,\n  applyConfigToArgv as defaultApplyConfigToArgv,\n} from './config.js';\n\nconst log = createLogger(import.meta.url);\nconst envPrefix = 'WEB_EXT';\n// Default to \"development\" (the value actually assigned will be interpolated\n// by babel-plugin-transform-inline-environment-variables).\nconst defaultGlobalEnv = process.env.WEBEXT_BUILD_ENV || 'development';\n\ntype ProgramOptions = {\n  absolutePackageDir?: string,\n};\n\nexport type VersionGetterFn = (absolutePackageDir: string) => Promise<string>;\n\n// TODO: add pipes to Flow type after https://github.com/facebook/flow/issues/2405 is fixed\n\ntype ExecuteOptions = {\n  checkForUpdates?: Function,\n  systemProcess?: typeof process,\n  logStream?: typeof defaultLogStream,\n  getVersion?: VersionGetterFn,\n  applyConfigToArgv?: typeof defaultApplyConfigToArgv,\n  discoverConfigFiles?: typeof defaultConfigDiscovery,\n  loadJSConfigFile?: typeof defaultLoadJSConfigFile,\n  shouldExitProgram?: boolean,\n  globalEnv?: string | void,\n};\n\nexport const AMO_BASE_URL = 'https://addons.mozilla.org/api/v5/';\n\n/*\n * The command line program.\n */\nexport class Program {\n  absolutePackageDir: string;\n  yargs: any;\n  commands: { [key: string]: Function };\n  shouldExitProgram: boolean;\n  verboseEnabled: boolean;\n  options: Object;\n  programArgv: Array<string>;\n  demandedOptions: Object;\n\n  constructor(\n    argv: ?Array<string>,\n    { absolutePackageDir = process.cwd() }: ProgramOptions = {}\n  ) {\n    // This allows us to override the process argv which is useful for\n    // testing.\n    // NOTE: process.argv.slice(2) removes the path to node and web-ext\n    // executables from the process.argv array.\n    argv = argv || process.argv.slice(2);\n    this.programArgv = argv;\n\n    // NOTE: always initialize yargs explicitly with the package dir\n    // to avoid side-effects due to yargs looking for its configuration\n    // section from a package.json file stored in an arbitrary directory\n    // (e.g. in tests yargs would end up loading yargs config from the\n    // mocha package.json). web-ext package.json doesn't contain any yargs\n    // section as it is deprecated and we configure yargs using\n    // yargs.parserConfiguration. See web-ext#469 for rationale.\n    const yargsInstance = yargs(argv, absolutePackageDir);\n\n    this.absolutePackageDir = absolutePackageDir;\n    this.verboseEnabled = false;\n    this.shouldExitProgram = true;\n\n    this.yargs = yargsInstance;\n    this.yargs.parserConfiguration({\n      'boolean-negation': true,\n    });\n    this.yargs.strict();\n    this.yargs.wrap(this.yargs.terminalWidth());\n\n    this.commands = {};\n    this.options = {};\n  }\n\n  command(\n    name: string,\n    description: string,\n    executor: Function,\n    commandOptions: Object = {}\n  ): Program {\n    this.options[camelCase(name)] = commandOptions;\n\n    this.yargs.command(name, description, (yargsForCmd) => {\n      if (!commandOptions) {\n        return;\n      }\n      return (\n        yargsForCmd\n          // Make sure the user does not add any extra commands. For example,\n          // this would be a mistake because lint does not accept arguments:\n          // web-ext lint ./src/path/to/file.js\n          .demandCommand(\n            0,\n            0,\n            undefined,\n            'This command does not take any arguments'\n          )\n          .strict()\n          .exitProcess(this.shouldExitProgram)\n          // Calling env() will be unnecessary after\n          // https://github.com/yargs/yargs/issues/486 is fixed\n          .env(envPrefix)\n          .options(commandOptions)\n      );\n    });\n    this.commands[name] = executor;\n    return this;\n  }\n\n  setGlobalOptions(options: Object): Program {\n    // This is a convenience for setting global options.\n    // An option is only global (i.e. available to all sub commands)\n    // with the `global` flag so this makes sure every option has it.\n    this.options = { ...this.options, ...options };\n    Object.keys(options).forEach((key) => {\n      options[key].global = true;\n      if (options[key].demandOption === undefined) {\n        // By default, all options should be \"demanded\" otherwise\n        // yargs.strict() will think they are missing when declared.\n        options[key].demandOption = true;\n      }\n    });\n    this.yargs.options(options);\n    return this;\n  }\n\n  enableVerboseMode(logStream: typeof defaultLogStream, version: string): void {\n    if (this.verboseEnabled) {\n      return;\n    }\n\n    logStream.makeVerbose();\n    log.info('Version:', version);\n    this.verboseEnabled = true;\n  }\n\n  // Retrieve the yargs argv object and apply any further fix needed\n  // on the output of the yargs options parsing.\n  getArguments(): Object {\n    // To support looking up required parameters via config files, we need to\n    // temporarily disable the requiredArguments validation. Otherwise yargs\n    // would exit early. Validation is enforced by the checkRequiredArguments()\n    // method, after reading configuration files.\n    //\n    // This is an undocumented internal API of yargs! Unit tests to avoid\n    // regressions are located at: tests/functional/test.cli.sign.js\n    //\n    // Replace hack if possible:  https://github.com/mozilla/web-ext/issues/1930\n    const validationInstance = this.yargs\n      .getInternalMethods()\n      .getValidationInstance();\n    const { requiredArguments } = validationInstance;\n    // Initialize demandedOptions (which is going to be set to an object with one\n    // property for each mandatory global options, then the arrow function below\n    // will receive as its demandedOptions parameter a new one that also includes\n    // all mandatory options for the sub command selected).\n    this.demandedOptions = this.yargs.getDemandedOptions();\n    validationInstance.requiredArguments = (args, demandedOptions) => {\n      this.demandedOptions = demandedOptions;\n    };\n    let argv;\n    try {\n      argv = this.yargs.argv;\n    } catch (err) {\n      if (\n        err.name === 'YError' &&\n        err.message.startsWith('Unknown argument: ')\n      ) {\n        throw new UsageError(err.message);\n      }\n      throw err;\n    }\n    validationInstance.requiredArguments = requiredArguments;\n\n    // Yargs boolean options doesn't define the no* counterpart\n    // with negate-boolean on Yargs 15. Define as expected by the\n    // web-ext execute method.\n    if (argv.configDiscovery != null) {\n      argv.noConfigDiscovery = !argv.configDiscovery;\n    }\n    if (argv.reload != null) {\n      argv.noReload = !argv.reload;\n    }\n\n    // Yargs doesn't accept --no-input as a valid option if there isn't a\n    // --input option defined to be negated, to fix that the --input is\n    // defined and hidden from the yargs help output and we define here\n    // the negated argument name that we expect to be set in the parsed\n    // arguments (and fix https://github.com/mozilla/web-ext/issues/1860).\n    if (argv.input != null) {\n      argv.noInput = !argv.input;\n    }\n\n    // Replacement for the \"requiresArg: true\" parameter until the following bug\n    // is fixed: https://github.com/yargs/yargs/issues/1098\n    if (argv.ignoreFiles && !argv.ignoreFiles.length) {\n      throw new UsageError('Not enough arguments following: ignore-files');\n    }\n\n    if (argv.startUrl && !argv.startUrl.length) {\n      throw new UsageError('Not enough arguments following: start-url');\n    }\n\n    if (Array.isArray(argv.firefoxPreview) && !argv.firefoxPreview.length) {\n      argv.firefoxPreview = ['mv3'];\n    }\n\n    return argv;\n  }\n\n  // getArguments() disables validation of required parameters, to allow us to\n  // read parameters from config files first. Before the program continues, it\n  // must call checkRequiredArguments() to ensure that required parameters are\n  // defined (in the CLI or in a config file).\n  checkRequiredArguments(adjustedArgv: Object): void {\n    const validationInstance = this.yargs\n      .getInternalMethods()\n      .getValidationInstance();\n    validationInstance.requiredArguments(adjustedArgv, this.demandedOptions);\n  }\n\n  // Remove WEB_EXT_* environment vars that are not a global cli options\n  // or an option supported by the current command (See #793).\n  cleanupProcessEnvConfigs(systemProcess: typeof process) {\n    const cmd = yargsParser(this.programArgv)._[0];\n    const env = systemProcess.env || {};\n    const toOptionKey = (k) =>\n      decamelize(camelCase(k.replace(envPrefix, '')), { separator: '-' });\n\n    if (cmd) {\n      Object.keys(env)\n        .filter((k) => k.startsWith(envPrefix))\n        .forEach((k) => {\n          const optKey = toOptionKey(k);\n          const globalOpt = this.options[optKey];\n          const cmdOpt = this.options[cmd] && this.options[cmd][optKey];\n\n          if (!globalOpt && !cmdOpt) {\n            log.debug(`Environment ${k} not supported by web-ext ${cmd}`);\n            delete env[k];\n          }\n        });\n    }\n  }\n\n  async execute({\n    checkForUpdates = defaultUpdateChecker,\n    systemProcess = process,\n    logStream = defaultLogStream,\n    getVersion = defaultVersionGetter,\n    applyConfigToArgv = defaultApplyConfigToArgv,\n    discoverConfigFiles = defaultConfigDiscovery,\n    loadJSConfigFile = defaultLoadJSConfigFile,\n    shouldExitProgram = true,\n    globalEnv = defaultGlobalEnv,\n  }: ExecuteOptions = {}): Promise<void> {\n    this.shouldExitProgram = shouldExitProgram;\n    this.yargs.exitProcess(this.shouldExitProgram);\n\n    this.cleanupProcessEnvConfigs(systemProcess);\n    const argv = this.getArguments();\n\n    const cmd = argv._[0];\n\n    const version = await getVersion(this.absolutePackageDir);\n    const runCommand = this.commands[cmd];\n\n    if (argv.verbose) {\n      this.enableVerboseMode(logStream, version);\n    }\n\n    let adjustedArgv = { ...argv, webextVersion: version };\n\n    try {\n      if (cmd === undefined) {\n        throw new UsageError('No sub-command was specified in the args');\n      }\n      if (!runCommand) {\n        throw new UsageError(`Unknown command: ${cmd}`);\n      }\n      if (globalEnv === 'production') {\n        checkForUpdates({ version });\n      }\n\n      const configFiles = [];\n\n      if (argv.configDiscovery) {\n        log.debug(\n          'Discovering config files. ' + 'Set --no-config-discovery to disable'\n        );\n        const discoveredConfigs = await discoverConfigFiles();\n        configFiles.push(...discoveredConfigs);\n      } else {\n        log.debug('Not discovering config files');\n      }\n\n      if (argv.config) {\n        configFiles.push(path.resolve(argv.config));\n      }\n\n      if (configFiles.length) {\n        const niceFileList = configFiles\n          .map((f) => f.replace(process.cwd(), '.'))\n          .map((f) => f.replace(os.homedir(), '~'))\n          .join(', ');\n        log.info(\n          'Applying config file' +\n            `${configFiles.length !== 1 ? 's' : ''}: ` +\n            `${niceFileList}`\n        );\n      }\n\n      configFiles.forEach((configFileName) => {\n        const configObject = loadJSConfigFile(configFileName);\n        adjustedArgv = applyConfigToArgv({\n          argv: adjustedArgv,\n          argvFromCLI: argv,\n          configFileName,\n          configObject,\n          options: this.options,\n        });\n      });\n\n      if (adjustedArgv.verbose) {\n        // Ensure that the verbose is enabled when specified in a config file.\n        this.enableVerboseMode(logStream, version);\n      }\n\n      this.checkRequiredArguments(adjustedArgv);\n\n      await runCommand(adjustedArgv, { shouldExitProgram });\n    } catch (error) {\n      if (!(error instanceof UsageError) || adjustedArgv.verbose) {\n        log.error(`\\n${error.stack}\\n`);\n      } else {\n        log.error(`\\n${String(error)}\\n`);\n      }\n      if (error.code) {\n        log.error(`Error code: ${error.code}\\n`);\n      }\n\n      log.debug(`Command executed: ${cmd}`);\n\n      if (this.shouldExitProgram) {\n        systemProcess.exit(1);\n      } else {\n        throw error;\n      }\n    }\n  }\n}\n\n//A defintion of type of argument for defaultVersionGetter\ntype VersionGetterOptions = {\n  globalEnv?: string,\n};\n\nexport async function defaultVersionGetter(\n  absolutePackageDir: string,\n  { globalEnv = defaultGlobalEnv }: VersionGetterOptions = {}\n): Promise<string> {\n  if (globalEnv === 'production') {\n    log.debug('Getting the version from package.json');\n    const packageData: any = readFileSync(\n      path.join(absolutePackageDir, 'package.json')\n    );\n    return JSON.parse(packageData).version;\n  } else {\n    log.debug('Getting version from the git revision');\n    // This branch is only reached during development.\n    // git-rev-sync is in devDependencies, and lazily imported using require.\n    // This also avoids logspam from https://github.com/mozilla/web-ext/issues/1916\n    // eslint-disable-next-line import/no-extraneous-dependencies\n    const git = await import('git-rev-sync');\n    return `${git.branch(absolutePackageDir)}-${git.long(absolutePackageDir)}`;\n  }\n}\n\n// TODO: add pipes to Flow type after https://github.com/facebook/flow/issues/2405 is fixed\n\ntype MainParams = {\n  getVersion?: VersionGetterFn,\n  commands?: Object,\n  argv: Array<any>,\n  runOptions?: Object,\n};\n\nexport function throwUsageErrorIfArray(errorMessage: string): any {\n  return (value: any): any => {\n    if (Array.isArray(value)) {\n      throw new UsageError(errorMessage);\n    }\n    return value;\n  };\n}\n\nexport async function main(\n  absolutePackageDir: string,\n  {\n    getVersion = defaultVersionGetter,\n    commands = defaultCommands,\n    argv,\n    runOptions = {},\n  }: MainParams = {}\n): Promise<any> {\n  const program = new Program(argv, { absolutePackageDir });\n  const version = await getVersion(absolutePackageDir);\n\n  // This is an option shared by some commands but not all of them, hence why\n  // it isn't a global option.\n  const firefoxPreviewOption = {\n    describe:\n      'Turn on developer preview features in Firefox' + ' (defaults to \"mv3\")',\n    demandOption: false,\n    type: 'array',\n  };\n\n  // yargs uses magic camel case expansion to expose options on the\n  // final argv object. For example, the 'artifacts-dir' option is alternatively\n  // available as argv.artifactsDir.\n  program.yargs\n    .usage(\n      `Usage: $0 [options] command\n\nOption values can also be set by declaring an environment variable prefixed\nwith $${envPrefix}_. For example: $${envPrefix}_SOURCE_DIR=/path is the same as\n--source-dir=/path.\n\nTo view specific help for any given command, add the command name.\nExample: $0 --help run.\n`\n    )\n    .help('help')\n    .alias('h', 'help')\n    .env(envPrefix)\n    .version(version)\n    .demandCommand(1, 'You must specify a command')\n    .strict()\n    .recommendCommands();\n\n  program.setGlobalOptions({\n    'source-dir': {\n      alias: 's',\n      describe: 'Web extension source directory.',\n      default: process.cwd(),\n      requiresArg: true,\n      type: 'string',\n      coerce: (arg) => (arg != null ? path.resolve(arg) : undefined),\n    },\n    'artifacts-dir': {\n      alias: 'a',\n      describe: 'Directory where artifacts will be saved.',\n      default: path.join(process.cwd(), 'web-ext-artifacts'),\n      normalize: true,\n      requiresArg: true,\n      type: 'string',\n    },\n    verbose: {\n      alias: 'v',\n      describe: 'Show verbose output',\n      type: 'boolean',\n      demandOption: false,\n    },\n    'ignore-files': {\n      alias: 'i',\n      describe:\n        'A list of glob patterns to define which files should be ' +\n        'ignored. (Example: --ignore-files=path/to/first.js ' +\n        'path/to/second.js \"**/*.log\")',\n      demandOption: false,\n      // The following option prevents yargs>=11 from parsing multiple values,\n      // so the minimum value requirement is enforced in execute instead.\n      // Upstream bug: https://github.com/yargs/yargs/issues/1098\n      // requiresArg: true,\n      type: 'array',\n    },\n    'no-input': {\n      describe: 'Disable all features that require standard input',\n      type: 'boolean',\n      demandOption: false,\n    },\n    input: {\n      // This option is defined to make yargs to accept the --no-input\n      // defined above, but we hide it from the yargs help output.\n      hidden: true,\n      type: 'boolean',\n      demandOption: false,\n    },\n    config: {\n      alias: 'c',\n      describe: 'Path to a CommonJS config file to set ' + 'option defaults',\n      default: undefined,\n      demandOption: false,\n      requiresArg: true,\n      type: 'string',\n    },\n    'config-discovery': {\n      describe:\n        'Discover config files in home directory and ' +\n        'working directory. Disable with --no-config-discovery.',\n      demandOption: false,\n      default: true,\n      type: 'boolean',\n    },\n  });\n\n  program\n    .command(\n      'build',\n      'Create an extension package from source',\n      commands.build,\n      {\n        'as-needed': {\n          describe: 'Watch for file changes and re-build as needed',\n          type: 'boolean',\n        },\n        filename: {\n          alias: 'n',\n          describe: 'Name of the created extension package file.',\n          default: undefined,\n          normalize: false,\n          demandOption: false,\n          requiresArg: true,\n          type: 'string',\n          coerce: (arg) =>\n            arg == null\n              ? undefined\n              : throwUsageErrorIfArray(\n                  'Multiple --filename/-n option are not allowed'\n                )(arg),\n        },\n        'overwrite-dest': {\n          alias: 'o',\n          describe: 'Overwrite destination package if it exists.',\n          type: 'boolean',\n        },\n      }\n    )\n    .command(\n      'sign',\n      'Sign the extension so it can be installed in Firefox',\n      commands.sign,\n      {\n        'amo-base-url': {\n          describe:\n            'Signing API URL prefix - only used with `use-submission-api`',\n          default: AMO_BASE_URL,\n          demandOption: true,\n          type: 'string',\n        },\n        'api-key': {\n          describe: 'API key (JWT issuer) from addons.mozilla.org',\n          demandOption: true,\n          type: 'string',\n        },\n        'api-secret': {\n          describe: 'API secret (JWT secret) from addons.mozilla.org',\n          demandOption: true,\n          type: 'string',\n        },\n        'api-url-prefix': {\n          describe: 'Signing API URL prefix',\n          default: 'https://addons.mozilla.org/api/v4',\n          demandOption: true,\n          type: 'string',\n        },\n        'api-proxy': {\n          describe:\n            'Use a proxy to access the signing API. ' +\n            'Example: https://yourproxy:6000 ',\n          demandOption: false,\n          type: 'string',\n        },\n        'use-submission-api': {\n          describe: 'Sign using the addon submission API',\n          demandOption: false,\n          type: 'boolean',\n        },\n        id: {\n          describe:\n            'A custom ID for the extension. This has no effect if the ' +\n            'extension already declares an explicit ID in its manifest.',\n          demandOption: false,\n          type: 'string',\n        },\n        timeout: {\n          describe: 'Number of milliseconds to wait before giving up',\n          type: 'number',\n        },\n        'disable-progress-bar': {\n          describe: 'Disable the progress bar in sign-addon',\n          demandOption: false,\n          type: 'boolean',\n        },\n        channel: {\n          describe:\n            'The channel for which to sign the addon. Either ' +\n            \"'listed' or 'unlisted'\",\n          type: 'string',\n        },\n        'amo-metadata': {\n          describe:\n            'Path to a JSON file containing an object with metadata ' +\n            'to be passed to the API. ' +\n            'See https://addons-server.readthedocs.io' +\n            '/en/latest/topics/api/addons.html for details. ' +\n            'Only used with `use-submission-api`',\n          type: 'string',\n        },\n      }\n    )\n    .command('run', 'Run the extension', commands.run, {\n      target: {\n        alias: 't',\n        describe:\n          'The extensions runners to enable. Specify this option ' +\n          'multiple times to run against multiple targets.',\n        default: 'firefox-desktop',\n        demandOption: false,\n        type: 'array',\n        choices: ['firefox-desktop', 'firefox-android', 'chromium'],\n      },\n      firefox: {\n        alias: ['f', 'firefox-binary'],\n        describe:\n          'Path or alias to a Firefox executable such as firefox-bin ' +\n          'or firefox.exe. ' +\n          'If not specified, the default Firefox will be used. ' +\n          'You can specify the following aliases in lieu of a path: ' +\n          'firefox, beta, nightly, firefoxdeveloperedition (or deved). ' +\n          'For Flatpak, use `flatpak:org.mozilla.firefox` where ' +\n          '`org.mozilla.firefox` is the application ID.',\n        demandOption: false,\n        type: 'string',\n      },\n      'firefox-profile': {\n        alias: 'p',\n        describe:\n          'Run Firefox using a copy of this profile. The profile ' +\n          'can be specified as a directory or a name, such as one ' +\n          'you would see in the Profile Manager. If not specified, ' +\n          'a new temporary profile will be created.',\n        demandOption: false,\n        type: 'string',\n      },\n      'chromium-binary': {\n        describe:\n          'Path or alias to a Chromium executable such as ' +\n          'google-chrome, google-chrome.exe or opera.exe etc. ' +\n          'If not specified, the default Google Chrome will be used.',\n        demandOption: false,\n        type: 'string',\n      },\n      'chromium-profile': {\n        describe: 'Path to a custom Chromium profile',\n        demandOption: false,\n        type: 'string',\n      },\n      'profile-create-if-missing': {\n        describe: 'Create the profile directory if it does not already exist',\n        demandOption: false,\n        type: 'boolean',\n      },\n      'keep-profile-changes': {\n        describe:\n          'Run Firefox directly in custom profile. Any changes to ' +\n          'the profile will be saved.',\n        demandOption: false,\n        type: 'boolean',\n      },\n      reload: {\n        describe:\n          'Reload the extension when source files change.' +\n          'Disable with --no-reload.',\n        demandOption: false,\n        default: true,\n        type: 'boolean',\n      },\n      'watch-file': {\n        alias: ['watch-files'],\n        describe:\n          'Reload the extension only when the contents of this' +\n          ' file changes. This is useful if you use a custom' +\n          ' build process for your extension',\n        demandOption: false,\n        type: 'array',\n      },\n      'watch-ignored': {\n        describe:\n          'Paths and globs patterns that should not be ' +\n          'watched for changes. This is useful if you want ' +\n          'to explicitly prevent web-ext from watching part ' +\n          'of the extension directory tree, ' +\n          'e.g. the node_modules folder.',\n        demandOption: false,\n        type: 'array',\n      },\n      'pre-install': {\n        describe:\n          'Pre-install the extension into the profile before ' +\n          'startup. This is only needed to support older versions ' +\n          'of Firefox.',\n        demandOption: false,\n        type: 'boolean',\n      },\n      pref: {\n        describe:\n          'Launch firefox with a custom preference ' +\n          '(example: --pref=general.useragent.locale=fr-FR). ' +\n          'You can repeat this option to set more than one ' +\n          'preference.',\n        demandOption: false,\n        requiresArg: true,\n        type: 'array',\n        coerce: (arg) =>\n          arg != null ? coerceCLICustomPreference(arg) : undefined,\n      },\n      'start-url': {\n        alias: ['u', 'url'],\n        describe: 'Launch firefox at specified page',\n        demandOption: false,\n        type: 'array',\n      },\n      devtools: {\n        describe:\n          'Open the DevTools for the installed add-on ' +\n          '(Firefox 106 and later)',\n        demandOption: false,\n        type: 'boolean',\n      },\n      'browser-console': {\n        alias: ['bc'],\n        describe: 'Open the DevTools Browser Console.',\n        demandOption: false,\n        type: 'boolean',\n      },\n      args: {\n        alias: ['arg'],\n        describe: 'Additional CLI options passed to the Browser binary',\n        demandOption: false,\n        type: 'array',\n      },\n      'firefox-preview': firefoxPreviewOption,\n      // Firefox for Android CLI options.\n      'adb-bin': {\n        describe: 'Specify a custom path to the adb binary',\n        demandOption: false,\n        type: 'string',\n        requiresArg: true,\n      },\n      'adb-host': {\n        describe: 'Connect to adb on the specified host',\n        demandOption: false,\n        type: 'string',\n        requiresArg: true,\n      },\n      'adb-port': {\n        describe: 'Connect to adb on the specified port',\n        demandOption: false,\n        type: 'string',\n        requiresArg: true,\n      },\n      'adb-device': {\n        alias: ['android-device'],\n        describe: 'Connect to the specified adb device name',\n        demandOption: false,\n        type: 'string',\n        requiresArg: true,\n      },\n      'adb-discovery-timeout': {\n        describe: 'Number of milliseconds to wait before giving up',\n        demandOption: false,\n        type: 'number',\n        requiresArg: true,\n      },\n      'adb-remove-old-artifacts': {\n        describe: 'Remove old artifacts directories from the adb device',\n        demandOption: false,\n        type: 'boolean',\n      },\n      'firefox-apk': {\n        describe:\n          'Run a specific Firefox for Android APK. ' +\n          'Example: org.mozilla.fennec_aurora',\n        demandOption: false,\n        type: 'string',\n        requiresArg: true,\n      },\n      'firefox-apk-component': {\n        describe:\n          'Run a specific Android Component (defaults to <firefox-apk>/.App)',\n        demandOption: false,\n        type: 'string',\n        requiresArg: true,\n      },\n    })\n    .command('lint', 'Validate the extension source', commands.lint, {\n      output: {\n        alias: 'o',\n        describe: 'The type of output to generate',\n        type: 'string',\n        default: 'text',\n        choices: ['json', 'text'],\n      },\n      metadata: {\n        describe: 'Output only metadata as JSON',\n        type: 'boolean',\n        default: false,\n      },\n      'warnings-as-errors': {\n        describe: 'Treat warnings as errors by exiting non-zero for warnings',\n        alias: 'w',\n        type: 'boolean',\n        default: false,\n      },\n      pretty: {\n        describe: 'Prettify JSON output',\n        type: 'boolean',\n        default: false,\n      },\n      privileged: {\n        describe: 'Treat your extension as a privileged extension',\n        type: 'boolean',\n        default: false,\n      },\n      'self-hosted': {\n        describe:\n          'Your extension will be self-hosted. This disables messages ' +\n          'related to hosting on addons.mozilla.org.',\n        type: 'boolean',\n        default: false,\n      },\n      boring: {\n        describe: 'Disables colorful shell output',\n        type: 'boolean',\n        default: false,\n      },\n      'firefox-preview': firefoxPreviewOption,\n    })\n    .command(\n      'docs',\n      'Open the web-ext documentation in a browser',\n      commands.docs,\n      {}\n    );\n\n  return program.execute({ getVersion, ...runOptions });\n}\n"], "mappings": "AACA,OAAOA,EAAE,MAAM,IAAI;AACnB,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,YAAY,QAAQ,IAAI;AAEjC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,IAAIC,WAAW,QAAQ,eAAe;AAErD,OAAOC,eAAe,MAAM,gBAAgB;AAC5C,SAASC,UAAU,QAAQ,aAAa;AACxC,SACEC,YAAY,EACZC,aAAa,IAAIC,gBAAgB,QAC5B,kBAAkB;AACzB,SAASC,yBAAyB,QAAQ,0BAA0B;AACpE,SAASC,eAAe,IAAIC,oBAAoB,QAAQ,mBAAmB;AAC3E,SACEC,mBAAmB,IAAIC,sBAAsB,EAC7CC,gBAAgB,IAAIC,uBAAuB,EAC3CC,iBAAiB,IAAIC,wBAAwB,QACxC,aAAa;AAEpB,MAAMC,GAAG,GAAGZ,YAAY,CAACa,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC;AACzC,MAAMC,SAAS,GAAG,SAAS;AAC3B;AACA;AACA,MAAMC,gBAAgB,GAAG,gBAAgC,aAAa;;AAQtE;;AAcA,OAAO,MAAMC,YAAY,GAAG,oCAAoC;;AAEhE;AACA;AACA;AACA,OAAO,MAAMC,OAAO,CAAC;EAUnBC,WAAWA,CACTC,IAAoB,EACpB;IAAEC,kBAAkB,GAAGC,OAAO,CAACC,GAAG,CAAC;EAAkB,CAAC,GAAG,CAAC,CAAC,EAC3D;IACA;IACA;IACA;IACA;IACAH,IAAI,GAAGA,IAAI,IAAIE,OAAO,CAACF,IAAI,CAACI,KAAK,CAAC,CAAC,CAAC;IACpC,IAAI,CAACC,WAAW,GAAGL,IAAI;;IAEvB;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMM,aAAa,GAAGhC,KAAK,CAAC0B,IAAI,EAAEC,kBAAkB,CAAC;IAErD,IAAI,CAACA,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACM,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAE7B,IAAI,CAAClC,KAAK,GAAGgC,aAAa;IAC1B,IAAI,CAAChC,KAAK,CAACmC,mBAAmB,CAAC;MAC7B,kBAAkB,EAAE;IACtB,CAAC,CAAC;IACF,IAAI,CAACnC,KAAK,CAACoC,MAAM,CAAC,CAAC;IACnB,IAAI,CAACpC,KAAK,CAACqC,IAAI,CAAC,IAAI,CAACrC,KAAK,CAACsC,aAAa,CAAC,CAAC,CAAC;IAE3C,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;EACnB;EAEAC,OAAOA,CACLC,IAAY,EACZC,WAAmB,EACnBC,QAAkB,EAClBC,cAAsB,GAAG,CAAC,CAAC,EAClB;IACT,IAAI,CAACL,OAAO,CAAC1C,SAAS,CAAC4C,IAAI,CAAC,CAAC,GAAGG,cAAc;IAE9C,IAAI,CAAC7C,KAAK,CAACyC,OAAO,CAACC,IAAI,EAAEC,WAAW,EAAGG,WAAW,IAAK;MACrD,IAAI,CAACD,cAAc,EAAE;QACnB;MACF;MACA,OACEC;MACE;MACA;MACA;MAAA,CACCC,aAAa,CACZ,CAAC,EACD,CAAC,EACDC,SAAS,EACT,0CACF,CAAC,CACAZ,MAAM,CAAC,CAAC,CACRa,WAAW,CAAC,IAAI,CAACf,iBAAiB;MACnC;MACA;MAAA,CACCgB,GAAG,CAAC7B,SAAS,CAAC,CACdmB,OAAO,CAACK,cAAc,CAAC;IAE9B,CAAC,CAAC;IACF,IAAI,CAACN,QAAQ,CAACG,IAAI,CAAC,GAAGE,QAAQ;IAC9B,OAAO,IAAI;EACb;EAEAO,gBAAgBA,CAACX,OAAe,EAAW;IACzC;IACA;IACA;IACA,IAAI,CAACA,OAAO,GAAG;MAAE,GAAG,IAAI,CAACA,OAAO;MAAE,GAAGA;IAAQ,CAAC;IAC9CY,MAAM,CAACC,IAAI,CAACb,OAAO,CAAC,CAACc,OAAO,CAAEC,GAAG,IAAK;MACpCf,OAAO,CAACe,GAAG,CAAC,CAACC,MAAM,GAAG,IAAI;MAC1B,IAAIhB,OAAO,CAACe,GAAG,CAAC,CAACE,YAAY,KAAKT,SAAS,EAAE;QAC3C;QACA;QACAR,OAAO,CAACe,GAAG,CAAC,CAACE,YAAY,GAAG,IAAI;MAClC;IACF,CAAC,CAAC;IACF,IAAI,CAACzD,KAAK,CAACwC,OAAO,CAACA,OAAO,CAAC;IAC3B,OAAO,IAAI;EACb;EAEAkB,iBAAiBA,CAACC,SAAkC,EAAEC,OAAe,EAAQ;IAC3E,IAAI,IAAI,CAAC3B,cAAc,EAAE;MACvB;IACF;IAEA0B,SAAS,CAACE,WAAW,CAAC,CAAC;IACvB5C,GAAG,CAAC6C,IAAI,CAAC,UAAU,EAAEF,OAAO,CAAC;IAC7B,IAAI,CAAC3B,cAAc,GAAG,IAAI;EAC5B;;EAEA;EACA;EACA8B,YAAYA,CAAA,EAAW;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMC,kBAAkB,GAAG,IAAI,CAAChE,KAAK,CAClCiE,kBAAkB,CAAC,CAAC,CACpBC,qBAAqB,CAAC,CAAC;IAC1B,MAAM;MAAEC;IAAkB,CAAC,GAAGH,kBAAkB;IAChD;IACA;IACA;IACA;IACA,IAAI,CAACI,eAAe,GAAG,IAAI,CAACpE,KAAK,CAACqE,kBAAkB,CAAC,CAAC;IACtDL,kBAAkB,CAACG,iBAAiB,GAAG,CAACG,IAAI,EAAEF,eAAe,KAAK;MAChE,IAAI,CAACA,eAAe,GAAGA,eAAe;IACxC,CAAC;IACD,IAAI1C,IAAI;IACR,IAAI;MACFA,IAAI,GAAG,IAAI,CAAC1B,KAAK,CAAC0B,IAAI;IACxB,CAAC,CAAC,OAAO6C,GAAG,EAAE;MACZ,IACEA,GAAG,CAAC7B,IAAI,KAAK,QAAQ,IACrB6B,GAAG,CAACC,OAAO,CAACC,UAAU,CAAC,oBAAoB,CAAC,EAC5C;QACA,MAAM,IAAIrE,UAAU,CAACmE,GAAG,CAACC,OAAO,CAAC;MACnC;MACA,MAAMD,GAAG;IACX;IACAP,kBAAkB,CAACG,iBAAiB,GAAGA,iBAAiB;;IAExD;IACA;IACA;IACA,IAAIzC,IAAI,CAACgD,eAAe,IAAI,IAAI,EAAE;MAChChD,IAAI,CAACiD,iBAAiB,GAAG,CAACjD,IAAI,CAACgD,eAAe;IAChD;IACA,IAAIhD,IAAI,CAACkD,MAAM,IAAI,IAAI,EAAE;MACvBlD,IAAI,CAACmD,QAAQ,GAAG,CAACnD,IAAI,CAACkD,MAAM;IAC9B;;IAEA;IACA;IACA;IACA;IACA;IACA,IAAIlD,IAAI,CAACoD,KAAK,IAAI,IAAI,EAAE;MACtBpD,IAAI,CAACqD,OAAO,GAAG,CAACrD,IAAI,CAACoD,KAAK;IAC5B;;IAEA;IACA;IACA,IAAIpD,IAAI,CAACsD,WAAW,IAAI,CAACtD,IAAI,CAACsD,WAAW,CAACC,MAAM,EAAE;MAChD,MAAM,IAAI7E,UAAU,CAAC,8CAA8C,CAAC;IACtE;IAEA,IAAIsB,IAAI,CAACwD,QAAQ,IAAI,CAACxD,IAAI,CAACwD,QAAQ,CAACD,MAAM,EAAE;MAC1C,MAAM,IAAI7E,UAAU,CAAC,2CAA2C,CAAC;IACnE;IAEA,IAAI+E,KAAK,CAACC,OAAO,CAAC1D,IAAI,CAAC2D,cAAc,CAAC,IAAI,CAAC3D,IAAI,CAAC2D,cAAc,CAACJ,MAAM,EAAE;MACrEvD,IAAI,CAAC2D,cAAc,GAAG,CAAC,KAAK,CAAC;IAC/B;IAEA,OAAO3D,IAAI;EACb;;EAEA;EACA;EACA;EACA;EACA4D,sBAAsBA,CAACC,YAAoB,EAAQ;IACjD,MAAMvB,kBAAkB,GAAG,IAAI,CAAChE,KAAK,CAClCiE,kBAAkB,CAAC,CAAC,CACpBC,qBAAqB,CAAC,CAAC;IAC1BF,kBAAkB,CAACG,iBAAiB,CAACoB,YAAY,EAAE,IAAI,CAACnB,eAAe,CAAC;EAC1E;;EAEA;EACA;EACAoB,wBAAwBA,CAACC,aAA6B,EAAE;IACtD,MAAMC,GAAG,GAAGxF,WAAW,CAAC,IAAI,CAAC6B,WAAW,CAAC,CAAC4D,CAAC,CAAC,CAAC,CAAC;IAC9C,MAAMzC,GAAG,GAAGuC,aAAa,CAACvC,GAAG,IAAI,CAAC,CAAC;IACnC,MAAM0C,WAAW,GAAIC,CAAC,IACpB9F,UAAU,CAACD,SAAS,CAAC+F,CAAC,CAACC,OAAO,CAACzE,SAAS,EAAE,EAAE,CAAC,CAAC,EAAE;MAAE0E,SAAS,EAAE;IAAI,CAAC,CAAC;IAErE,IAAIL,GAAG,EAAE;MACPtC,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,CACb8C,MAAM,CAAEH,CAAC,IAAKA,CAAC,CAACpB,UAAU,CAACpD,SAAS,CAAC,CAAC,CACtCiC,OAAO,CAAEuC,CAAC,IAAK;QACd,MAAMI,MAAM,GAAGL,WAAW,CAACC,CAAC,CAAC;QAC7B,MAAMK,SAAS,GAAG,IAAI,CAAC1D,OAAO,CAACyD,MAAM,CAAC;QACtC,MAAME,MAAM,GAAG,IAAI,CAAC3D,OAAO,CAACkD,GAAG,CAAC,IAAI,IAAI,CAAClD,OAAO,CAACkD,GAAG,CAAC,CAACO,MAAM,CAAC;QAE7D,IAAI,CAACC,SAAS,IAAI,CAACC,MAAM,EAAE;UACzBlF,GAAG,CAACmF,KAAK,CAAE,eAAcP,CAAE,6BAA4BH,GAAI,EAAC,CAAC;UAC7D,OAAOxC,GAAG,CAAC2C,CAAC,CAAC;QACf;MACF,CAAC,CAAC;IACN;EACF;EAEA,MAAMQ,OAAOA,CAAC;IACZ5F,eAAe,GAAGC,oBAAoB;IACtC+E,aAAa,GAAG7D,OAAO;IACvB+B,SAAS,GAAGpD,gBAAgB;IAC5B+F,UAAU,GAAGC,oBAAoB;IACjCxF,iBAAiB,GAAGC,wBAAwB;IAC5CL,mBAAmB,GAAGC,sBAAsB;IAC5CC,gBAAgB,GAAGC,uBAAuB;IAC1CoB,iBAAiB,GAAG,IAAI;IACxBsE,SAAS,GAAGlF;EACE,CAAC,GAAG,CAAC,CAAC,EAAiB;IACrC,IAAI,CAACY,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAAClC,KAAK,CAACiD,WAAW,CAAC,IAAI,CAACf,iBAAiB,CAAC;IAE9C,IAAI,CAACsD,wBAAwB,CAACC,aAAa,CAAC;IAC5C,MAAM/D,IAAI,GAAG,IAAI,CAACqC,YAAY,CAAC,CAAC;IAEhC,MAAM2B,GAAG,GAAGhE,IAAI,CAACiE,CAAC,CAAC,CAAC,CAAC;IAErB,MAAM/B,OAAO,GAAG,MAAM0C,UAAU,CAAC,IAAI,CAAC3E,kBAAkB,CAAC;IACzD,MAAM8E,UAAU,GAAG,IAAI,CAAClE,QAAQ,CAACmD,GAAG,CAAC;IAErC,IAAIhE,IAAI,CAACgF,OAAO,EAAE;MAChB,IAAI,CAAChD,iBAAiB,CAACC,SAAS,EAAEC,OAAO,CAAC;IAC5C;IAEA,IAAI2B,YAAY,GAAG;MAAE,GAAG7D,IAAI;MAAEiF,aAAa,EAAE/C;IAAQ,CAAC;IAEtD,IAAI;MACF,IAAI8B,GAAG,KAAK1C,SAAS,EAAE;QACrB,MAAM,IAAI5C,UAAU,CAAC,0CAA0C,CAAC;MAClE;MACA,IAAI,CAACqG,UAAU,EAAE;QACf,MAAM,IAAIrG,UAAU,CAAE,oBAAmBsF,GAAI,EAAC,CAAC;MACjD;MACA,IAAIc,SAAS,KAAK,YAAY,EAAE;QAC9B/F,eAAe,CAAC;UAAEmD;QAAQ,CAAC,CAAC;MAC9B;MAEA,MAAMgD,WAAW,GAAG,EAAE;MAEtB,IAAIlF,IAAI,CAACgD,eAAe,EAAE;QACxBzD,GAAG,CAACmF,KAAK,CACP,4BAA4B,GAAG,sCACjC,CAAC;QACD,MAAMS,iBAAiB,GAAG,MAAMlG,mBAAmB,CAAC,CAAC;QACrDiG,WAAW,CAACE,IAAI,CAAC,GAAGD,iBAAiB,CAAC;MACxC,CAAC,MAAM;QACL5F,GAAG,CAACmF,KAAK,CAAC,8BAA8B,CAAC;MAC3C;MAEA,IAAI1E,IAAI,CAACqF,MAAM,EAAE;QACfH,WAAW,CAACE,IAAI,CAAClH,IAAI,CAACoH,OAAO,CAACtF,IAAI,CAACqF,MAAM,CAAC,CAAC;MAC7C;MAEA,IAAIH,WAAW,CAAC3B,MAAM,EAAE;QACtB,MAAMgC,YAAY,GAAGL,WAAW,CAC7BM,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACrB,OAAO,CAAClE,OAAO,CAACC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CACzCqF,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACrB,OAAO,CAACnG,EAAE,CAACyH,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CACxCC,IAAI,CAAC,IAAI,CAAC;QACbpG,GAAG,CAAC6C,IAAI,CACN,sBAAsB,GACnB,GAAE8C,WAAW,CAAC3B,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAG,IAAG,GACzC,GAAEgC,YAAa,EACpB,CAAC;MACH;MAEAL,WAAW,CAACtD,OAAO,CAAEgE,cAAc,IAAK;QACtC,MAAMC,YAAY,GAAG1G,gBAAgB,CAACyG,cAAc,CAAC;QACrD/B,YAAY,GAAGxE,iBAAiB,CAAC;UAC/BW,IAAI,EAAE6D,YAAY;UAClBiC,WAAW,EAAE9F,IAAI;UACjB4F,cAAc;UACdC,YAAY;UACZ/E,OAAO,EAAE,IAAI,CAACA;QAChB,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF,IAAI+C,YAAY,CAACmB,OAAO,EAAE;QACxB;QACA,IAAI,CAAChD,iBAAiB,CAACC,SAAS,EAAEC,OAAO,CAAC;MAC5C;MAEA,IAAI,CAAC0B,sBAAsB,CAACC,YAAY,CAAC;MAEzC,MAAMkB,UAAU,CAAClB,YAAY,EAAE;QAAErD;MAAkB,CAAC,CAAC;IACvD,CAAC,CAAC,OAAOuF,KAAK,EAAE;MACd,IAAI,EAAEA,KAAK,YAAYrH,UAAU,CAAC,IAAImF,YAAY,CAACmB,OAAO,EAAE;QAC1DzF,GAAG,CAACwG,KAAK,CAAE,KAAIA,KAAK,CAACC,KAAM,IAAG,CAAC;MACjC,CAAC,MAAM;QACLzG,GAAG,CAACwG,KAAK,CAAE,KAAIE,MAAM,CAACF,KAAK,CAAE,IAAG,CAAC;MACnC;MACA,IAAIA,KAAK,CAACG,IAAI,EAAE;QACd3G,GAAG,CAACwG,KAAK,CAAE,eAAcA,KAAK,CAACG,IAAK,IAAG,CAAC;MAC1C;MAEA3G,GAAG,CAACmF,KAAK,CAAE,qBAAoBV,GAAI,EAAC,CAAC;MAErC,IAAI,IAAI,CAACxD,iBAAiB,EAAE;QAC1BuD,aAAa,CAACoC,IAAI,CAAC,CAAC,CAAC;MACvB,CAAC,MAAM;QACL,MAAMJ,KAAK;MACb;IACF;EACF;AACF;;AAEA;;AAKA,OAAO,eAAelB,oBAAoBA,CACxC5E,kBAA0B,EAC1B;EAAE6E,SAAS,GAAGlF;AAAuC,CAAC,GAAG,CAAC,CAAC,EAC1C;EACjB,IAAIkF,SAAS,KAAK,YAAY,EAAE;IAC9BvF,GAAG,CAACmF,KAAK,CAAC,uCAAuC,CAAC;IAClD,MAAM0B,WAAgB,GAAGjI,YAAY,CACnCD,IAAI,CAACyH,IAAI,CAAC1F,kBAAkB,EAAE,cAAc,CAC9C,CAAC;IACD,OAAOoG,IAAI,CAACC,KAAK,CAACF,WAAW,CAAC,CAAClE,OAAO;EACxC,CAAC,MAAM;IACL3C,GAAG,CAACmF,KAAK,CAAC,uCAAuC,CAAC;IAClD;IACA;IACA;IACA;IACA,MAAM6B,GAAG,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC;IACxC,OAAQ,GAAEA,GAAG,CAACC,MAAM,CAACvG,kBAAkB,CAAE,IAAGsG,GAAG,CAACE,IAAI,CAACxG,kBAAkB,CAAE,EAAC;EAC5E;AACF;;AAEA;;AASA,OAAO,SAASyG,sBAAsBA,CAACC,YAAoB,EAAO;EAChE,OAAQC,KAAU,IAAU;IAC1B,IAAInD,KAAK,CAACC,OAAO,CAACkD,KAAK,CAAC,EAAE;MACxB,MAAM,IAAIlI,UAAU,CAACiI,YAAY,CAAC;IACpC;IACA,OAAOC,KAAK;EACd,CAAC;AACH;AAEA,OAAO,eAAeC,IAAIA,CACxB5G,kBAA0B,EAC1B;EACE2E,UAAU,GAAGC,oBAAoB;EACjChE,QAAQ,GAAGpC,eAAe;EAC1BuB,IAAI;EACJ8G,UAAU,GAAG,CAAC;AACJ,CAAC,GAAG,CAAC,CAAC,EACJ;EACd,MAAMC,OAAO,GAAG,IAAIjH,OAAO,CAACE,IAAI,EAAE;IAAEC;EAAmB,CAAC,CAAC;EACzD,MAAMiC,OAAO,GAAG,MAAM0C,UAAU,CAAC3E,kBAAkB,CAAC;;EAEpD;EACA;EACA,MAAM+G,oBAAoB,GAAG;IAC3BC,QAAQ,EACN,+CAA+C,GAAG,sBAAsB;IAC1ElF,YAAY,EAAE,KAAK;IACnBmF,IAAI,EAAE;EACR,CAAC;;EAED;EACA;EACA;EACAH,OAAO,CAACzI,KAAK,CACV6I,KAAK,CACH;AACP;AACA;AACA,QAAQxH,SAAU,oBAAmBA,SAAU;AAC/C;AACA;AACA;AACA;AACA,CACI,CAAC,CACAyH,IAAI,CAAC,MAAM,CAAC,CACZC,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,CAClB7F,GAAG,CAAC7B,SAAS,CAAC,CACduC,OAAO,CAACA,OAAO,CAAC,CAChBb,aAAa,CAAC,CAAC,EAAE,4BAA4B,CAAC,CAC9CX,MAAM,CAAC,CAAC,CACR4G,iBAAiB,CAAC,CAAC;EAEtBP,OAAO,CAACtF,gBAAgB,CAAC;IACvB,YAAY,EAAE;MACZ4F,KAAK,EAAE,GAAG;MACVJ,QAAQ,EAAE,iCAAiC;MAC3CM,OAAO,EAAErH,OAAO,CAACC,GAAG,CAAC,CAAC;MACtBqH,WAAW,EAAE,IAAI;MACjBN,IAAI,EAAE,QAAQ;MACdO,MAAM,EAAGC,GAAG,IAAMA,GAAG,IAAI,IAAI,GAAGxJ,IAAI,CAACoH,OAAO,CAACoC,GAAG,CAAC,GAAGpG;IACtD,CAAC;IACD,eAAe,EAAE;MACf+F,KAAK,EAAE,GAAG;MACVJ,QAAQ,EAAE,0CAA0C;MACpDM,OAAO,EAAErJ,IAAI,CAACyH,IAAI,CAACzF,OAAO,CAACC,GAAG,CAAC,CAAC,EAAE,mBAAmB,CAAC;MACtDwH,SAAS,EAAE,IAAI;MACfH,WAAW,EAAE,IAAI;MACjBN,IAAI,EAAE;IACR,CAAC;IACDlC,OAAO,EAAE;MACPqC,KAAK,EAAE,GAAG;MACVJ,QAAQ,EAAE,qBAAqB;MAC/BC,IAAI,EAAE,SAAS;MACfnF,YAAY,EAAE;IAChB,CAAC;IACD,cAAc,EAAE;MACdsF,KAAK,EAAE,GAAG;MACVJ,QAAQ,EACN,0DAA0D,GAC1D,qDAAqD,GACrD,+BAA+B;MACjClF,YAAY,EAAE,KAAK;MACnB;MACA;MACA;MACA;MACAmF,IAAI,EAAE;IACR,CAAC;IACD,UAAU,EAAE;MACVD,QAAQ,EAAE,kDAAkD;MAC5DC,IAAI,EAAE,SAAS;MACfnF,YAAY,EAAE;IAChB,CAAC;IACDqB,KAAK,EAAE;MACL;MACA;MACAwE,MAAM,EAAE,IAAI;MACZV,IAAI,EAAE,SAAS;MACfnF,YAAY,EAAE;IAChB,CAAC;IACDsD,MAAM,EAAE;MACNgC,KAAK,EAAE,GAAG;MACVJ,QAAQ,EAAE,wCAAwC,GAAG,iBAAiB;MACtEM,OAAO,EAAEjG,SAAS;MAClBS,YAAY,EAAE,KAAK;MACnByF,WAAW,EAAE,IAAI;MACjBN,IAAI,EAAE;IACR,CAAC;IACD,kBAAkB,EAAE;MAClBD,QAAQ,EACN,8CAA8C,GAC9C,wDAAwD;MAC1DlF,YAAY,EAAE,KAAK;MACnBwF,OAAO,EAAE,IAAI;MACbL,IAAI,EAAE;IACR;EACF,CAAC,CAAC;EAEFH,OAAO,CACJhG,OAAO,CACN,OAAO,EACP,yCAAyC,EACzCF,QAAQ,CAACgH,KAAK,EACd;IACE,WAAW,EAAE;MACXZ,QAAQ,EAAE,+CAA+C;MACzDC,IAAI,EAAE;IACR,CAAC;IACDY,QAAQ,EAAE;MACRT,KAAK,EAAE,GAAG;MACVJ,QAAQ,EAAE,6CAA6C;MACvDM,OAAO,EAAEjG,SAAS;MAClBqG,SAAS,EAAE,KAAK;MAChB5F,YAAY,EAAE,KAAK;MACnByF,WAAW,EAAE,IAAI;MACjBN,IAAI,EAAE,QAAQ;MACdO,MAAM,EAAGC,GAAG,IACVA,GAAG,IAAI,IAAI,GACPpG,SAAS,GACToF,sBAAsB,CACpB,+CACF,CAAC,CAACgB,GAAG;IACb,CAAC;IACD,gBAAgB,EAAE;MAChBL,KAAK,EAAE,GAAG;MACVJ,QAAQ,EAAE,6CAA6C;MACvDC,IAAI,EAAE;IACR;EACF,CACF,CAAC,CACAnG,OAAO,CACN,MAAM,EACN,sDAAsD,EACtDF,QAAQ,CAACkH,IAAI,EACb;IACE,cAAc,EAAE;MACdd,QAAQ,EACN,8DAA8D;MAChEM,OAAO,EAAE1H,YAAY;MACrBkC,YAAY,EAAE,IAAI;MAClBmF,IAAI,EAAE;IACR,CAAC;IACD,SAAS,EAAE;MACTD,QAAQ,EAAE,8CAA8C;MACxDlF,YAAY,EAAE,IAAI;MAClBmF,IAAI,EAAE;IACR,CAAC;IACD,YAAY,EAAE;MACZD,QAAQ,EAAE,iDAAiD;MAC3DlF,YAAY,EAAE,IAAI;MAClBmF,IAAI,EAAE;IACR,CAAC;IACD,gBAAgB,EAAE;MAChBD,QAAQ,EAAE,wBAAwB;MAClCM,OAAO,EAAE,mCAAmC;MAC5CxF,YAAY,EAAE,IAAI;MAClBmF,IAAI,EAAE;IACR,CAAC;IACD,WAAW,EAAE;MACXD,QAAQ,EACN,yCAAyC,GACzC,kCAAkC;MACpClF,YAAY,EAAE,KAAK;MACnBmF,IAAI,EAAE;IACR,CAAC;IACD,oBAAoB,EAAE;MACpBD,QAAQ,EAAE,qCAAqC;MAC/ClF,YAAY,EAAE,KAAK;MACnBmF,IAAI,EAAE;IACR,CAAC;IACDc,EAAE,EAAE;MACFf,QAAQ,EACN,2DAA2D,GAC3D,4DAA4D;MAC9DlF,YAAY,EAAE,KAAK;MACnBmF,IAAI,EAAE;IACR,CAAC;IACDe,OAAO,EAAE;MACPhB,QAAQ,EAAE,iDAAiD;MAC3DC,IAAI,EAAE;IACR,CAAC;IACD,sBAAsB,EAAE;MACtBD,QAAQ,EAAE,wCAAwC;MAClDlF,YAAY,EAAE,KAAK;MACnBmF,IAAI,EAAE;IACR,CAAC;IACDgB,OAAO,EAAE;MACPjB,QAAQ,EACN,kDAAkD,GAClD,wBAAwB;MAC1BC,IAAI,EAAE;IACR,CAAC;IACD,cAAc,EAAE;MACdD,QAAQ,EACN,yDAAyD,GACzD,2BAA2B,GAC3B,0CAA0C,GAC1C,iDAAiD,GACjD,qCAAqC;MACvCC,IAAI,EAAE;IACR;EACF,CACF,CAAC,CACAnG,OAAO,CAAC,KAAK,EAAE,mBAAmB,EAAEF,QAAQ,CAACsH,GAAG,EAAE;IACjDC,MAAM,EAAE;MACNf,KAAK,EAAE,GAAG;MACVJ,QAAQ,EACN,wDAAwD,GACxD,iDAAiD;MACnDM,OAAO,EAAE,iBAAiB;MAC1BxF,YAAY,EAAE,KAAK;MACnBmF,IAAI,EAAE,OAAO;MACbmB,OAAO,EAAE,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,UAAU;IAC5D,CAAC;IACDC,OAAO,EAAE;MACPjB,KAAK,EAAE,CAAC,GAAG,EAAE,gBAAgB,CAAC;MAC9BJ,QAAQ,EACN,4DAA4D,GAC5D,kBAAkB,GAClB,sDAAsD,GACtD,2DAA2D,GAC3D,8DAA8D,GAC9D,uDAAuD,GACvD,8CAA8C;MAChDlF,YAAY,EAAE,KAAK;MACnBmF,IAAI,EAAE;IACR,CAAC;IACD,iBAAiB,EAAE;MACjBG,KAAK,EAAE,GAAG;MACVJ,QAAQ,EACN,wDAAwD,GACxD,yDAAyD,GACzD,0DAA0D,GAC1D,0CAA0C;MAC5ClF,YAAY,EAAE,KAAK;MACnBmF,IAAI,EAAE;IACR,CAAC;IACD,iBAAiB,EAAE;MACjBD,QAAQ,EACN,iDAAiD,GACjD,qDAAqD,GACrD,2DAA2D;MAC7DlF,YAAY,EAAE,KAAK;MACnBmF,IAAI,EAAE;IACR,CAAC;IACD,kBAAkB,EAAE;MAClBD,QAAQ,EAAE,mCAAmC;MAC7ClF,YAAY,EAAE,KAAK;MACnBmF,IAAI,EAAE;IACR,CAAC;IACD,2BAA2B,EAAE;MAC3BD,QAAQ,EAAE,2DAA2D;MACrElF,YAAY,EAAE,KAAK;MACnBmF,IAAI,EAAE;IACR,CAAC;IACD,sBAAsB,EAAE;MACtBD,QAAQ,EACN,yDAAyD,GACzD,4BAA4B;MAC9BlF,YAAY,EAAE,KAAK;MACnBmF,IAAI,EAAE;IACR,CAAC;IACDhE,MAAM,EAAE;MACN+D,QAAQ,EACN,gDAAgD,GAChD,2BAA2B;MAC7BlF,YAAY,EAAE,KAAK;MACnBwF,OAAO,EAAE,IAAI;MACbL,IAAI,EAAE;IACR,CAAC;IACD,YAAY,EAAE;MACZG,KAAK,EAAE,CAAC,aAAa,CAAC;MACtBJ,QAAQ,EACN,qDAAqD,GACrD,mDAAmD,GACnD,mCAAmC;MACrClF,YAAY,EAAE,KAAK;MACnBmF,IAAI,EAAE;IACR,CAAC;IACD,eAAe,EAAE;MACfD,QAAQ,EACN,8CAA8C,GAC9C,kDAAkD,GAClD,mDAAmD,GACnD,mCAAmC,GACnC,+BAA+B;MACjClF,YAAY,EAAE,KAAK;MACnBmF,IAAI,EAAE;IACR,CAAC;IACD,aAAa,EAAE;MACbD,QAAQ,EACN,oDAAoD,GACpD,yDAAyD,GACzD,aAAa;MACflF,YAAY,EAAE,KAAK;MACnBmF,IAAI,EAAE;IACR,CAAC;IACDqB,IAAI,EAAE;MACJtB,QAAQ,EACN,0CAA0C,GAC1C,oDAAoD,GACpD,kDAAkD,GAClD,aAAa;MACflF,YAAY,EAAE,KAAK;MACnByF,WAAW,EAAE,IAAI;MACjBN,IAAI,EAAE,OAAO;MACbO,MAAM,EAAGC,GAAG,IACVA,GAAG,IAAI,IAAI,GAAG5I,yBAAyB,CAAC4I,GAAG,CAAC,GAAGpG;IACnD,CAAC;IACD,WAAW,EAAE;MACX+F,KAAK,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC;MACnBJ,QAAQ,EAAE,kCAAkC;MAC5ClF,YAAY,EAAE,KAAK;MACnBmF,IAAI,EAAE;IACR,CAAC;IACDsB,QAAQ,EAAE;MACRvB,QAAQ,EACN,6CAA6C,GAC7C,yBAAyB;MAC3BlF,YAAY,EAAE,KAAK;MACnBmF,IAAI,EAAE;IACR,CAAC;IACD,iBAAiB,EAAE;MACjBG,KAAK,EAAE,CAAC,IAAI,CAAC;MACbJ,QAAQ,EAAE,oCAAoC;MAC9ClF,YAAY,EAAE,KAAK;MACnBmF,IAAI,EAAE;IACR,CAAC;IACDtE,IAAI,EAAE;MACJyE,KAAK,EAAE,CAAC,KAAK,CAAC;MACdJ,QAAQ,EAAE,qDAAqD;MAC/DlF,YAAY,EAAE,KAAK;MACnBmF,IAAI,EAAE;IACR,CAAC;IACD,iBAAiB,EAAEF,oBAAoB;IACvC;IACA,SAAS,EAAE;MACTC,QAAQ,EAAE,yCAAyC;MACnDlF,YAAY,EAAE,KAAK;MACnBmF,IAAI,EAAE,QAAQ;MACdM,WAAW,EAAE;IACf,CAAC;IACD,UAAU,EAAE;MACVP,QAAQ,EAAE,sCAAsC;MAChDlF,YAAY,EAAE,KAAK;MACnBmF,IAAI,EAAE,QAAQ;MACdM,WAAW,EAAE;IACf,CAAC;IACD,UAAU,EAAE;MACVP,QAAQ,EAAE,sCAAsC;MAChDlF,YAAY,EAAE,KAAK;MACnBmF,IAAI,EAAE,QAAQ;MACdM,WAAW,EAAE;IACf,CAAC;IACD,YAAY,EAAE;MACZH,KAAK,EAAE,CAAC,gBAAgB,CAAC;MACzBJ,QAAQ,EAAE,0CAA0C;MACpDlF,YAAY,EAAE,KAAK;MACnBmF,IAAI,EAAE,QAAQ;MACdM,WAAW,EAAE;IACf,CAAC;IACD,uBAAuB,EAAE;MACvBP,QAAQ,EAAE,iDAAiD;MAC3DlF,YAAY,EAAE,KAAK;MACnBmF,IAAI,EAAE,QAAQ;MACdM,WAAW,EAAE;IACf,CAAC;IACD,0BAA0B,EAAE;MAC1BP,QAAQ,EAAE,sDAAsD;MAChElF,YAAY,EAAE,KAAK;MACnBmF,IAAI,EAAE;IACR,CAAC;IACD,aAAa,EAAE;MACbD,QAAQ,EACN,0CAA0C,GAC1C,oCAAoC;MACtClF,YAAY,EAAE,KAAK;MACnBmF,IAAI,EAAE,QAAQ;MACdM,WAAW,EAAE;IACf,CAAC;IACD,uBAAuB,EAAE;MACvBP,QAAQ,EACN,mEAAmE;MACrElF,YAAY,EAAE,KAAK;MACnBmF,IAAI,EAAE,QAAQ;MACdM,WAAW,EAAE;IACf;EACF,CAAC,CAAC,CACDzG,OAAO,CAAC,MAAM,EAAE,+BAA+B,EAAEF,QAAQ,CAAC4H,IAAI,EAAE;IAC/DC,MAAM,EAAE;MACNrB,KAAK,EAAE,GAAG;MACVJ,QAAQ,EAAE,gCAAgC;MAC1CC,IAAI,EAAE,QAAQ;MACdK,OAAO,EAAE,MAAM;MACfc,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM;IAC1B,CAAC;IACDM,QAAQ,EAAE;MACR1B,QAAQ,EAAE,8BAA8B;MACxCC,IAAI,EAAE,SAAS;MACfK,OAAO,EAAE;IACX,CAAC;IACD,oBAAoB,EAAE;MACpBN,QAAQ,EAAE,2DAA2D;MACrEI,KAAK,EAAE,GAAG;MACVH,IAAI,EAAE,SAAS;MACfK,OAAO,EAAE;IACX,CAAC;IACDqB,MAAM,EAAE;MACN3B,QAAQ,EAAE,sBAAsB;MAChCC,IAAI,EAAE,SAAS;MACfK,OAAO,EAAE;IACX,CAAC;IACDsB,UAAU,EAAE;MACV5B,QAAQ,EAAE,gDAAgD;MAC1DC,IAAI,EAAE,SAAS;MACfK,OAAO,EAAE;IACX,CAAC;IACD,aAAa,EAAE;MACbN,QAAQ,EACN,6DAA6D,GAC7D,2CAA2C;MAC7CC,IAAI,EAAE,SAAS;MACfK,OAAO,EAAE;IACX,CAAC;IACDuB,MAAM,EAAE;MACN7B,QAAQ,EAAE,gCAAgC;MAC1CC,IAAI,EAAE,SAAS;MACfK,OAAO,EAAE;IACX,CAAC;IACD,iBAAiB,EAAEP;EACrB,CAAC,CAAC,CACDjG,OAAO,CACN,MAAM,EACN,6CAA6C,EAC7CF,QAAQ,CAACkI,IAAI,EACb,CAAC,CACH,CAAC;EAEH,OAAOhC,OAAO,CAACpC,OAAO,CAAC;IAAEC,UAAU;IAAE,GAAGkC;EAAW,CAAC,CAAC;AACvD"}