{"name": "strip-json-comments", "version": "5.0.0", "description": "Strip comments from JSON. Lets you use comments in your JSON files!", "license": "MIT", "repository": "sindresorhus/strip-json-comments", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "types": "./index.d.ts", "engines": {"node": ">=14.16"}, "scripts": {"test": "xo && ava && tsd", "bench": "matcha benchmark.js"}, "files": ["index.js", "index.d.ts"], "keywords": ["json", "strip", "comments", "remove", "delete", "trim", "multiline", "parse", "config", "configuration", "settings", "util", "env", "environment", "jsonc"], "devDependencies": {"ava": "^4.3.1", "matcha": "^0.7.0", "tsd": "^0.22.0", "xo": "^0.51.0"}, "xo": {"rules": {"complexity": "off"}}}