{"Commands:": "Příkazy:", "Options:": "Možnosti:", "Examples:": "Příklady:", "boolean": "logická hodnota", "count": "po<PERSON><PERSON>", "string": "řetě<PERSON>c", "number": "<PERSON><PERSON><PERSON>", "array": "pole", "required": "pov<PERSON><PERSON>", "default": "výchozí", "default:": "výchozí:", "choices:": "volby:", "aliases:": "aliasy:", "generated-value": "generovaná-hodnota", "Not enough non-option arguments: got %s, need at least %s": {"one": "Nedostatek argumentů: zad<PERSON><PERSON> %s, je potřeba alespoň %s", "other": "Nedostatek argumentů: zad<PERSON><PERSON> %s, je potřeba alespoň %s"}, "Too many non-option arguments: got %s, maximum of %s": {"one": "<PERSON><PERSON><PERSON><PERSON><PERSON> mnoho argumentů: zadáno %s, maximálně %s", "other": "<PERSON><PERSON><PERSON><PERSON><PERSON> mnoho argumentů: zadáno %s, maximálně %s"}, "Missing argument value: %s": {"one": "Chybí hodnota argumentu: %s", "other": "Chybí hodnoty argumentů: %s"}, "Missing required argument: %s": {"one": "Chybí p<PERSON>žado<PERSON>ý argument: %s", "other": "Chybí požadované argumenty: %s"}, "Unknown argument: %s": {"one": "Neznámý argument: %s", "other": "Neznámé argumenty: %s"}, "Invalid values:": "Neplat<PERSON>é hodnoty:", "Argument: %s, Given: %s, Choices: %s": "Argument: %s, Zadáno: %s, Možnosti: %s", "Argument check failed: %s": "Kontrola argumentů se nezdařila: %s", "Implications failed:": "Chybí zá<PERSON>lé argumenty:", "Not enough arguments following: %s": "Následuje nedostatek argumentů: %s", "Invalid JSON config file: %s": "Neplatný konfigurační soubor JSON: %s", "Path to JSON config file": "Cesta ke konfiguračnímu souboru JSON", "Show help": "Zobrazit nápovědu", "Show version number": "Zobrazit číslo verze", "Did you mean %s?": "Měl jste na mysli %s?", "Arguments %s and %s are mutually exclusive": "Argumenty %s a %s se vzájemně vylučují", "Positionals:": "Poziční:", "command": "příkaz", "deprecated": "zastara<PERSON>", "deprecated: %s": "zastaralé: %s"}