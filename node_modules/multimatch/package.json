{"name": "multimatch", "version": "6.0.0", "description": "Extends `minimatch.match()` with support for multiple patterns", "license": "MIT", "repository": "sindresorhus/multimatch", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["expand", "find", "glob", "globbing", "globs", "match", "matcher", "minimatch", "pattern", "patterns", "wildcard"], "dependencies": {"@types/minimatch": "^3.0.5", "array-differ": "^4.0.0", "array-union": "^3.0.1", "minimatch": "^3.0.4"}, "devDependencies": {"ava": "^3.15.0", "tsd": "^0.18.0", "xo": "^0.45.0"}}