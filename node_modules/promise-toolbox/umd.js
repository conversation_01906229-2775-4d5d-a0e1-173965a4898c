!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).promiseToolbox=e()}}((function(){return function e(t,r,n){function o(i,c){if(!r[i]){if(!t[i]){var a="function"==typeof require&&require;if(!c&&a)return a(i,!0);if(s)return s(i,!0);var u=new Error("Cannot find module '"+i+"'");throw u.code="MODULE_NOT_FOUND",u}var l=r[i]={exports:{}};t[i][0].call(l.exports,(function(e){return o(t[i][1][e]||e)}),l,l.exports,e,t,r,n)}return r[i].exports}for(var s="function"==typeof require&&require,i=0;i<n.length;i++)o(n[i]);return o}({1:[function(e,t,r){"use strict";t.exports=class{constructor(e="this action has been canceled"){Object.defineProperty(this,"message",{enumerable:!0,value:e})}toString(){return`Cancel: ${this.message}`}}},{}],2:[function(e,t,r){"use strict";const n=e("./defer"),o=e("./Cancel"),s=e("./isPromise"),i=e("./_noop"),c=e("./_symbols").$$toStringTag,a="CancelToken";function u(e){if(void 0!==this._reason)return;const t=this._reason=e instanceof o?e:new o(e),r=this._resolve;void 0!==r&&(this._resolve=void 0,r(t));const i=this.onabort;"function"==typeof i&&i();const c=this._handlers;if(void 0!==c){this._handlers=void 0;const e=n(),r=e.promise,o=e.resolve;let i=0;const a=()=>{if(0==--i)return o()};for(let e=0,r=c.length;e<r;++e)try{const r=c[e](t);s(r)&&(++i,r.then(a,a))}catch(e){}if(0!==i)return r}}function l(e){u.call(this,e.reason)}function f(e){const t=this._handlers;if(void 0!==t){const r=t.indexOf(e);-1!==r&&t.splice(r,1)}}const p={};function h(e){const t=this.cancel=u.bind(this.token=new d(p));null!=e&&e.forEach((e=>{const r=e.reason;if(void 0!==r)return t(r),!1;e.addHandler(t)}))}class d{static from(e){if(d.isCancelToken(e))return e;const t=new d(p);return e.addEventListener("abort",l.bind(t,e)),t}static isCancelToken(e){return null!=e&&e[c]===a}static source(e){return new h(e)}constructor(e){this._handlers=void 0,this._promise=void 0,this._reason=void 0,this._resolve=void 0,this.onabort=void 0,e!==p&&e(u.bind(this))}get promise(){let e=this._promise;if(void 0===e){const t=this._reason;e=this._promise=void 0!==t?Promise.resolve(t):new Promise((e=>{this._resolve=e}))}return e}get reason(){return this._reason}get requested(){return void 0!==this._reason}addHandler(e){let t=this._handlers;if(void 0===t){if(this.requested)throw new TypeError("cannot add a handler to an already canceled token");t=this._handlers=[]}return t.push(e),f.bind(this,e)}throwIfRequested(){const e=this._reason;if(void 0!==e)throw e}get[c](){return a}get aborted(){return this.requested}addEventListener(e,t){if("abort"!==e)return;const r={type:"abort"},n="function"==typeof t?()=>t(r):()=>t.handleEvent(r);n.listener=t,this.addHandler(n)}removeEventListener(e,t){if("abort"!==e)return;const r=this._handlers;if(void 0!==r){const e=r.findIndex((e=>e.listener===t));-1!==e&&r.splice(e,1)}}}u.call(d.canceled=new d(p)),d.none=new d(p),d.none.addHandler=function(e){return i},d.none._promise={catch(){return this},then(){return this}},t.exports=d},{"./Cancel":1,"./_noop":13,"./_symbols":17,"./defer":23,"./isPromise":36}],3:[function(e,t,r){"use strict";const n=e("./_evalDisposable"),o=e("./_isDisposable"),s=e("./_finally"),i=e("./_setFunctionNameAndLength"),c=e("./wrapApply"),a=e("./wrapCall");class u{constructor(e,t){if("function"!=typeof e)throw new Error("dispose must be a function");this._dispose=e,this._value=t}get value(){if(void 0===this._dispose)throw new TypeError("cannot get value of already disposed disposable");return this._value}dispose(){if(void 0===this._dispose)throw new TypeError("cannot dispose already disposed disposable");const e=this._dispose;return this._dispose=this._value=void 0,e()}}t.exports=u,u.all=function(e){let t=[];const r=()=>{const e=t;t=void 0,e.forEach((e=>e.dispose()))},s=e=>void 0===t?o(e)&&e.dispose():o(e)?(t.push(e),e.value):e,i=e=>{if(void 0!==t)throw r(),e};return Promise.all(Array.from(e,(e=>n(e).then(s,i)))).then((e=>new u(r,e)))};const l=e("./_ExitStack");u.factory=e=>i((function(){const t=e.apply(this,arguments),r=new l,s=r.dispose,i=r.value,c=e=>o(e)?p(i.enter(e)):e,f=({value:e})=>n(e).then(c),p=e=>a(t.next,e,t).then(f);return p().then((e=>new u((()=>a(t.return,void 0,t).then(s)),e)),(e=>{const t=()=>{throw e};return s().then(t,t)}))}),e.name,e.length);const f=e=>{const t=new l,r=t.dispose,o=t.value,i=e=>u(o.enter(e)),c=e=>e.done?e.value:n(e.value).then(i),u=t=>a(e.next,t,e).then(c);return s(u(),r)};u.use=function(){let e=arguments.length-1;if(e<0)throw new TypeError("Disposable.use expects at least 1 argument");const t=arguments[e];if(0===e)return new Promise((e=>e(t.call(this)))).then(f);let r;const n=!Array.isArray(r=arguments[0]);return n?r=Array.prototype.slice.call(arguments,0,e):e=r.length,u.all(r).then((e=>s((n?c:a)(t,e.value,this),(()=>e.dispose()))))},u.wrap=function(e){return i((function(){return u.use((()=>e.apply(this,arguments)))}),e.name,e.length)}},{"./_ExitStack":5,"./_evalDisposable":6,"./_finally":7,"./_isDisposable":9,"./_setFunctionNameAndLength":16,"./wrapApply":52,"./wrapCall":53}],4:[function(e,t,r){"use strict";const n=e("make-error").BaseError;t.exports=class extends n{constructor(){super("operation timed out")}}},{"make-error":38}],5:[function(e,t,r){"use strict";const n=e("./_isDisposable"),o=e("./_resolve");t.exports=class{constructor(){this._disposables=[];const e=()=>{const t=this._disposables.pop();return void 0!==t?o(t.dispose()).then(e):Promise.resolve()};return{dispose:e,value:this}}enter(e){if(!n(e))throw new TypeError("not a disposable");return this._disposables.push(e),e.value}}},{"./_isDisposable":9,"./_resolve":15}],6:[function(e,t,r){"use strict";const n=e("./try");t.exports=e=>"function"==typeof e?n(e):Promise.resolve(e)},{"./try":50}],7:[function(e,t,r){"use strict";t.exports=(e,t)=>e.then(t,t).then((()=>e))},{}],8:[function(e,t,r){"use strict";t.exports=e=>e},{}],9:[function(e,t,r){"use strict";t.exports=e=>null!=e&&"function"==typeof e.dispose},{}],10:[function(e,t,r){"use strict";t.exports=e=>e instanceof ReferenceError||e instanceof SyntaxError||e instanceof TypeError},{}],11:[function(e,t,r){"use strict";const n=e("./_noop"),o=e("./_once");t.exports=(e,t,r)=>{const s=t.addEventListener||t.addListener||t.on;if(void 0===s)throw new Error("cannot register event listener");const i=t.removeEventListener||t.removeListener||t.off,c=[];let a=n;return void 0!==i&&(a=o((()=>{for(let e=0,r=c.length;e<r;e+=2)i.call(t,c[e],c[e+1])})),e.promise.then(a)),r?(e,r)=>{function n(){a();const t=Array.prototype.slice.call(arguments);t.args=t,t.event=t.name=e,r(t)}c.push(e,n),s.call(t,e,n)}:(e,r)=>{const n=e=>{a(),r(e)};c.push(e,n),s.call(t,e,n)}}},{"./_noop":13,"./_once":14}],12:[function(e,t,r){"use strict";const n=e("./_isProgrammerError");t.exports=function e(t,r){if(void 0===t)return!n(r);const o=typeof t;if("boolean"===o)return t;if("function"===o)return t===Error||t.prototype instanceof Error?r instanceof t:t(r);if(Array.isArray(t)){const n=t.length;for(let o=0;o<n;++o)if(e(t[o],r))return!0;return!1}if(null!=r&&"object"===o){for(const e in t)if(hasOwnProperty.call(t,e)&&r[e]!==t[e])return!1;return!0}}},{"./_isProgrammerError":10}],13:[function(e,t,r){"use strict";t.exports=Function.prototype},{}],14:[function(e,t,r){"use strict";t.exports=e=>{let t;return function(){return void 0!==e&&(t=e.apply(this,arguments),e=void 0),t}}},{}],15:[function(e,t,r){"use strict";const n=e("./isPromise");t.exports=e=>n(e)?e:Promise.resolve(e)},{"./isPromise":36}],16:[function(e,t,r){"use strict";t.exports=(()=>{const t=Object.defineProperties;try{const e=t((function(){}),{length:{value:2},name:{value:"foo"}});if(2===e.length&&"foo"===e.name)return(e,r,n)=>t(e,{length:{configurable:!0,value:n>0?n:0},name:{configurable:!0,value:r}})}catch(e){}return e("./_identity")})()},{"./_identity":8}],17:[function(e,t,r){"use strict";const n="function"==typeof Symbol?e=>{const t=Symbol[e];return void 0!==t?t:`@@${e}`}:e=>`@@${e}`;r.$$iterator=n("iterator"),r.$$toStringTag=n("toStringTag")},{}],18:[function(e,t,r){"use strict";if("function"!=typeof Promise||"function"!=typeof Promise.reject||"function"!=typeof Promise.resolve)throw new Error("a standard Promise implementation is required (https://github.com/JsCommunity/promise-toolbox#usage)");const n=e("./isPromise"),o=e("./_symbols").$$iterator,s=r.forArray=(e,t)=>{const r=e.length;for(let n=0;n<r;++n)t(e[n],n,e)};r.forIn=(e,t)=>{for(const r in e)t(e[r],r,e)};const i=r.forIterable=(e,t)=>{const r=e[o]();let n;for(;!(n=r.next()).done;)t(n.value,void 0,e)},c=Object.prototype.hasOwnProperty,a=r.forOwn=(e,t)=>{for(const r in e)c.call(e,r)&&t(e[r],r,e)},u=r.forEach=(e,t)=>{return Array.isArray(e)?s(e,t):null!=(r=e)&&"function"==typeof r[o]?i(e,t):l(e)?s(e,t):a(e,t);var r},l=r.isArrayLike=e=>"function"!=typeof e&&null!=e&&(e=>"number"==typeof e&&e>=0&&e<1/0&&Math.floor(e)===e)(e.length);r.makeAsyncIterator=e=>{const t=(r,o)=>{if(n(r))return r.then((e=>t(e,o)));let s=Promise.resolve();return e(r,((e,t)=>{s=n(e)?s.then((()=>e.then((e=>o(e,t,r))))):s.then((()=>o(e,t,r)))})),s};return t},r.map=(e,t)=>{const r=[];return u(e,((e,n,o)=>{r.push(t(e,n,o))})),r},r.mapAuto=(e,t)=>{const r=l(e)?new Array(e.length):Object.create(null);return void 0!==t&&u(e,((e,n,o)=>{r[n]=t(e,n,o)})),r}},{"./_symbols":17,"./isPromise":36}],19:[function(e,t,r){"use strict";t.exports=function(e){return"function"==typeof e&&this.then((t=>e(void 0,t)),e),this}},{}],20:[function(e,t,r){"use strict";const n=e("./_identity"),o=e("./isPromise"),s=e("./_resolve"),i=Function.prototype;function c(e,t){let r;try{r=this._iterator[e](t)}catch(e){return this.finally(),this._reject(e)}t=r.value,r.done?(this.finally(),this._resolve(t)):this.toPromise(t).then(this.next,this._throw)}function a(e,t,r){this._iterator=e,this._reject=r,this._resolve=t,this._throw=c.bind(this,"throw"),this.next=c.bind(this,"next")}a.prototype.finally=i,a.prototype.toPromise=s;const u=e=>function(){return new Promise(((t,r)=>new a(e.apply(this,arguments),t,r).next()))};function l(e){a.apply(this,[].slice.call(arguments,1)),this._cancelToken=e,this._onCancel=i,this.finally=e.addHandler((e=>(this._onCancel(e),new Promise((e=>{this.finally=e})))))}Object.setPrototypeOf(l.prototype,Object.getPrototypeOf(a.prototype)).toPromise=function(e){if(Array.isArray(e))return s(e[0]);const t=this._cancelToken;return t.requested?Promise.reject(t.reason):o(e)?new Promise(((t,r)=>{e.then(t,r),this._onCancel=r})):Promise.resolve(e)},u.cancelable=(e,t=n)=>function(){const r=t.apply(this,arguments);return r.requested?Promise.reject(r.reason):new Promise(((t,n)=>{new l(r,e.apply(this,arguments),t,n).next()}))},t.exports=u},{"./_identity":8,"./_resolve":15,"./isPromise":36}],21:[function(e,t,r){"use strict";const n=e("./_setFunctionNameAndLength"),o=e("./CancelToken"),s=o.isCancelToken,i=o.source;t.exports=(e,t,r)=>{const o=void 0!==r?r.value:e,c=n((function(){const e=arguments.length;if(0!==e&&s(arguments[0]))return o.apply(this,arguments);const t=i(),r=t.cancel,n=t.token,c=new Array(e+1);c[0]=n;for(let t=0;t<e;++t)c[t+1]=arguments[t];const a=o.apply(this,c);return a.cancel=r,a}),o.name,o.length-1);return void 0!==r?(r.value=c,r):c}},{"./CancelToken":2,"./_setFunctionNameAndLength":16}],22:[function(e,t,r){"use strict";const n=e("./_matchError");function o(e,t,r){return n(e,r)?t(r):this}t.exports=function(){let e,t=arguments.length;return 0===t||"function"!=typeof(e=arguments[--t])?this:this.then(void 0,o.bind(this,0===t?void 0:1===t?arguments[0]:Array.prototype.slice.call(arguments,0,t),e))}},{"./_matchError":12}],23:[function(e,t,r){"use strict";t.exports=()=>{let e,t;return{promise:new Promise(((r,n)=>{e=r,t=n})),reject:t,resolve:e}}},{}],24:[function(e,t,r){"use strict";const n=e("./isPromise");t.exports=function(e){const t=2===arguments.length?arguments[1]:this;if(n(t))return t.then((t=>new Promise((r=>{setTimeout(r,e,t)}))));let r;const o=new Promise((n=>{r=setTimeout(n,e,t)}));return o.unref=()=>(null!=r&&"function"==typeof r.unref&&r.unref(),o),o}},{"./isPromise":36}],25:[function(e,t,r){"use strict";t.exports=function(e){return this.then(e,e).then((()=>this))}},{}],26:[function(e,t,r){"use strict";t.exports=e("./makeAsyncIterator")(e("./_utils").forArray)},{"./_utils":18,"./makeAsyncIterator":37}],27:[function(e,t,r){"use strict";t.exports=e("./makeAsyncIterator")(e("./_utils").forEach)},{"./_utils":18,"./makeAsyncIterator":37}],28:[function(e,t,r){"use strict";t.exports=e("./makeAsyncIterator")(e("./_utils").forIn)},{"./_utils":18,"./makeAsyncIterator":37}],29:[function(e,t,r){"use strict";t.exports=e("./makeAsyncIterator")(e("./_utils").forIterable)},{"./_utils":18,"./makeAsyncIterator":37}],30:[function(e,t,r){"use strict";t.exports=e("./makeAsyncIterator")(e("./_utils").forOwn)},{"./_utils":18,"./makeAsyncIterator":37}],31:[function(e,t,r){"use strict";function n(e,t,r,n){t.push(((e,t)=>null!=e&&!1!==e?n(e):r(t))),e.apply(this,t)}t.exports=function(e,...t){return new Promise(n.bind(this,"function"==typeof e?e:this[e],t))}},{}],32:[function(e,t,r){"use strict";const n=e("./cancelable"),o=e("./_makeEventAdder"),s=n(((e,t,r,n={})=>new Promise(((s,i)=>{const c=o(e,t,n.array);if(c(r,s),!n.ignoreErrors){const e=n.error,t=void 0===e?"error":e;t!==r&&c(t,i)}}))));t.exports=s},{"./_makeEventAdder":11,"./cancelable":21}],33:[function(e,t,r){"use strict";const n=e("./cancelable"),o=e("./_makeEventAdder"),s=e("./_utils").forArray,i=n(((e,t,r,n=["error"])=>new Promise(((i,c)=>{const a=o(e,t,!0);s(r,(e=>a(e,i))),s(n,(e=>a(e,c)))}))));t.exports=i},{"./_makeEventAdder":11,"./_utils":18,"./cancelable":21}],34:[function(e,t,r){"use strict";const n=e("./_isProgrammerError"),o=e=>{if(n(e))throw e};t.exports=function(){return this.then(void 0,o)}},{"./_isProgrammerError":10}],35:[function(e,t,r){"use strict";r.pAsCallback=r.asCallback=e("./asCallback"),r.pAsyncFn=r.asyncFn=e("./asyncFn"),r.pCancel=r.Cancel=e("./Cancel"),r.pCancelable=r.cancelable=e("./cancelable"),r.pCancelToken=r.CancelToken=e("./CancelToken"),r.pCatch=r.catch=e("./catch"),r.pDefer=r.defer=e("./defer"),r.pDelay=r.delay=e("./delay"),r.pDisposable=r.Disposable=e("./Disposable"),r.pFinally=r.finally=e("./finally"),r.pForArray=r.forArray=e("./forArray"),r.pForEach=r.forEach=e("./forEach"),r.pForIn=r.forIn=e("./forIn"),r.pForIterable=r.forIterable=e("./forIterable"),r.pForOwn=r.forOwn=e("./forOwn"),r.pFromCallback=r.fromCallback=e("./fromCallback"),r.pFromEvent=r.fromEvent=e("./fromEvent"),r.pFromEvents=r.fromEvents=e("./fromEvents"),r.pIgnoreErrors=r.ignoreErrors=e("./ignoreErrors"),r.pIsPromise=r.isPromise=e("./isPromise"),r.pMakeAsyncIterator=r.makeAsyncIterator=e("./makeAsyncIterator"),r.pNodeify=r.nodeify=e("./nodeify"),r.pPipe=r.pipe=e("./pipe"),r.pPromisify=r.promisify=e("./promisify"),r.pPromisifyAll=r.promisifyAll=e("./promisifyAll"),r.pReflect=r.reflect=e("./reflect"),r.pRetry=r.retry=e("./retry"),r.pSome=r.some=e("./some"),r.pSuppressUnhandledRejections=r.suppressUnhandledRejections=e("./suppressUnhandledRejections"),r.pTap=r.tap=e("./tap"),r.pTapCatch=r.tapCatch=e("./tapCatch"),r.pTimeout=r.timeout=e("./timeout"),r.pTimeoutError=r.TimeoutError=e("./TimeoutError"),r.pTry=r.try=e("./try"),r.pUnpromisify=r.unpromisify=e("./unpromisify"),r.pWrapApply=r.wrapApply=e("./wrapApply"),r.pWrapCall=r.wrapCall=e("./wrapCall")},{"./Cancel":1,"./CancelToken":2,"./Disposable":3,"./TimeoutError":4,"./asCallback":19,"./asyncFn":20,"./cancelable":21,"./catch":22,"./defer":23,"./delay":24,"./finally":25,"./forArray":26,"./forEach":27,"./forIn":28,"./forIterable":29,"./forOwn":30,"./fromCallback":31,"./fromEvent":32,"./fromEvents":33,"./ignoreErrors":34,"./isPromise":36,"./makeAsyncIterator":37,"./nodeify":39,"./pipe":40,"./promisify":41,"./promisifyAll":42,"./reflect":43,"./retry":44,"./some":45,"./suppressUnhandledRejections":46,"./tap":47,"./tapCatch":48,"./timeout":49,"./try":50,"./unpromisify":51,"./wrapApply":52,"./wrapCall":53}],36:[function(e,t,r){"use strict";t.exports=e=>null!=e&&"function"==typeof e.then},{}],37:[function(e,t,r){"use strict";const n=e("./_noop"),o=e("./_utils").makeAsyncIterator;t.exports=e=>{const t=o(e);return function(e){return t(this,e).then(n)}}},{"./_noop":13,"./_utils":18}],38:[function(e,t,r){"use strict";var n="undefined"!=typeof Reflect?Reflect.construct:void 0,o=Object.defineProperty,s=Error.captureStackTrace;function i(e){void 0!==e&&o(this,"message",{configurable:!0,value:e,writable:!0});var t=this.constructor.name;void 0!==t&&t!==this.name&&o(this,"name",{configurable:!0,value:t,writable:!0}),s(this,this.constructor)}void 0===s&&(s=function(e){var t=new Error;o(e,"stack",{configurable:!0,get:function(){var e=t.stack;return o(this,"stack",{configurable:!0,value:e,writable:!0}),e},set:function(t){o(e,"stack",{configurable:!0,value:t,writable:!0})}})}),i.prototype=Object.create(Error.prototype,{constructor:{configurable:!0,value:i,writable:!0}});var c=function(){function e(e,t){return o(e,"name",{configurable:!0,value:t})}try{var t=function(){};if(e(t,"foo"),"foo"===t.name)return e}catch(e){}}();r=t.exports=function(e,t){if(null==t||t===Error)t=i;else if("function"!=typeof t)throw new TypeError("super_ should be a function");var r;if("string"==typeof e)r=e,e=void 0!==n?function(){return n(t,arguments,this.constructor)}:function(){t.apply(this,arguments)},void 0!==c&&(c(e,r),r=void 0);else if("function"!=typeof e)throw new TypeError("constructor should be either a string or a function");e.super_=e.super=t;var o={constructor:{configurable:!0,value:e,writable:!0}};return void 0!==r&&(o.name={configurable:!0,value:r,writable:!0}),e.prototype=Object.create(t.prototype,o),e},r.BaseError=i},{}],39:[function(e,t,r){"use strict";const n=e("./_setFunctionNameAndLength"),o=e("./wrapApply"),s=Array.prototype.slice;t.exports=e=>n((function(){const t=arguments.length-1;let r;if(t<0||"function"!=typeof(r=arguments[t]))throw new TypeError("missing callback");const n=s.call(arguments,0,t);o(e,n).then((e=>r(void 0,e)),r)}),e.name,e.length+1)},{"./_setFunctionNameAndLength":16,"./wrapApply":52}],40:[function(e,t,r){"use strict";const n=Array.isArray,o=Array.prototype.slice,s=(e,t)=>e.then(t);t.exports=function(e){return n(e)||(e=o.call(arguments)),"function"!=typeof e[0]?(e[0]=Promise.resolve(e[0]),e.reduce(s)):t=>e.reduce(s,Promise.resolve(t))}},{}],41:[function(e,t,r){"use strict";const n=e("./_setFunctionNameAndLength");t.exports=(e,t)=>n((function(){const r=arguments.length,n=new Array(r+1);for(let e=0;e<r;++e)n[e]=arguments[e];return new Promise(((o,s)=>{n[r]=(e,t)=>null!=e&&!1!==e?s(e):o(t),e.apply(void 0===t?this:t,n)}))}),e.name,e.length-1)},{"./_setFunctionNameAndLength":16}],42:[function(e,t,r){"use strict";const n=e("./promisify"),o=e("./_utils").forIn,s=(e,t)=>!(t.endsWith("Sync")||t.endsWith("Async"))&&t;t.exports=(e,{mapper:t=s,target:r={},context:i=e}={})=>(o(e,((o,s)=>{let c;"function"==typeof o&&(c=t(o,s,e))&&(r[c]=n(o,i))})),r)},{"./_utils":18,"./promisify":41}],43:[function(e,t,r){"use strict";const n=()=>!1,o=()=>!0,s=(i={isFulfilled:o,isPending:n,isRejected:n,reason:()=>{throw new Error("no reason, the promise has resolved")}},e=>({__proto__:i,value:()=>e}));var i;const c=(e=>t=>({__proto__:e,reason:()=>t}))({isFulfilled:n,isPending:n,isRejected:o,value:()=>{throw new Error("no value, the promise has rejected")}});t.exports=function(){return this.then(s,c)}},{}],44:[function(e,t,r){"use strict";const n=e("./_matchError"),o=e("./_noop"),s=e("./_setFunctionNameAndLength");function i(e,{delay:t,delays:r,onRetry:s=o,retries:i,tries:a,when:u}={},l){let f;if(void 0!==r){if(void 0!==t||void 0!==a||void 0!==i)throw new TypeError("delays is incompatible with delay, tries and retries");const e=r[Symbol.iterator]();f=()=>{const r=e.next(),n=r.done,o=r.value;return!n&&(t=o,!0)}}else{if(void 0===a)a=void 0!==i?i+1:10;else if(void 0!==i)throw new TypeError("retries and tries options are mutually exclusive");void 0===t&&(t=1e3),f=()=>0!=--a}u=n.bind(void 0,u);let p=0;const h=e=>setTimeout(e,t),d=()=>new Promise(h),y=r=>{if(r instanceof c)throw r.error;if(u(r)&&f()){let n=Promise.resolve(s.call({arguments:l,attemptNumber:p++,delay:t,fn:e,this:this},r));return 0!==t&&(n=n.then(d)),n.then(v)}throw r},m=t=>t(e.apply(this,l)),v=()=>new Promise(m).catch(y);return v()}function c(e){this.error=e}t.exports=i,i.bail=function(e){throw new c(e)},i.wrap=function(e,t){const r="function"!=typeof t?()=>t:t;return s((function(){return i.call(this,e,r.apply(this,arguments),Array.from(arguments))}),e.name,e.length)}},{"./_matchError":12,"./_noop":13,"./_setFunctionNameAndLength":16}],45:[function(e,t,r){"use strict";const n=e("./_resolve"),o=e("./_utils").forEach;t.exports=function(e){return n(this).then((t=>((e,t)=>new Promise(((r,n)=>{let s=[],i=[];const c=e=>{s&&(s.push(e),0==--t&&(r(s),s=i=void 0))};let a=-t;const u=e=>{s&&(i.push(e),0==--a&&(n(i),s=i=void 0))};o(e,(e=>{++a,r(e).then(c,u)}))})))(t,e)))}},{"./_resolve":15,"./_utils":18}],46:[function(e,t,r){"use strict";const n=e("./_noop");t.exports=function(){const e=this.suppressUnhandledRejections;return"function"==typeof e?e.call(this):this.then(void 0,n),this}},{"./_noop":13}],47:[function(e,t,r){"use strict";t.exports=function(e,t){return this.then(e,t).then((()=>this))}},{}],48:[function(e,t,r){"use strict";t.exports=function(e){return this.then(void 0,e).then((()=>this))}},{}],49:[function(e,t,r){"use strict";const n=e("./TimeoutError");t.exports=function(e,t){return 0===e?this:(void 0===t&&(t=new n),new Promise(((r,n)=>{let o=setTimeout((()=>{if(o=void 0,"function"==typeof this.cancel&&this.cancel(),"function"==typeof t)try{r(t())}catch(e){n(e)}else n(t)}),e);this.then((e=>{void 0!==o&&clearTimeout(o),r(e)}),(e=>{void 0!==o&&clearTimeout(o),n(e)}))})))}},{"./TimeoutError":4}],50:[function(e,t,r){"use strict";const n=e("./_resolve");t.exports=function(e){try{return n(e())}catch(e){return Promise.reject(e)}}},{"./_resolve":15}],51:[function(e,t,r){"use strict";const n=e("./_setFunctionNameAndLength");t.exports=function(){const e=this;return n((function(){const t=arguments.length-1;let r;if(t<0||"function"!=typeof(r=arguments[t]))throw new Error("missing callback");const n=new Array(t);for(let e=0;e<t;++e)n[e]=arguments[e];e.apply(this,n).then((e=>r(void 0,e)),(e=>r(e)))}),e.name,e.length+1)}},{"./_setFunctionNameAndLength":16}],52:[function(e,t,r){"use strict";const n=e("./_resolve");t.exports=(e,t,r)=>{try{return n(e.apply(r,t))}catch(e){return Promise.reject(e)}}},{"./_resolve":15}],53:[function(e,t,r){"use strict";const n=e("./_resolve");t.exports=(e,t,r)=>{try{return n(e.call(r,t))}catch(e){return Promise.reject(e)}}},{"./_resolve":15}]},{},[35])(35)}));
