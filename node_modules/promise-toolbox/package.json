{"name": "promise-toolbox", "version": "0.21.0", "license": "ISC", "description": "Essential utils for promises", "keywords": ["callback", "cancel", "cancellable", "cancelable", "cancellation", "cancelation", "token", "CancelToken", "compose", "delay", "event", "fromCallback", "fromEvent", "fromEvents", "nodeify", "pipe", "promise", "promisification", "promisify", "retry", "sleep", "thenification", "thenify", "timeout", "utils"], "homepage": "https://github.com/JsCommunity/promise-toolbox", "bugs": "https://github.com/JsCommunity/promise-toolbox/issues", "repository": {"type": "git", "url": "https://github.com/JsCommunity/promise-toolbox"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "browserslist": [">2%"], "engines": {"node": ">=6"}, "dependencies": {"make-error": "^1.3.2"}, "devDependencies": {"@babel/cli": "^7.0.0", "@babel/core": "^7.0.0", "@babel/eslint-parser": "^7.13.14", "@babel/plugin-proposal-function-bind": "^7.0.0", "@babel/preset-env": "^7.0.0", "babelify": "^10.0.0", "browserify": "^17.0.0", "cross-env": "^7.0.3", "eslint": "^7.25.0", "eslint-config-prettier": "^8.3.0", "eslint-config-standard": "^16.0.2", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.3.1", "husky": "^4.3.8", "jest": "^26.6.3", "lint-staged": "^10.5.4", "prettier": "^2.2.1", "rimraf": "^3.0.0", "terser": "^5.7.0"}, "scripts": {"build": "cross-env NODE_ENV=production babel --out-dir=./ src/", "clean": "rimraf '*.js' '*.js.map'", "dev": "cross-env NODE_ENV=development babel --watch --out-dir=./ src/", "dev-test": "jest --bail --watch", "postbuild": "./.update-exports.js && browserify -s promiseToolbox index.js | terser -cm > umd.js", "prebuild": "npm run clean", "predev": "npm run prebuild", "prepublishOnly": "npm run build", "pretest": "eslint --ignore-path .gitignore src/", "test": "jest"}, "jest": {"testEnvironment": "node", "roots": ["<rootDir>/src"], "testRegex": "\\.spec\\.js$"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.js": ["echo", "./.update-exports.js", "prettier --write", "eslint --ignore-pattern '!*'", "jest --findRelatedTests --passWithNoTests"]}, "exports": {"./Cancel": "./Cancel.js", "./CancelToken": "./CancelToken.js", "./Disposable": "./Disposable.js", "./TimeoutError": "./TimeoutError.js", "./asCallback": "./asCallback.js", "./asyncFn": "./asyncFn.js", "./cancelable": "./cancelable.js", "./catch": "./catch.js", "./defer": "./defer.js", "./delay": "./delay.js", "./finally": "./finally.js", "./fixtures": "./fixtures.js", "./forArray": "./forArray.js", "./forEach": "./forEach.js", "./forIn": "./forIn.js", "./forIterable": "./forIterable.js", "./forOwn": "./forOwn.js", "./fromCallback": "./fromCallback.js", "./fromEvent": "./fromEvent.js", "./fromEvents": "./fromEvents.js", "./ignoreErrors": "./ignoreErrors.js", ".": "./index.js", "./isPromise": "./isPromise.js", "./makeAsyncIterator": "./makeAsyncIterator.js", "./nodeify": "./nodeify.js", "./pipe": "./pipe.js", "./promisify": "./promisify.js", "./promisifyAll": "./promisifyAll.js", "./reflect": "./reflect.js", "./retry": "./retry.js", "./some": "./some.js", "./suppressUnhandledRejections": "./suppressUnhandledRejections.js", "./tap": "./tap.js", "./tapCatch": "./tapCatch.js", "./timeout": "./timeout.js", "./try": "./try.js", "./unpromisify": "./unpromisify.js", "./wrapApply": "./wrapApply.js", "./wrapCall": "./wrapCall.js"}}